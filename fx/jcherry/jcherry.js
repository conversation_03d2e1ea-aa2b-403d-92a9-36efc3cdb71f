import { FxElement, html } from '/fx.js';

import { $styles } from './jcherry.x.js';

customElements.define('fx-jcherry', class FxJCherry extends FxElement {
    static properties = {
        src: { type: String, notify: true },
        isPreview: { type: Boolean, default: false },
        editMode: { type: Boolean, default: false, notify: true },
        marquee: { type: String, default: '' },
        cell: { type: Object, default: undefined, notify: true },
        h: { type: Number, default: 4 }
    }
    'editMode-changed'() {
        this['src-changed']();
    }
    'cell-changed'() {
        if (this.cell) {
            this.src = this.cell.source;
        }
    }
    'src-changed'() {
        if (!this.isReady) return;
        this.async(async () => {
            if (this.editMode) {
                await this.init();
            } else {
                let htmlContent = this.cherryEngineInstance.makeHtml(this.cell?.source || this.src || '');
                if (this.marquee)
                    htmlContent = `<marquee>${this.marquee}</marquee>` + htmlContent;
                this.$qs('#html-view').innerHTML = htmlContent;
                htmlContent = style + htmlContent;
                const blob = new Blob([htmlContent], { type: 'text/html' });
                this.checkedURL = URL.createObjectURL(blob);
            }
            this.$update();
        }, 20)
    }

    firstUpdated() {
        super.firstUpdated();
        this.async(async () => {
            await this.init();
        }, 100)
    }
    async init() {
        if (this.editMode) {
            const iframe = this.$qs('iframe');
            iframe.addEventListener('load', async e => {
                let w = iframe.contentWindow;
                this.cherry = w.cherry;
                this.isReady = true;
                iframe.style.opacity = 1;
                this.cherry?.setValue(this.cell?.source || this.src || '');
                setTimeout(() => {
                    this.cherry.editor.editor.on('change', e => {
                        let src = this.cherry.getMarkdown(),
                            html = this.cherry.getHtml();
                        this.fire('change', { src, html });
                        if (this.cell)
                            this.cell.source = src;
                        this.$update();
                    })
                }, 1000)
            })
            iframe.srcdoc = srcdoc(this.isPreview, this.isPreview ? toolbarsPreview : toolbars);
        } else {
            if (this.cherryEngineInstance) return;
            ['woff', 'woff2', 'eot', 'ttf', 'svg'].forEach(format => {
                document.fonts.add(new FontFace('ch-icon', `url(/fx/jcherry/dist/fonts/ch-icon.${format})`));
            })
            await new Promise((r) => setTimeout(r, 0));
            const m = await import('/fx/jcherry/dist/cherry-markdown.engine.core.esm.js');
            let CherryEngine = m.default;
            this.cherryEngineInstance = new CherryEngine();
            this.isReady = true;
            this['src-changed']();
        }
    }

    static styles = [$styles.main];

    render() {
        return html`
            ${this.editMode ? html`
                <iframe style="width: 100%; height: 100%; border: none; overflow: hidden; min-height: 0px;"></iframe>
            ` : html`
                <link rel="stylesheet" href="/fx/jcherry/dist/cherry-markdown.min.css">
                <div id="html-wrapper">
                    <div id="html-view"></div>
                </div>
            `}
        `
    }
})

const style = `
<!DOCTYPE html>
<meta charset="utf-8">
<link rel="stylesheet" href="${FX.$url.replace('fx.js', '')}fx/jcherry/dist/cherry-markdown.min.css">
<style>
    html, body {
        overflow: auto;
        font-family: Arial;
    }
    .anchor {
        color: transparent !important;
    }
    .cherry-previewer {
        background-color: white;
        padding: 16px;
    }
    .cherry-markdown blockquote {
        border-left: 2px solid #d6dbdf;
    }
    .cherry-markdown h1, .cherry-markdown h2, .cherry-markdown h3, .cherry-markdown h4, .cherry-markdown h5, .cherry-markdown h6, .cherry-markdown .h1, .cherry-markdown .h2, .cherry-markdown .h3, .cherry-markdown .h4, .cherry-markdown .h5, .cherry-markdown .h6 {
        font-weight: 400; 
    }
    [alt=tr5] {
        transform:translate(0, 5px);
    }
    [alt=tr6] {
        transform:translate(0, 6px);
    }
    [alt=tr7] {
        transform:translate(0, 7px);
    }
    [alt=tr8] {
        transform:translate(0, 8px);
    }
    [alt=tr9] {
        transform:translate(0, 9px);
    }
    [alt=tr10] {
        transform:translate(0, 10px);
    }
    [alt=tr11] {
        transform:translate(0, 11px);
    }
    [alt=tr12] {
        transform:translate(0, 12px);
    }
</style>
`
const srcdoc = (isPreview, toolbars) => {
    return `
<link rel="stylesheet" href="/fx/jcherry/dist/cherry-markdown.min.css">
<div id="markdown-container"></div>
<script src="/fx/jcherry/dist/cherry-markdown.min.js"></script>
${style}
<style>
    html, body {
        padding: 0;
        margin: 0;
        width: 100%;
        height: 100%;
    }
</style>
<script type="module">
    var basicConfig = {
        id: 'markdown',
        isPreviewOnly: ${isPreview ? true : false},
        engine: {
            syntax: {
                codeBlock: {
                    theme: 'twilight',
                },
                table: {
                    enableChart: false,
                },
                fontEmphasis: {
                    allowWhitespace: false,
                },
                strikethrough: {
                    needWhitespace: false,
                },
                emoji: {
                    useUnicode: true,
                    customResourceURL: 'https://github.githubassets.com/images/icons/emoji/unicode/\${code}.png?v8',
                    upperCase: true,
                }
            }
        },
        ${toolbars},
        editor: {
            defaultModel: 'edit&preview',
        },
        editor: {
            id: 'cherry-md',
            name: 'cherry-md',
            autoSave2Textarea: true,
            locale: 'en_EN'
        }
    }
    var config = Object.assign({}, basicConfig);
    window.cherry = new Cherry(config);
</script>

`
}

const toolbarsPreview = `
toolbars: {
    showToolbar: false,
    toolbar: false,
    bubble: false,
    float: false,
}
`
const toolbars = `
toolbars: {
    toolbar: [
        'bold',
        'italic',
        {
            strikethrough: ['strikethrough', 'underline', 'sub', 'sup'],
        },
        'size',
        '|',
        'color',
        'header',
        '|',
        'ol',
        'ul',
        'checklist',
        'panel',
        'justify',
        'detail',
        '|',
        {
            insert: ['image', 'audio', 'video', 'link', 'hr', 'br'],
        },
        'togglePreview'
    ],
    bubble: ['bold', 'italic', 'underline', 'strikethrough', 'sub', 'sup', 'quote', '|', 'size', 'color'],
}
`
