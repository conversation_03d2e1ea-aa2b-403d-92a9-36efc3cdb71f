import { css } from '/fx.js';

export const $styles = css`
    * { box-sizing: border-box; }
    :host {
        position: relative;
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        overflow: hidden;
        color: gray;
    }
    #prev, #next {
        cursor: pointer;
        font-size: 14px;
        text-decoration: underline;
        user-select: none;
        font-weight: 600;
        width: 33%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        min-width: 22px;
    }
`
