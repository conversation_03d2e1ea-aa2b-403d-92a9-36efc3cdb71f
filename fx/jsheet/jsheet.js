import { FxElement, html, css } from '../../fx.js';
import '../jtmp/jtmp.js';

const distPath = import.meta.url.split('/').slice(0, -1).join('/') + '/dist/';

customElements.define('fx-jsheet', class FxJsheet extends FxElement {
    static properties = {
        cell: { type: Object },
        src: {
            type: String, default: `{
                worksheets: [{ minDimensions: [3, 1] }],
                toolbar: true,
                tabs: false
            }`
        },
        idx: { type: Number, default: 0 }
    }

    get worksSheets() {
        try {
            return JSON.parse(this.cell?.source || this.src)?.worksheets || [];
        } catch { return [] }
    }
    get ifs() { return this.worksSheets.map((_, idx) => `if${idx}`) }

    firstUpdated() {
        super.firstUpdated();
        this.listen('is-ok', e => this.grid = e.detail);
        this.delay();
        if (this.cell?.source) {
            this.src = this.cell.source;
        }
        this.listen('change', e => {
            if (!this.showSettings && this.cell && e.detail !== undefined) {
                this.cell.source = e.detail;
                this.$update();
            }
        })
    }

    aceWorksheetsSrc(idx) {
        try {
            return JSON.stringify(this.grid?.[0].parent.getConfig().worksheets[idx] || {}, null, 4);
        } catch { return '{}' }
    }

    runCmd(e) {
        const { isTab, isBtn, btn: cmd, idx } = e.detail;
        this.idx = +(idx || 0);

        const cmds = {
            close: () => {
                this.showSettings = false;
                this.$update();
            },
            save: () => {
                let worksheet = this.$qs('#ace-worksheets').value;
                try {
                    if (this.idx === 0) {
                        this.src = worksheet;
                    } else {
                        try {
                            worksheet = JSON.parse(worksheet);
                            const src = JSON.parse(this.cell?.source || this.src);
                            src.worksheets[this.idx - 1] = worksheet;
                            this.src = JSON.stringify(src);
                        } catch { }
                    }
                    if (this.cell) {
                        this.cell.source = this.src;
                    }
                    this.$update();
                } catch (error) {
                    console.error('Error saving worksheet:', error);
                }
            }
        }

        cmds[cmd]?.();
        this.$update();
    }

    render() {
        return html`
            <fx-jtmp id="editor" src=${this.src} .srcdoc=${srcdoc} editMode></fx-jtmp>
        `
    }
})

const srcdoc = (src) => {
    return `
<script src="${distPath}jspreadsheet.js"></script>
<script src="${distPath}jsuites.js"></script>
<link rel="stylesheet" href="${distPath}jspreadsheet.css" type="text/css" />
<link rel="stylesheet" href="${distPath}jsuites.css" type="text/css" />
<style>
* {
    font-family: Roboto, Tahoma, Verdana, sans-serif;
}
@font-face {
    font-family: 'Material Icons';
    font-style: normal;
    font-weight: 400;
    src: url(${distPath}woff.woff2) format('woff2');
}
.material-icons {
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    font-size: 24px;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
}
</style>

<div id="spreadsheet" style="overflow: auto;"></div>

<script>
const copy = () => {
    return JSON.stringify(grid[0].parent.getConfig());
}
const createHandler = () => {
    document.dispatchEvent(new CustomEvent('change', { detail: copy() }));
}
let grid = window.gridJspreadsheet = jspreadsheet(document.getElementById('spreadsheet'), ${src});
if (grid?.[0]?.parent) {
    const config = grid[0].parent.config;
    config.onchange = () => { createHandler() }
    config.oninsertrow = () => { createHandler() }
    config.ondeleterow = () => { createHandler() }
    config.oninsertcolumn = () => { createHandler() }
    config.ondeletecolumn = () => { createHandler() }
    config.onmoverow = () => { createHandler() }
    config.onmovecolumn = () => { createHandler() }
    config.onresizerow = () => { createHandler() }
    config.onresizecolumn = () => { createHandler() }
    config.onmerge = () => { createHandler() }
    config.onchangeheader = () => { createHandler() }
    config.onchangestyle = () => { createHandler() }
    config.onchangemeta = () => { createHandler() }
    config.onundo = () => { createHandler() }
    config.onredo = () => { createHandler() }
    config.oncreateworksheet = () => { createHandler() }
    config.ondeleteworksheet = () => { createHandler() }
    setTimeout(() => document.dispatchEvent(new CustomEvent('is-ok', { detail: grid })), 100)
}
</script>
    `
}
