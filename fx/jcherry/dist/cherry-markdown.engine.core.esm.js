var e="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function t(e,t){return e(t={exports:{}},t.exports),t.exports}var n,r,a=function(e){return e&&e.Math==Math&&e},i=a("object"==typeof globalThis&&globalThis)||a("object"==typeof window&&window)||a("object"==typeof self&&self)||a("object"==typeof e&&e)||function(){return this}()||Function("return this")(),o=function(e){try{return!!e()}catch(e){return!0}},s=!o((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")})),c=Function.prototype,l=c.apply,u=c.call,f="object"==typeof Reflect&&Reflect.apply||(s?u.bind(l):function(){return u.apply(l,arguments)}),d=Function.prototype,p=d.bind,h=d.call,g=s&&p.bind(h,h),m=s?function(e){return e&&g(e)}:function(e){return e&&function(){return h.apply(e,arguments)}},b=function(e){return"function"==typeof e},v=!o((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),y=Function.prototype.call,_=s?y.bind(y):function(){return y.apply(y,arguments)},k={}.propertyIsEnumerable,w=Object.getOwnPropertyDescriptor,E={f:w&&!k.call({1:2},1)?function(e){var t=w(this,e);return!!t&&t.enumerable}:k},S=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},A=m({}.toString),x=m("".slice),C=function(e){return x(A(e),8,-1)},T=i.Object,$=m("".split),R=o((function(){return!T("z").propertyIsEnumerable(0)}))?function(e){return"String"==C(e)?$(e,""):T(e)}:T,O=i.TypeError,P=function(e){if(null==e)throw O("Can't call method on "+e);return e},L=function(e){return R(P(e))},I=function(e){return"object"==typeof e?null!==e:b(e)},N={},M=function(e){return b(e)?e:void 0},j=function(e,t){return arguments.length<2?M(N[e])||M(i[e]):N[e]&&N[e][t]||i[e]&&i[e][t]},D=m({}.isPrototypeOf),B=j("navigator","userAgent")||"",F=i.process,H=i.Deno,z=F&&F.versions||H&&H.version,U=z&&z.v8;U&&(r=(n=U.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!r&&B&&(!(n=B.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=B.match(/Chrome\/(\d+)/))&&(r=+n[1]);var W=r,q=!!Object.getOwnPropertySymbols&&!o((function(){var e=Symbol();return!String(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&W&&W<41})),G=q&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,K=i.Object,Z=G?function(e){return"symbol"==typeof e}:function(e){var t=j("Symbol");return b(t)&&D(t.prototype,K(e))},Y=i.String,X=function(e){try{return Y(e)}catch(e){return"Object"}},V=i.TypeError,J=function(e){if(b(e))return e;throw V(X(e)+" is not a function")},Q=function(e,t){var n=e[t];return null==n?void 0:J(n)},ee=i.TypeError,te=Object.defineProperty,ne="__core-js_shared__",re=i[ne]||function(e,t){try{te(i,e,{value:t,configurable:!0,writable:!0})}catch(n){i[e]=t}return t}(ne,{}),ae=t((function(e){(e.exports=function(e,t){return re[e]||(re[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.22.6",mode:"pure",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.22.6/LICENSE",source:"https://github.com/zloirock/core-js"})})),ie=i.Object,oe=function(e){return ie(P(e))},se=m({}.hasOwnProperty),ce=Object.hasOwn||function(e,t){return se(oe(e),t)},le=0,ue=Math.random(),fe=m(1..toString),de=function(e){return"Symbol("+(void 0===e?"":e)+")_"+fe(++le+ue,36)},pe=ae("wks"),he=i.Symbol,ge=he&&he.for,me=G?he:he&&he.withoutSetter||de,be=function(e){if(!ce(pe,e)||!q&&"string"!=typeof pe[e]){var t="Symbol."+e;q&&ce(he,e)?pe[e]=he[e]:pe[e]=G&&ge?ge(t):me(t)}return pe[e]},ve=i.TypeError,ye=be("toPrimitive"),_e=function(e,t){if(!I(e)||Z(e))return e;var n,r=Q(e,ye);if(r){if(void 0===t&&(t="default"),n=_(r,e,t),!I(n)||Z(n))return n;throw ve("Can't convert object to primitive value")}return void 0===t&&(t="number"),function(e,t){var n,r;if("string"===t&&b(n=e.toString)&&!I(r=_(n,e)))return r;if(b(n=e.valueOf)&&!I(r=_(n,e)))return r;if("string"!==t&&b(n=e.toString)&&!I(r=_(n,e)))return r;throw ee("Can't convert object to primitive value")}(e,t)},ke=function(e){var t=_e(e,"string");return Z(t)?t:t+""},we=i.document,Ee=I(we)&&I(we.createElement),Se=function(e){return Ee?we.createElement(e):{}},Ae=!v&&!o((function(){return 7!=Object.defineProperty(Se("div"),"a",{get:function(){return 7}}).a})),xe=Object.getOwnPropertyDescriptor,Ce={f:v?xe:function(e,t){if(e=L(e),t=ke(t),Ae)try{return xe(e,t)}catch(e){}if(ce(e,t))return S(!_(E.f,e,t),e[t])}},Te=/#|\.prototype\./,$e=function(e,t){var n=Oe[Re(e)];return n==Le||n!=Pe&&(b(t)?o(t):!!t)},Re=$e.normalize=function(e){return String(e).replace(Te,".").toLowerCase()},Oe=$e.data={},Pe=$e.NATIVE="N",Le=$e.POLYFILL="P",Ie=$e,Ne=m(m.bind),Me=function(e,t){return J(e),void 0===t?e:s?Ne(e,t):function(){return e.apply(t,arguments)}},je=v&&o((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),De=i.String,Be=i.TypeError,Fe=function(e){if(I(e))return e;throw Be(De(e)+" is not an object")},He=i.TypeError,ze=Object.defineProperty,Ue=Object.getOwnPropertyDescriptor,We="enumerable",qe="configurable",Ge="writable",Ke={f:v?je?function(e,t,n){if(Fe(e),t=ke(t),Fe(n),"function"==typeof e&&"prototype"===t&&"value"in n&&Ge in n&&!n[Ge]){var r=Ue(e,t);r&&r[Ge]&&(e[t]=n.value,n={configurable:qe in n?n[qe]:r[qe],enumerable:We in n?n[We]:r[We],writable:!1})}return ze(e,t,n)}:ze:function(e,t,n){if(Fe(e),t=ke(t),Fe(n),Ae)try{return ze(e,t,n)}catch(e){}if("get"in n||"set"in n)throw He("Accessors not supported");return"value"in n&&(e[t]=n.value),e}},Ze=v?function(e,t,n){return Ke.f(e,t,S(1,n))}:function(e,t,n){return e[t]=n,e},Ye=Ce.f,Xe=function(e){var t=function(n,r,a){if(this instanceof t){switch(arguments.length){case 0:return new e;case 1:return new e(n);case 2:return new e(n,r)}return new e(n,r,a)}return f(e,this,arguments)};return t.prototype=e.prototype,t},Ve=function(e,t){var n,r,a,o,s,c,l,u,f=e.target,d=e.global,p=e.stat,h=e.proto,g=d?i:p?i[f]:(i[f]||{}).prototype,v=d?N:N[f]||Ze(N,f,{})[f],y=v.prototype;for(a in t)n=!Ie(d?a:f+(p?".":"#")+a,e.forced)&&g&&ce(g,a),s=v[a],n&&(c=e.dontCallGetSet?(u=Ye(g,a))&&u.value:g[a]),o=n&&c?c:t[a],n&&typeof s==typeof o||(l=e.bind&&n?Me(o,i):e.wrap&&n?Xe(o):h&&b(o)?m(o):o,(e.sham||o&&o.sham||s&&s.sham)&&Ze(l,"sham",!0),Ze(v,a,l),h&&(ce(N,r=f+"Prototype")||Ze(N,r,{}),Ze(N[r],a,o),e.real&&y&&!y[a]&&Ze(y,a,o)))},Je=m([].slice),Qe=i.Function,et=m([].concat),tt=m([].join),nt={},rt=s?Qe.bind:function(e){var t=J(this),n=t.prototype,r=Je(arguments,1),a=function(){var n=et(r,Je(arguments));return this instanceof a?function(e,t,n){if(!ce(nt,t)){for(var r=[],a=0;a<t;a++)r[a]="a["+a+"]";nt[t]=Qe("C,a","return new C("+tt(r,",")+")")}return nt[t](e,n)}(t,n.length,n):t.apply(e,n)};return I(n)&&(a.prototype=n),a},at={};at[be("toStringTag")]="z";var it="[object z]"===String(at),ot=be("toStringTag"),st=i.Object,ct="Arguments"==C(function(){return arguments}()),lt=it?C:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=st(e),ot))?n:ct?C(t):"Object"==(r=C(t))&&b(t.callee)?"Arguments":r},ut=m(Function.toString);b(re.inspectSource)||(re.inspectSource=function(e){return ut(e)});var ft=re.inspectSource,dt=function(){},pt=[],ht=j("Reflect","construct"),gt=/^\s*(?:class|function)\b/,mt=m(gt.exec),bt=!gt.exec(dt),vt=function(e){if(!b(e))return!1;try{return ht(dt,pt,e),!0}catch(e){return!1}},yt=function(e){if(!b(e))return!1;switch(lt(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return bt||!!mt(gt,ft(e))}catch(e){return!0}};yt.sham=!0;var _t,kt=!ht||o((function(){var e;return vt(vt.call)||!vt(Object)||!vt((function(){e=!0}))||e}))?yt:vt,wt=i.TypeError,Et=function(e){if(kt(e))return e;throw wt(X(e)+" is not a constructor")},St=Math.ceil,At=Math.floor,xt=Math.trunc||function(e){var t=+e;return(t>0?At:St)(t)},Ct=function(e){var t=+e;return t!=t||0===t?0:xt(t)},Tt=Math.max,$t=Math.min,Rt=function(e,t){var n=Ct(e);return n<0?Tt(n+t,0):$t(n,t)},Ot=Math.min,Pt=function(e){return e>0?Ot(Ct(e),9007199254740991):0},Lt=function(e){return Pt(e.length)},It=function(e){return function(t,n,r){var a,i=L(t),o=Lt(i),s=Rt(r,o);if(e&&n!=n){for(;o>s;)if((a=i[s++])!=a)return!0}else for(;o>s;s++)if((e||s in i)&&i[s]===n)return e||s||0;return!e&&-1}},Nt={includes:It(!0),indexOf:It(!1)},Mt={},jt=Nt.indexOf,Dt=m([].push),Bt=function(e,t){var n,r=L(e),a=0,i=[];for(n in r)!ce(Mt,n)&&ce(r,n)&&Dt(i,n);for(;t.length>a;)ce(r,n=t[a++])&&(~jt(i,n)||Dt(i,n));return i},Ft=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],Ht=Object.keys||function(e){return Bt(e,Ft)},zt=v&&!je?Object.defineProperties:function(e,t){Fe(e);for(var n,r=L(t),a=Ht(t),i=a.length,o=0;i>o;)Ke.f(e,n=a[o++],r[n]);return e},Ut={f:zt},Wt=j("document","documentElement"),qt=ae("keys"),Gt=function(e){return qt[e]||(qt[e]=de(e))},Kt="prototype",Zt="script",Yt=Gt("IE_PROTO"),Xt=function(){},Vt=function(e){return"<"+Zt+">"+e+"</"+Zt+">"},Jt=function(e){e.write(Vt("")),e.close();var t=e.parentWindow.Object;return e=null,t},Qt=function(){try{_t=new ActiveXObject("htmlfile")}catch(e){}var e,t,n;Qt="undefined"!=typeof document?document.domain&&_t?Jt(_t):(t=Se("iframe"),n="java"+Zt+":",t.style.display="none",Wt.appendChild(t),t.src=String(n),(e=t.contentWindow.document).open(),e.write(Vt("document.F=Object")),e.close(),e.F):Jt(_t);for(var r=Ft.length;r--;)delete Qt[Kt][Ft[r]];return Qt()};Mt[Yt]=!0;var en=Object.create||function(e,t){var n;return null!==e?(Xt[Kt]=Fe(e),n=new Xt,Xt[Kt]=null,n[Yt]=e):n=Qt(),void 0===t?n:Ut.f(n,t)},tn=j("Reflect","construct"),nn=Object.prototype,rn=[].push,an=o((function(){function e(){}return!(tn((function(){}),[],e)instanceof e)})),on=!o((function(){tn((function(){}))})),sn=an||on;Ve({target:"Reflect",stat:!0,forced:sn,sham:sn},{construct:function(e,t){Et(e),Fe(t);var n=arguments.length<3?e:Et(arguments[2]);if(on&&!an)return tn(e,t,n);if(e==n){switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3])}var r=[null];return f(rn,r,t),new(f(rt,e,r))}var a=n.prototype,i=en(I(a)?a:nn),o=f(e,i,t);return I(o)?o:i}});var cn=N.Reflect.construct,ln=cn,un=Ke.f;Ve({target:"Object",stat:!0,forced:Object.defineProperty!==un,sham:!v},{defineProperty:un});var fn=t((function(e){var t=N.Object,n=e.exports=function(e,n,r){return t.defineProperty(e,n,r)};t.defineProperty.sham&&(n.sham=!0)})),dn=fn,pn=dn;function hn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),pn(e,r.key,r)}}function gn(e,t,n){return t&&hn(e.prototype,t),n&&hn(e,n),pn(e,"prototype",{writable:!1}),e}function mn(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Ve({target:"Object",stat:!0,sham:!v},{create:en});var bn=N.Object,vn=function(e,t){return bn.create(e,t)},yn=vn,_n=i.String,kn=i.TypeError,wn=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=m(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(n,[]),t=n instanceof Array}catch(e){}return function(n,r){return Fe(n),function(e){if("object"==typeof e||b(e))return e;throw kn("Can't set "+_n(e)+" as a prototype")}(r),t?e(n,r):n.__proto__=r,n}}():void 0);Ve({target:"Object",stat:!0},{setPrototypeOf:wn});var En=N.Object.setPrototypeOf;function Sn(e,t){return Sn=En||function(e,t){return e.__proto__=t,e},Sn(e,t)}function An(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=yn(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),pn(e,"prototype",{writable:!1}),t&&Sn(e,t)}var xn=Array.isArray||function(e){return"Array"==C(e)},Cn=function(e,t,n){var r=ke(t);r in e?Ke.f(e,r,S(0,n)):e[r]=n},Tn=be("species"),$n=i.Array,Rn=function(e,t){return new(function(e){var t;return xn(e)&&(t=e.constructor,(kt(t)&&(t===$n||xn(t.prototype))||I(t)&&null===(t=t[Tn]))&&(t=void 0)),void 0===t?$n:t}(e))(0===t?0:t)},On=be("species"),Pn=function(e){return W>=51||!o((function(){var t=[];return(t.constructor={})[On]=function(){return{foo:1}},1!==t[e](Boolean).foo}))},Ln=be("isConcatSpreadable"),In=9007199254740991,Nn="Maximum allowed index exceeded",Mn=i.TypeError,jn=W>=51||!o((function(){var e=[];return e[Ln]=!1,e.concat()[0]!==e})),Dn=Pn("concat"),Bn=function(e){if(!I(e))return!1;var t=e[Ln];return void 0!==t?!!t:xn(e)};Ve({target:"Array",proto:!0,arity:1,forced:!jn||!Dn},{concat:function(e){var t,n,r,a,i,o=oe(this),s=Rn(o,0),c=0;for(t=-1,r=arguments.length;t<r;t++)if(Bn(i=-1===t?o:arguments[t])){if(c+(a=Lt(i))>In)throw Mn(Nn);for(n=0;n<a;n++,c++)n in i&&Cn(s,c,i[n])}else{if(c>=In)throw Mn(Nn);Cn(s,c++,i)}return s.length=c,s}});var Fn,Hn,zn,Un=i.String,Wn=function(e){if("Symbol"===lt(e))throw TypeError("Cannot convert a Symbol value to a string");return Un(e)},qn=Ft.concat("length","prototype"),Gn={f:Object.getOwnPropertyNames||function(e){return Bt(e,qn)}},Kn=i.Array,Zn=Math.max,Yn=Gn.f,Xn="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Vn=function(e){try{return Yn(e)}catch(e){return function(e,t,n){for(var r=Lt(e),a=Rt(t,r),i=Rt(void 0===n?r:n,r),o=Kn(Zn(i-a,0)),s=0;a<i;a++,s++)Cn(o,s,e[a]);return o.length=s,o}(Xn)}},Jn={f:function(e){return Xn&&"Window"==C(e)?Vn(e):Yn(L(e))}},Qn={f:Object.getOwnPropertySymbols},er=function(e,t,n,r){return r&&r.enumerable?e[t]=n:Ze(e,t,n),e},tr={f:be},nr=Ke.f,rr=function(e){var t=N.Symbol||(N.Symbol={});ce(t,e)||nr(t,e,{value:tr.f(e)})},ar=function(){var e=j("Symbol"),t=e&&e.prototype,n=t&&t.valueOf,r=be("toPrimitive");t&&!t[r]&&er(t,r,(function(e){return _(n,this)}),{arity:1})},ir=it?{}.toString:function(){return"[object "+lt(this)+"]"},or=Ke.f,sr=be("toStringTag"),cr=function(e,t,n,r){if(e){var a=n?e:e.prototype;ce(a,sr)||or(a,sr,{configurable:!0,value:t}),r&&!it&&Ze(a,"toString",ir)}},lr=i.WeakMap,ur=b(lr)&&/native code/.test(ft(lr)),fr="Object already initialized",dr=i.TypeError,pr=i.WeakMap;if(ur||re.state){var hr=re.state||(re.state=new pr),gr=m(hr.get),mr=m(hr.has),br=m(hr.set);Fn=function(e,t){if(mr(hr,e))throw new dr(fr);return t.facade=e,br(hr,e,t),t},Hn=function(e){return gr(hr,e)||{}},zn=function(e){return mr(hr,e)}}else{var vr=Gt("state");Mt[vr]=!0,Fn=function(e,t){if(ce(e,vr))throw new dr(fr);return t.facade=e,Ze(e,vr,t),t},Hn=function(e){return ce(e,vr)?e[vr]:{}},zn=function(e){return ce(e,vr)}}var yr={set:Fn,get:Hn,has:zn,enforce:function(e){return zn(e)?Hn(e):Fn(e,{})},getterFor:function(e){return function(t){var n;if(!I(t)||(n=Hn(t)).type!==e)throw dr("Incompatible receiver, "+e+" required");return n}}},_r=m([].push),kr=function(e){var t=1==e,n=2==e,r=3==e,a=4==e,i=6==e,o=7==e,s=5==e||i;return function(c,l,u,f){for(var d,p,h=oe(c),g=R(h),m=Me(l,u),b=Lt(g),v=0,y=f||Rn,_=t?y(c,b):n||o?y(c,0):void 0;b>v;v++)if((s||v in g)&&(p=m(d=g[v],v,h),e))if(t)_[v]=p;else if(p)switch(e){case 3:return!0;case 5:return d;case 6:return v;case 2:_r(_,d)}else switch(e){case 4:return!1;case 7:_r(_,d)}return i?-1:r||a?a:_}},wr={forEach:kr(0),map:kr(1),filter:kr(2),some:kr(3),every:kr(4),find:kr(5),findIndex:kr(6),filterReject:kr(7)},Er=wr.forEach,Sr=Gt("hidden"),Ar="Symbol",xr="prototype",Cr=yr.set,Tr=yr.getterFor(Ar),$r=Object[xr],Rr=i.Symbol,Or=Rr&&Rr[xr],Pr=i.TypeError,Lr=i.QObject,Ir=Ce.f,Nr=Ke.f,Mr=Jn.f,jr=E.f,Dr=m([].push),Br=ae("symbols"),Fr=ae("op-symbols"),Hr=ae("wks"),zr=!Lr||!Lr[xr]||!Lr[xr].findChild,Ur=v&&o((function(){return 7!=en(Nr({},"a",{get:function(){return Nr(this,"a",{value:7}).a}})).a}))?function(e,t,n){var r=Ir($r,t);r&&delete $r[t],Nr(e,t,n),r&&e!==$r&&Nr($r,t,r)}:Nr,Wr=function(e,t){var n=Br[e]=en(Or);return Cr(n,{type:Ar,tag:e,description:t}),v||(n.description=t),n},qr=function(e,t,n){e===$r&&qr(Fr,t,n),Fe(e);var r=ke(t);return Fe(n),ce(Br,r)?(n.enumerable?(ce(e,Sr)&&e[Sr][r]&&(e[Sr][r]=!1),n=en(n,{enumerable:S(0,!1)})):(ce(e,Sr)||Nr(e,Sr,S(1,{})),e[Sr][r]=!0),Ur(e,r,n)):Nr(e,r,n)},Gr=function(e,t){Fe(e);var n=L(t),r=Ht(n).concat(Xr(n));return Er(r,(function(t){v&&!_(Kr,n,t)||qr(e,t,n[t])})),e},Kr=function(e){var t=ke(e),n=_(jr,this,t);return!(this===$r&&ce(Br,t)&&!ce(Fr,t))&&(!(n||!ce(this,t)||!ce(Br,t)||ce(this,Sr)&&this[Sr][t])||n)},Zr=function(e,t){var n=L(e),r=ke(t);if(n!==$r||!ce(Br,r)||ce(Fr,r)){var a=Ir(n,r);return!a||!ce(Br,r)||ce(n,Sr)&&n[Sr][r]||(a.enumerable=!0),a}},Yr=function(e){var t=Mr(L(e)),n=[];return Er(t,(function(e){ce(Br,e)||ce(Mt,e)||Dr(n,e)})),n},Xr=function(e){var t=e===$r,n=Mr(t?Fr:L(e)),r=[];return Er(n,(function(e){!ce(Br,e)||t&&!ce($r,e)||Dr(r,Br[e])})),r};q||(Or=(Rr=function(){if(D(Or,this))throw Pr("Symbol is not a constructor");var e=arguments.length&&void 0!==arguments[0]?Wn(arguments[0]):void 0,t=de(e),n=function(e){this===$r&&_(n,Fr,e),ce(this,Sr)&&ce(this[Sr],t)&&(this[Sr][t]=!1),Ur(this,t,S(1,e))};return v&&zr&&Ur($r,t,{configurable:!0,set:n}),Wr(t,e)})[xr],er(Or,"toString",(function(){return Tr(this).tag})),er(Rr,"withoutSetter",(function(e){return Wr(de(e),e)})),E.f=Kr,Ke.f=qr,Ut.f=Gr,Ce.f=Zr,Gn.f=Jn.f=Yr,Qn.f=Xr,tr.f=function(e){return Wr(be(e),e)},v&&Nr(Or,"description",{configurable:!0,get:function(){return Tr(this).description}})),Ve({global:!0,constructor:!0,wrap:!0,forced:!q,sham:!q},{Symbol:Rr}),Er(Ht(Hr),(function(e){rr(e)})),Ve({target:Ar,stat:!0,forced:!q},{useSetter:function(){zr=!0},useSimple:function(){zr=!1}}),Ve({target:"Object",stat:!0,forced:!q,sham:!v},{create:function(e,t){return void 0===t?en(e):Gr(en(e),t)},defineProperty:qr,defineProperties:Gr,getOwnPropertyDescriptor:Zr}),Ve({target:"Object",stat:!0,forced:!q},{getOwnPropertyNames:Yr}),ar(),cr(Rr,Ar),Mt[Sr]=!0;var Vr=q&&!!Symbol.for&&!!Symbol.keyFor,Jr=ae("string-to-symbol-registry"),Qr=ae("symbol-to-string-registry");Ve({target:"Symbol",stat:!0,forced:!Vr},{for:function(e){var t=Wn(e);if(ce(Jr,t))return Jr[t];var n=j("Symbol")(t);return Jr[t]=n,Qr[n]=t,n}});var ea=ae("symbol-to-string-registry");Ve({target:"Symbol",stat:!0,forced:!Vr},{keyFor:function(e){if(!Z(e))throw TypeError(X(e)+" is not a symbol");if(ce(ea,e))return ea[e]}});var ta=j("JSON","stringify"),na=m(/./.exec),ra=m("".charAt),aa=m("".charCodeAt),ia=m("".replace),oa=m(1..toString),sa=/[\uD800-\uDFFF]/g,ca=/^[\uD800-\uDBFF]$/,la=/^[\uDC00-\uDFFF]$/,ua=!q||o((function(){var e=j("Symbol")();return"[null]"!=ta([e])||"{}"!=ta({a:e})||"{}"!=ta(Object(e))})),fa=o((function(){return'"\\udf06\\ud834"'!==ta("\udf06\ud834")||'"\\udead"'!==ta("\udead")})),da=function(e,t){var n=Je(arguments),r=t;if((I(t)||void 0!==e)&&!Z(e))return xn(t)||(t=function(e,t){if(b(r)&&(t=_(r,this,e,t)),!Z(t))return t}),n[1]=t,f(ta,null,n)},pa=function(e,t,n){var r=ra(n,t-1),a=ra(n,t+1);return na(ca,e)&&!na(la,a)||na(la,e)&&!na(ca,r)?"\\u"+oa(aa(e,0),16):e};ta&&Ve({target:"JSON",stat:!0,arity:3,forced:ua||fa},{stringify:function(e,t,n){var r=Je(arguments),a=f(ua?da:ta,null,r);return fa&&"string"==typeof a?ia(a,sa,pa):a}});var ha=!q||o((function(){Qn.f(1)}));Ve({target:"Object",stat:!0,forced:ha},{getOwnPropertySymbols:function(e){var t=Qn.f;return t?t(oe(e)):[]}}),rr("asyncIterator"),rr("hasInstance"),rr("isConcatSpreadable"),rr("iterator"),rr("match"),rr("matchAll"),rr("replace"),rr("search"),rr("species"),rr("split"),rr("toPrimitive"),ar(),rr("toStringTag"),cr(j("Symbol"),"Symbol"),rr("unscopables"),cr(i.JSON,"JSON",!0);var ga,ma,ba,va=N.Symbol,ya={},_a=Function.prototype,ka=v&&Object.getOwnPropertyDescriptor,wa=ce(_a,"name"),Ea={EXISTS:wa,PROPER:wa&&"something"===function(){}.name,CONFIGURABLE:wa&&(!v||v&&ka(_a,"name").configurable)},Sa=!o((function(){function e(){}return e.prototype.constructor=null,Object.getPrototypeOf(new e)!==e.prototype})),Aa=Gt("IE_PROTO"),xa=i.Object,Ca=xa.prototype,Ta=Sa?xa.getPrototypeOf:function(e){var t=oe(e);if(ce(t,Aa))return t[Aa];var n=t.constructor;return b(n)&&t instanceof n?n.prototype:t instanceof xa?Ca:null},$a=be("iterator"),Ra=!1;[].keys&&("next"in(ba=[].keys())?(ma=Ta(Ta(ba)))!==Object.prototype&&(ga=ma):Ra=!0);var Oa=null==ga||o((function(){var e={};return ga[$a].call(e)!==e}));ga=Oa?{}:en(ga),b(ga[$a])||er(ga,$a,(function(){return this}));var Pa={IteratorPrototype:ga,BUGGY_SAFARI_ITERATORS:Ra},La=Pa.IteratorPrototype,Ia=function(){return this},Na=Ea.PROPER,Ma=Pa.BUGGY_SAFARI_ITERATORS,ja=be("iterator"),Da="keys",Ba="values",Fa="entries",Ha=function(){return this},za=function(e,t,n,r,a,i,o){!function(e,t,n,r){var a=t+" Iterator";e.prototype=en(La,{next:S(+!r,n)}),cr(e,a,!1,!0),ya[a]=Ia}(n,t,r);var s,c,l,u=function(e){if(e===a&&g)return g;if(!Ma&&e in p)return p[e];switch(e){case Da:case Ba:case Fa:return function(){return new n(this,e)}}return function(){return new n(this)}},f=t+" Iterator",d=!1,p=e.prototype,h=p[ja]||p["@@iterator"]||a&&p[a],g=!Ma&&h||u(a),m="Array"==t&&p.entries||h;if(m&&(s=Ta(m.call(new e)))!==Object.prototype&&s.next&&(cr(s,f,!0,!0),ya[f]=Ha),Na&&a==Ba&&h&&h.name!==Ba&&(d=!0,g=function(){return _(h,this)}),a)if(c={values:u(Ba),keys:i?g:u(Da),entries:u(Fa)},o)for(l in c)(Ma||d||!(l in p))&&er(p,l,c[l]);else Ve({target:t,proto:!0,forced:Ma||d},c);return o&&p[ja]!==g&&er(p,ja,g,{name:a}),ya[t]=g,c};Ke.f;var Ua="Array Iterator",Wa=yr.set,qa=yr.getterFor(Ua);za(Array,"Array",(function(e,t){Wa(this,{type:Ua,target:L(e),index:0,kind:t})}),(function(){var e=qa(this),t=e.target,n=e.kind,r=e.index++;return!t||r>=t.length?(e.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:t[r],done:!1}:{value:[r,t[r]],done:!1}}),"values"),ya.Arguments=ya.Array;var Ga=be("toStringTag");for(var Ka in{CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}){var Za=i[Ka],Ya=Za&&Za.prototype;Ya&&lt(Ya)!==Ga&&Ze(Ya,Ga,Ka),ya[Ka]=ya.Array}var Xa=va,Va=Xa;rr("asyncDispose"),rr("dispose"),rr("matcher"),rr("metadata"),rr("observable"),rr("patternMatch"),rr("replaceAll");var Ja=Va,Qa=m("".charAt),ei=m("".charCodeAt),ti=m("".slice),ni=function(e){return function(t,n){var r,a,i=Wn(P(t)),o=Ct(n),s=i.length;return o<0||o>=s?e?"":void 0:(r=ei(i,o))<55296||r>56319||o+1===s||(a=ei(i,o+1))<56320||a>57343?e?Qa(i,o):r:e?ti(i,o,o+2):a-56320+(r-55296<<10)+65536}},ri={codeAt:ni(!1),charAt:ni(!0)}.charAt,ai="String Iterator",ii=yr.set,oi=yr.getterFor(ai);za(String,"String",(function(e){ii(this,{type:ai,string:Wn(e),index:0})}),(function(){var e,t=oi(this),n=t.string,r=t.index;return r>=n.length?{value:void 0,done:!0}:(e=ri(n,r),t.index+=e.length,{value:e,done:!1})}));var si=tr.f("iterator");function ci(e){return ci="function"==typeof Ja&&"symbol"==typeof si?function(e){return typeof e}:function(e){return e&&"function"==typeof Ja&&e.constructor===Ja&&e!==Ja.prototype?"symbol":typeof e},ci(e)}function li(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ui(e,t){if(t&&("object"===ci(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return li(e)}var fi=o((function(){Ta(1)}));Ve({target:"Object",stat:!0,forced:fi,sham:!Sa},{getPrototypeOf:function(e){return Ta(oe(e))}});var di=N.Object.getPrototypeOf;function pi(e){return pi=En?di:function(e){return e.__proto__||di(e)},pi(e)}function hi(e,t,n){return t in e?pn(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var gi=function(){this.__data__=[],this.size=0};var mi=function(e,t){return e===t||e!=e&&t!=t};var bi=function(e,t){for(var n=e.length;n--;)if(mi(e[n][0],t))return n;return-1},vi=Array.prototype.splice;var yi=function(e){var t=this.__data__,n=bi(t,e);return!(n<0)&&(n==t.length-1?t.pop():vi.call(t,n,1),--this.size,!0)};var _i=function(e){var t=this.__data__,n=bi(t,e);return n<0?void 0:t[n][1]};var ki=function(e){return bi(this.__data__,e)>-1};var wi=function(e,t){var n=this.__data__,r=bi(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};function Ei(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Ei.prototype.clear=gi,Ei.prototype.delete=yi,Ei.prototype.get=_i,Ei.prototype.has=ki,Ei.prototype.set=wi;var Si=Ei;var Ai=function(){this.__data__=new Si,this.size=0};var xi=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n};var Ci=function(e){return this.__data__.get(e)};var Ti=function(e){return this.__data__.has(e)},$i="object"==typeof e&&e&&e.Object===Object&&e,Ri="object"==typeof self&&self&&self.Object===Object&&self,Oi=$i||Ri||Function("return this")(),Pi=Oi.Symbol,Li=Object.prototype,Ii=Li.hasOwnProperty,Ni=Li.toString,Mi=Pi?Pi.toStringTag:void 0;var ji=function(e){var t=Ii.call(e,Mi),n=e[Mi];try{e[Mi]=void 0;var r=!0}catch(e){}var a=Ni.call(e);return r&&(t?e[Mi]=n:delete e[Mi]),a},Di=Object.prototype.toString;var Bi=function(e){return Di.call(e)},Fi=Pi?Pi.toStringTag:void 0;var Hi=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Fi&&Fi in Object(e)?ji(e):Bi(e)};var zi=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)};var Ui=function(e){if(!zi(e))return!1;var t=Hi(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},Wi=Oi["__core-js_shared__"],qi=function(){var e=/[^.]+$/.exec(Wi&&Wi.keys&&Wi.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();var Gi=function(e){return!!qi&&qi in e},Ki=Function.prototype.toString;var Zi=function(e){if(null!=e){try{return Ki.call(e)}catch(e){}try{return e+""}catch(e){}}return""},Yi=/^\[object .+?Constructor\]$/,Xi=Function.prototype,Vi=Object.prototype,Ji=Xi.toString,Qi=Vi.hasOwnProperty,eo=RegExp("^"+Ji.call(Qi).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");var to=function(e){return!(!zi(e)||Gi(e))&&(Ui(e)?eo:Yi).test(Zi(e))};var no=function(e,t){return null==e?void 0:e[t]};var ro=function(e,t){var n=no(e,t);return to(n)?n:void 0},ao=ro(Oi,"Map"),io=ro(Object,"create");var oo=function(){this.__data__=io?io(null):{},this.size=0};var so=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},co=Object.prototype.hasOwnProperty;var lo=function(e){var t=this.__data__;if(io){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return co.call(t,e)?t[e]:void 0},uo=Object.prototype.hasOwnProperty;var fo=function(e){var t=this.__data__;return io?void 0!==t[e]:uo.call(t,e)};var po=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=io&&void 0===t?"__lodash_hash_undefined__":t,this};function ho(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}ho.prototype.clear=oo,ho.prototype.delete=so,ho.prototype.get=lo,ho.prototype.has=fo,ho.prototype.set=po;var go=ho;var mo=function(){this.size=0,this.__data__={hash:new go,map:new(ao||Si),string:new go}};var bo=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e};var vo=function(e,t){var n=e.__data__;return bo(t)?n["string"==typeof t?"string":"hash"]:n.map};var yo=function(e){var t=vo(this,e).delete(e);return this.size-=t?1:0,t};var _o=function(e){return vo(this,e).get(e)};var ko=function(e){return vo(this,e).has(e)};var wo=function(e,t){var n=vo(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this};function Eo(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}Eo.prototype.clear=mo,Eo.prototype.delete=yo,Eo.prototype.get=_o,Eo.prototype.has=ko,Eo.prototype.set=wo;var So=Eo;var Ao=function(e,t){var n=this.__data__;if(n instanceof Si){var r=n.__data__;if(!ao||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new So(r)}return n.set(e,t),this.size=n.size,this};function xo(e){var t=this.__data__=new Si(e);this.size=t.size}xo.prototype.clear=Ai,xo.prototype.delete=xi,xo.prototype.get=Ci,xo.prototype.has=Ti,xo.prototype.set=Ao;var Co=xo,To=function(){try{var e=ro(Object,"defineProperty");return e({},"",{}),e}catch(e){}}();var $o=function(e,t,n){"__proto__"==t&&To?To(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n};var Ro=function(e,t,n){(void 0!==n&&!mi(e[t],n)||void 0===n&&!(t in e))&&$o(e,t,n)};var Oo=function(e){return function(t,n,r){for(var a=-1,i=Object(t),o=r(t),s=o.length;s--;){var c=o[e?s:++a];if(!1===n(i[c],c,i))break}return t}}(),Po=t((function(e,t){var n=t&&!t.nodeType&&t,r=n&&e&&!e.nodeType&&e,a=r&&r.exports===n?Oi.Buffer:void 0,i=a?a.allocUnsafe:void 0;e.exports=function(e,t){if(t)return e.slice();var n=e.length,r=i?i(n):new e.constructor(n);return e.copy(r),r}})),Lo=Oi.Uint8Array;var Io=function(e){var t=new e.constructor(e.byteLength);return new Lo(t).set(new Lo(e)),t};var No=function(e,t){var n=t?Io(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.length)};var Mo=function(e,t){var n=-1,r=e.length;for(t||(t=Array(r));++n<r;)t[n]=e[n];return t},jo=Object.create,Do=function(){function e(){}return function(t){if(!zi(t))return{};if(jo)return jo(t);e.prototype=t;var n=new e;return e.prototype=void 0,n}}();var Bo=function(e,t){return function(n){return e(t(n))}},Fo=Bo(Object.getPrototypeOf,Object),Ho=Object.prototype;var zo=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||Ho)};var Uo=function(e){return"function"!=typeof e.constructor||zo(e)?{}:Do(Fo(e))};var Wo=function(e){return null!=e&&"object"==typeof e};var qo=function(e){return Wo(e)&&"[object Arguments]"==Hi(e)},Go=Object.prototype,Ko=Go.hasOwnProperty,Zo=Go.propertyIsEnumerable,Yo=qo(function(){return arguments}())?qo:function(e){return Wo(e)&&Ko.call(e,"callee")&&!Zo.call(e,"callee")},Xo=Array.isArray;var Vo=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991};var Jo=function(e){return null!=e&&Vo(e.length)&&!Ui(e)};var Qo=function(e){return Wo(e)&&Jo(e)};var es=function(){return!1},ts=t((function(e,t){var n=t&&!t.nodeType&&t,r=n&&e&&!e.nodeType&&e,a=r&&r.exports===n?Oi.Buffer:void 0,i=(a?a.isBuffer:void 0)||es;e.exports=i})),ns=Function.prototype,rs=Object.prototype,as=ns.toString,is=rs.hasOwnProperty,os=as.call(Object);var ss=function(e){if(!Wo(e)||"[object Object]"!=Hi(e))return!1;var t=Fo(e);if(null===t)return!0;var n=is.call(t,"constructor")&&t.constructor;return"function"==typeof n&&n instanceof n&&as.call(n)==os},cs={};cs["[object Float32Array]"]=cs["[object Float64Array]"]=cs["[object Int8Array]"]=cs["[object Int16Array]"]=cs["[object Int32Array]"]=cs["[object Uint8Array]"]=cs["[object Uint8ClampedArray]"]=cs["[object Uint16Array]"]=cs["[object Uint32Array]"]=!0,cs["[object Arguments]"]=cs["[object Array]"]=cs["[object ArrayBuffer]"]=cs["[object Boolean]"]=cs["[object DataView]"]=cs["[object Date]"]=cs["[object Error]"]=cs["[object Function]"]=cs["[object Map]"]=cs["[object Number]"]=cs["[object Object]"]=cs["[object RegExp]"]=cs["[object Set]"]=cs["[object String]"]=cs["[object WeakMap]"]=!1;var ls=function(e){return Wo(e)&&Vo(e.length)&&!!cs[Hi(e)]};var us=function(e){return function(t){return e(t)}},fs=t((function(e,t){var n=t&&!t.nodeType&&t,r=n&&e&&!e.nodeType&&e,a=r&&r.exports===n&&$i.process,i=function(){try{var e=r&&r.require&&r.require("util").types;return e||a&&a.binding&&a.binding("util")}catch(e){}}();e.exports=i})),ds=fs&&fs.isTypedArray,ps=ds?us(ds):ls;var hs=function(e,t){if(("constructor"!==t||"function"!=typeof e[t])&&"__proto__"!=t)return e[t]},gs=Object.prototype.hasOwnProperty;var ms=function(e,t,n){var r=e[t];gs.call(e,t)&&mi(r,n)&&(void 0!==n||t in e)||$o(e,t,n)};var bs=function(e,t,n,r){var a=!n;n||(n={});for(var i=-1,o=t.length;++i<o;){var s=t[i],c=r?r(n[s],e[s],s,n,e):void 0;void 0===c&&(c=e[s]),a?$o(n,s,c):ms(n,s,c)}return n};var vs=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r},ys=/^(?:0|[1-9]\d*)$/;var _s=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&ys.test(e))&&e>-1&&e%1==0&&e<t},ks=Object.prototype.hasOwnProperty;var ws=function(e,t){var n=Xo(e),r=!n&&Yo(e),a=!n&&!r&&ts(e),i=!n&&!r&&!a&&ps(e),o=n||r||a||i,s=o?vs(e.length,String):[],c=s.length;for(var l in e)!t&&!ks.call(e,l)||o&&("length"==l||a&&("offset"==l||"parent"==l)||i&&("buffer"==l||"byteLength"==l||"byteOffset"==l)||_s(l,c))||s.push(l);return s};var Es=function(e){var t=[];if(null!=e)for(var n in Object(e))t.push(n);return t},Ss=Object.prototype.hasOwnProperty;var As=function(e){if(!zi(e))return Es(e);var t=zo(e),n=[];for(var r in e)("constructor"!=r||!t&&Ss.call(e,r))&&n.push(r);return n};var xs=function(e){return Jo(e)?ws(e,!0):As(e)};var Cs=function(e){return bs(e,xs(e))};var Ts=function(e,t,n,r,a,i,o){var s=hs(e,n),c=hs(t,n),l=o.get(c);if(l)Ro(e,n,l);else{var u=i?i(s,c,n+"",e,t,o):void 0,f=void 0===u;if(f){var d=Xo(c),p=!d&&ts(c),h=!d&&!p&&ps(c);u=c,d||p||h?Xo(s)?u=s:Qo(s)?u=Mo(s):p?(f=!1,u=Po(c,!0)):h?(f=!1,u=No(c,!0)):u=[]:ss(c)||Yo(c)?(u=s,Yo(s)?u=Cs(s):zi(s)&&!Ui(s)||(u=Uo(c))):f=!1}f&&(o.set(c,u),a(u,c,r,i,o),o.delete(c)),Ro(e,n,u)}};var $s=function e(t,n,r,a,i){t!==n&&Oo(n,(function(o,s){if(i||(i=new Co),zi(o))Ts(t,n,s,r,e,a,i);else{var c=a?a(hs(t,s),o,s+"",t,n,i):void 0;void 0===c&&(c=o),Ro(t,s,c)}}),xs)};var Rs=function(e){return e};var Os=function(e,t,n){switch(n.length){case 0:return e.call(t);case 1:return e.call(t,n[0]);case 2:return e.call(t,n[0],n[1]);case 3:return e.call(t,n[0],n[1],n[2])}return e.apply(t,n)},Ps=Math.max;var Ls=function(e,t,n){return t=Ps(void 0===t?e.length-1:t,0),function(){for(var r=arguments,a=-1,i=Ps(r.length-t,0),o=Array(i);++a<i;)o[a]=r[t+a];a=-1;for(var s=Array(t+1);++a<t;)s[a]=r[a];return s[t]=n(o),Os(e,this,s)}};var Is=function(e){return function(){return e}},Ns=To?function(e,t){return To(e,"toString",{configurable:!0,enumerable:!1,value:Is(t),writable:!0})}:Rs,Ms=Date.now;var js=function(e){var t=0,n=0;return function(){var r=Ms(),a=16-(r-n);if(n=r,a>0){if(++t>=800)return arguments[0]}else t=0;return e.apply(void 0,arguments)}}(Ns);var Ds=function(e,t){return js(Ls(e,t,Rs),e+"")};var Bs=function(e,t,n){if(!zi(n))return!1;var r=typeof t;return!!("number"==r?Jo(n)&&_s(t,n.length):"string"==r&&t in n)&&mi(n[t],e)};var Fs=function(e){return Ds((function(t,n){var r=-1,a=n.length,i=a>1?n[a-1]:void 0,o=a>2?n[2]:void 0;for(i=e.length>3&&"function"==typeof i?(a--,i):void 0,o&&Bs(n[0],n[1],o)&&(i=a<3?void 0:i,a=1),t=Object(t);++r<a;){var s=n[r];s&&e(t,s,r,i)}return t}))},Hs=Fs((function(e,t,n,r){$s(e,t,n,r)})),zs=Hs,Us=dn;Ve({target:"Function",proto:!0,forced:Function.bind!==rt},{bind:rt});var Ws=function(e){return N[e+"Prototype"]},qs=Ws("Function").bind,Gs=Function.prototype,Ks=function(e){var t=e.bind;return e===Gs||D(Gs,e)&&t===Gs.bind?qs:t},Zs=Ks,Ys=Ws("Array").concat,Xs=Array.prototype,Vs=function(e){var t=e.concat;return e===Xs||D(Xs,e)&&t===Xs.concat?Ys:t},Js=function(e,t){var n=[][e];return!!n&&o((function(){n.call(null,t||function(){return 1},1)}))},Qs=wr.forEach,ec=Js("forEach")?[].forEach:function(e){return Qs(this,e,arguments.length>1?arguments[1]:void 0)};Ve({target:"Array",proto:!0,forced:[].forEach!=ec},{forEach:ec});var tc=Ws("Array").forEach,nc=Array.prototype,rc={DOMTokenList:!0,NodeList:!0},ac=function(e){var t=e.forEach;return e===nc||D(nc,e)&&t===nc.forEach||ce(rc,lt(e))?tc:t},ic=o((function(){Ht(1)}));Ve({target:"Object",stat:!0,forced:ic},{keys:function(e){return Ht(oe(e))}});var oc=N.Object.keys,sc=wr.filter,cc=Pn("filter");Ve({target:"Array",proto:!0,forced:!cc},{filter:function(e){return sc(this,e,arguments.length>1?arguments[1]:void 0)}});var lc=Ws("Array").filter,uc=Array.prototype,fc=function(e){var t=e.filter;return e===uc||D(uc,e)&&t===uc.filter?lc:t},dc=wr.findIndex,pc="findIndex",hc=!0;pc in[]&&Array(1)[pc]((function(){hc=!1})),Ve({target:"Array",proto:!0,forced:hc},{findIndex:function(e){return dc(this,e,arguments.length>1?arguments[1]:void 0)}});var gc=Ws("Array").findIndex,mc=Array.prototype,bc=function(e){var t=e.findIndex;return e===mc||D(mc,e)&&t===mc.findIndex?gc:t},vc=Pn("splice"),yc=i.TypeError,_c=Math.max,kc=Math.min;Ve({target:"Array",proto:!0,forced:!vc},{splice:function(e,t){var n,r,a,i,o,s,c=oe(this),l=Lt(c),u=Rt(e,l),f=arguments.length;if(0===f?n=r=0:1===f?(n=0,r=l-u):(n=f-2,r=kc(_c(Ct(t),0),l-u)),l+n-r>9007199254740991)throw yc("Maximum allowed length exceeded");for(a=Rn(c,r),i=0;i<r;i++)(o=u+i)in c&&Cn(a,i,c[o]);if(a.length=r,n<r){for(i=u;i<l-r;i++)s=i+n,(o=i+r)in c?c[s]=c[o]:delete c[s];for(i=l;i>l-r+n;i--)delete c[i-1]}else if(n>r)for(i=l-r;i>u;i--)s=i+n-1,(o=i+r-1)in c?c[s]=c[o]:delete c[s];for(i=0;i<n;i++)c[i+u]=arguments[i+2];return c.length=l-r+n,a}});var wc=Ws("Array").splice,Ec=Array.prototype,Sc=function(e){var t=e.splice;return e===Ec||D(Ec,e)&&t===Ec.splice?wc:t},Ac=!1,xc={SEN:"sentence",PAR:"paragraph",DEFAULT:"sentence"},Cc=function(){function e(t){mn(this,e),hi(this,"$engine",void 0),hi(this,"$locale",void 0),this.RULE=this.rule(t)}return gn(e,[{key:"getType",value:function(){return this.constructor.HOOK_TYPE||xc.DEFAULT}},{key:"getName",value:function(){return this.constructor.HOOK_NAME}},{key:"afterInit",value:function(e){"function"==typeof e&&e()}},{key:"setLocale",value:function(e){this.$locale=e}},{key:"beforeMakeHtml",value:function(e){return e}},{key:"makeHtml",value:function(e){return e}},{key:"afterMakeHtml",value:function(e){return e}},{key:"onKeyDown",value:function(e,t){}},{key:"getOnKeyDown",value:function(){return this.onKeyDown||!1}},{key:"getAttributesTest",value:function(){return/^(color|fontSize|font-size|id|title|class|target|underline|line-through|overline|sub|super)$/}},{key:"$testAttributes",value:function(e,t){this.getAttributesTest().test(e)&&t()}},{key:"getAttributes",value:function(e){return{attrs:{},str:e}}},{key:"test",value:function(e){return!!this.RULE.reg&&this.RULE.reg.test(e)}},{key:"rule",value:function(e){return{begin:"",end:"",content:"",reg:new RegExp("")}}},{key:"mounted",value:function(){}}],[{key:"getMathJaxConfig",value:function(){return Ac}},{key:"setMathJaxConfig",value:function(e){Ac=e}}]),e}();hi(Cc,"HOOK_NAME","default"),hi(Cc,"HOOK_TYPE",xc.DEFAULT);var Tc=wr.map,$c=Pn("map");Ve({target:"Array",proto:!0,forced:!$c},{map:function(e){return Tc(this,e,arguments.length>1?arguments[1]:void 0)}});var Rc,Oc=Ws("Array").map,Pc=Array.prototype,Lc=function(e){var t=e.map;return e===Pc||D(Pc,e)&&t===Pc.map?Oc:t},Ic="\t\n\v\f\r                　\u2028\u2029\ufeff",Nc=m("".replace),Mc="["+Ic+"]",jc=RegExp("^"+Mc+Mc+"*"),Dc=RegExp(Mc+Mc+"*$"),Bc=function(e){return function(t){var n=Wn(P(t));return 1&e&&(n=Nc(n,jc,"")),2&e&&(n=Nc(n,Dc,"")),n}},Fc={start:Bc(1),end:Bc(2),trim:Bc(3)},Hc=Ea.PROPER,zc=Fc.trim;Ve({target:"String",proto:!0,forced:(Rc="trim",o((function(){return!!Ic[Rc]()||"​᠎"!=="​᠎"[Rc]()||Hc&&Ic[Rc].name!==Rc})))},{trim:function(){return zc(this)}});var Uc=Ws("String").trim,Wc=String.prototype,qc=function(e){var t=e.trim;return"string"==typeof e||e===Wc||D(Wc,e)&&t===Wc.trim?Uc:t},Gc=Fc.trim,Kc=i.parseInt,Zc=i.Symbol,Yc=Zc&&Zc.iterator,Xc=/^[+-]?0x/i,Vc=m(Xc.exec),Jc=8!==Kc(Ic+"08")||22!==Kc(Ic+"0x16")||Yc&&!o((function(){Kc(Object(Yc))}))?function(e,t){var n=Gc(Wn(e));return Kc(n,t>>>0||(Vc(Xc,n)?16:10))}:Kc;Ve({global:!0,forced:parseInt!=Jc},{parseInt:Jc});var Qc=N.parseInt;function el(e,t){var n,r,a,i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return/^\n/.test(e)?i?(null!==(n=null===(r=e.match(/^\n+/g))||void 0===r||null===(a=r[0])||void 0===a?void 0:a.length)&&void 0!==n?n:0)>1?"\n\n".concat(t):"\n".concat(t):"\n\n".concat(t):t}function tl(e,t){var n=(e.match(/\n/g)||[]).length;return""!==e&&(n-=2),n+t}Ve({target:"Array",stat:!0},{isArray:xn});var nl=N.Array.isArray,rl=nl;function al(e,t){if(rl(t))return t}function il(e){return"undefined"!=typeof localStorage&&null!==localStorage.getItem("cherry-".concat(e))}function ol(){var e="false";return"undefined"!=typeof localStorage&&(e=localStorage.getItem("cherry-classicBr")),"true"===e}var sl=N.Object.getOwnPropertySymbols,cl=Ce.f,ll=o((function(){cl(1)}));Ve({target:"Object",stat:!0,forced:!v||ll,sham:!v},{getOwnPropertyDescriptor:function(e,t){return cl(L(e),t)}});var ul=t((function(e){var t=N.Object,n=e.exports=function(e,n){return t.getOwnPropertyDescriptor(e,n)};t.getOwnPropertyDescriptor.sham&&(n.sham=!0)})),fl=ul,dl=fl,pl=m([].concat),hl=j("Reflect","ownKeys")||function(e){var t=Gn.f(Fe(e)),n=Qn.f;return n?pl(t,n(e)):t};Ve({target:"Object",stat:!0,sham:!v},{getOwnPropertyDescriptors:function(e){for(var t,n,r=L(e),a=Ce.f,i=hl(r),o={},s=0;i.length>s;)void 0!==(n=a(r,t=i[s++]))&&Cn(o,t,n);return o}});var gl=N.Object.getOwnPropertyDescriptors,ml=Ut.f;Ve({target:"Object",stat:!0,forced:Object.defineProperties!==ml,sham:!v},{defineProperties:ml});var bl=t((function(e){var t=N.Object,n=e.exports=function(e,n){return t.defineProperties(e,n)};t.defineProperties.sham&&(n.sham=!0)})),vl=bl,yl=i.RangeError,_l=String.fromCharCode,kl=String.fromCodePoint,wl=m([].join),El=!!kl&&1!=kl.length;Ve({target:"String",stat:!0,arity:1,forced:El},{fromCodePoint:function(e){for(var t,n=[],r=arguments.length,a=0;r>a;){if(t=+arguments[a++],Rt(t,1114111)!==t)throw yl(t+" is not a valid code point");n[a]=t<65536?_l(t):_l(55296+((t-=65536)>>10),t%1024+56320)}return wl(n,"")}});var Sl=N.String.fromCodePoint,Al=Nt.indexOf,xl=m([].indexOf),Cl=!!xl&&1/xl([1],1,-0)<0,Tl=Js("indexOf");Ve({target:"Array",proto:!0,forced:Cl||!Tl},{indexOf:function(e){var t=arguments.length>1?arguments[1]:void 0;return Cl?xl(this,e,t)||0:Al(this,e,t)}});var $l,Rl,Ol=Ws("Array").indexOf,Pl=Array.prototype,Ll=function(e){var t=e.indexOf;return e===Pl||D(Pl,e)&&t===Pl.indexOf?Ol:t},Il=Ll;function Nl(e,t){var n=oc(e);if(sl){var r=sl(e);t&&(r=fc(r).call(r,(function(t){return dl(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ml(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?ac(n=Nl(Object(a),!0)).call(n,(function(t){hi(e,t,a[t])})):gl?vl(e,gl(a)):ac(r=Nl(Object(a))).call(r,(function(t){Us(e,t,dl(a,t))}))}return e}var jl={"<":"&lt;",">":"&gt;","&":"&amp;",'"':"&quot;","'":"&#x27;"},Dl={lt:"<",gt:">",amp:"&",quot:'"',apos:"'"},Bl=Ml(Ml(Ml(Ml(Ml(Ml({},{34:"&quot;",38:"&amp;",39:"&apos;",60:"&lt;",62:"&gt;"}),{192:"&Agrave;",193:"&Aacute;",194:"&Acirc;",195:"&Atilde;",196:"&Auml;",197:"&Aring;",198:"&AElig;",199:"&Ccedil;",200:"&Egrave;",201:"&Eacute;",202:"&Ecirc;",203:"&Euml;",204:"&Igrave;",205:"&Iacute;",206:"&Icirc;",207:"&Iuml;",208:"&ETH;",209:"&Ntilde;",210:"&Ograve;",211:"&Oacute;",212:"&Ocirc;",213:"&Otilde;",214:"&Ouml;",216:"&Oslash;",217:"&Ugrave;",218:"&Uacute;",219:"&Ucirc;",220:"&Uuml;",221:"&Yacute;",222:"&THORN;",223:"&szlig;",224:"&agrave;",225:"&aacute;",226:"&acirc;",227:"&atilde;",228:"&auml;",229:"&aring;",230:"&aelig;",231:"&ccedil;",232:"&egrave;",233:"&eacute;",234:"&ecirc;",235:"&euml;",236:"&igrave;",237:"&iacute;",238:"&icirc;",239:"&iuml;",240:"&eth;",241:"&ntilde;",242:"&ograve;",243:"&oacute;",244:"&ocirc;",245:"&otilde;",246:"&ouml;",248:"&oslash;",249:"&ugrave;",250:"&uacute;",251:"&ucirc;",252:"&uuml;",253:"&yacute;",254:"&thorn;",255:"&yuml;"}),{160:"&nbsp;",161:"&iexcl;",162:"&cent;",163:"&pound;",164:"&curren;",165:"&yen;",166:"&brvbar;",167:"&sect;",168:"&uml;",169:"&copy;",170:"&ordf;",171:"&laquo;",172:"&not;",173:"&shy;",174:"&reg;",175:"&macr;",176:"&deg;",177:"&plusmn;",178:"&sup2;",179:"&sup3;",180:"&acute;",181:"&micro;",182:"&para;",184:"&cedil;",185:"&sup1;",186:"&ordm;",187:"&raquo;",188:"&frac14;",189:"&frac12;",190:"&frac34;",191:"&iquest;",215:"&times;",247:"&divide;"}),{8704:"&forall;",8706:"&part;",8707:"&exist;",8709:"&empty;",8711:"&nabla;",8712:"&isin;",8713:"&notin;",8715:"&ni;",8719:"&prod;",8721:"&sum;",8722:"&minus;",8727:"&lowast;",8730:"&radic;",8733:"&prop;",8734:"&infin;",8736:"&ang;",8743:"&and;",8744:"&or;",8745:"&cap;",8746:"&cup;",8747:"&int;",8756:"&there4;",8764:"&sim;",8773:"&cong;",8776:"&asymp;",8800:"&ne;",8801:"&equiv;",8804:"&le;",8805:"&ge;",8834:"&sub;",8835:"&sup;",8836:"&nsub;",8838:"&sube;",8839:"&supe;",8853:"&oplus;",8855:"&otimes;",8869:"&perp;",8901:"&sdot;"}),{913:"&Alpha;",914:"&Beta;",915:"&Gamma;",916:"&Delta;",917:"&Epsilon;",918:"&Zeta;",919:"&Eta;",920:"&Theta;",921:"&Iota;",922:"&Kappa;",923:"&Lambda;",924:"&Mu;",925:"&Nu;",926:"&Xi;",927:"&Omicron;",928:"&Pi;",929:"&Rho;",931:"&Sigma;",932:"&Tau;",933:"&Upsilon;",934:"&Phi;",935:"&Chi;",936:"&Psi;",937:"&Omega;",945:"&alpha;",946:"&beta;",947:"&gamma;",948:"&delta;",949:"&epsilon;",950:"&zeta;",951:"&eta;",952:"&theta;",953:"&iota;",954:"&kappa;",955:"&lambda;",956:"&mu;",957:"&nu;",958:"&xi;",959:"&omicron;",960:"&pi;",961:"&rho;",962:"&sigmaf;",963:"&sigma;",964:"&tau;",965:"&upsilon;",966:"&phi;",967:"&chi;",968:"&psi;",969:"&omega;",977:"&thetasym;",978:"&upsih;",982:"&piv;"}),{338:"&OElig;",339:"&oelig;",352:"&Scaron;",353:"&scaron;",376:"&Yuml;",402:"&fnof;",710:"&circ;",732:"&tilde;",8194:"&ensp;",8195:"&emsp;",8201:"&thinsp;",8204:"&zwnj;",8205:"&zwj;",8206:"&lrm;",8207:"&rlm;",8211:"&ndash;",8212:"&mdash;",8216:"&lsquo;",8217:"&rsquo;",8218:"&sbquo;",8220:"&ldquo;",8221:"&rdquo;",8222:"&bdquo;",8224:"&dagger;",8225:"&Dagger;",8226:"&bull;",8230:"&hellip;",8240:"&permil;",8242:"&prime;",8243:"&Prime;",8249:"&lsaquo;",8250:"&rsaquo;",8254:"&oline;",8364:"&euro;",8482:"&trade;",8592:"&larr;",8593:"&uarr;",8594:"&rarr;",8595:"&darr;",8596:"&harr;",8629:"&crarr;",8968:"&lceil;",8969:"&rceil;",8970:"&lfloor;",8971:"&rfloor;",9674:"&loz;",9824:"&spades;",9827:"&clubs;",9829:"&hearts;",9830:"&diams;"}),Fl=oc(Bl),Hl=Lc(Fl).call(Fl,(function(e){return Bl[e].replace(/^&(\w+);$/g,(function(e,t){return t.toLowerCase()}))})),zl=function(e){return"string"!=typeof e||e.length<=0},Ul=function(e){try{var t=Sl(e);return!zl(t)}catch(e){return!1}};var Wl=["h1|h2|h3|h4|h5|h6","ul|ol|li|dd|dl|dt","table|thead|tbody|tfoot|col|colgroup|th|td|tr","div|article|section|footer|aside|details|summary|code|audio|video|canvas|figure","address|center|cite|p|pre|blockquote|marquee|caption|figcaption|track|source|output|svg"].join("|"),ql=["span|a|link|b|s|i|del|u|em|strong|sup|sub|kbd","nav|font|bdi|samp|map|area|small|time|bdo|var|wbr|meter|dfn","ruby|rt|rp|mark|q|progress|input|textarea|select|ins"].join("|"),Gl=new RegExp(Vs($l=Vs(Rl="^(".concat(Wl,"|")).call(Rl,ql,"|")).call($l,"br|img|hr",")( |$|/)"),"i");function Kl(e,t){return"string"!=typeof e?"":t?e.replace(/[<>&]/g,(function(e){return jl[e]||e})):e.replace(/[<>&"']/g,(function(e){return jl[e]||e}))}function Zl(e,t){if("string"!=typeof e)return"";var n=Yl(e);return n=function(e){return"string"!=typeof e?"":e.replace(/&(\w+);?/g,(function(e,t){return Dl[t]||e}))}(n),Kl(n,t)}function Yl(e){return e.replace(/&#(\d+);?/g,(function(e,t){return Bl[t]||e}))}function Xl(e){var t=function(e){return e.replace(/&#x([0-9a-f]+);?/gi,(function(e,t){var n=Qc("0x".concat(t),16);try{return Sl(n)}catch(t){return e}}))}(function(e){return e.replace(/&#(\d+);?/g,(function(e,t){try{return Sl(t)}catch(t){return e}}))}(e)).match(/^\s*([\w\W]+?)(?=:)/i);if(!t)return!0;var n=["javascript","data"],r=t[1].replace(/[\s]/g,"");return-1===Il(n).call(n,r.toLowerCase())}function Vl(e){return encodeURI(e).replace(/%25/g,"%")}function Jl(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var Ql=0,eu=function(e){An(n,Cc);var t=Jl(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{needCache:!1},a=r.needCache,i=r.defaultCache,o=void 0===i?{}:i;return mn(this,n),(e=t.call(this,{})).needCache=!!a,e.sign="",a&&(e.cache=o||{},e.cacheKey="~~C".concat(Ql),Ql+=1),e}return gn(n,[{key:"initBrReg",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.classicBr=il("classicBr")?ol():e,this.removeBrAfterBlock=null,this.removeBrBeforeBlock=null,this.removeNewlinesBetweenTags=null}},{key:"$cleanParagraph",value:function(e){var t=e.replace(/^\n+/,"").replace(/\n+$/,"");return this.classicBr?t:this.joinRawHtml(t).replace(/\n/g,"<br>").replace(/\r/g,"\n")}},{key:"joinRawHtml",value:function(e){if(!this.removeBrAfterBlock){var t,n,r,a,i=null!==(t=null===(n=this.$engine.htmlWhiteListAppend)||void 0===n?void 0:n.split("|"))&&void 0!==t?t:[];i=fc(r=Lc(i).call(i,(function(e){return/[a-z-]+/gi.test(e)?e:null}))).call(r,(function(e){return null!==e}));var o=Vs(i).call(i,Wl).join("|");this.removeBrAfterBlock=new RegExp("<(".concat(o,")(>| [^>]*?>)[^\\S\\n]*?\\n"),"ig"),this.removeBrBeforeBlock=new RegExp("\\n[^\\S\\n]*?<\\/(".concat(o,")>[^\\S\\n]*?\\n"),"ig"),this.removeNewlinesBetweenTags=new RegExp(Vs(a="<\\/(".concat(o,")>[^\\S\\n]*?\\n([^\\S\\n]*?)<(")).call(a,o,")(>| [^>]*?>)"),"ig")}return e.replace(this.removeBrAfterBlock,"<$1$2").replace(this.removeBrBeforeBlock,"</$1>").replace(this.removeNewlinesBetweenTags,"</$1>\r$2<$3$4")}},{key:"toHtml",value:function(e,t){return e}},{key:"makeHtml",value:function(e,t){return t(e).html}},{key:"afterMakeHtml",value:function(e){return this.restoreCache(e)}},{key:"isContainsCache",value:function(e,t){if(t){var r=/^(\s*~~C\d+I\w+\$\s*)+$/g.test(e),a=new RegExp("~~C\\d+I".concat(n.IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX,"\\w+\\$"),"g").test(e);return r&&!a}return new RegExp("~~C\\d+I(?!".concat(n.IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX,")\\w+\\$"),"g").test(e)}},{key:"$splitHtmlByCache",value:function(e){var t=new RegExp("\\n*~~C\\d+I(?!".concat(n.IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX,")\\w+\\$\\n?"),"g");return{caches:e.match(t),contents:e.split(t)}}},{key:"makeExcludingCached",value:function(e,t){for(var n=this.$splitHtmlByCache(e),r=n.caches,a=n.contents,i=Lc(a).call(a,t),o="",s=0;s<i.length;s++){var c;if(o+=i[s],r&&r[s])o+=qc(c=r[s]).call(c)}return o}},{key:"getCacheWithSpace",value:function(e,t){var n,r,a,i,o,s,c=arguments.length>2&&void 0!==arguments[2]&&arguments[2],l=null!==(n=null===(r=t.match(/^\n+/))||void 0===r?void 0:r[0])&&void 0!==n?n:"",u=null!==(a=null===(i=t.match(/\n+$/))||void 0===i?void 0:i[0])&&void 0!==a?a:"";return c?el(t,e):Vs(o=Vs(s="".concat(l)).call(s,e)).call(o,u)}},{key:"getLineCount",value:function(e){var t,r,a,i=e,o=null!==(t=null===(r=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"").match(/^\n+/g))||void 0===r||null===(a=r[0])||void 0===a?void 0:a.length)&&void 0!==t?t:0;o=1===o?1:0,i=i.replace(/^\n+/g,"");var s=new RegExp("\n*~~C\\d+I(?:".concat(n.IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX,")?\\w+?_L(\\d+)\\$"),"g"),c=0;return i=i.replace(s,(function(e,t){return c+=Qc(t,10),e.replace(/^\n+/g,"")})),o+c+(i.match(/\n/g)||[]).length+1}},{key:"pushCache",value:function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(this.needCache){var i=r||this.$engine.md5(e);return this.cache[i]={content:e,using:!0},Vs(t=Vs(n="".concat(this.cacheKey,"I")).call(n,i,"_L")).call(t,a,"$")}}},{key:"popCache",value:function(e){if(this.needCache)return this.cache[e].content||""}},{key:"resetCache",value:function(){if(this.needCache){for(var e=0,t=oc(this.cache);e<t.length;e++){var n=t[e];this.cache[n].using||delete this.cache[n]}for(var r=0,a=oc(this.cache);r<a.length;r++){var i=a[r];this.cache[i].using=!1}}}},{key:"restoreCache",value:function(e){var t,r=this;if(!this.needCache)return e;var a=new RegExp(Vs(t="".concat(this.cacheKey,"I((?:")).call(t,n.IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX,")?\\w+)\\$"),"g"),i=e.replace(a,(function(e,t){return r.popCache(t.replace(/_L\d+$/,""))}));return this.resetCache(),i}},{key:"checkCache",value:function(e,t){var n,r,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return this.sign=this.$engine.md5(e),this.cache[this.sign]?(this.cache[this.sign].using=!0,Vs(n=Vs(r="".concat(this.cacheKey,"I")).call(r,this.sign,"_L")).call(n,a,"$")):this.toHtml(e,t)}},{key:"mounted",value:function(){}},{key:"signWithCache",value:function(e){return!1}}]),n}();hi(eu,"HOOK_TYPE",xc.PAR),hi(eu,"IN_PARAGRAPH_CACHE_KEY_PREFIX","!"),hi(eu,"IN_PARAGRAPH_CACHE_KEY_PREFIX_REGEX","\\!");var tu=o((function(){if("function"==typeof ArrayBuffer){var e=new ArrayBuffer(8);Object.isExtensible(e)&&Object.defineProperty(e,"a",{value:8})}})),nu=Object.isExtensible,ru=o((function(){nu(1)}))||tu?function(e){return!!I(e)&&((!tu||"ArrayBuffer"!=C(e))&&(!nu||nu(e)))}:nu,au=!o((function(){return Object.isExtensible(Object.preventExtensions({}))})),iu=t((function(e){var t=Ke.f,n=!1,r=de("meta"),a=0,i=function(e){t(e,r,{value:{objectID:"O"+a++,weakData:{}}})},o=e.exports={enable:function(){o.enable=function(){},n=!0;var e=Gn.f,t=m([].splice),a={};a[r]=1,e(a).length&&(Gn.f=function(n){for(var a=e(n),i=0,o=a.length;i<o;i++)if(a[i]===r){t(a,i,1);break}return a},Ve({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Jn.f}))},fastKey:function(e,t){if(!I(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!ce(e,r)){if(!ru(e))return"F";if(!t)return"E";i(e)}return e[r].objectID},getWeakData:function(e,t){if(!ce(e,r)){if(!ru(e))return!0;if(!t)return!1;i(e)}return e[r].weakData},onFreeze:function(e){return au&&n&&ru(e)&&!ce(e,r)&&i(e),e}};Mt[r]=!0}));iu.enable,iu.fastKey,iu.getWeakData,iu.onFreeze;var ou=be("iterator"),su=Array.prototype,cu=function(e){return void 0!==e&&(ya.Array===e||su[ou]===e)},lu=be("iterator"),uu=function(e){if(null!=e)return Q(e,lu)||Q(e,"@@iterator")||ya[lt(e)]},fu=i.TypeError,du=function(e,t){var n=arguments.length<2?uu(e):t;if(J(n))return Fe(_(n,e));throw fu(X(e)+" is not iterable")},pu=function(e,t,n){var r,a;Fe(e);try{if(!(r=Q(e,"return"))){if("throw"===t)throw n;return n}r=_(r,e)}catch(e){a=!0,r=e}if("throw"===t)throw n;if(a)throw r;return Fe(r),n},hu=i.TypeError,gu=function(e,t){this.stopped=e,this.result=t},mu=gu.prototype,bu=function(e,t,n){var r,a,i,o,s,c,l,u=n&&n.that,f=!(!n||!n.AS_ENTRIES),d=!(!n||!n.IS_ITERATOR),p=!(!n||!n.INTERRUPTED),h=Me(t,u),g=function(e){return r&&pu(r,"normal",e),new gu(!0,e)},m=function(e){return f?(Fe(e),p?h(e[0],e[1],g):h(e[0],e[1])):p?h(e,g):h(e)};if(d)r=e;else{if(!(a=uu(e)))throw hu(X(e)+" is not iterable");if(cu(a)){for(i=0,o=Lt(e);o>i;i++)if((s=m(e[i]))&&D(mu,s))return s;return new gu(!1)}r=du(e,a)}for(c=r.next;!(l=_(c,r)).done;){try{s=m(l.value)}catch(e){pu(r,"throw",e)}if("object"==typeof s&&s&&D(mu,s))return s}return new gu(!1)},vu=i.TypeError,yu=function(e,t){if(D(t,e))return e;throw vu("Incorrect invocation")},_u=Ke.f,ku=wr.forEach,wu=yr.set,Eu=yr.getterFor,Su=function(e,t,n){for(var r in t)n&&n.unsafe&&e[r]?e[r]=t[r]:er(e,r,t[r],n);return e},Au=be("species"),xu=Ke.f,Cu=iu.fastKey,Tu=yr.set,$u=yr.getterFor,Ru={getConstructor:function(e,t,n,r){var a=e((function(e,a){yu(e,i),Tu(e,{type:t,index:en(null),first:void 0,last:void 0,size:0}),v||(e.size=0),null!=a&&bu(a,e[r],{that:e,AS_ENTRIES:n})})),i=a.prototype,o=$u(t),s=function(e,t,n){var r,a,i=o(e),s=c(e,t);return s?s.value=n:(i.last=s={index:a=Cu(t,!0),key:t,value:n,previous:r=i.last,next:void 0,removed:!1},i.first||(i.first=s),r&&(r.next=s),v?i.size++:e.size++,"F"!==a&&(i.index[a]=s)),e},c=function(e,t){var n,r=o(e),a=Cu(t);if("F"!==a)return r.index[a];for(n=r.first;n;n=n.next)if(n.key==t)return n};return Su(i,{clear:function(){for(var e=o(this),t=e.index,n=e.first;n;)n.removed=!0,n.previous&&(n.previous=n.previous.next=void 0),delete t[n.index],n=n.next;e.first=e.last=void 0,v?e.size=0:this.size=0},delete:function(e){var t=this,n=o(t),r=c(t,e);if(r){var a=r.next,i=r.previous;delete n.index[r.index],r.removed=!0,i&&(i.next=a),a&&(a.previous=i),n.first==r&&(n.first=a),n.last==r&&(n.last=i),v?n.size--:t.size--}return!!r},forEach:function(e){for(var t,n=o(this),r=Me(e,arguments.length>1?arguments[1]:void 0);t=t?t.next:n.first;)for(r(t.value,t.key,this);t&&t.removed;)t=t.previous},has:function(e){return!!c(this,e)}}),Su(i,n?{get:function(e){var t=c(this,e);return t&&t.value},set:function(e,t){return s(this,0===e?0:e,t)}}:{add:function(e){return s(this,e=0===e?0:e,e)}}),v&&xu(i,"size",{get:function(){return o(this).size}}),a},setStrong:function(e,t,n){var r=t+" Iterator",a=$u(t),i=$u(r);za(e,t,(function(e,t){Tu(this,{type:r,target:e,state:a(e),kind:t,last:void 0})}),(function(){for(var e=i(this),t=e.kind,n=e.last;n&&n.removed;)n=n.previous;return e.target&&(e.last=n=n?n.next:e.state.first)?"keys"==t?{value:n.key,done:!1}:"values"==t?{value:n.value,done:!1}:{value:[n.key,n.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),n?"entries":"values",!n,!0),function(e){var t=j(e),n=Ke.f;v&&t&&!t[Au]&&n(t,Au,{configurable:!0,get:function(){return this}})}(t)}};!function(e,t,n){var r,a=-1!==e.indexOf("Map"),s=-1!==e.indexOf("Weak"),c=a?"set":"add",l=i[e],u=l&&l.prototype,f={};if(v&&b(l)&&(s||u.forEach&&!o((function(){(new l).entries().next()})))){var d=(r=t((function(t,n){wu(yu(t,d),{type:e,collection:new l}),null!=n&&bu(n,t[c],{that:t,AS_ENTRIES:a})}))).prototype,p=Eu(e);ku(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(e){var t="add"==e||"set"==e;!(e in u)||s&&"clear"==e||Ze(d,e,(function(n,r){var a=p(this).collection;if(!t&&s&&!I(n))return"get"==e&&void 0;var i=a[e](0===n?0:n,r);return t?this:i}))})),s||_u(d,"size",{configurable:!0,get:function(){return p(this).collection.size}})}else r=n.getConstructor(t,e,a,c),iu.enable();cr(r,e,!1,!0),f[e]=r,Ve({global:!0,forced:!0},f),s||n.setStrong(r,e,a)}("Map",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),Ru);var Ou=N.Map,Pu=[].push;Ve({target:"Map",stat:!0,forced:!0},{from:function(e){var t,n,r,a,i=arguments.length,o=i>1?arguments[1]:void 0;return Et(this),(t=void 0!==o)&&J(o),null==e?new this:(n=[],t?(r=0,a=Me(o,i>2?arguments[2]:void 0),bu(e,(function(e){_(Pu,n,a(e,r++))}))):bu(e,Pu,{that:n}),new this(n))}});Ve({target:"Map",stat:!0,forced:!0},{of:function(){return new this(Je(arguments))}});Ve({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function(){for(var e,t=Fe(this),n=J(t.delete),r=!0,a=0,i=arguments.length;a<i;a++)e=_(n,t,arguments[a]),r=r&&e;return!!r}});Ve({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function(e,t){var n=Fe(this),r=J(n.get),a=J(n.has),i=J(n.set),o=_(a,n,e)&&"update"in t?t.update(_(r,n,e),e,n):t.insert(e,n);return _(i,n,e,o),o}});var Lu=du;Ve({target:"Map",proto:!0,real:!0,forced:!0},{every:function(e){var t=Fe(this),n=Lu(t),r=Me(e,arguments.length>1?arguments[1]:void 0);return!bu(n,(function(e,n,a){if(!r(n,e,t))return a()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var Iu=be("species"),Nu=function(e,t){var n,r=Fe(e).constructor;return void 0===r||null==(n=Fe(r)[Iu])?t:Et(n)};Ve({target:"Map",proto:!0,real:!0,forced:!0},{filter:function(e){var t=Fe(this),n=Lu(t),r=Me(e,arguments.length>1?arguments[1]:void 0),a=new(Nu(t,j("Map"))),i=J(a.set);return bu(n,(function(e,n){r(n,e,t)&&_(i,a,e,n)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),a}}),Ve({target:"Map",proto:!0,real:!0,forced:!0},{find:function(e){var t=Fe(this),n=Lu(t),r=Me(e,arguments.length>1?arguments[1]:void 0);return bu(n,(function(e,n,a){if(r(n,e,t))return a(n)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),Ve({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function(e){var t=Fe(this),n=Lu(t),r=Me(e,arguments.length>1?arguments[1]:void 0);return bu(n,(function(e,n,a){if(r(n,e,t))return a(e)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var Mu=m([].push);Ve({target:"Map",stat:!0,forced:!0},{groupBy:function(e,t){J(t);var n=du(e),r=new this,a=J(r.has),i=J(r.get),o=J(r.set);return bu(n,(function(e){var n=t(e);_(a,r,n)?Mu(_(i,r,n),e):_(o,r,n,[e])}),{IS_ITERATOR:!0}),r}});Ve({target:"Map",proto:!0,real:!0,forced:!0},{includes:function(e){return bu(Lu(Fe(this)),(function(t,n,r){if((a=n)===(i=e)||a!=a&&i!=i)return r();var a,i}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),Ve({target:"Map",stat:!0,forced:!0},{keyBy:function(e,t){var n=new this;J(t);var r=J(n.set);return bu(e,(function(e){_(r,n,t(e),e)})),n}}),Ve({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function(e){return bu(Lu(Fe(this)),(function(t,n,r){if(n===e)return r(t)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),Ve({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function(e){var t=Fe(this),n=Lu(t),r=Me(e,arguments.length>1?arguments[1]:void 0),a=new(Nu(t,j("Map"))),i=J(a.set);return bu(n,(function(e,n){_(i,a,r(n,e,t),n)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),a}}),Ve({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function(e){var t=Fe(this),n=Lu(t),r=Me(e,arguments.length>1?arguments[1]:void 0),a=new(Nu(t,j("Map"))),i=J(a.set);return bu(n,(function(e,n){_(i,a,e,r(n,e,t))}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),a}}),Ve({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function(e){for(var t=Fe(this),n=J(t.set),r=arguments.length,a=0;a<r;)bu(arguments[a++],n,{that:t,AS_ENTRIES:!0});return t}});var ju=i.TypeError;Ve({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function(e){var t=Fe(this),n=Lu(t),r=arguments.length<2,a=r?void 0:arguments[1];if(J(e),bu(n,(function(n,i){r?(r=!1,a=i):a=e(a,i,n,t)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),r)throw ju("Reduce of empty map with no initial value");return a}}),Ve({target:"Map",proto:!0,real:!0,forced:!0},{some:function(e){var t=Fe(this),n=Lu(t),r=Me(e,arguments.length>1?arguments[1]:void 0);return bu(n,(function(e,n,a){if(r(n,e,t))return a()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var Du=i.TypeError;Ve({target:"Map",proto:!0,real:!0,forced:!0},{update:function(e,t){var n=Fe(this),r=J(n.get),a=J(n.has),i=J(n.set),o=arguments.length;J(t);var s=_(a,n,e);if(!s&&o<3)throw Du("Updating absent value");var c=s?_(r,n,e):J(o>2?arguments[2]:void 0)(e,n);return _(i,n,e,t(c,e,n)),n}});var Bu=i.TypeError,Fu=function(e,t){var n,r=Fe(this),a=J(r.get),i=J(r.has),o=J(r.set),s=arguments.length>2?arguments[2]:void 0;if(!b(t)&&!b(s))throw Bu("At least one callback required");return _(i,r,e)?(n=_(a,r,e),b(t)&&(n=t(n),_(o,r,e,n))):b(s)&&(n=s(),_(o,r,e,n)),n};Ve({target:"Map",proto:!0,real:!0,forced:!0},{upsert:Fu}),Ve({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:Fu});var Hu=Ou,zu=Ll;var Uu=cn,Wu=Ks;function qu(e,t,n){return qu=function(){if("undefined"==typeof Reflect||!Uu)return!1;if(Uu.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Uu(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?Uu:function(e,t,n){var r=[null];r.push.apply(r,t);var a=new(Wu(Function).apply(e,r));return n&&Sn(a,n.prototype),a},qu.apply(null,arguments)}function Gu(e){var t="function"==typeof Hu?new Hu:void 0;return Gu=function(e){if(null===e||!function(e){var t;return-1!==zu(t=Function.toString.call(e)).call(t,"[native code]")}(e))return e;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return qu(e,arguments,pi(this).constructor)}return n.prototype=yn(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),Sn(n,e)},Gu(e)}function Ku(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var Zu=function(e,t){if(!rl(e)&&ci(e)!==t.name.toLowerCase()||!rl(e)&&"array"===t.name.toLowerCase())throw new TypeError("parameter given must be ".concat(t.name));return!0},Yu=function(e,t){if(!(e instanceof t))throw new Error("the hook does not correctly inherit");return!0},Xu=function(e){if("object"!==ci(e))throw new Error("the hook must be a instance, not a class");return!0},Vu=function(e){An(n,Gu(Error));var t=Ku(n);function n(e,r){var a;return mn(this,n),(a=t.call(this,e)).name="Error",a.stack=a.buildStackTrace(r),a}return gn(n,[{key:"buildStackTrace",value:function(e){var t,n=e&&e.stack?e.stack:"";return Vs(t="".concat(this.stack,"\nCaused By: ")).call(t,n)}}]),n}(),Ju=new Proxy({},{get:function(e,t,n){return function(){}}});function Qu(e,t,n){var r,a;if(-1===e)Ju.warn(Vs(r=Vs(a="Duplicate hook name [".concat(t.HOOK_NAME,"] found, hook [")).call(a,t.toString(),"] ")).call(r,isNaN(n)?"":"at index [".concat(n,"] "),"will not take effect."));else if(-2===e){var i;Ju.warn(Vs(i="Hook [".concat(t.toString(),"] ")).call(i,isNaN(n)?"":"at index [".concat(n,"] "),"is not a valid hook, and will not take effect."))}}function ef(e){return tf(e)||nf(e)}function tf(e){return Object.prototype.isPrototypeOf.call(Cc,e)}function nf(e){return Object.prototype.isPrototypeOf.call(eu,e)}function rf(e){return ef(e)&&!0===(null==e?void 0:e.Cherry$$CUSTOM)}var af=function(){function e(t,n,r){mn(this,e),this.$locale=r.locale,this.$cherry=r,this.hookList={},this.hookNameList={},Zu(t,Array),this.registerInternalHooks(t,n),this.registerCustomHooks(n.engine.customSyntax,n)}return gn(e,[{key:"registerInternalHooks",value:function(e,t){var n=this;ac(e).call(e,(function(e,r){Qu(n.register(e,t),e,r)}))}},{key:"registerCustomHooks",value:function(e,t){var n=this;if(e){var r=oc(e);ac(r).call(r,(function(r){var a,i,o,s,c={},l=e[r];if(tf(l))i=l;else{if(!tf(s=null==(o=l)?void 0:o.syntaxClass)&&!nf(s))return;i=l.syntaxClass,c.force=Boolean(l.force),l.before?c.before=l.before:l.after&&(c.after=l.after)}ef(i)?(Us(i,"Cherry$$CUSTOM",{enumerable:!1,configurable:!1,writable:!1,value:!0}),a=n.register(i,t,c)):a=-2,Qu(a,i,void 0)}))}}},{key:"getHookList",value:function(){return this.hookList}},{key:"getHookNameList",value:function(){return this.hookNameList}},{key:"register",value:function(e,t,n){var r,a,i=this,o=t.externals,s=t.engine,c=s.syntax,l=this.$cherry;if(ef(e)){a=e.HOOK_NAME;var u=(null==c?void 0:c[a])||{};(r=new e({externals:o,config:u,globalConfig:s.global,cherry:l})).afterInit((function(){r.setLocale(i.$locale)}))}else{if("function"!=typeof e)return-2;if(!(r=e(t))||!ef(r.constructor))return-2;a=r.getName()}if(!1!==c[a]||rf(e)){var f=r.getType();if(this.hookNameList[a]){var d;if(!rf(e))return-1;if(!n.force)return-1;var p=this.hookNameList[a].type;this.hookList[p]=fc(d=this.hookList[p]).call(d,(function(e){return e.getName()!==a}))}if(this.hookNameList[a]={type:f},this.hookList[f]=this.hookList[f]||[],rf(e)){var h,g,m,b=-1;if(n.before){if(-1===(b=bc(h=this.hookList[f]).call(h,(function(e){return e.getName()===n.before}))))Ju.warn(Vs(g="Cannot find hook named [".concat(n.before,"],\n            custom hook [")).call(g,a,"] will append to the end of the hooks."))}else if(n.after){var v,y;-1===(b=bc(v=this.hookList[f]).call(v,(function(e){return e.getName()===n.after})))?Ju.warn(Vs(y="Cannot find hook named [".concat(n.after,"],\n              custom hook [")).call(y,a,"] will append to the end of the hooks.")):b+=1}if(b<0||b>=this.hookList[f].length)this.hookList[f].push(r);else Sc(m=this.hookList[f]).call(m,b,0,r)}else this.hookList[f].push(r)}}}]),e}();function of(e,t){var n=oc(e);if(sl){var r=sl(e);t&&(r=fc(r).call(r,(function(t){return dl(e,t).enumerable}))),n.push.apply(n,r)}return n}function sf(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?ac(n=of(Object(a),!0)).call(n,(function(t){hi(e,t,a[t])})):gl?vl(e,gl(a)):ac(r=of(Object(a))).call(r,(function(t){Us(e,t,dl(a,t))}))}return e}function cf(e,t,n){var r=e.begin+e.content+e.end;return n&&(r=r.replace(/\[\\h\]/g,uf).replace(/\\h/g,uf)),new RegExp(r,t||"g")}function lf(){try{return new RegExp("(?<=.)"),!0}catch(e){}return!1}var uf="[ \\t\\u00a0]",ff="(?:[^\\n]*?\\S[^\\n]*?)",df="[\\u0021-\\u002F\\u003a-\\u0040\\u005b-\\u0060\\u007b-\\u007e]",pf="[\\u0021-\\u002F\\u003a-\\u0040\\u005b\\u005d\\u005e\\u0060\\u007b-\\u007e \\t\\n！“”¥‘’（），。—：；《》？【】「」·～｜]",hf=new RegExp([/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+/.source,"@",/[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*/.source].join("")),gf=new RegExp("^".concat(hf.source,"$")),mf=new RegExp('(?:\\S+(?::\\S*)?@)?(?:(?:1\\d\\d|2[01]\\d|22[0-3]|[1-9]\\d?)(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:1\\d\\d|2[0-4]\\d|25[0-4]|[1-9]\\d?))|(?![-_])(?:[-\\w\\xa1-\\xff]{0,63}[^-_]\\.)+(?:[a-zA-Z\\xa1-\\xff]{2,}\\.?))(?::\\d{2,5})?(?:[/?#][^\\s<>\\x00-\\x1f"\\(\\)]*)?'),bf=new RegExp("(?:\\/\\/)".concat(mf.source)),vf=new RegExp("^".concat(mf.source,"$")),yf=new RegExp("^".concat(bf.source,"$"));function _f(){var e,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n={begin:"(?:^|\\n)(\\n*)",content:["(\\h*\\|[^\\n]+\\|?\\h*)","\\n","(?:(?:\\h*\\|\\h*:?[-]{1,}:?\\h*)+\\|?\\h*)","((\\n\\h*\\|[^\\n]+\\|?\\h*)*)"].join(""),end:"(?=$|\\n)"};n.reg=cf(n,"g",!0);var r={begin:"(?:^|\\n)(\\n*)",content:["(\\|?[^\\n|]+(\\|[^\\n|]+)+\\|?)","\\n","(?:\\|?\\h*:?[-]{1,}:?[\\h]*(?:\\|[\\h]*:?[-]{1,}:?\\h*)+\\|?)","((\\n\\|?([^\\n|]+(\\|[^\\n|]*)+)\\|?)*)"].join(""),end:"(?=$|\\n)"};return r.reg=cf(r,"g",!0),!1===t?{strict:n,loose:r}:cf({begin:"",content:Vs(e="(?:".concat(n.begin+n.content+n.end,"|")).call(e,r.begin+r.content+r.end,")"),end:""},"g",!0)}var kf=nl;function wf(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ef=uu,Sf=function(e,t,n,r){try{return r?t(Fe(n)[0],n[1]):t(n)}catch(t){pu(e,"throw",t)}},Af=i.Array,xf=be("iterator"),Cf=!1;try{var Tf=0,$f={next:function(){return{done:!!Tf++}},return:function(){Cf=!0}};$f[xf]=function(){return this},Array.from($f,(function(){throw 2}))}catch(e){}var Rf=!function(e,t){if(!t&&!Cf)return!1;var n=!1;try{var r={};r[xf]=function(){return{next:function(){return{done:n=!0}}}},e(r)}catch(e){}return n}((function(e){Array.from(e)}));Ve({target:"Array",stat:!0,forced:Rf},{from:function(e){var t=oe(e),n=kt(this),r=arguments.length,a=r>1?arguments[1]:void 0,i=void 0!==a;i&&(a=Me(a,r>2?arguments[2]:void 0));var o,s,c,l,u,f,d=uu(t),p=0;if(!d||this==Af&&cu(d))for(o=Lt(t),s=n?new this(o):Af(o);o>p;p++)f=i?a(t[p],p):t[p],Cn(s,p,f);else for(u=(l=du(t,d)).next,s=n?new this:[];!(c=_(u,l)).done;p++)f=i?Sf(l,a,[c.value,p],!0):c.value,Cn(s,p,f);return s.length=p,s}});var Of=N.Array.from,Pf=Of;function Lf(e){if(void 0!==Ja&&null!=Ef(e)||null!=e["@@iterator"])return Pf(e)}var If=Pn("slice"),Nf=be("species"),Mf=i.Array,jf=Math.max;Ve({target:"Array",proto:!0,forced:!If},{slice:function(e,t){var n,r,a,i=L(this),o=Lt(i),s=Rt(e,o),c=Rt(void 0===t?o:t,o);if(xn(i)&&(n=i.constructor,(kt(n)&&(n===Mf||xn(n.prototype))||I(n)&&null===(n=n[Nf]))&&(n=void 0),n===Mf||void 0===n))return Je(i,s,c);for(r=new(void 0===n?Mf:n)(jf(c-s,0)),a=0;s<c;s++,a++)s in i&&Cn(r,a,i[s]);return r.length=a,r}});var Df=Ws("Array").slice,Bf=Array.prototype,Ff=function(e){var t=e.slice;return e===Bf||D(Bf,e)&&t===Bf.slice?Df:t},Hf=Ff;function zf(e,t){var n;if(e){if("string"==typeof e)return wf(e,t);var r=Hf(n=Object.prototype.toString.call(e)).call(n,8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Pf(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?wf(e,t):void 0}}function Uf(e){return function(e){if(kf(e))return wf(e)}(e)||Lf(e)||zf(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Wf(e){if(kf(e))return e}function qf(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function Gf(e){return Wf(e)||Lf(e)||zf(e)||qf()}var Kf=Ff;function Zf(e,t){var n=oc(e);if(sl){var r=sl(e);t&&(r=fc(r).call(r,(function(t){return dl(e,t).enumerable}))),n.push.apply(n,r)}return n}function Yf(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?ac(n=Zf(Object(a),!0)).call(n,(function(t){hi(e,t,a[t])})):gl?vl(e,gl(a)):ac(r=Zf(Object(a))).call(r,(function(t){Us(e,t,dl(a,t))}))}return e}function Xf(e,t,n){var r,a=arguments.length>3&&void 0!==arguments[3]&&arguments[3],i=arguments.length>4&&void 0!==arguments[4]?arguments[4]:1;if(!t)return e;t.lastIndex=0;for(var o=0,s=[];null!==(r=t.exec(e));){var c={begin:r.index,length:r[0].length};if(a&&r.index===o-i){var l,u=Gf(r),f=u[0],d=Kf(u).call(u,2);s.push({begin:c.begin+i,length:c.length-i,replacedText:n.apply(void 0,Vs(l=[Kf(f).call(f,i),""]).call(l,Uf(d)))})}else s.push(Yf(Yf({},c),{},{replacedText:n.apply(void 0,Uf(r))}));o=t.lastIndex,t.lastIndex-=i}return t.lastIndex=0,function(e,t){if(!t.length)return e;var n=[],r=0;return ac(t).call(t,(function(a,i){n.push(Kf(e).call(e,r,a.begin)),n.push(a.replacedText),r=a.begin+a.length,i===t.length-1&&n.push(Kf(e).call(e,r))})),n.join("")}(e,s)}function Vf(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var Jf=function(e){An(n,Cc);var t=Vf(n);function n(){return mn(this,n),t.apply(this,arguments)}return gn(n,[{key:"toHtml",value:function(e,t,n,r){var a,i;return Vs(a=Vs(i="".concat(t,'<span style="color:')).call(i,n,'">')).call(a,r,"</span>")}},{key:"makeHtml",value:function(e){return lf()?e.replace(this.RULE.reg,this.toHtml):Xf(e,this.RULE.reg,this.toHtml,!0,1)}},{key:"rule",value:function(){var e={begin:lf()?"((?<!\\\\))!!":"(^|[^\\\\])!!",end:"!!",content:"(#[0-9a-zA-Z]{3,6}|[a-z]{3,20})[\\s]([\\w\\W]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();function Qf(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(Jf,"HOOK_NAME","fontColor");var ed=function(e){An(n,Cc);var t=Qf(n);function n(){return mn(this,n),t.apply(this,arguments)}return gn(n,[{key:"toHtml",value:function(e,t,n,r){var a,i;return Vs(a=Vs(i="".concat(t,'<span style="background-color:')).call(i,n,'">')).call(a,r,"</span>")}},{key:"makeHtml",value:function(e){return lf()?e.replace(this.RULE.reg,this.toHtml):Xf(e,this.RULE.reg,this.toHtml,!0,1)}},{key:"rule",value:function(){var e={begin:lf()?"((?<!\\\\))!!!":"(^|[^\\\\])!!!",end:"!!!",content:"(#[0-9a-zA-Z]{3,6}|[a-z]{3,10})[\\s]([\\w\\W]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();function td(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(ed,"HOOK_NAME","bgColor");var nd=function(e){An(n,Cc);var t=td(n);function n(){return mn(this,n),t.apply(this,arguments)}return gn(n,[{key:"toHtml",value:function(e,t,n,r){var a,i;return Vs(a=Vs(i="".concat(t,'<span style="font-size:')).call(i,n,'px;line-height:1em;">')).call(a,r,"</span>")}},{key:"makeHtml",value:function(e){return this.test(e)?lf()?e.replace(this.RULE.reg,this.toHtml):Xf(e,this.RULE.reg,this.toHtml,!0,1):e}},{key:"rule",value:function(){var e={begin:lf()?"((?<!\\\\))!":"(^|[^\\\\])!",end:"!",content:"([0-9]{1,2})[\\s]([\\w\\W]*?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();function rd(e,t){var n=oc(e);if(sl){var r=sl(e);t&&(r=fc(r).call(r,(function(t){return dl(e,t).enumerable}))),n.push.apply(n,r)}return n}function ad(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?ac(n=rd(Object(a),!0)).call(n,(function(t){hi(e,t,a[t])})):gl?vl(e,gl(a)):ac(r=rd(Object(a))).call(r,(function(t){Us(e,t,dl(a,t))}))}return e}function id(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(nd,"HOOK_NAME","fontSize");var od=function(e){An(n,Cc);var t=id(n);function n(){var e,r=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0}).config;return mn(this,n),e=t.call(this,{config:r}),r?(e.needWhitespace=!!r.needWhitespace,e):ui(e)}return gn(n,[{key:"makeHtml",value:function(e){return this.test(e)?e.replace(this.RULE.reg,"$1<del>$2</del>"):e}},{key:"rule",value:function(){var e={};return(e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0}).config.needWhitespace?ad(ad({},e),{},{begin:"(^|[\\s])\\~T\\~T",end:"\\~T\\~T(?=\\s|$)",content:"([\\w\\W]+?)"}):ad(ad({},e),{},{begin:"(^|[^\\\\])\\~T\\~T",end:"\\~T\\~T",content:"([\\w\\W]+?)"})).reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();function sd(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(od,"HOOK_NAME","strikethrough");var cd=function(e){An(n,Cc);var t=sd(n);function n(){return mn(this,n),t.apply(this,arguments)}return gn(n,[{key:"toHtml",value:function(e,t,n){var r;return Vs(r="".concat(t,"<sup>")).call(r,n,"</sup>")}},{key:"makeHtml",value:function(e){return lf()?e.replace(this.RULE.reg,this.toHtml):Xf(e,this.RULE.reg,this.toHtml,!0,1)}},{key:"rule",value:function(){var e={begin:lf()?"((?<!\\\\))\\^":"(^|[^\\\\])\\^",end:"\\^",content:"([\\w\\W]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();function ld(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(cd,"HOOK_NAME","sup");var ud=function(e){An(n,Cc);var t=ld(n);function n(){return mn(this,n),t.apply(this,arguments)}return gn(n,[{key:"toHtml",value:function(e,t,n){var r;return Vs(r="".concat(t,"<sub>")).call(r,n,"</sub>")}},{key:"makeHtml",value:function(e){return lf()?e.replace(this.RULE.reg,this.toHtml):Xf(e,this.RULE.reg,this.toHtml,!0,1)}},{key:"rule",value:function(){var e={begin:lf()?"((?<!\\\\))\\^\\^":"(^|[^\\\\])\\^\\^",end:"\\^\\^",content:"([\\w\\W]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();function fd(e,t){return Wf(e)||function(e,t){var n=null==e?null:void 0!==Ja&&Ef(e)||e["@@iterator"];if(null!=n){var r,a,i=[],o=!0,s=!1;try{for(n=n.call(e);!(o=(r=n.next()).done)&&(i.push(r.value),!t||i.length!==t);o=!0);}catch(e){s=!0,a=e}finally{try{o||null==n.return||n.return()}finally{if(s)throw a}}return i}}(e,t)||zf(e,t)||qf()}hi(ud,"HOOK_NAME","sub");var dd=t((function(t){var n=function(e){var t=/(?:^|\s)lang(?:uage)?-([\w-]+)(?=\s|$)/i,n=0,r={},a={manual:e.Prism&&e.Prism.manual,disableWorkerMessageHandler:e.Prism&&e.Prism.disableWorkerMessageHandler,util:{encode:function e(t){return t instanceof i?new i(t.type,e(t.content),t.alias):Array.isArray(t)?t.map(e):t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++n}),e.__id},clone:function e(t,n){var r,i;switch(n=n||{},a.util.type(t)){case"Object":if(i=a.util.objId(t),n[i])return n[i];for(var o in r={},n[i]=r,t)t.hasOwnProperty(o)&&(r[o]=e(t[o],n));return r;case"Array":return i=a.util.objId(t),n[i]?n[i]:(r=[],n[i]=r,t.forEach((function(t,a){r[a]=e(t,n)})),r);default:return t}},getLanguage:function(e){for(;e;){var n=t.exec(e.className);if(n)return n[1].toLowerCase();e=e.parentElement}return"none"},setLanguage:function(e,n){e.className=e.className.replace(RegExp(t,"gi"),""),e.classList.add("language-"+n)},currentScript:function(){if("undefined"==typeof document)return null;if("currentScript"in document)return document.currentScript;try{throw new Error}catch(r){var e=(/at [^(\r\n]*\((.*):[^:]+:[^:]+\)$/i.exec(r.stack)||[])[1];if(e){var t=document.getElementsByTagName("script");for(var n in t)if(t[n].src==e)return t[n]}return null}},isActive:function(e,t,n){for(var r="no-"+t;e;){var a=e.classList;if(a.contains(t))return!0;if(a.contains(r))return!1;e=e.parentElement}return!!n}},languages:{plain:r,plaintext:r,text:r,txt:r,extend:function(e,t){var n=a.util.clone(a.languages[e]);for(var r in t)n[r]=t[r];return n},insertBefore:function(e,t,n,r){var i=(r=r||a.languages)[e],o={};for(var s in i)if(i.hasOwnProperty(s)){if(s==t)for(var c in n)n.hasOwnProperty(c)&&(o[c]=n[c]);n.hasOwnProperty(s)||(o[s]=i[s])}var l=r[e];return r[e]=o,a.languages.DFS(a.languages,(function(t,n){n===l&&t!=e&&(this[t]=o)})),o},DFS:function e(t,n,r,i){i=i||{};var o=a.util.objId;for(var s in t)if(t.hasOwnProperty(s)){n.call(t,s,t[s],r||s);var c=t[s],l=a.util.type(c);"Object"!==l||i[o(c)]?"Array"!==l||i[o(c)]||(i[o(c)]=!0,e(c,n,s,i)):(i[o(c)]=!0,e(c,n,null,i))}}},plugins:{},highlightAll:function(e,t){a.highlightAllUnder(document,e,t)},highlightAllUnder:function(e,t,n){var r={callback:n,container:e,selector:'code[class*="language-"], [class*="language-"] code, code[class*="lang-"], [class*="lang-"] code'};a.hooks.run("before-highlightall",r),r.elements=Array.prototype.slice.apply(r.container.querySelectorAll(r.selector)),a.hooks.run("before-all-elements-highlight",r);for(var i,o=0;i=r.elements[o++];)a.highlightElement(i,!0===t,r.callback)},highlightElement:function(t,n,r){var i=a.util.getLanguage(t),o=a.languages[i];a.util.setLanguage(t,i);var s=t.parentElement;s&&"pre"===s.nodeName.toLowerCase()&&a.util.setLanguage(s,i);var c={element:t,language:i,grammar:o,code:t.textContent};function l(e){c.highlightedCode=e,a.hooks.run("before-insert",c),c.element.innerHTML=c.highlightedCode,a.hooks.run("after-highlight",c),a.hooks.run("complete",c),r&&r.call(c.element)}if(a.hooks.run("before-sanity-check",c),(s=c.element.parentElement)&&"pre"===s.nodeName.toLowerCase()&&!s.hasAttribute("tabindex")&&s.setAttribute("tabindex","0"),!c.code)return a.hooks.run("complete",c),void(r&&r.call(c.element));if(a.hooks.run("before-highlight",c),c.grammar)if(n&&e.Worker){var u=new Worker(a.filename);u.onmessage=function(e){l(e.data)},u.postMessage(JSON.stringify({language:c.language,code:c.code,immediateClose:!0}))}else l(a.highlight(c.code,c.grammar,c.language));else l(a.util.encode(c.code))},highlight:function(e,t,n){var r={code:e,grammar:t,language:n};if(a.hooks.run("before-tokenize",r),!r.grammar)throw new Error('The language "'+r.language+'" has no grammar.');return r.tokens=a.tokenize(r.code,r.grammar),a.hooks.run("after-tokenize",r),i.stringify(a.util.encode(r.tokens),r.language)},tokenize:function(e,t){var n=t.rest;if(n){for(var r in n)t[r]=n[r];delete t.rest}var a=new c;return l(a,a.head,e),s(e,a,t,a.head,0),function(e){var t=[],n=e.head.next;for(;n!==e.tail;)t.push(n.value),n=n.next;return t}(a)},hooks:{all:{},add:function(e,t){var n=a.hooks.all;n[e]=n[e]||[],n[e].push(t)},run:function(e,t){var n=a.hooks.all[e];if(n&&n.length)for(var r,i=0;r=n[i++];)r(t)}},Token:i};function i(e,t,n,r){this.type=e,this.content=t,this.alias=n,this.length=0|(r||"").length}function o(e,t,n,r){e.lastIndex=t;var a=e.exec(n);if(a&&r&&a[1]){var i=a[1].length;a.index+=i,a[0]=a[0].slice(i)}return a}function s(e,t,n,r,c,f){for(var d in n)if(n.hasOwnProperty(d)&&n[d]){var p=n[d];p=Array.isArray(p)?p:[p];for(var h=0;h<p.length;++h){if(f&&f.cause==d+","+h)return;var g=p[h],m=g.inside,b=!!g.lookbehind,v=!!g.greedy,y=g.alias;if(v&&!g.pattern.global){var _=g.pattern.toString().match(/[imsuy]*$/)[0];g.pattern=RegExp(g.pattern.source,_+"g")}for(var k=g.pattern||g,w=r.next,E=c;w!==t.tail&&!(f&&E>=f.reach);E+=w.value.length,w=w.next){var S=w.value;if(t.length>e.length)return;if(!(S instanceof i)){var A,x=1;if(v){if(!(A=o(k,E,e,b))||A.index>=e.length)break;var C=A.index,T=A.index+A[0].length,$=E;for($+=w.value.length;C>=$;)$+=(w=w.next).value.length;if(E=$-=w.value.length,w.value instanceof i)continue;for(var R=w;R!==t.tail&&($<T||"string"==typeof R.value);R=R.next)x++,$+=R.value.length;x--,S=e.slice(E,$),A.index-=E}else if(!(A=o(k,0,S,b)))continue;C=A.index;var O=A[0],P=S.slice(0,C),L=S.slice(C+O.length),I=E+S.length;f&&I>f.reach&&(f.reach=I);var N=w.prev;if(P&&(N=l(t,N,P),E+=P.length),u(t,N,x),w=l(t,N,new i(d,m?a.tokenize(O,m):O,y,O)),L&&l(t,w,L),x>1){var M={cause:d+","+h,reach:I};s(e,t,n,w.prev,E,M),f&&M.reach>f.reach&&(f.reach=M.reach)}}}}}}function c(){var e={value:null,prev:null,next:null},t={value:null,prev:e,next:null};e.next=t,this.head=e,this.tail=t,this.length=0}function l(e,t,n){var r=t.next,a={value:n,prev:t,next:r};return t.next=a,r.prev=a,e.length++,a}function u(e,t,n){for(var r=t.next,a=0;a<n&&r!==e.tail;a++)r=r.next;t.next=r,r.prev=t,e.length-=a}if(e.Prism=a,i.stringify=function e(t,n){if("string"==typeof t)return t;if(Array.isArray(t)){var r="";return t.forEach((function(t){r+=e(t,n)})),r}var i={type:t.type,content:e(t.content,n),tag:"span",classes:["token",t.type],attributes:{},language:n},o=t.alias;o&&(Array.isArray(o)?Array.prototype.push.apply(i.classes,o):i.classes.push(o)),a.hooks.run("wrap",i);var s="";for(var c in i.attributes)s+=" "+c+'="'+(i.attributes[c]||"").replace(/"/g,"&quot;")+'"';return"<"+i.tag+' class="'+i.classes.join(" ")+'"'+s+">"+i.content+"</"+i.tag+">"},!e.document)return e.addEventListener?(a.disableWorkerMessageHandler||e.addEventListener("message",(function(t){var n=JSON.parse(t.data),r=n.language,i=n.code,o=n.immediateClose;e.postMessage(a.highlight(i,a.languages[r],r)),o&&e.close()}),!1),a):a;var f=a.util.currentScript();function d(){a.manual||a.highlightAll()}if(f&&(a.filename=f.src,f.hasAttribute("data-manual")&&(a.manual=!0)),!a.manual){var p=document.readyState;"loading"===p||"interactive"===p&&f&&f.defer?document.addEventListener("DOMContentLoaded",d):window.requestAnimationFrame?window.requestAnimationFrame(d):window.setTimeout(d,16)}return a}("undefined"!=typeof window?window:"undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope?self:{});t.exports&&(t.exports=n),void 0!==e&&(e.Prism=n)}));function pd(e,t){var n=oc(e);if(sl){var r=sl(e);t&&(r=fc(r).call(r,(function(t){return dl(e,t).enumerable}))),n.push.apply(n,r)}return n}function hd(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}Prism.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|trait)\s+|\bcatch\s+\()[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:break|catch|continue|do|else|finally|for|function|if|in|instanceof|new|null|return|throw|try|while)\b/,boolean:/\b(?:false|true)\b/,function:/\b\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/[<>]=?|[!=]=?=?|--?|\+\+?|&&?|\|\|?|[?*/~^%]/,punctuation:/[{}[\];(),.:]/},Prism.languages.c=Prism.languages.extend("clike",{comment:{pattern:/\/\/(?:[^\r\n\\]|\\(?:\r\n?|\n|(?![\r\n])))*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},string:{pattern:/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},"class-name":{pattern:/(\b(?:enum|struct)\s+(?:__attribute__\s*\(\([\s\S]*?\)\)\s*)?)\w+|\b[a-z]\w*_t\b/,lookbehind:!0},keyword:/\b(?:_Alignas|_Alignof|_Atomic|_Bool|_Complex|_Generic|_Imaginary|_Noreturn|_Static_assert|_Thread_local|__attribute__|asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|inline|int|long|register|return|short|signed|sizeof|static|struct|switch|typedef|typeof|union|unsigned|void|volatile|while)\b/,function:/\b[a-z_]\w*(?=\s*\()/i,number:/(?:\b0x(?:[\da-f]+(?:\.[\da-f]*)?|\.[\da-f]+)(?:p[+-]?\d+)?|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:e[+-]?\d+)?)[ful]{0,4}/i,operator:/>>=?|<<=?|->|([-+&|:])\1|[?:~]|[-+*/%&|^!=<>]=?/}),Prism.languages.insertBefore("c","string",{char:{pattern:/'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n]){0,32}'/,greedy:!0}}),Prism.languages.insertBefore("c","string",{macro:{pattern:/(^[\t ]*)#\s*[a-z](?:[^\r\n\\/]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|\\(?:\r\n|[\s\S]))*/im,lookbehind:!0,greedy:!0,alias:"property",inside:{string:[{pattern:/^(#\s*include\s*)<[^>]+>/,lookbehind:!0},Prism.languages.c.string],char:Prism.languages.c.char,comment:Prism.languages.c.comment,"macro-name":[{pattern:/(^#\s*define\s+)\w+\b(?!\()/i,lookbehind:!0},{pattern:/(^#\s*define\s+)\w+\b(?=\()/i,lookbehind:!0,alias:"function"}],directive:{pattern:/^(#\s*)[a-z]+/,lookbehind:!0,alias:"keyword"},"directive-hash":/^#/,punctuation:/##|\\(?=[\r\n])/,expression:{pattern:/\S[\s\S]*/,inside:Prism.languages.c}}}}),Prism.languages.insertBefore("c","function",{constant:/\b(?:EOF|NULL|SEEK_CUR|SEEK_END|SEEK_SET|__DATE__|__FILE__|__LINE__|__TIMESTAMP__|__TIME__|__func__|stderr|stdin|stdout)\b/}),delete Prism.languages.c.boolean,function(e){function t(e,t){return e.replace(/<<(\d+)>>/g,(function(e,n){return"(?:"+t[+n]+")"}))}function n(e,n,r){return RegExp(t(e,n),r||"")}function r(e,t){for(var n=0;n<t;n++)e=e.replace(/<<self>>/g,(function(){return"(?:"+e+")"}));return e.replace(/<<self>>/g,"[^\\s\\S]")}var a="bool byte char decimal double dynamic float int long object sbyte short string uint ulong ushort var void",i="class enum interface record struct",o="add alias and ascending async await by descending from(?=\\s*(?:\\w|$)) get global group into init(?=\\s*;) join let nameof not notnull on or orderby partial remove select set unmanaged value when where with(?=\\s*{)",s="abstract as base break case catch checked const continue default delegate do else event explicit extern finally fixed for foreach goto if implicit in internal is lock namespace new null operator out override params private protected public readonly ref return sealed sizeof stackalloc static switch this throw try typeof unchecked unsafe using virtual volatile while yield";function c(e){return"\\b(?:"+e.trim().replace(/ /g,"|")+")\\b"}var l=c(i),u=RegExp(c(a+" "+i+" "+o+" "+s)),f=c(i+" "+o+" "+s),d=c(a+" "+i+" "+s),p=r(/<(?:[^<>;=+\-*/%&|^]|<<self>>)*>/.source,2),h=r(/\((?:[^()]|<<self>>)*\)/.source,2),g=/@?\b[A-Za-z_]\w*\b/.source,m=t(/<<0>>(?:\s*<<1>>)?/.source,[g,p]),b=t(/(?!<<0>>)<<1>>(?:\s*\.\s*<<1>>)*/.source,[f,m]),v=/\[\s*(?:,\s*)*\]/.source,y=t(/<<0>>(?:\s*(?:\?\s*)?<<1>>)*(?:\s*\?)?/.source,[b,v]),_=t(/[^,()<>[\];=+\-*/%&|^]|<<0>>|<<1>>|<<2>>/.source,[p,h,v]),k=t(/\(<<0>>+(?:,<<0>>+)+\)/.source,[_]),w=t(/(?:<<0>>|<<1>>)(?:\s*(?:\?\s*)?<<2>>)*(?:\s*\?)?/.source,[k,b,v]),E={keyword:u,punctuation:/[<>()?,.:[\]]/},S=/'(?:[^\r\n'\\]|\\.|\\[Uux][\da-fA-F]{1,8})'/.source,A=/"(?:\\.|[^\\"\r\n])*"/.source,x=/@"(?:""|\\[\s\S]|[^\\"])*"(?!")/.source;e.languages.csharp=e.languages.extend("clike",{string:[{pattern:n(/(^|[^$\\])<<0>>/.source,[x]),lookbehind:!0,greedy:!0},{pattern:n(/(^|[^@$\\])<<0>>/.source,[A]),lookbehind:!0,greedy:!0}],"class-name":[{pattern:n(/(\busing\s+static\s+)<<0>>(?=\s*;)/.source,[b]),lookbehind:!0,inside:E},{pattern:n(/(\busing\s+<<0>>\s*=\s*)<<1>>(?=\s*;)/.source,[g,w]),lookbehind:!0,inside:E},{pattern:n(/(\busing\s+)<<0>>(?=\s*=)/.source,[g]),lookbehind:!0},{pattern:n(/(\b<<0>>\s+)<<1>>/.source,[l,m]),lookbehind:!0,inside:E},{pattern:n(/(\bcatch\s*\(\s*)<<0>>/.source,[b]),lookbehind:!0,inside:E},{pattern:n(/(\bwhere\s+)<<0>>/.source,[g]),lookbehind:!0},{pattern:n(/(\b(?:is(?:\s+not)?|as)\s+)<<0>>/.source,[y]),lookbehind:!0,inside:E},{pattern:n(/\b<<0>>(?=\s+(?!<<1>>|with\s*\{)<<2>>(?:\s*[=,;:{)\]]|\s+(?:in|when)\b))/.source,[w,d,g]),inside:E}],keyword:u,number:/(?:\b0(?:x[\da-f_]*[\da-f]|b[01_]*[01])|(?:\B\.\d+(?:_+\d+)*|\b\d+(?:_+\d+)*(?:\.\d+(?:_+\d+)*)?)(?:e[-+]?\d+(?:_+\d+)*)?)(?:[dflmu]|lu|ul)?\b/i,operator:/>>=?|<<=?|[-=]>|([-+&|])\1|~|\?\?=?|[-+*/%&|^!=<>]=?/,punctuation:/\?\.?|::|[{}[\];(),.:]/}),e.languages.insertBefore("csharp","number",{range:{pattern:/\.\./,alias:"operator"}}),e.languages.insertBefore("csharp","punctuation",{"named-parameter":{pattern:n(/([(,]\s*)<<0>>(?=\s*:)/.source,[g]),lookbehind:!0,alias:"punctuation"}}),e.languages.insertBefore("csharp","class-name",{namespace:{pattern:n(/(\b(?:namespace|using)\s+)<<0>>(?:\s*\.\s*<<0>>)*(?=\s*[;{])/.source,[g]),lookbehind:!0,inside:{punctuation:/\./}},"type-expression":{pattern:n(/(\b(?:default|sizeof|typeof)\s*\(\s*(?!\s))(?:[^()\s]|\s(?!\s)|<<0>>)*(?=\s*\))/.source,[h]),lookbehind:!0,alias:"class-name",inside:E},"return-type":{pattern:n(/<<0>>(?=\s+(?:<<1>>\s*(?:=>|[({]|\.\s*this\s*\[)|this\s*\[))/.source,[w,b]),inside:E,alias:"class-name"},"constructor-invocation":{pattern:n(/(\bnew\s+)<<0>>(?=\s*[[({])/.source,[w]),lookbehind:!0,inside:E,alias:"class-name"},"generic-method":{pattern:n(/<<0>>\s*<<1>>(?=\s*\()/.source,[g,p]),inside:{function:n(/^<<0>>/.source,[g]),generic:{pattern:RegExp(p),alias:"class-name",inside:E}}},"type-list":{pattern:n(/\b((?:<<0>>\s+<<1>>|record\s+<<1>>\s*<<5>>|where\s+<<2>>)\s*:\s*)(?:<<3>>|<<4>>|<<1>>\s*<<5>>|<<6>>)(?:\s*,\s*(?:<<3>>|<<4>>|<<6>>))*(?=\s*(?:where|[{;]|=>|$))/.source,[l,m,g,w,u.source,h,/\bnew\s*\(\s*\)/.source]),lookbehind:!0,inside:{"record-arguments":{pattern:n(/(^(?!new\s*\()<<0>>\s*)<<1>>/.source,[m,h]),lookbehind:!0,greedy:!0,inside:e.languages.csharp},keyword:u,"class-name":{pattern:RegExp(w),greedy:!0,inside:E},punctuation:/[,()]/}},preprocessor:{pattern:/(^[\t ]*)#.*/m,lookbehind:!0,alias:"property",inside:{directive:{pattern:/(#)\b(?:define|elif|else|endif|endregion|error|if|line|nullable|pragma|region|undef|warning)\b/,lookbehind:!0,alias:"keyword"}}}});var C=A+"|"+S,T=t(/\/(?![*/])|\/\/[^\r\n]*[\r\n]|\/\*(?:[^*]|\*(?!\/))*\*\/|<<0>>/.source,[C]),$=r(t(/[^"'/()]|<<0>>|\(<<self>>*\)/.source,[T]),2),R=/\b(?:assembly|event|field|method|module|param|property|return|type)\b/.source,O=t(/<<0>>(?:\s*\(<<1>>*\))?/.source,[b,$]);e.languages.insertBefore("csharp","class-name",{attribute:{pattern:n(/((?:^|[^\s\w>)?])\s*\[\s*)(?:<<0>>\s*:\s*)?<<1>>(?:\s*,\s*<<1>>)*(?=\s*\])/.source,[R,O]),lookbehind:!0,greedy:!0,inside:{target:{pattern:n(/^<<0>>(?=\s*:)/.source,[R]),alias:"keyword"},"attribute-arguments":{pattern:n(/\(<<0>>*\)/.source,[$]),inside:e.languages.csharp},"class-name":{pattern:RegExp(b),inside:{punctuation:/\./}},punctuation:/[:,]/}}});var P=/:[^}\r\n]+/.source,L=r(t(/[^"'/()]|<<0>>|\(<<self>>*\)/.source,[T]),2),I=t(/\{(?!\{)(?:(?![}:])<<0>>)*<<1>>?\}/.source,[L,P]),N=r(t(/[^"'/()]|\/(?!\*)|\/\*(?:[^*]|\*(?!\/))*\*\/|<<0>>|\(<<self>>*\)/.source,[C]),2),M=t(/\{(?!\{)(?:(?![}:])<<0>>)*<<1>>?\}/.source,[N,P]);function j(t,r){return{interpolation:{pattern:n(/((?:^|[^{])(?:\{\{)*)<<0>>/.source,[t]),lookbehind:!0,inside:{"format-string":{pattern:n(/(^\{(?:(?![}:])<<0>>)*)<<1>>(?=\}$)/.source,[r,P]),lookbehind:!0,inside:{punctuation:/^:/}},punctuation:/^\{|\}$/,expression:{pattern:/[\s\S]+/,alias:"language-csharp",inside:e.languages.csharp}}},string:/[\s\S]+/}}e.languages.insertBefore("csharp","string",{"interpolation-string":[{pattern:n(/(^|[^\\])(?:\$@|@\$)"(?:""|\\[\s\S]|\{\{|<<0>>|[^\\{"])*"/.source,[I]),lookbehind:!0,greedy:!0,inside:j(I,L)},{pattern:n(/(^|[^@\\])\$"(?:\\.|\{\{|<<0>>|[^\\"{])*"/.source,[M]),lookbehind:!0,greedy:!0,inside:j(M,N)}],char:{pattern:RegExp(S),greedy:!0}}),e.languages.dotnet=e.languages.cs=e.languages.csharp}(Prism),function(e){var t=/\b(?:alignas|alignof|asm|auto|bool|break|case|catch|char|char16_t|char32_t|char8_t|class|co_await|co_return|co_yield|compl|concept|const|const_cast|consteval|constexpr|constinit|continue|decltype|default|delete|do|double|dynamic_cast|else|enum|explicit|export|extern|final|float|for|friend|goto|if|import|inline|int|int16_t|int32_t|int64_t|int8_t|long|module|mutable|namespace|new|noexcept|nullptr|operator|override|private|protected|public|register|reinterpret_cast|requires|return|short|signed|sizeof|static|static_assert|static_cast|struct|switch|template|this|thread_local|throw|try|typedef|typeid|typename|uint16_t|uint32_t|uint64_t|uint8_t|union|unsigned|using|virtual|void|volatile|wchar_t|while)\b/,n=/\b(?!<keyword>)\w+(?:\s*\.\s*\w+)*\b/.source.replace(/<keyword>/g,(function(){return t.source}));e.languages.cpp=e.languages.extend("c",{"class-name":[{pattern:RegExp(/(\b(?:class|concept|enum|struct|typename)\s+)(?!<keyword>)\w+/.source.replace(/<keyword>/g,(function(){return t.source}))),lookbehind:!0},/\b[A-Z]\w*(?=\s*::\s*\w+\s*\()/,/\b[A-Z_]\w*(?=\s*::\s*~\w+\s*\()/i,/\b\w+(?=\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>\s*::\s*\w+\s*\()/],keyword:t,number:{pattern:/(?:\b0b[01']+|\b0x(?:[\da-f']+(?:\.[\da-f']*)?|\.[\da-f']+)(?:p[+-]?[\d']+)?|(?:\b[\d']+(?:\.[\d']*)?|\B\.[\d']+)(?:e[+-]?[\d']+)?)[ful]{0,4}/i,greedy:!0},operator:/>>=?|<<=?|->|--|\+\+|&&|\|\||[?:~]|<=>|[-+*/%&|^!=<>]=?|\b(?:and|and_eq|bitand|bitor|not|not_eq|or|or_eq|xor|xor_eq)\b/,boolean:/\b(?:false|true)\b/}),e.languages.insertBefore("cpp","string",{module:{pattern:RegExp(/(\b(?:import|module)\s+)/.source+"(?:"+/"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|<[^<>\r\n]*>/.source+"|"+/<mod-name>(?:\s*:\s*<mod-name>)?|:\s*<mod-name>/.source.replace(/<mod-name>/g,(function(){return n}))+")"),lookbehind:!0,greedy:!0,inside:{string:/^[<"][\s\S]+/,operator:/:/,punctuation:/\./}},"raw-string":{pattern:/R"([^()\\ ]{0,16})\([\s\S]*?\)\1"/,alias:"string",greedy:!0}}),e.languages.insertBefore("cpp","keyword",{"generic-function":{pattern:/\b(?!operator\b)[a-z_]\w*\s*<(?:[^<>]|<[^<>]*>)*>(?=\s*\()/i,inside:{function:/^\w+/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:e.languages.cpp}}}}),e.languages.insertBefore("cpp","operator",{"double-colon":{pattern:/::/,alias:"punctuation"}}),e.languages.insertBefore("cpp","class-name",{"base-clause":{pattern:/(\b(?:class|struct)\s+\w+\s*:\s*)[^;{}"'\s]+(?:\s+[^;{}"'\s]+)*(?=\s*[;{])/,lookbehind:!0,greedy:!0,inside:e.languages.extend("cpp",{})}}),e.languages.insertBefore("inside","double-colon",{"class-name":/\b[a-z_]\w*\b(?!\s*::)/i},e.languages.cpp["base-clause"])}(Prism),Prism.languages.markup={comment:{pattern:/<!--(?:(?!<!--)[\s\S])*?-->/,greedy:!0},prolog:{pattern:/<\?[\s\S]+?\?>/,greedy:!0},doctype:{pattern:/<!DOCTYPE(?:[^>"'[\]]|"[^"]*"|'[^']*')+(?:\[(?:[^<"'\]]|"[^"]*"|'[^']*'|<(?!!--)|<!--(?:[^-]|-(?!->))*-->)*\]\s*)?>/i,greedy:!0,inside:{"internal-subset":{pattern:/(^[^\[]*\[)[\s\S]+(?=\]>$)/,lookbehind:!0,greedy:!0,inside:null},string:{pattern:/"[^"]*"|'[^']*'/,greedy:!0},punctuation:/^<!|>$|[[\]]/,"doctype-tag":/^DOCTYPE/i,name:/[^\s<>'"]+/}},cdata:{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,greedy:!0},tag:{pattern:/<\/?(?!\d)[^\s>\/=$<%]+(?:\s(?:\s*[^\s>\/=]+(?:\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))|(?=[\s/>])))+)?\s*\/?>/,greedy:!0,inside:{tag:{pattern:/^<\/?[^\s>\/]+/,inside:{punctuation:/^<\/?/,namespace:/^[^\s>\/:]+:/}},"special-attr":[],"attr-value":{pattern:/=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+)/,inside:{punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}},punctuation:/\/?>/,"attr-name":{pattern:/[^\s>\/]+/,inside:{namespace:/^[^\s>\/:]+:/}}}},entity:[{pattern:/&[\da-z]{1,8};/i,alias:"named-entity"},/&#x?[\da-f]{1,8};/i]},Prism.languages.markup.tag.inside["attr-value"].inside.entity=Prism.languages.markup.entity,Prism.languages.markup.doctype.inside["internal-subset"].inside=Prism.languages.markup,Prism.hooks.add("wrap",(function(e){"entity"===e.type&&(e.attributes.title=e.content.replace(/&amp;/,"&"))})),Object.defineProperty(Prism.languages.markup.tag,"addInlined",{value:function(e,t){var n={};n["language-"+t]={pattern:/(^<!\[CDATA\[)[\s\S]+?(?=\]\]>$)/i,lookbehind:!0,inside:Prism.languages[t]},n.cdata=/^<!\[CDATA\[|\]\]>$/i;var r={"included-cdata":{pattern:/<!\[CDATA\[[\s\S]*?\]\]>/i,inside:n}};r["language-"+t]={pattern:/[\s\S]+/,inside:Prism.languages[t]};var a={};a[e]={pattern:RegExp(/(<__[^>]*>)(?:<!\[CDATA\[(?:[^\]]|\](?!\]>))*\]\]>|(?!<!\[CDATA\[)[\s\S])*?(?=<\/__>)/.source.replace(/__/g,(function(){return e})),"i"),lookbehind:!0,greedy:!0,inside:r},Prism.languages.insertBefore("markup","cdata",a)}}),Object.defineProperty(Prism.languages.markup.tag,"addAttribute",{value:function(e,t){Prism.languages.markup.tag.inside["special-attr"].push({pattern:RegExp(/(^|["'\s])/.source+"(?:"+e+")"+/\s*=\s*(?:"[^"]*"|'[^']*'|[^\s'">=]+(?=[\s>]))/.source,"i"),lookbehind:!0,inside:{"attr-name":/^[^\s=]+/,"attr-value":{pattern:/=[\s\S]+/,inside:{value:{pattern:/(^=\s*(["']|(?!["'])))\S[\s\S]*(?=\2$)/,lookbehind:!0,alias:[t,"language-"+t],inside:Prism.languages[t]},punctuation:[{pattern:/^=/,alias:"attr-equals"},/"|'/]}}}})}}),Prism.languages.html=Prism.languages.markup,Prism.languages.mathml=Prism.languages.markup,Prism.languages.svg=Prism.languages.markup,Prism.languages.xml=Prism.languages.extend("markup",{}),Prism.languages.ssml=Prism.languages.xml,Prism.languages.atom=Prism.languages.xml,Prism.languages.rss=Prism.languages.xml,function(e){var t=/(?:"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"|'(?:\\(?:\r\n|[\s\S])|[^'\\\r\n])*')/;e.languages.css={comment:/\/\*[\s\S]*?\*\//,atrule:{pattern:/@[\w-](?:[^;{\s]|\s+(?![\s{]))*(?:;|(?=\s*\{))/,inside:{rule:/^@[\w-]+/,"selector-function-argument":{pattern:/(\bselector\s*\(\s*(?![\s)]))(?:[^()\s]|\s+(?![\s)])|\((?:[^()]|\([^()]*\))*\))+(?=\s*\))/,lookbehind:!0,alias:"selector"},keyword:{pattern:/(^|[^\w-])(?:and|not|only|or)(?![\w-])/,lookbehind:!0}}},url:{pattern:RegExp("\\burl\\((?:"+t.source+"|"+/(?:[^\\\r\n()"']|\\[\s\S])*/.source+")\\)","i"),greedy:!0,inside:{function:/^url/i,punctuation:/^\(|\)$/,string:{pattern:RegExp("^"+t.source+"$"),alias:"url"}}},selector:{pattern:RegExp("(^|[{}\\s])[^{}\\s](?:[^{};\"'\\s]|\\s+(?![\\s{])|"+t.source+")*(?=\\s*\\{)"),lookbehind:!0},string:{pattern:t,greedy:!0},property:{pattern:/(^|[^-\w\xA0-\uFFFF])(?!\s)[-_a-z\xA0-\uFFFF](?:(?!\s)[-\w\xA0-\uFFFF])*(?=\s*:)/i,lookbehind:!0},important:/!important\b/i,function:{pattern:/(^|[^-a-z0-9])[-a-z0-9]+(?=\()/i,lookbehind:!0},punctuation:/[(){};:,]/},e.languages.css.atrule.inside.rest=e.languages.css;var n=e.languages.markup;n&&(n.tag.addInlined("style","css"),n.tag.addAttribute("style","css"))}(Prism),function(e){var t=[/\b(?:async|sync|yield)\*/,/\b(?:abstract|assert|async|await|break|case|catch|class|const|continue|covariant|default|deferred|do|dynamic|else|enum|export|extends|extension|external|factory|final|finally|for|get|hide|if|implements|import|in|interface|library|mixin|new|null|on|operator|part|rethrow|return|set|show|static|super|switch|sync|this|throw|try|typedef|var|void|while|with|yield)\b/],n=/(^|[^\w.])(?:[a-z]\w*\s*\.\s*)*(?:[A-Z]\w*\s*\.\s*)*/.source,r={pattern:RegExp(n+/[A-Z](?:[\d_A-Z]*[a-z]\w*)?\b/.source),lookbehind:!0,inside:{namespace:{pattern:/^[a-z]\w*(?:\s*\.\s*[a-z]\w*)*(?:\s*\.)?/,inside:{punctuation:/\./}}}};e.languages.dart=e.languages.extend("clike",{"class-name":[r,{pattern:RegExp(n+/[A-Z]\w*(?=\s+\w+\s*[;,=()])/.source),lookbehind:!0,inside:r.inside}],keyword:t,operator:/\bis!|\b(?:as|is)\b|\+\+|--|&&|\|\||<<=?|>>=?|~(?:\/=?)?|[+\-*\/%&^|=!<>]=?|\?/}),e.languages.insertBefore("dart","string",{"string-literal":{pattern:/r?(?:("""|''')[\s\S]*?\1|(["'])(?:\\.|(?!\2)[^\\\r\n])*\2(?!\2))/,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$(?:\w+|\{(?:[^{}]|\{[^{}]*\})*\})/,lookbehind:!0,inside:{punctuation:/^\$\{?|\}$/,expression:{pattern:/[\s\S]+/,inside:e.languages.dart}}},string:/[\s\S]+/}},string:void 0}),e.languages.insertBefore("dart","class-name",{metadata:{pattern:/@\w+/,alias:"function"}}),e.languages.insertBefore("dart","class-name",{generics:{pattern:/<(?:[\w\s,.&?]|<(?:[\w\s,.&?]|<(?:[\w\s,.&?]|<[\w\s,.&?]*>)*>)*>)*>/,inside:{"class-name":r,keyword:t,punctuation:/[<>(),.:]/,operator:/[?&|]/}}})}(Prism),function(e){e.languages.diff={coord:[/^(?:\*{3}|-{3}|\+{3}).*$/m,/^@@.*@@$/m,/^\d.*$/m]};var t={"deleted-sign":"-","deleted-arrow":"<","inserted-sign":"+","inserted-arrow":">",unchanged:" ",diff:"!"};Object.keys(t).forEach((function(n){var r=t[n],a=[];/^\w+$/.test(n)||a.push(/\w+/.exec(n)[0]),"diff"===n&&a.push("bold"),e.languages.diff[n]={pattern:RegExp("^(?:["+r+"].*(?:\r\n?|\n|(?![\\s\\S])))+","m"),alias:a,inside:{line:{pattern:/(.)(?=[\s\S]).*(?:\r\n?|\n)?/,lookbehind:!0},prefix:{pattern:/[\s\S]/,alias:/\w+/.exec(n)[0]}}}})),Object.defineProperty(e.languages.diff,"PREFIXES",{value:t})}(Prism),function(e){var t=/\\[\r\n](?:\s|\\[\r\n]|#.*(?!.))*(?![\s#]|\\[\r\n])/.source,n=/(?:[ \t]+(?![ \t])(?:<SP_BS>)?|<SP_BS>)/.source.replace(/<SP_BS>/g,(function(){return t})),r=/"(?:[^"\\\r\n]|\\(?:\r\n|[\s\S]))*"|'(?:[^'\\\r\n]|\\(?:\r\n|[\s\S]))*'/.source,a=/--[\w-]+=(?:<STR>|(?!["'])(?:[^\s\\]|\\.)+)/.source.replace(/<STR>/g,(function(){return r})),i={pattern:RegExp(r),greedy:!0},o={pattern:/(^[ \t]*)#.*/m,lookbehind:!0,greedy:!0};function s(e,t){return e=e.replace(/<OPT>/g,(function(){return a})).replace(/<SP>/g,(function(){return n})),RegExp(e,t)}e.languages.docker={instruction:{pattern:/(^[ \t]*)(?:ADD|ARG|CMD|COPY|ENTRYPOINT|ENV|EXPOSE|FROM|HEALTHCHECK|LABEL|MAINTAINER|ONBUILD|RUN|SHELL|STOPSIGNAL|USER|VOLUME|WORKDIR)(?=\s)(?:\\.|[^\r\n\\])*(?:\\$(?:\s|#.*$)*(?![\s#])(?:\\.|[^\r\n\\])*)*/im,lookbehind:!0,greedy:!0,inside:{options:{pattern:s(/(^(?:ONBUILD<SP>)?\w+<SP>)<OPT>(?:<SP><OPT>)*/.source,"i"),lookbehind:!0,greedy:!0,inside:{property:{pattern:/(^|\s)--[\w-]+/,lookbehind:!0},string:[i,{pattern:/(=)(?!["'])(?:[^\s\\]|\\.)+/,lookbehind:!0}],operator:/\\$/m,punctuation:/=/}},keyword:[{pattern:s(/(^(?:ONBUILD<SP>)?HEALTHCHECK<SP>(?:<OPT><SP>)*)(?:CMD|NONE)\b/.source,"i"),lookbehind:!0,greedy:!0},{pattern:s(/(^(?:ONBUILD<SP>)?FROM<SP>(?:<OPT><SP>)*(?!--)[^ \t\\]+<SP>)AS/.source,"i"),lookbehind:!0,greedy:!0},{pattern:s(/(^ONBUILD<SP>)\w+/.source,"i"),lookbehind:!0,greedy:!0},{pattern:/^\w+/,greedy:!0}],comment:o,string:i,variable:/\$(?:\w+|\{[^{}"'\\]*\})/,operator:/\\$/m}},comment:o},e.languages.dockerfile=e.languages.docker}(Prism),Prism.languages.git={comment:/^#.*/m,deleted:/^[-–].*/m,inserted:/^\+.*/m,string:/("|')(?:\\.|(?!\1)[^\\\r\n])*\1/,command:{pattern:/^.*\$ git .*$/m,inside:{parameter:/\s--?\w+/}},coord:/^@@.*@@$/m,"commit-sha1":/^commit \w{40}$/m},Prism.languages.glsl=Prism.languages.extend("c",{keyword:/\b(?:active|asm|atomic_uint|attribute|[ibdu]?vec[234]|bool|break|buffer|case|cast|centroid|class|coherent|common|const|continue|d?mat[234](?:x[234])?|default|discard|do|double|else|enum|extern|external|false|filter|fixed|flat|float|for|fvec[234]|goto|half|highp|hvec[234]|[iu]?sampler2DMS(?:Array)?|[iu]?sampler2DRect|[iu]?samplerBuffer|[iu]?samplerCube|[iu]?samplerCubeArray|[iu]?sampler[123]D|[iu]?sampler[12]DArray|[iu]?image2DMS(?:Array)?|[iu]?image2DRect|[iu]?imageBuffer|[iu]?imageCube|[iu]?imageCubeArray|[iu]?image[123]D|[iu]?image[12]DArray|if|in|inline|inout|input|int|interface|invariant|layout|long|lowp|mediump|namespace|noinline|noperspective|out|output|partition|patch|precise|precision|public|readonly|resource|restrict|return|sample|sampler[12]DArrayShadow|sampler[12]DShadow|sampler2DRectShadow|sampler3DRect|samplerCubeArrayShadow|samplerCubeShadow|shared|short|sizeof|smooth|static|struct|subroutine|superp|switch|template|this|true|typedef|uint|uniform|union|unsigned|using|varying|void|volatile|while|writeonly)\b/}),Prism.languages.go=Prism.languages.extend("clike",{string:{pattern:/(^|[^\\])"(?:\\.|[^"\\\r\n])*"|`[^`]*`/,lookbehind:!0,greedy:!0},keyword:/\b(?:break|case|chan|const|continue|default|defer|else|fallthrough|for|func|go(?:to)?|if|import|interface|map|package|range|return|select|struct|switch|type|var)\b/,boolean:/\b(?:_|false|iota|nil|true)\b/,number:[/\b0(?:b[01_]+|o[0-7_]+)i?\b/i,/\b0x(?:[a-f\d_]+(?:\.[a-f\d_]*)?|\.[a-f\d_]+)(?:p[+-]?\d+(?:_\d+)*)?i?(?!\w)/i,/(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?[\d_]+)?i?(?!\w)/i],operator:/[*\/%^!=]=?|\+[=+]?|-[=-]?|\|[=|]?|&(?:=|&|\^=?)?|>(?:>=?|=)?|<(?:<=?|=|-)?|:=|\.\.\./,builtin:/\b(?:append|bool|byte|cap|close|complex|complex(?:64|128)|copy|delete|error|float(?:32|64)|u?int(?:8|16|32|64)?|imag|len|make|new|panic|print(?:ln)?|real|recover|rune|string|uintptr)\b/}),Prism.languages.insertBefore("go","string",{char:{pattern:/'(?:\\.|[^'\\\r\n]){0,10}'/,greedy:!0}}),delete Prism.languages.go["class-name"],Prism.languages["go-mod"]=Prism.languages["go-module"]={comment:{pattern:/\/\/.*/,greedy:!0},version:{pattern:/(^|[\s()[\],])v\d+\.\d+\.\d+(?:[+-][-+.\w]*)?(?![^\s()[\],])/,lookbehind:!0,alias:"number"},"go-version":{pattern:/((?:^|\s)go\s+)\d+(?:\.\d+){1,2}/,lookbehind:!0,alias:"number"},keyword:{pattern:/^([ \t]*)(?:exclude|go|module|replace|require|retract)\b/m,lookbehind:!0},operator:/=>/,punctuation:/[()[\],]/},function(e){var t=/[*&][^\s[\]{},]+/,n=/!(?:<[\w\-%#;/?:@&=+$,.!~*'()[\]]+>|(?:[a-zA-Z\d-]*!)?[\w\-%#;/?:@&=+$.~*'()]+)?/,r="(?:"+n.source+"(?:[ \t]+"+t.source+")?|"+t.source+"(?:[ \t]+"+n.source+")?)",a=/(?:[^\s\x00-\x08\x0e-\x1f!"#%&'*,\-:>?@[\]`{|}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]|[?:-]<PLAIN>)(?:[ \t]*(?:(?![#:])<PLAIN>|:<PLAIN>))*/.source.replace(/<PLAIN>/g,(function(){return/[^\s\x00-\x08\x0e-\x1f,[\]{}\x7f-\x84\x86-\x9f\ud800-\udfff\ufffe\uffff]/.source})),i=/"(?:[^"\\\r\n]|\\.)*"|'(?:[^'\\\r\n]|\\.)*'/.source;function o(e,t){t=(t||"").replace(/m/g,"")+"m";var n=/([:\-,[{]\s*(?:\s<<prop>>[ \t]+)?)(?:<<value>>)(?=[ \t]*(?:$|,|\]|\}|(?:[\r\n]\s*)?#))/.source.replace(/<<prop>>/g,(function(){return r})).replace(/<<value>>/g,(function(){return e}));return RegExp(n,t)}e.languages.yaml={scalar:{pattern:RegExp(/([\-:]\s*(?:\s<<prop>>[ \t]+)?[|>])[ \t]*(?:((?:\r?\n|\r)[ \t]+)\S[^\r\n]*(?:\2[^\r\n]+)*)/.source.replace(/<<prop>>/g,(function(){return r}))),lookbehind:!0,alias:"string"},comment:/#.*/,key:{pattern:RegExp(/((?:^|[:\-,[{\r\n?])[ \t]*(?:<<prop>>[ \t]+)?)<<key>>(?=\s*:\s)/.source.replace(/<<prop>>/g,(function(){return r})).replace(/<<key>>/g,(function(){return"(?:"+a+"|"+i+")"}))),lookbehind:!0,greedy:!0,alias:"atrule"},directive:{pattern:/(^[ \t]*)%.+/m,lookbehind:!0,alias:"important"},datetime:{pattern:o(/\d{4}-\d\d?-\d\d?(?:[tT]|[ \t]+)\d\d?:\d{2}:\d{2}(?:\.\d*)?(?:[ \t]*(?:Z|[-+]\d\d?(?::\d{2})?))?|\d{4}-\d{2}-\d{2}|\d\d?:\d{2}(?::\d{2}(?:\.\d*)?)?/.source),lookbehind:!0,alias:"number"},boolean:{pattern:o(/false|true/.source,"i"),lookbehind:!0,alias:"important"},null:{pattern:o(/null|~/.source,"i"),lookbehind:!0,alias:"important"},string:{pattern:o(i),lookbehind:!0,greedy:!0},number:{pattern:o(/[+-]?(?:0x[\da-f]+|0o[0-7]+|(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?|\.inf|\.nan)/.source,"i"),lookbehind:!0},tag:n,important:t,punctuation:/---|[:[\]{}\-,|>?]|\.\.\./},e.languages.yml=e.languages.yaml}(Prism),function(e){var t=/(?:\\.|[^\\\n\r]|(?:\n|\r\n?)(?![\r\n]))/.source;function n(e){return e=e.replace(/<inner>/g,(function(){return t})),RegExp(/((?:^|[^\\])(?:\\{2})*)/.source+"(?:"+e+")")}var r=/(?:\\.|``(?:[^`\r\n]|`(?!`))+``|`[^`\r\n]+`|[^\\|\r\n`])+/.source,a=/\|?__(?:\|__)+\|?(?:(?:\n|\r\n?)|(?![\s\S]))/.source.replace(/__/g,(function(){return r})),i=/\|?[ \t]*:?-{3,}:?[ \t]*(?:\|[ \t]*:?-{3,}:?[ \t]*)+\|?(?:\n|\r\n?)/.source;e.languages.markdown=e.languages.extend("markup",{}),e.languages.insertBefore("markdown","prolog",{"front-matter-block":{pattern:/(^(?:\s*[\r\n])?)---(?!.)[\s\S]*?[\r\n]---(?!.)/,lookbehind:!0,greedy:!0,inside:{punctuation:/^---|---$/,"front-matter":{pattern:/\S+(?:\s+\S+)*/,alias:["yaml","language-yaml"],inside:e.languages.yaml}}},blockquote:{pattern:/^>(?:[\t ]*>)*/m,alias:"punctuation"},table:{pattern:RegExp("^"+a+i+"(?:"+a+")*","m"),inside:{"table-data-rows":{pattern:RegExp("^("+a+i+")(?:"+a+")*$"),lookbehind:!0,inside:{"table-data":{pattern:RegExp(r),inside:e.languages.markdown},punctuation:/\|/}},"table-line":{pattern:RegExp("^("+a+")"+i+"$"),lookbehind:!0,inside:{punctuation:/\||:?-{3,}:?/}},"table-header-row":{pattern:RegExp("^"+a+"$"),inside:{"table-header":{pattern:RegExp(r),alias:"important",inside:e.languages.markdown},punctuation:/\|/}}}},code:[{pattern:/((?:^|\n)[ \t]*\n|(?:^|\r\n?)[ \t]*\r\n?)(?: {4}|\t).+(?:(?:\n|\r\n?)(?: {4}|\t).+)*/,lookbehind:!0,alias:"keyword"},{pattern:/^```[\s\S]*?^```$/m,greedy:!0,inside:{"code-block":{pattern:/^(```.*(?:\n|\r\n?))[\s\S]+?(?=(?:\n|\r\n?)^```$)/m,lookbehind:!0},"code-language":{pattern:/^(```).+/,lookbehind:!0},punctuation:/```/}}],title:[{pattern:/\S.*(?:\n|\r\n?)(?:==+|--+)(?=[ \t]*$)/m,alias:"important",inside:{punctuation:/==+$|--+$/}},{pattern:/(^\s*)#.+/m,lookbehind:!0,alias:"important",inside:{punctuation:/^#+|#+$/}}],hr:{pattern:/(^\s*)([*-])(?:[\t ]*\2){2,}(?=\s*$)/m,lookbehind:!0,alias:"punctuation"},list:{pattern:/(^\s*)(?:[*+-]|\d+\.)(?=[\t ].)/m,lookbehind:!0,alias:"punctuation"},"url-reference":{pattern:/!?\[[^\]]+\]:[\t ]+(?:\S+|<(?:\\.|[^>\\])+>)(?:[\t ]+(?:"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\((?:\\.|[^)\\])*\)))?/,inside:{variable:{pattern:/^(!?\[)[^\]]+/,lookbehind:!0},string:/(?:"(?:\\.|[^"\\])*"|'(?:\\.|[^'\\])*'|\((?:\\.|[^)\\])*\))$/,punctuation:/^[\[\]!:]|[<>]/},alias:"url"},bold:{pattern:n(/\b__(?:(?!_)<inner>|_(?:(?!_)<inner>)+_)+__\b|\*\*(?:(?!\*)<inner>|\*(?:(?!\*)<inner>)+\*)+\*\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^..)[\s\S]+(?=..$)/,lookbehind:!0,inside:{}},punctuation:/\*\*|__/}},italic:{pattern:n(/\b_(?:(?!_)<inner>|__(?:(?!_)<inner>)+__)+_\b|\*(?:(?!\*)<inner>|\*\*(?:(?!\*)<inner>)+\*\*)+\*/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^.)[\s\S]+(?=.$)/,lookbehind:!0,inside:{}},punctuation:/[*_]/}},strike:{pattern:n(/(~~?)(?:(?!~)<inner>)+\2/.source),lookbehind:!0,greedy:!0,inside:{content:{pattern:/(^~~?)[\s\S]+(?=\1$)/,lookbehind:!0,inside:{}},punctuation:/~~?/}},"code-snippet":{pattern:/(^|[^\\`])(?:``[^`\r\n]+(?:`[^`\r\n]+)*``(?!`)|`[^`\r\n]+`(?!`))/,lookbehind:!0,greedy:!0,alias:["code","keyword"]},url:{pattern:n(/!?\[(?:(?!\])<inner>)+\](?:\([^\s)]+(?:[\t ]+"(?:\\.|[^"\\])*")?\)|[ \t]?\[(?:(?!\])<inner>)+\])/.source),lookbehind:!0,greedy:!0,inside:{operator:/^!/,content:{pattern:/(^\[)[^\]]+(?=\])/,lookbehind:!0,inside:{}},variable:{pattern:/(^\][ \t]?\[)[^\]]+(?=\]$)/,lookbehind:!0},url:{pattern:/(^\]\()[^\s)]+/,lookbehind:!0},string:{pattern:/(^[ \t]+)"(?:\\.|[^"\\])*"(?=\)$)/,lookbehind:!0}}}}),["url","bold","italic","strike"].forEach((function(t){["url","bold","italic","strike","code-snippet"].forEach((function(n){t!==n&&(e.languages.markdown[t].inside.content.inside[n]=e.languages.markdown[n])}))})),e.hooks.add("after-tokenize",(function(e){"markdown"!==e.language&&"md"!==e.language||function e(t){if(t&&"string"!=typeof t)for(var n=0,r=t.length;n<r;n++){var a=t[n];if("code"===a.type){var i=a.content[1],o=a.content[3];if(i&&o&&"code-language"===i.type&&"code-block"===o.type&&"string"==typeof i.content){var s=i.content.replace(/\b#/g,"sharp").replace(/\b\+\+/g,"pp"),c="language-"+(s=(/[a-z][\w-]*/i.exec(s)||[""])[0].toLowerCase());o.alias?"string"==typeof o.alias?o.alias=[o.alias,c]:o.alias.push(c):o.alias=[c]}}else e(a.content)}}(e.tokens)})),e.hooks.add("wrap",(function(t){if("code-block"===t.type){for(var n="",r=0,a=t.classes.length;r<a;r++){var i=t.classes[r],l=/language-(.+)/.exec(i);if(l){n=l[1];break}}var u=e.languages[n];if(u)t.content=e.highlight(function(e){var t=e.replace(o,"");return t=t.replace(/&(\w{1,8}|#x?[\da-f]{1,8});/gi,(function(e,t){var n;if("#"===(t=t.toLowerCase())[0])return n="x"===t[1]?parseInt(t.slice(2),16):Number(t.slice(1)),c(n);var r=s[t];return r||e}))}(t.content),u,n);else if(n&&"none"!==n&&e.plugins.autoloader){var f="md-"+(new Date).valueOf()+"-"+Math.floor(1e16*Math.random());t.attributes.id=f,e.plugins.autoloader.loadLanguages(n,(function(){var t=document.getElementById(f);t&&(t.innerHTML=e.highlight(t.textContent,e.languages[n],n))}))}}}));var o=RegExp(e.languages.markup.tag.pattern.source,"gi"),s={amp:"&",lt:"<",gt:">",quot:'"'},c=String.fromCodePoint||String.fromCharCode;e.languages.md=e.languages.markdown}(Prism),Prism.languages.graphql={comment:/#.*/,description:{pattern:/(?:"""(?:[^"]|(?!""")")*"""|"(?:\\.|[^\\"\r\n])*")(?=\s*[a-z_])/i,greedy:!0,alias:"string",inside:{"language-markdown":{pattern:/(^"(?:"")?)(?!\1)[\s\S]+(?=\1$)/,lookbehind:!0,inside:Prism.languages.markdown}}},string:{pattern:/"""(?:[^"]|(?!""")")*"""|"(?:\\.|[^\\"\r\n])*"/,greedy:!0},number:/(?:\B-|\b)\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,boolean:/\b(?:false|true)\b/,variable:/\$[a-z_]\w*/i,directive:{pattern:/@[a-z_]\w*/i,alias:"function"},"attr-name":{pattern:/\b[a-z_]\w*(?=\s*(?:\((?:[^()"]|"(?:\\.|[^\\"\r\n])*")*\))?:)/i,greedy:!0},"atom-input":{pattern:/\b[A-Z]\w*Input\b/,alias:"class-name"},scalar:/\b(?:Boolean|Float|ID|Int|String)\b/,constant:/\b[A-Z][A-Z_\d]*\b/,"class-name":{pattern:/(\b(?:enum|implements|interface|on|scalar|type|union)\s+|&\s*|:\s*|\[)[A-Z_]\w*/,lookbehind:!0},fragment:{pattern:/(\bfragment\s+|\.{3}\s*(?!on\b))[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},"definition-mutation":{pattern:/(\bmutation\s+)[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},"definition-query":{pattern:/(\bquery\s+)[a-zA-Z_]\w*/,lookbehind:!0,alias:"function"},keyword:/\b(?:directive|enum|extend|fragment|implements|input|interface|mutation|on|query|repeatable|scalar|schema|subscription|type|union)\b/,operator:/[!=|&]|\.{3}/,"property-query":/\w+(?=\s*\()/,object:/\w+(?=\s*\{)/,punctuation:/[!(){}\[\]:=,]/,property:/\w+/},Prism.hooks.add("after-tokenize",(function(e){if("graphql"===e.language)for(var t=e.tokens.filter((function(e){return"string"!=typeof e&&"comment"!==e.type&&"scalar"!==e.type})),n=0;n<t.length;){var r=t[n++];if("keyword"===r.type&&"mutation"===r.content){var a=[];if(f(["definition-mutation","punctuation"])&&"("===u(1).content){n+=2;var i=d(/^\($/,/^\)$/);if(-1===i)continue;for(;n<i;n++){var o=u(0);"variable"===o.type&&(p(o,"variable-input"),a.push(o.content))}n=i+1}if(f(["punctuation","property-query"])&&"{"===u(0).content&&(n++,p(u(0),"property-mutation"),a.length>0)){var s=d(/^\{$/,/^\}$/);if(-1===s)continue;for(var c=n;c<s;c++){var l=t[c];"variable"===l.type&&a.indexOf(l.content)>=0&&p(l,"variable-input")}}}}function u(e){return t[n+e]}function f(e,t){t=t||0;for(var n=0;n<e.length;n++){var r=u(n+t);if(!r||r.type!==e[n])return!1}return!0}function d(e,r){for(var a=1,i=n;i<t.length;i++){var o=t[i],s=o.content;if("punctuation"===o.type&&"string"==typeof s)if(e.test(s))a++;else if(r.test(s)&&0===--a)return i}return-1}function p(e,t){var n=e.alias;n?Array.isArray(n)||(e.alias=n=[n]):e.alias=n=[],n.push(t)}})),function(e){e.languages.ruby=e.languages.extend("clike",{comment:{pattern:/#.*|^=begin\s[\s\S]*?^=end/m,greedy:!0},"class-name":{pattern:/(\b(?:class|module)\s+|\bcatch\s+\()[\w.\\]+|\b[A-Z_]\w*(?=\s*\.\s*new\b)/,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:BEGIN|END|alias|and|begin|break|case|class|def|define_method|defined|do|each|else|elsif|end|ensure|extend|for|if|in|include|module|new|next|nil|not|or|prepend|private|protected|public|raise|redo|require|rescue|retry|return|self|super|then|throw|undef|unless|until|when|while|yield)\b/,operator:/\.{2,3}|&\.|===|<?=>|[!=]?~|(?:&&|\|\||<<|>>|\*\*|[+\-*/%<>!^&|=])=?|[?:]/,punctuation:/[(){}[\].,;]/}),e.languages.insertBefore("ruby","operator",{"double-colon":{pattern:/::/,alias:"punctuation"}});var t={pattern:/((?:^|[^\\])(?:\\{2})*)#\{(?:[^{}]|\{[^{}]*\})*\}/,lookbehind:!0,inside:{content:{pattern:/^(#\{)[\s\S]+(?=\}$)/,lookbehind:!0,inside:e.languages.ruby},delimiter:{pattern:/^#\{|\}$/,alias:"punctuation"}}};delete e.languages.ruby.function;var n="(?:"+[/([^a-zA-Z0-9\s{(\[<=])(?:(?!\1)[^\\]|\\[\s\S])*\1/.source,/\((?:[^()\\]|\\[\s\S]|\((?:[^()\\]|\\[\s\S])*\))*\)/.source,/\{(?:[^{}\\]|\\[\s\S]|\{(?:[^{}\\]|\\[\s\S])*\})*\}/.source,/\[(?:[^\[\]\\]|\\[\s\S]|\[(?:[^\[\]\\]|\\[\s\S])*\])*\]/.source,/<(?:[^<>\\]|\\[\s\S]|<(?:[^<>\\]|\\[\s\S])*>)*>/.source].join("|")+")",r=/(?:"(?:\\.|[^"\\\r\n])*"|(?:\b[a-zA-Z_]\w*|[^\s\0-\x7F]+)[?!]?|\$.)/.source;e.languages.insertBefore("ruby","keyword",{"regex-literal":[{pattern:RegExp(/%r/.source+n+/[egimnosux]{0,6}/.source),greedy:!0,inside:{interpolation:t,regex:/[\s\S]+/}},{pattern:/(^|[^/])\/(?!\/)(?:\[[^\r\n\]]+\]|\\.|[^[/\\\r\n])+\/[egimnosux]{0,6}(?=\s*(?:$|[\r\n,.;})#]))/,lookbehind:!0,greedy:!0,inside:{interpolation:t,regex:/[\s\S]+/}}],variable:/[@$]+[a-zA-Z_]\w*(?:[?!]|\b)/,symbol:[{pattern:RegExp(/(^|[^:]):/.source+r),lookbehind:!0,greedy:!0},{pattern:RegExp(/([\r\n{(,][ \t]*)/.source+r+/(?=:(?!:))/.source),lookbehind:!0,greedy:!0}],"method-definition":{pattern:/(\bdef\s+)\w+(?:\s*\.\s*\w+)?/,lookbehind:!0,inside:{function:/\b\w+$/,keyword:/^self\b/,"class-name":/^\w+/,punctuation:/\./}}}),e.languages.insertBefore("ruby","string",{"string-literal":[{pattern:RegExp(/%[qQiIwWs]?/.source+n),greedy:!0,inside:{interpolation:t,string:/[\s\S]+/}},{pattern:/("|')(?:#\{[^}]+\}|#(?!\{)|\\(?:\r\n|[\s\S])|(?!\1)[^\\#\r\n])*\1/,greedy:!0,inside:{interpolation:t,string:/[\s\S]+/}},{pattern:/<<[-~]?([a-z_]\w*)[\r\n](?:.*[\r\n])*?[\t ]*\1/i,alias:"heredoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<[-~]?[a-z_]\w*|\b[a-z_]\w*$/i,inside:{symbol:/\b\w+/,punctuation:/^<<[-~]?/}},interpolation:t,string:/[\s\S]+/}},{pattern:/<<[-~]?'([a-z_]\w*)'[\r\n](?:.*[\r\n])*?[\t ]*\1/i,alias:"heredoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<[-~]?'[a-z_]\w*'|\b[a-z_]\w*$/i,inside:{symbol:/\b\w+/,punctuation:/^<<[-~]?'|'$/}},string:/[\s\S]+/}}],"command-literal":[{pattern:RegExp(/%x/.source+n),greedy:!0,inside:{interpolation:t,command:{pattern:/[\s\S]+/,alias:"string"}}},{pattern:/`(?:#\{[^}]+\}|#(?!\{)|\\(?:\r\n|[\s\S])|[^\\`#\r\n])*`/,greedy:!0,inside:{interpolation:t,command:{pattern:/[\s\S]+/,alias:"string"}}}]}),delete e.languages.ruby.string,e.languages.insertBefore("ruby","number",{builtin:/\b(?:Array|Bignum|Binding|Class|Continuation|Dir|Exception|FalseClass|File|Fixnum|Float|Hash|IO|Integer|MatchData|Method|Module|NilClass|Numeric|Object|Proc|Range|Regexp|Stat|String|Struct|Symbol|TMS|Thread|ThreadGroup|Time|TrueClass)\b/,constant:/\b[A-Z][A-Z0-9_]*(?:[?!]|\b)/}),e.languages.rb=e.languages.ruby}(Prism),function(e){var t={pattern:/\\[\\(){}[\]^$+*?|.]/,alias:"escape"},n=/\\(?:x[\da-fA-F]{2}|u[\da-fA-F]{4}|u\{[\da-fA-F]+\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/,r="(?:[^\\\\-]|"+n.source+")",a=RegExp(r+"-"+r),i={pattern:/(<|')[^<>']+(?=[>']$)/,lookbehind:!0,alias:"variable"};e.languages.regex={"char-class":{pattern:/((?:^|[^\\])(?:\\\\)*)\[(?:[^\\\]]|\\[\s\S])*\]/,lookbehind:!0,inside:{"char-class-negation":{pattern:/(^\[)\^/,lookbehind:!0,alias:"operator"},"char-class-punctuation":{pattern:/^\[|\]$/,alias:"punctuation"},range:{pattern:a,inside:{escape:n,"range-punctuation":{pattern:/-/,alias:"operator"}}},"special-escape":t,"char-set":{pattern:/\\[wsd]|\\p\{[^{}]+\}/i,alias:"class-name"},escape:n}},"special-escape":t,"char-set":{pattern:/\.|\\[wsd]|\\p\{[^{}]+\}/i,alias:"class-name"},backreference:[{pattern:/\\(?![123][0-7]{2})[1-9]/,alias:"keyword"},{pattern:/\\k<[^<>']+>/,alias:"keyword",inside:{"group-name":i}}],anchor:{pattern:/[$^]|\\[ABbGZz]/,alias:"function"},escape:n,group:[{pattern:/\((?:\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,alias:"punctuation",inside:{"group-name":i}},{pattern:/\)/,alias:"punctuation"}],quantifier:{pattern:/(?:[+*?]|\{\d+(?:,\d*)?\})[?+]?/,alias:"number"},alternation:{pattern:/\|/,alias:"keyword"}}}(Prism),Prism.languages.javascript=Prism.languages.extend("clike",{"class-name":[Prism.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$A-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\.(?:constructor|prototype))/,lookbehind:!0}],keyword:[{pattern:/((?:^|\})\s*)catch\b/,lookbehind:!0},{pattern:/(^|[^.]|\.\.\.\s*)\b(?:as|assert(?=\s*\{)|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally(?=\s*(?:\{|$))|for|from(?=\s*(?:['"]|$))|function|(?:get|set)(?=\s*(?:[#\[$\w\xA0-\uFFFF]|$))|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],function:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,number:{pattern:RegExp(/(^|[^\w$])/.source+"(?:"+/NaN|Infinity/.source+"|"+/0[bB][01]+(?:_[01]+)*n?/.source+"|"+/0[oO][0-7]+(?:_[0-7]+)*n?/.source+"|"+/0[xX][\dA-Fa-f]+(?:_[\dA-Fa-f]+)*n?/.source+"|"+/\d+(?:_\d+)*n/.source+"|"+/(?:\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\.\d+(?:_\d+)*)(?:[Ee][+-]?\d+(?:_\d+)*)?/.source+")"+/(?![\w$])/.source),lookbehind:!0},operator:/--|\+\+|\*\*=?|=>|&&=?|\|\|=?|[!=]==|<<=?|>>>?=?|[-+*/%&|^!=<>]=?|\.{3}|\?\?=?|\?\.?|[~:]/}),Prism.languages.javascript["class-name"][0].pattern=/(\b(?:class|extends|implements|instanceof|interface|new)\s+)[\w.\\]+/,Prism.languages.insertBefore("javascript","keyword",{regex:{pattern:RegExp(/((?:^|[^$\w\xA0-\uFFFF."'\])\s]|\b(?:return|yield))\s*)/.source+/\//.source+"(?:"+/(?:\[(?:[^\]\\\r\n]|\\.)*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}/.source+"|"+/(?:\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.|\[(?:[^[\]\\\r\n]|\\.)*\])*\])*\]|\\.|[^/\\\[\r\n])+\/[dgimyus]{0,7}v[dgimyus]{0,7}/.source+")"+/(?=(?:\s|\/\*(?:[^*]|\*(?!\/))*\*\/)*(?:$|[\r\n,.;:})\]]|\/\/))/.source),lookbehind:!0,greedy:!0,inside:{"regex-source":{pattern:/^(\/)[\s\S]+(?=\/[a-z]*$)/,lookbehind:!0,alias:"language-regex",inside:Prism.languages.regex},"regex-delimiter":/^\/|\/$/,"regex-flags":/^[a-z]+$/}},"function-variable":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)?\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\))/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(^|[^$\w\xA0-\uFFFF])(?!\s)[_$a-z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*=>)/i,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*=>)/,lookbehind:!0,inside:Prism.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*)\(\s*|\]\s*\(\s*)(?!\s)(?:[^()\s]|\s+(?![\s)])|\([^()]*\))+(?=\s*\)\s*\{)/,lookbehind:!0,inside:Prism.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/}),Prism.languages.insertBefore("javascript","string",{hashbang:{pattern:/^#!.*/,greedy:!0,alias:"comment"},"template-string":{pattern:/`(?:\\[\s\S]|\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}|(?!\$\{)[^\\`])*`/,greedy:!0,inside:{"template-punctuation":{pattern:/^`|`$/,alias:"string"},interpolation:{pattern:/((?:^|[^\\])(?:\\{2})*)\$\{(?:[^{}]|\{(?:[^{}]|\{[^}]*\})*\})+\}/,lookbehind:!0,inside:{"interpolation-punctuation":{pattern:/^\$\{|\}$/,alias:"punctuation"},rest:Prism.languages.javascript}},string:/[\s\S]+/}},"string-property":{pattern:/((?:^|[,{])[ \t]*)(["'])(?:\\(?:\r\n|[\s\S])|(?!\2)[^\\\r\n])*\2(?=\s*:)/m,lookbehind:!0,greedy:!0,alias:"property"}}),Prism.languages.insertBefore("javascript","operator",{"literal-property":{pattern:/((?:^|[,{])[ \t]*)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/m,lookbehind:!0,alias:"property"}}),Prism.languages.markup&&(Prism.languages.markup.tag.addInlined("script","javascript"),Prism.languages.markup.tag.addAttribute(/on(?:abort|blur|change|click|composition(?:end|start|update)|dblclick|error|focus(?:in|out)?|key(?:down|up)|load|mouse(?:down|enter|leave|move|out|over|up)|reset|resize|scroll|select|slotchange|submit|unload|wheel)/.source,"javascript")),Prism.languages.js=Prism.languages.javascript,function(e){function t(e,t){return"___"+e.toUpperCase()+t+"___"}Object.defineProperties(e.languages["markup-templating"]={},{buildPlaceholders:{value:function(n,r,a,i){if(n.language===r){var o=n.tokenStack=[];n.code=n.code.replace(a,(function(e){if("function"==typeof i&&!i(e))return e;for(var a,s=o.length;-1!==n.code.indexOf(a=t(r,s));)++s;return o[s]=e,a})),n.grammar=e.languages.markup}}},tokenizePlaceholders:{value:function(n,r){if(n.language===r&&n.tokenStack){n.grammar=e.languages[r];var a=0,i=Object.keys(n.tokenStack);!function o(s){for(var c=0;c<s.length&&!(a>=i.length);c++){var l=s[c];if("string"==typeof l||l.content&&"string"==typeof l.content){var u=i[a],f=n.tokenStack[u],d="string"==typeof l?l:l.content,p=t(r,u),h=d.indexOf(p);if(h>-1){++a;var g=d.substring(0,h),m=new e.Token(r,e.tokenize(f,n.grammar),"language-"+r,f),b=d.substring(h+p.length),v=[];g&&v.push.apply(v,o([g])),v.push(m),b&&v.push.apply(v,o([b])),"string"==typeof l?s.splice.apply(s,[c,1].concat(v)):l.content=v}}else l.content&&o(l.content)}return s}(n.tokens)}}}})}(Prism),Prism.languages.less=Prism.languages.extend("css",{comment:[/\/\*[\s\S]*?\*\//,{pattern:/(^|[^\\])\/\/.*/,lookbehind:!0}],atrule:{pattern:/@[\w-](?:\((?:[^(){}]|\([^(){}]*\))*\)|[^(){};\s]|\s+(?!\s))*?(?=\s*\{)/,inside:{punctuation:/[:()]/}},selector:{pattern:/(?:@\{[\w-]+\}|[^{};\s@])(?:@\{[\w-]+\}|\((?:[^(){}]|\([^(){}]*\))*\)|[^(){};@\s]|\s+(?!\s))*?(?=\s*\{)/,inside:{variable:/@+[\w-]+/}},property:/(?:@\{[\w-]+\}|[\w-])+(?:\+_?)?(?=\s*:)/,operator:/[+\-*\/]/}),Prism.languages.insertBefore("less","property",{variable:[{pattern:/@[\w-]+\s*:/,inside:{punctuation:/:/}},/@@?[\w-]+/],"mixin-usage":{pattern:/([{;]\s*)[.#](?!\d)[\w-].*?(?=[(;])/,lookbehind:!0,alias:"function"}}),Prism.languages.scss=Prism.languages.extend("css",{comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|\/\/.*)/,lookbehind:!0},atrule:{pattern:/@[\w-](?:\([^()]+\)|[^()\s]|\s+(?!\s))*?(?=\s+[{;])/,inside:{rule:/@[\w-]+/}},url:/(?:[-a-z]+-)?url(?=\()/i,selector:{pattern:/(?=\S)[^@;{}()]?(?:[^@;{}()\s]|\s+(?!\s)|#\{\$[-\w]+\})+(?=\s*\{(?:\}|\s|[^}][^:{}]*[:{][^}]))/,inside:{parent:{pattern:/&/,alias:"important"},placeholder:/%[-\w]+/,variable:/\$[-\w]+|#\{\$[-\w]+\}/}},property:{pattern:/(?:[-\w]|\$[-\w]|#\{\$[-\w]+\})+(?=\s*:)/,inside:{variable:/\$[-\w]+|#\{\$[-\w]+\}/}}}),Prism.languages.insertBefore("scss","atrule",{keyword:[/@(?:content|debug|each|else(?: if)?|extend|for|forward|function|if|import|include|mixin|return|use|warn|while)\b/i,{pattern:/( )(?:from|through)(?= )/,lookbehind:!0}]}),Prism.languages.insertBefore("scss","important",{variable:/\$[-\w]+|#\{\$[-\w]+\}/}),Prism.languages.insertBefore("scss","function",{"module-modifier":{pattern:/\b(?:as|hide|show|with)\b/i,alias:"keyword"},placeholder:{pattern:/%[-\w]+/,alias:"selector"},statement:{pattern:/\B!(?:default|optional)\b/i,alias:"keyword"},boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"},operator:{pattern:/(\s)(?:[-+*\/%]|[=!]=|<=?|>=?|and|not|or)(?=\s)/,lookbehind:!0}}),Prism.languages.scss.atrule.inside.rest=Prism.languages.scss,function(e){e.languages.haml={"multiline-comment":{pattern:/((?:^|\r?\n|\r)([\t ]*))(?:\/|-#).*(?:(?:\r?\n|\r)\2[\t ].+)*/,lookbehind:!0,alias:"comment"},"multiline-code":[{pattern:/((?:^|\r?\n|\r)([\t ]*)(?:[~-]|[&!]?=)).*,[\t ]*(?:(?:\r?\n|\r)\2[\t ].*,[\t ]*)*(?:(?:\r?\n|\r)\2[\t ].+)/,lookbehind:!0,inside:e.languages.ruby},{pattern:/((?:^|\r?\n|\r)([\t ]*)(?:[~-]|[&!]?=)).*\|[\t ]*(?:(?:\r?\n|\r)\2[\t ].*\|[\t ]*)*/,lookbehind:!0,inside:e.languages.ruby}],filter:{pattern:/((?:^|\r?\n|\r)([\t ]*)):[\w-]+(?:(?:\r?\n|\r)(?:\2[\t ].+|\s*?(?=\r?\n|\r)))+/,lookbehind:!0,inside:{"filter-name":{pattern:/^:[\w-]+/,alias:"symbol"}}},markup:{pattern:/((?:^|\r?\n|\r)[\t ]*)<.+/,lookbehind:!0,inside:e.languages.markup},doctype:{pattern:/((?:^|\r?\n|\r)[\t ]*)!!!(?: .+)?/,lookbehind:!0},tag:{pattern:/((?:^|\r?\n|\r)[\t ]*)[%.#][\w\-#.]*[\w\-](?:\([^)]+\)|\{(?:\{[^}]+\}|[^{}])+\}|\[[^\]]+\])*[\/<>]*/,lookbehind:!0,inside:{attributes:[{pattern:/(^|[^#])\{(?:\{[^}]+\}|[^{}])+\}/,lookbehind:!0,inside:e.languages.ruby},{pattern:/\([^)]+\)/,inside:{"attr-value":{pattern:/(=\s*)(?:"(?:\\.|[^\\"\r\n])*"|[^)\s]+)/,lookbehind:!0},"attr-name":/[\w:-]+(?=\s*!?=|\s*[,)])/,punctuation:/[=(),]/}},{pattern:/\[[^\]]+\]/,inside:e.languages.ruby}],punctuation:/[<>]/}},code:{pattern:/((?:^|\r?\n|\r)[\t ]*(?:[~-]|[&!]?=)).+/,lookbehind:!0,inside:e.languages.ruby},interpolation:{pattern:/#\{[^}]+\}/,inside:{delimiter:{pattern:/^#\{|\}$/,alias:"punctuation"},ruby:{pattern:/[\s\S]+/,inside:e.languages.ruby}}},punctuation:{pattern:/((?:^|\r?\n|\r)[\t ]*)[~=\-&!]+/,lookbehind:!0}};for(var t=["css",{filter:"coffee",language:"coffeescript"},"erb","javascript","less","markdown","ruby","scss","textile"],n={},r=0,a=t.length;r<a;r++){var i=t[r];i="string"==typeof i?{filter:i,language:i}:i,e.languages[i.language]&&(n["filter-"+i.filter]={pattern:RegExp("((?:^|\\r?\\n|\\r)([\\t ]*)):{{filter_name}}(?:(?:\\r?\\n|\\r)(?:\\2[\\t ].+|\\s*?(?=\\r?\\n|\\r)))+".replace("{{filter_name}}",(function(){return i.filter}))),lookbehind:!0,inside:{"filter-name":{pattern:/^:[\w-]+/,alias:"symbol"},text:{pattern:/[\s\S]+/,alias:[i.language,"language-"+i.language],inside:e.languages[i.language]}}})}e.languages.insertBefore("haml","filter",n)}(Prism),Prism.languages.ini={comment:{pattern:/(^[ \f\t\v]*)[#;][^\n\r]*/m,lookbehind:!0},section:{pattern:/(^[ \f\t\v]*)\[[^\n\r\]]*\]?/m,lookbehind:!0,inside:{"section-name":{pattern:/(^\[[ \f\t\v]*)[^ \f\t\v\]]+(?:[ \f\t\v]+[^ \f\t\v\]]+)*/,lookbehind:!0,alias:"selector"},punctuation:/\[|\]/}},key:{pattern:/(^[ \f\t\v]*)[^ \f\n\r\t\v=]+(?:[ \f\t\v]+[^ \f\n\r\t\v=]+)*(?=[ \f\t\v]*=)/m,lookbehind:!0,alias:"attr-name"},value:{pattern:/(=[ \f\t\v]*)[^ \f\n\r\t\v]+(?:[ \f\t\v]+[^ \f\n\r\t\v]+)*/,lookbehind:!0,alias:"attr-value",inside:{"inner-value":{pattern:/^("|').+(?=\1$)/,lookbehind:!0}}},punctuation:/=/},function(e){var t=/\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record(?!\s*[(){}[\]<>=%~.:,;?+\-*/&|^])|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\b/,n=/(?:[a-z]\w*\s*\.\s*)*(?:[A-Z]\w*\s*\.\s*)*/.source,r={pattern:RegExp(/(^|[^\w.])/.source+n+/[A-Z](?:[\d_A-Z]*[a-z]\w*)?\b/.source),lookbehind:!0,inside:{namespace:{pattern:/^[a-z]\w*(?:\s*\.\s*[a-z]\w*)*(?:\s*\.)?/,inside:{punctuation:/\./}},punctuation:/\./}};e.languages.java=e.languages.extend("clike",{string:{pattern:/(^|[^\\])"(?:\\.|[^"\\\r\n])*"/,lookbehind:!0,greedy:!0},"class-name":[r,{pattern:RegExp(/(^|[^\w.])/.source+n+/[A-Z]\w*(?=\s+\w+\s*[;,=()]|\s*(?:\[[\s,]*\]\s*)?::\s*new\b)/.source),lookbehind:!0,inside:r.inside},{pattern:RegExp(/(\b(?:class|enum|extends|implements|instanceof|interface|new|record|throws)\s+)/.source+n+/[A-Z]\w*\b/.source),lookbehind:!0,inside:r.inside}],keyword:t,function:[e.languages.clike.function,{pattern:/(::\s*)[a-z_]\w*/,lookbehind:!0}],number:/\b0b[01][01_]*L?\b|\b0x(?:\.[\da-f_p+-]+|[\da-f_]+(?:\.[\da-f_p+-]+)?)\b|(?:\b\d[\d_]*(?:\.[\d_]*)?|\B\.\d[\d_]*)(?:e[+-]?\d[\d_]*)?[dfl]?/i,operator:{pattern:/(^|[^.])(?:<<=?|>>>?=?|->|--|\+\+|&&|\|\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,lookbehind:!0}}),e.languages.insertBefore("java","string",{"triple-quoted-string":{pattern:/"""[ \t]*[\r\n](?:(?:"|"")?(?:\\.|[^"\\]))*"""/,greedy:!0,alias:"string"},char:{pattern:/'(?:\\.|[^'\\\r\n]){1,6}'/,greedy:!0}}),e.languages.insertBefore("java","class-name",{annotation:{pattern:/(^|[^.])@\w+(?:\s*\.\s*\w+)*/,lookbehind:!0,alias:"punctuation"},generics:{pattern:/<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&)|<(?:[\w\s,.?]|&(?!&))*>)*>)*>)*>/,inside:{"class-name":r,keyword:t,punctuation:/[<>(),.:]/,operator:/[?&|]/}},import:[{pattern:RegExp(/(\bimport\s+)/.source+n+/(?:[A-Z]\w*|\*)(?=\s*;)/.source),lookbehind:!0,inside:{namespace:r.inside.namespace,punctuation:/\./,operator:/\*/,"class-name":/\w+/}},{pattern:RegExp(/(\bimport\s+static\s+)/.source+n+/(?:\w+|\*)(?=\s*;)/.source),lookbehind:!0,alias:"static",inside:{namespace:r.inside.namespace,static:/\b\w+$/,punctuation:/\./,operator:/\*/,"class-name":/\w+/}}],namespace:{pattern:RegExp(/(\b(?:exports|import(?:\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\s+)(?!<keyword>)[a-z]\w*(?:\.[a-z]\w*)*\.?/.source.replace(/<keyword>/g,(function(){return t.source}))),lookbehind:!0,inside:{punctuation:/\./}}})}(Prism),Prism.languages.json={property:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?=\s*:)/,lookbehind:!0,greedy:!0},string:{pattern:/(^|[^\\])"(?:\\.|[^\\"\r\n])*"(?!\s*:)/,lookbehind:!0,greedy:!0},comment:{pattern:/\/\/.*|\/\*[\s\S]*?(?:\*\/|$)/,greedy:!0},number:/-?\b\d+(?:\.\d+)?(?:e[+-]?\d+)?\b/i,punctuation:/[{}[\],]/,operator:/:/,boolean:/\b(?:false|true)\b/,null:{pattern:/\bnull\b/,alias:"keyword"}},Prism.languages.webmanifest=Prism.languages.json,function(e){var t=/("|')(?:\\(?:\r\n?|\n|.)|(?!\1)[^\\\r\n])*\1/;e.languages.json5=e.languages.extend("json",{property:[{pattern:RegExp(t.source+"(?=\\s*:)"),greedy:!0},{pattern:/(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?=\s*:)/,alias:"unquoted"}],string:{pattern:t,greedy:!0},number:/[+-]?\b(?:NaN|Infinity|0x[a-fA-F\d]+)\b|[+-]?(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:[eE][+-]?\d+\b)?/})}(Prism),Prism.languages.lua={comment:/^#!.+|--(?:\[(=*)\[[\s\S]*?\]\1\]|.*)/m,string:{pattern:/(["'])(?:(?!\1)[^\\\r\n]|\\z(?:\r\n|\s)|\\(?:\r\n|[^z]))*\1|\[(=*)\[[\s\S]*?\]\2\]/,greedy:!0},number:/\b0x[a-f\d]+(?:\.[a-f\d]*)?(?:p[+-]?\d+)?\b|\b\d+(?:\.\B|(?:\.\d*)?(?:e[+-]?\d+)?\b)|\B\.\d+(?:e[+-]?\d+)?\b/i,keyword:/\b(?:and|break|do|else|elseif|end|false|for|function|goto|if|in|local|nil|not|or|repeat|return|then|true|until|while)\b/,function:/(?!\d)\w+(?=\s*(?:[({]))/,operator:[/[-+*%^&|#]|\/\/?|<[<=]?|>[>=]?|[=~]=?/,{pattern:/(^|[^.])\.\.(?!\.)/,lookbehind:!0}],punctuation:/[\[\](){},;]|\.+|:+/},Prism.languages.matlab={comment:[/%\{[\s\S]*?\}%/,/%.+/],string:{pattern:/\B'(?:''|[^'\r\n])*'/,greedy:!0},number:/(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:[eE][+-]?\d+)?(?:[ij])?|\b[ij]\b/,keyword:/\b(?:NaN|break|case|catch|continue|else|elseif|end|for|function|if|inf|otherwise|parfor|pause|pi|return|switch|try|while)\b/,function:/\b(?!\d)\w+(?=\s*\()/,operator:/\.?[*^\/\\']|[+\-:@]|[<>=~]=?|&&?|\|\|?/,punctuation:/\.{3}|[.,;\[\](){}!]/},function(e){var t=["$eq","$gt","$gte","$in","$lt","$lte","$ne","$nin","$and","$not","$nor","$or","$exists","$type","$expr","$jsonSchema","$mod","$regex","$text","$where","$geoIntersects","$geoWithin","$near","$nearSphere","$all","$elemMatch","$size","$bitsAllClear","$bitsAllSet","$bitsAnyClear","$bitsAnySet","$comment","$elemMatch","$meta","$slice","$currentDate","$inc","$min","$max","$mul","$rename","$set","$setOnInsert","$unset","$addToSet","$pop","$pull","$push","$pullAll","$each","$position","$slice","$sort","$bit","$addFields","$bucket","$bucketAuto","$collStats","$count","$currentOp","$facet","$geoNear","$graphLookup","$group","$indexStats","$limit","$listLocalSessions","$listSessions","$lookup","$match","$merge","$out","$planCacheStats","$project","$redact","$replaceRoot","$replaceWith","$sample","$set","$skip","$sort","$sortByCount","$unionWith","$unset","$unwind","$setWindowFields","$abs","$accumulator","$acos","$acosh","$add","$addToSet","$allElementsTrue","$and","$anyElementTrue","$arrayElemAt","$arrayToObject","$asin","$asinh","$atan","$atan2","$atanh","$avg","$binarySize","$bsonSize","$ceil","$cmp","$concat","$concatArrays","$cond","$convert","$cos","$dateFromParts","$dateToParts","$dateFromString","$dateToString","$dayOfMonth","$dayOfWeek","$dayOfYear","$degreesToRadians","$divide","$eq","$exp","$filter","$first","$floor","$function","$gt","$gte","$hour","$ifNull","$in","$indexOfArray","$indexOfBytes","$indexOfCP","$isArray","$isNumber","$isoDayOfWeek","$isoWeek","$isoWeekYear","$last","$last","$let","$literal","$ln","$log","$log10","$lt","$lte","$ltrim","$map","$max","$mergeObjects","$meta","$min","$millisecond","$minute","$mod","$month","$multiply","$ne","$not","$objectToArray","$or","$pow","$push","$radiansToDegrees","$range","$reduce","$regexFind","$regexFindAll","$regexMatch","$replaceOne","$replaceAll","$reverseArray","$round","$rtrim","$second","$setDifference","$setEquals","$setIntersection","$setIsSubset","$setUnion","$size","$sin","$slice","$split","$sqrt","$stdDevPop","$stdDevSamp","$strcasecmp","$strLenBytes","$strLenCP","$substr","$substrBytes","$substrCP","$subtract","$sum","$switch","$tan","$toBool","$toDate","$toDecimal","$toDouble","$toInt","$toLong","$toObjectId","$toString","$toLower","$toUpper","$trim","$trunc","$type","$week","$year","$zip","$count","$dateAdd","$dateDiff","$dateSubtract","$dateTrunc","$getField","$rand","$sampleRate","$setField","$unsetField","$comment","$explain","$hint","$max","$maxTimeMS","$min","$orderby","$query","$returnKey","$showDiskLoc","$natural"],n="(?:"+(t=t.map((function(e){return e.replace("$","\\$")}))).join("|")+")\\b";e.languages.mongodb=e.languages.extend("javascript",{}),e.languages.insertBefore("mongodb","string",{property:{pattern:/(?:(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1|(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*)(?=\s*:)/,greedy:!0,inside:{keyword:RegExp("^(['\"])?"+n+"(?:\\1)?$")}}}),e.languages.mongodb.string.inside={url:{pattern:/https?:\/\/[-\w@:%.+~#=]{1,256}\.[a-z0-9()]{1,6}\b[-\w()@:%+.~#?&/=]*/i,greedy:!0},entity:{pattern:/\b(?:(?:[01]?\d\d?|2[0-4]\d|25[0-5])\.){3}(?:[01]?\d\d?|2[0-4]\d|25[0-5])\b/,greedy:!0}},e.languages.insertBefore("mongodb","constant",{builtin:{pattern:RegExp("\\b(?:"+["ObjectId","Code","BinData","DBRef","Timestamp","NumberLong","NumberDecimal","MaxKey","MinKey","RegExp","ISODate","UUID"].join("|")+")\\b"),alias:"keyword"}})}(Prism),function(e){var t=/\$(?:\w[a-z\d]*(?:_[^\x00-\x1F\s"'\\()$]*)?|\{[^}\s"'\\]+\})/i;e.languages.nginx={comment:{pattern:/(^|[\s{};])#.*/,lookbehind:!0,greedy:!0},directive:{pattern:/(^|\s)\w(?:[^;{}"'\\\s]|\\.|"(?:[^"\\]|\\.)*"|'(?:[^'\\]|\\.)*'|\s+(?:#.*(?!.)|(?![#\s])))*?(?=\s*[;{])/,lookbehind:!0,greedy:!0,inside:{string:{pattern:/((?:^|[^\\])(?:\\\\)*)(?:"(?:[^"\\]|\\.)*"|'(?:[^'\\]|\\.)*')/,lookbehind:!0,greedy:!0,inside:{escape:{pattern:/\\["'\\nrt]/,alias:"entity"},variable:t}},comment:{pattern:/(\s)#.*/,lookbehind:!0,greedy:!0},keyword:{pattern:/^\S+/,greedy:!0},boolean:{pattern:/(\s)(?:off|on)(?!\S)/,lookbehind:!0},number:{pattern:/(\s)\d+[a-z]*(?!\S)/i,lookbehind:!0},variable:t}},punctuation:/[{};]/}}(Prism),Prism.languages.objectivec=Prism.languages.extend("c",{string:{pattern:/@?"(?:\\(?:\r\n|[\s\S])|[^"\\\r\n])*"/,greedy:!0},keyword:/\b(?:asm|auto|break|case|char|const|continue|default|do|double|else|enum|extern|float|for|goto|if|in|inline|int|long|register|return|self|short|signed|sizeof|static|struct|super|switch|typedef|typeof|union|unsigned|void|volatile|while)\b|(?:@interface|@end|@implementation|@protocol|@class|@public|@protected|@private|@property|@try|@catch|@finally|@throw|@synthesize|@dynamic|@selector)\b/,operator:/-[->]?|\+\+?|!=?|<<?=?|>>?=?|==?|&&?|\|\|?|[~^%?*\/@]/}),delete Prism.languages.objectivec["class-name"],Prism.languages.objc=Prism.languages.objectivec,Prism.languages.pascal={directive:{pattern:/\{\$[\s\S]*?\}/,greedy:!0,alias:["marco","property"]},comment:{pattern:/\(\*[\s\S]*?\*\)|\{[\s\S]*?\}|\/\/.*/,greedy:!0},string:{pattern:/(?:'(?:''|[^'\r\n])*'(?!')|#[&$%]?[a-f\d]+)+|\^[a-z]/i,greedy:!0},asm:{pattern:/(\basm\b)[\s\S]+?(?=\bend\s*[;[])/i,lookbehind:!0,greedy:!0,inside:null},keyword:[{pattern:/(^|[^&])\b(?:absolute|array|asm|begin|case|const|constructor|destructor|do|downto|else|end|file|for|function|goto|if|implementation|inherited|inline|interface|label|nil|object|of|operator|packed|procedure|program|record|reintroduce|repeat|self|set|string|then|to|type|unit|until|uses|var|while|with)\b/i,lookbehind:!0},{pattern:/(^|[^&])\b(?:dispose|exit|false|new|true)\b/i,lookbehind:!0},{pattern:/(^|[^&])\b(?:class|dispinterface|except|exports|finalization|finally|initialization|inline|library|on|out|packed|property|raise|resourcestring|threadvar|try)\b/i,lookbehind:!0},{pattern:/(^|[^&])\b(?:absolute|abstract|alias|assembler|bitpacked|break|cdecl|continue|cppdecl|cvar|default|deprecated|dynamic|enumerator|experimental|export|external|far|far16|forward|generic|helper|implements|index|interrupt|iochecks|local|message|name|near|nodefault|noreturn|nostackframe|oldfpccall|otherwise|overload|override|pascal|platform|private|protected|public|published|read|register|reintroduce|result|safecall|saveregisters|softfloat|specialize|static|stdcall|stored|strict|unaligned|unimplemented|varargs|virtual|write)\b/i,lookbehind:!0}],number:[/(?:[&%]\d+|\$[a-f\d]+)/i,/\b\d+(?:\.\d+)?(?:e[+-]?\d+)?/i],operator:[/\.\.|\*\*|:=|<[<=>]?|>[>=]?|[+\-*\/]=?|[@^=]/,{pattern:/(^|[^&])\b(?:and|as|div|exclude|in|include|is|mod|not|or|shl|shr|xor)\b/,lookbehind:!0}],punctuation:/\(\.|\.\)|[()\[\]:;,.]/},Prism.languages.pascal.asm.inside=Prism.languages.extend("pascal",{asm:void 0,keyword:void 0,operator:void 0}),Prism.languages.objectpascal=Prism.languages.pascal,function(e){var t=/\/\*[\s\S]*?\*\/|\/\/.*|#(?!\[).*/,n=[{pattern:/\b(?:false|true)\b/i,alias:"boolean"},{pattern:/(::\s*)\b[a-z_]\w*\b(?!\s*\()/i,greedy:!0,lookbehind:!0},{pattern:/(\b(?:case|const)\s+)\b[a-z_]\w*(?=\s*[;=])/i,greedy:!0,lookbehind:!0},/\b(?:null)\b/i,/\b[A-Z_][A-Z0-9_]*\b(?!\s*\()/],r=/\b0b[01]+(?:_[01]+)*\b|\b0o[0-7]+(?:_[0-7]+)*\b|\b0x[\da-f]+(?:_[\da-f]+)*\b|(?:\b\d+(?:_\d+)*\.?(?:\d+(?:_\d+)*)?|\B\.\d+)(?:e[+-]?\d+)?/i,a=/<?=>|\?\?=?|\.{3}|\??->|[!=]=?=?|::|\*\*=?|--|\+\+|&&|\|\||<<|>>|[?~]|[/^|%*&<>.+-]=?/,i=/[{}\[\](),:;]/;e.languages.php={delimiter:{pattern:/\?>$|^<\?(?:php(?=\s)|=)?/i,alias:"important"},comment:t,variable:/\$+(?:\w+\b|(?=\{))/,package:{pattern:/(namespace\s+|use\s+(?:function\s+)?)(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,lookbehind:!0,inside:{punctuation:/\\/}},"class-name-definition":{pattern:/(\b(?:class|enum|interface|trait)\s+)\b[a-z_]\w*(?!\\)\b/i,lookbehind:!0,alias:"class-name"},"function-definition":{pattern:/(\bfunction\s+)[a-z_]\w*(?=\s*\()/i,lookbehind:!0,alias:"function"},keyword:[{pattern:/(\(\s*)\b(?:array|bool|boolean|float|int|integer|object|string)\b(?=\s*\))/i,alias:"type-casting",greedy:!0,lookbehind:!0},{pattern:/([(,?]\s*)\b(?:array(?!\s*\()|bool|callable|(?:false|null)(?=\s*\|)|float|int|iterable|mixed|object|self|static|string)\b(?=\s*\$)/i,alias:"type-hint",greedy:!0,lookbehind:!0},{pattern:/(\)\s*:\s*(?:\?\s*)?)\b(?:array(?!\s*\()|bool|callable|(?:false|null)(?=\s*\|)|float|int|iterable|mixed|never|object|self|static|string|void)\b/i,alias:"return-type",greedy:!0,lookbehind:!0},{pattern:/\b(?:array(?!\s*\()|bool|float|int|iterable|mixed|object|string|void)\b/i,alias:"type-declaration",greedy:!0},{pattern:/(\|\s*)(?:false|null)\b|\b(?:false|null)(?=\s*\|)/i,alias:"type-declaration",greedy:!0,lookbehind:!0},{pattern:/\b(?:parent|self|static)(?=\s*::)/i,alias:"static-context",greedy:!0},{pattern:/(\byield\s+)from\b/i,lookbehind:!0},/\bclass\b/i,{pattern:/((?:^|[^\s>:]|(?:^|[^-])>|(?:^|[^:]):)\s*)\b(?:abstract|and|array|as|break|callable|case|catch|clone|const|continue|declare|default|die|do|echo|else|elseif|empty|enddeclare|endfor|endforeach|endif|endswitch|endwhile|enum|eval|exit|extends|final|finally|fn|for|foreach|function|global|goto|if|implements|include|include_once|instanceof|insteadof|interface|isset|list|match|namespace|never|new|or|parent|print|private|protected|public|readonly|require|require_once|return|self|static|switch|throw|trait|try|unset|use|var|while|xor|yield|__halt_compiler)\b/i,lookbehind:!0}],"argument-name":{pattern:/([(,]\s*)\b[a-z_]\w*(?=\s*:(?!:))/i,lookbehind:!0},"class-name":[{pattern:/(\b(?:extends|implements|instanceof|new(?!\s+self|\s+static))\s+|\bcatch\s*\()\b[a-z_]\w*(?!\\)\b/i,greedy:!0,lookbehind:!0},{pattern:/(\|\s*)\b[a-z_]\w*(?!\\)\b/i,greedy:!0,lookbehind:!0},{pattern:/\b[a-z_]\w*(?!\\)\b(?=\s*\|)/i,greedy:!0},{pattern:/(\|\s*)(?:\\?\b[a-z_]\w*)+\b/i,alias:"class-name-fully-qualified",greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/(?:\\?\b[a-z_]\w*)+\b(?=\s*\|)/i,alias:"class-name-fully-qualified",greedy:!0,inside:{punctuation:/\\/}},{pattern:/(\b(?:extends|implements|instanceof|new(?!\s+self\b|\s+static\b))\s+|\bcatch\s*\()(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,alias:"class-name-fully-qualified",greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/\b[a-z_]\w*(?=\s*\$)/i,alias:"type-declaration",greedy:!0},{pattern:/(?:\\?\b[a-z_]\w*)+(?=\s*\$)/i,alias:["class-name-fully-qualified","type-declaration"],greedy:!0,inside:{punctuation:/\\/}},{pattern:/\b[a-z_]\w*(?=\s*::)/i,alias:"static-context",greedy:!0},{pattern:/(?:\\?\b[a-z_]\w*)+(?=\s*::)/i,alias:["class-name-fully-qualified","static-context"],greedy:!0,inside:{punctuation:/\\/}},{pattern:/([(,?]\s*)[a-z_]\w*(?=\s*\$)/i,alias:"type-hint",greedy:!0,lookbehind:!0},{pattern:/([(,?]\s*)(?:\\?\b[a-z_]\w*)+(?=\s*\$)/i,alias:["class-name-fully-qualified","type-hint"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}},{pattern:/(\)\s*:\s*(?:\?\s*)?)\b[a-z_]\w*(?!\\)\b/i,alias:"return-type",greedy:!0,lookbehind:!0},{pattern:/(\)\s*:\s*(?:\?\s*)?)(?:\\?\b[a-z_]\w*)+\b(?!\\)/i,alias:["class-name-fully-qualified","return-type"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}}],constant:n,function:{pattern:/(^|[^\\\w])\\?[a-z_](?:[\w\\]*\w)?(?=\s*\()/i,lookbehind:!0,inside:{punctuation:/\\/}},property:{pattern:/(->\s*)\w+/,lookbehind:!0},number:r,operator:a,punctuation:i};var o={pattern:/\{\$(?:\{(?:\{[^{}]+\}|[^{}]+)\}|[^{}])+\}|(^|[^\\{])\$+(?:\w+(?:\[[^\r\n\[\]]+\]|->\w+)?)/,lookbehind:!0,inside:e.languages.php},s=[{pattern:/<<<'([^']+)'[\r\n](?:.*[\r\n])*?\1;/,alias:"nowdoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<<'[^']+'|[a-z_]\w*;$/i,alias:"symbol",inside:{punctuation:/^<<<'?|[';]$/}}}},{pattern:/<<<(?:"([^"]+)"[\r\n](?:.*[\r\n])*?\1;|([a-z_]\w*)[\r\n](?:.*[\r\n])*?\2;)/i,alias:"heredoc-string",greedy:!0,inside:{delimiter:{pattern:/^<<<(?:"[^"]+"|[a-z_]\w*)|[a-z_]\w*;$/i,alias:"symbol",inside:{punctuation:/^<<<"?|[";]$/}},interpolation:o}},{pattern:/`(?:\\[\s\S]|[^\\`])*`/,alias:"backtick-quoted-string",greedy:!0},{pattern:/'(?:\\[\s\S]|[^\\'])*'/,alias:"single-quoted-string",greedy:!0},{pattern:/"(?:\\[\s\S]|[^\\"])*"/,alias:"double-quoted-string",greedy:!0,inside:{interpolation:o}}];e.languages.insertBefore("php","variable",{string:s,attribute:{pattern:/#\[(?:[^"'\/#]|\/(?![*/])|\/\/.*$|#(?!\[).*$|\/\*(?:[^*]|\*(?!\/))*\*\/|"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*')+\](?=\s*[a-z$#])/im,greedy:!0,inside:{"attribute-content":{pattern:/^(#\[)[\s\S]+(?=\]$)/,lookbehind:!0,inside:{comment:t,string:s,"attribute-class-name":[{pattern:/([^:]|^)\b[a-z_]\w*(?!\\)\b/i,alias:"class-name",greedy:!0,lookbehind:!0},{pattern:/([^:]|^)(?:\\?\b[a-z_]\w*)+/i,alias:["class-name","class-name-fully-qualified"],greedy:!0,lookbehind:!0,inside:{punctuation:/\\/}}],constant:n,number:r,operator:a,punctuation:i}},delimiter:{pattern:/^#\[|\]$/,alias:"punctuation"}}}}),e.hooks.add("before-tokenize",(function(t){if(/<\?/.test(t.code)){e.languages["markup-templating"].buildPlaceholders(t,"php",/<\?(?:[^"'/#]|\/(?![*/])|("|')(?:\\[\s\S]|(?!\1)[^\\])*\1|(?:\/\/|#(?!\[))(?:[^?\n\r]|\?(?!>))*(?=$|\?>|[\r\n])|#\[|\/\*(?:[^*]|\*(?!\/))*(?:\*\/|$))*?(?:\?>|$)/g)}})),e.hooks.add("after-tokenize",(function(t){e.languages["markup-templating"].tokenizePlaceholders(t,"php")}))}(Prism),function(e){var t=/\b(?:bool|bytes|double|s?fixed(?:32|64)|float|[su]?int(?:32|64)|string)\b/;e.languages.protobuf=e.languages.extend("clike",{"class-name":[{pattern:/(\b(?:enum|extend|message|service)\s+)[A-Za-z_]\w*(?=\s*\{)/,lookbehind:!0},{pattern:/(\b(?:rpc\s+\w+|returns)\s*\(\s*(?:stream\s+)?)\.?[A-Za-z_]\w*(?:\.[A-Za-z_]\w*)*(?=\s*\))/,lookbehind:!0}],keyword:/\b(?:enum|extend|extensions|import|message|oneof|option|optional|package|public|repeated|required|reserved|returns|rpc(?=\s+\w)|service|stream|syntax|to)\b(?!\s*=\s*\d)/,function:/\b[a-z_]\w*(?=\s*\()/i}),e.languages.insertBefore("protobuf","operator",{map:{pattern:/\bmap<\s*[\w.]+\s*,\s*[\w.]+\s*>(?=\s+[a-z_]\w*\s*[=;])/i,alias:"class-name",inside:{punctuation:/[<>.,]/,builtin:t}},builtin:t,"positional-class-name":{pattern:/(?:\b|\B\.)[a-z_]\w*(?:\.[a-z_]\w*)*(?=\s+[a-z_]\w*\s*[=;])/i,alias:"class-name",inside:{punctuation:/\./}},annotation:{pattern:/(\[\s*)[a-z_]\w*(?=\s*=)/i,lookbehind:!0}})}(Prism),Prism.languages.python={comment:{pattern:/(^|[^\\])#.*/,lookbehind:!0,greedy:!0},"string-interpolation":{pattern:/(?:f|fr|rf)(?:("""|''')[\s\S]*?\1|("|')(?:\\.|(?!\2)[^\\\r\n])*\2)/i,greedy:!0,inside:{interpolation:{pattern:/((?:^|[^{])(?:\{\{)*)\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}]|\{(?!\{)(?:[^{}])+\})+\})+\}/,lookbehind:!0,inside:{"format-spec":{pattern:/(:)[^:(){}]+(?=\}$)/,lookbehind:!0},"conversion-option":{pattern:/![sra](?=[:}]$)/,alias:"punctuation"},rest:null}},string:/[\s\S]+/}},"triple-quoted-string":{pattern:/(?:[rub]|br|rb)?("""|''')[\s\S]*?\1/i,greedy:!0,alias:"string"},string:{pattern:/(?:[rub]|br|rb)?("|')(?:\\.|(?!\1)[^\\\r\n])*\1/i,greedy:!0},function:{pattern:/((?:^|\s)def[ \t]+)[a-zA-Z_]\w*(?=\s*\()/g,lookbehind:!0},"class-name":{pattern:/(\bclass\s+)\w+/i,lookbehind:!0},decorator:{pattern:/(^[\t ]*)@\w+(?:\.\w+)*/m,lookbehind:!0,alias:["annotation","punctuation"],inside:{punctuation:/\./}},keyword:/\b(?:_(?=\s*:)|and|as|assert|async|await|break|case|class|continue|def|del|elif|else|except|exec|finally|for|from|global|if|import|in|is|lambda|match|nonlocal|not|or|pass|print|raise|return|try|while|with|yield)\b/,builtin:/\b(?:__import__|abs|all|any|apply|ascii|basestring|bin|bool|buffer|bytearray|bytes|callable|chr|classmethod|cmp|coerce|compile|complex|delattr|dict|dir|divmod|enumerate|eval|execfile|file|filter|float|format|frozenset|getattr|globals|hasattr|hash|help|hex|id|input|int|intern|isinstance|issubclass|iter|len|list|locals|long|map|max|memoryview|min|next|object|oct|open|ord|pow|property|range|raw_input|reduce|reload|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|unichr|unicode|vars|xrange|zip)\b/,boolean:/\b(?:False|None|True)\b/,number:/\b0(?:b(?:_?[01])+|o(?:_?[0-7])+|x(?:_?[a-f0-9])+)\b|(?:\b\d+(?:_\d+)*(?:\.(?:\d+(?:_\d+)*)?)?|\B\.\d+(?:_\d+)*)(?:e[+-]?\d+(?:_\d+)*)?j?(?!\w)/i,operator:/[-+%=]=?|!=|:=|\*\*?=?|\/\/?=?|<[<=>]?|>[=>]?|[&|^~]/,punctuation:/[{}[\];(),.:]/},Prism.languages.python["string-interpolation"].inside.interpolation.inside.rest=Prism.languages.python,Prism.languages.py=Prism.languages.python,Prism.languages.r={comment:/#.*/,string:{pattern:/(['"])(?:\\.|(?!\1)[^\\\r\n])*\1/,greedy:!0},"percent-operator":{pattern:/%[^%\s]*%/,alias:"operator"},boolean:/\b(?:FALSE|TRUE)\b/,ellipsis:/\.\.(?:\.|\d+)/,number:[/\b(?:Inf|NaN)\b/,/(?:\b0x[\dA-Fa-f]+(?:\.\d*)?|\b\d+(?:\.\d*)?|\B\.\d+)(?:[EePp][+-]?\d+)?[iL]?/],keyword:/\b(?:NA|NA_character_|NA_complex_|NA_integer_|NA_real_|NULL|break|else|for|function|if|in|next|repeat|while)\b/,operator:/->?>?|<(?:=|<?-)?|[>=!]=?|::?|&&?|\|\|?|[+*\/^$@~]/,punctuation:/[(){}\[\],;]/},function(e){for(var t=/\/\*(?:[^*/]|\*(?!\/)|\/(?!\*)|<self>)*\*\//.source,n=0;n<2;n++)t=t.replace(/<self>/g,(function(){return t}));t=t.replace(/<self>/g,(function(){return/[^\s\S]/.source})),e.languages.rust={comment:[{pattern:RegExp(/(^|[^\\])/.source+t),lookbehind:!0,greedy:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/b?"(?:\\[\s\S]|[^\\"])*"|b?r(#*)"(?:[^"]|"(?!\1))*"\1/,greedy:!0},char:{pattern:/b?'(?:\\(?:x[0-7][\da-fA-F]|u\{(?:[\da-fA-F]_*){1,6}\}|.)|[^\\\r\n\t'])'/,greedy:!0},attribute:{pattern:/#!?\[(?:[^\[\]"]|"(?:\\[\s\S]|[^\\"])*")*\]/,greedy:!0,alias:"attr-name",inside:{string:null}},"closure-params":{pattern:/([=(,:]\s*|\bmove\s*)\|[^|]*\||\|[^|]*\|(?=\s*(?:\{|->))/,lookbehind:!0,greedy:!0,inside:{"closure-punctuation":{pattern:/^\||\|$/,alias:"punctuation"},rest:null}},"lifetime-annotation":{pattern:/'\w+/,alias:"symbol"},"fragment-specifier":{pattern:/(\$\w+:)[a-z]+/,lookbehind:!0,alias:"punctuation"},variable:/\$\w+/,"function-definition":{pattern:/(\bfn\s+)\w+/,lookbehind:!0,alias:"function"},"type-definition":{pattern:/(\b(?:enum|struct|trait|type|union)\s+)\w+/,lookbehind:!0,alias:"class-name"},"module-declaration":[{pattern:/(\b(?:crate|mod)\s+)[a-z][a-z_\d]*/,lookbehind:!0,alias:"namespace"},{pattern:/(\b(?:crate|self|super)\s*)::\s*[a-z][a-z_\d]*\b(?:\s*::(?:\s*[a-z][a-z_\d]*\s*::)*)?/,lookbehind:!0,alias:"namespace",inside:{punctuation:/::/}}],keyword:[/\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\b/,/\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\b/],function:/\b[a-z_]\w*(?=\s*(?:::\s*<|\())/,macro:{pattern:/\b\w+!/,alias:"property"},constant:/\b[A-Z_][A-Z_\d]+\b/,"class-name":/\b[A-Z]\w*\b/,namespace:{pattern:/(?:\b[a-z][a-z_\d]*\s*::\s*)*\b[a-z][a-z_\d]*\s*::(?!\s*<)/,inside:{punctuation:/::/}},number:/\b(?:0x[\dA-Fa-f](?:_?[\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\d(?:_?\d)*)?\.)?\d(?:_?\d)*(?:[Ee][+-]?\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\b/,boolean:/\b(?:false|true)\b/,punctuation:/->|\.\.=|\.{1,3}|::|[{}[\];(),:]/,operator:/[-+*\/%!^]=?|=[=>]?|&[&=]?|\|[|=]?|<<?=?|>>?=?|[@?]/},e.languages.rust["closure-params"].inside.rest=e.languages.rust,e.languages.rust.attribute.inside.string=e.languages.rust.string}(Prism),Prism.languages.sql={comment:{pattern:/(^|[^\\])(?:\/\*[\s\S]*?\*\/|(?:--|\/\/|#).*)/,lookbehind:!0},variable:[{pattern:/@(["'`])(?:\\[\s\S]|(?!\1)[^\\])+\1/,greedy:!0},/@[\w.$]+/],string:{pattern:/(^|[^@\\])("|')(?:\\[\s\S]|(?!\2)[^\\]|\2\2)*\2/,greedy:!0,lookbehind:!0},identifier:{pattern:/(^|[^@\\])`(?:\\[\s\S]|[^`\\]|``)*`/,greedy:!0,lookbehind:!0,inside:{punctuation:/^`|`$/}},function:/\b(?:AVG|COUNT|FIRST|FORMAT|LAST|LCASE|LEN|MAX|MID|MIN|MOD|NOW|ROUND|SUM|UCASE)(?=\s*\()/i,keyword:/\b(?:ACTION|ADD|AFTER|ALGORITHM|ALL|ALTER|ANALYZE|ANY|APPLY|AS|ASC|AUTHORIZATION|AUTO_INCREMENT|BACKUP|BDB|BEGIN|BERKELEYDB|BIGINT|BINARY|BIT|BLOB|BOOL|BOOLEAN|BREAK|BROWSE|BTREE|BULK|BY|CALL|CASCADED?|CASE|CHAIN|CHAR(?:ACTER|SET)?|CHECK(?:POINT)?|CLOSE|CLUSTERED|COALESCE|COLLATE|COLUMNS?|COMMENT|COMMIT(?:TED)?|COMPUTE|CONNECT|CONSISTENT|CONSTRAINT|CONTAINS(?:TABLE)?|CONTINUE|CONVERT|CREATE|CROSS|CURRENT(?:_DATE|_TIME|_TIMESTAMP|_USER)?|CURSOR|CYCLE|DATA(?:BASES?)?|DATE(?:TIME)?|DAY|DBCC|DEALLOCATE|DEC|DECIMAL|DECLARE|DEFAULT|DEFINER|DELAYED|DELETE|DELIMITERS?|DENY|DESC|DESCRIBE|DETERMINISTIC|DISABLE|DISCARD|DISK|DISTINCT|DISTINCTROW|DISTRIBUTED|DO|DOUBLE|DROP|DUMMY|DUMP(?:FILE)?|DUPLICATE|ELSE(?:IF)?|ENABLE|ENCLOSED|END|ENGINE|ENUM|ERRLVL|ERRORS|ESCAPED?|EXCEPT|EXEC(?:UTE)?|EXISTS|EXIT|EXPLAIN|EXTENDED|FETCH|FIELDS|FILE|FILLFACTOR|FIRST|FIXED|FLOAT|FOLLOWING|FOR(?: EACH ROW)?|FORCE|FOREIGN|FREETEXT(?:TABLE)?|FROM|FULL|FUNCTION|GEOMETRY(?:COLLECTION)?|GLOBAL|GOTO|GRANT|GROUP|HANDLER|HASH|HAVING|HOLDLOCK|HOUR|IDENTITY(?:COL|_INSERT)?|IF|IGNORE|IMPORT|INDEX|INFILE|INNER|INNODB|INOUT|INSERT|INT|INTEGER|INTERSECT|INTERVAL|INTO|INVOKER|ISOLATION|ITERATE|JOIN|KEYS?|KILL|LANGUAGE|LAST|LEAVE|LEFT|LEVEL|LIMIT|LINENO|LINES|LINESTRING|LOAD|LOCAL|LOCK|LONG(?:BLOB|TEXT)|LOOP|MATCH(?:ED)?|MEDIUM(?:BLOB|INT|TEXT)|MERGE|MIDDLEINT|MINUTE|MODE|MODIFIES|MODIFY|MONTH|MULTI(?:LINESTRING|POINT|POLYGON)|NATIONAL|NATURAL|NCHAR|NEXT|NO|NONCLUSTERED|NULLIF|NUMERIC|OFF?|OFFSETS?|ON|OPEN(?:DATASOURCE|QUERY|ROWSET)?|OPTIMIZE|OPTION(?:ALLY)?|ORDER|OUT(?:ER|FILE)?|OVER|PARTIAL|PARTITION|PERCENT|PIVOT|PLAN|POINT|POLYGON|PRECEDING|PRECISION|PREPARE|PREV|PRIMARY|PRINT|PRIVILEGES|PROC(?:EDURE)?|PUBLIC|PURGE|QUICK|RAISERROR|READS?|REAL|RECONFIGURE|REFERENCES|RELEASE|RENAME|REPEAT(?:ABLE)?|REPLACE|REPLICATION|REQUIRE|RESIGNAL|RESTORE|RESTRICT|RETURN(?:ING|S)?|REVOKE|RIGHT|ROLLBACK|ROUTINE|ROW(?:COUNT|GUIDCOL|S)?|RTREE|RULE|SAVE(?:POINT)?|SCHEMA|SECOND|SELECT|SERIAL(?:IZABLE)?|SESSION(?:_USER)?|SET(?:USER)?|SHARE|SHOW|SHUTDOWN|SIMPLE|SMALLINT|SNAPSHOT|SOME|SONAME|SQL|START(?:ING)?|STATISTICS|STATUS|STRIPED|SYSTEM_USER|TABLES?|TABLESPACE|TEMP(?:ORARY|TABLE)?|TERMINATED|TEXT(?:SIZE)?|THEN|TIME(?:STAMP)?|TINY(?:BLOB|INT|TEXT)|TOP?|TRAN(?:SACTIONS?)?|TRIGGER|TRUNCATE|TSEQUAL|TYPES?|UNBOUNDED|UNCOMMITTED|UNDEFINED|UNION|UNIQUE|UNLOCK|UNPIVOT|UNSIGNED|UPDATE(?:TEXT)?|USAGE|USE|USER|USING|VALUES?|VAR(?:BINARY|CHAR|CHARACTER|YING)|VIEW|WAITFOR|WARNINGS|WHEN|WHERE|WHILE|WITH(?: ROLLUP|IN)?|WORK|WRITE(?:TEXT)?|YEAR)\b/i,boolean:/\b(?:FALSE|NULL|TRUE)\b/i,number:/\b0x[\da-f]+\b|\b\d+(?:\.\d*)?|\B\.\d+\b/i,operator:/[-+*\/=%^~]|&&?|\|\|?|!=?|<(?:=>?|<|>)?|>[>=]?|\b(?:AND|BETWEEN|DIV|ILIKE|IN|IS|LIKE|NOT|OR|REGEXP|RLIKE|SOUNDS LIKE|XOR)\b/i,punctuation:/[;[\]()`,.]/},function(e){e.languages.typescript=e.languages.extend("javascript",{"class-name":{pattern:/(\b(?:class|extends|implements|instanceof|interface|new|type)\s+)(?!keyof\b)(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*(?:\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,lookbehind:!0,greedy:!0,inside:null},builtin:/\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\b/}),e.languages.typescript.keyword.push(/\b(?:abstract|declare|is|keyof|readonly|require)\b/,/\b(?:asserts|infer|interface|module|namespace|type)\b(?=\s*(?:[{_$a-zA-Z\xA0-\uFFFF]|$))/,/\btype\b(?=\s*(?:[\{*]|$))/),delete e.languages.typescript.parameter,delete e.languages.typescript["literal-property"];var t=e.languages.extend("typescript",{});delete t["class-name"],e.languages.typescript["class-name"].inside=t,e.languages.insertBefore("typescript","function",{decorator:{pattern:/@[$\w\xA0-\uFFFF]+/,inside:{at:{pattern:/^@/,alias:"operator"},function:/^[\s\S]+/}},"generic-function":{pattern:/#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\s*\()/,greedy:!0,inside:{function:/^#?(?!\s)[_$a-zA-Z\xA0-\uFFFF](?:(?!\s)[$\w\xA0-\uFFFF])*/,generic:{pattern:/<[\s\S]+/,alias:"class-name",inside:t}}}}),e.languages.ts=e.languages.typescript}(Prism),function(e){var t=e.util.clone(e.languages.javascript),n=/(?:\s|\/\/.*(?!.)|\/\*(?:[^*]|\*(?!\/))\*\/)/.source,r=/(?:\{(?:\{(?:\{[^{}]*\}|[^{}])*\}|[^{}])*\})/.source,a=/(?:\{<S>*\.{3}(?:[^{}]|<BRACES>)*\})/.source;function i(e,t){return e=e.replace(/<S>/g,(function(){return n})).replace(/<BRACES>/g,(function(){return r})).replace(/<SPREAD>/g,(function(){return a})),RegExp(e,t)}a=i(a).source,e.languages.jsx=e.languages.extend("markup",t),e.languages.jsx.tag.pattern=i(/<\/?(?:[\w.:-]+(?:<S>+(?:[\w.:$-]+(?:=(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s{'"/>=]+|<BRACES>))?|<SPREAD>))*<S>*\/?)?>/.source),e.languages.jsx.tag.inside.tag.pattern=/^<\/?[^\s>\/]*/,e.languages.jsx.tag.inside["attr-value"].pattern=/=(?!\{)(?:"(?:\\[\s\S]|[^\\"])*"|'(?:\\[\s\S]|[^\\'])*'|[^\s'">]+)/,e.languages.jsx.tag.inside.tag.inside["class-name"]=/^[A-Z]\w*(?:\.[A-Z]\w*)*$/,e.languages.jsx.tag.inside.comment=t.comment,e.languages.insertBefore("inside","attr-name",{spread:{pattern:i(/<SPREAD>/.source),inside:e.languages.jsx}},e.languages.jsx.tag),e.languages.insertBefore("inside","special-attr",{script:{pattern:i(/=<BRACES>/.source),alias:"language-javascript",inside:{"script-punctuation":{pattern:/^=(?=\{)/,alias:"punctuation"},rest:e.languages.jsx}}},e.languages.jsx.tag);var o=function(e){return e?"string"==typeof e?e:"string"==typeof e.content?e.content:e.content.map(o).join(""):""},s=function(t){for(var n=[],r=0;r<t.length;r++){var a=t[r],i=!1;if("string"!=typeof a&&("tag"===a.type&&a.content[0]&&"tag"===a.content[0].type?"</"===a.content[0].content[0].content?n.length>0&&n[n.length-1].tagName===o(a.content[0].content[1])&&n.pop():"/>"===a.content[a.content.length-1].content||n.push({tagName:o(a.content[0].content[1]),openedBraces:0}):n.length>0&&"punctuation"===a.type&&"{"===a.content?n[n.length-1].openedBraces++:n.length>0&&n[n.length-1].openedBraces>0&&"punctuation"===a.type&&"}"===a.content?n[n.length-1].openedBraces--:i=!0),(i||"string"==typeof a)&&n.length>0&&0===n[n.length-1].openedBraces){var c=o(a);r<t.length-1&&("string"==typeof t[r+1]||"plain-text"===t[r+1].type)&&(c+=o(t[r+1]),t.splice(r+1,1)),r>0&&("string"==typeof t[r-1]||"plain-text"===t[r-1].type)&&(c=o(t[r-1])+c,t.splice(r-1,1),r--),t[r]=new e.Token("plain-text",c,null,c)}a.content&&"string"!=typeof a.content&&s(a.content)}};e.hooks.add("after-tokenize",(function(e){"jsx"!==e.language&&"tsx"!==e.language||s(e.tokens)}))}(Prism),function(e){var t=e.util.clone(e.languages.typescript);e.languages.tsx=e.languages.extend("jsx",t),delete e.languages.tsx.parameter,delete e.languages.tsx["literal-property"];var n=e.languages.tsx.tag;n.pattern=RegExp(/(^|[^\w$]|(?=<\/))/.source+"(?:"+n.pattern.source+")",n.pattern.flags),n.lookbehind=!0}(Prism),function(e){e.languages.sass=e.languages.extend("css",{comment:{pattern:/^([ \t]*)\/[\/*].*(?:(?:\r?\n|\r)\1[ \t].+)*/m,lookbehind:!0,greedy:!0}}),e.languages.insertBefore("sass","atrule",{"atrule-line":{pattern:/^(?:[ \t]*)[@+=].+/m,greedy:!0,inside:{atrule:/(?:@[\w-]+|[+=])/}}}),delete e.languages.sass.atrule;var t=/\$[-\w]+|#\{\$[-\w]+\}/,n=[/[+*\/%]|[=!]=|<=?|>=?|\b(?:and|not|or)\b/,{pattern:/(\s)-(?=\s)/,lookbehind:!0}];e.languages.insertBefore("sass","property",{"variable-line":{pattern:/^[ \t]*\$.+/m,greedy:!0,inside:{punctuation:/:/,variable:t,operator:n}},"property-line":{pattern:/^[ \t]*(?:[^:\s]+ *:.*|:[^:\s].*)/m,greedy:!0,inside:{property:[/[^:\s]+(?=\s*:)/,{pattern:/(:)[^:\s]+/,lookbehind:!0}],punctuation:/:/,variable:t,operator:n,important:e.languages.sass.important}}}),delete e.languages.sass.property,delete e.languages.sass.important,e.languages.insertBefore("sass","punctuation",{selector:{pattern:/^([ \t]*)\S(?:,[^,\r\n]+|[^,\r\n]*)(?:,[^,\r\n]+)*(?:,(?:\r?\n|\r)\1[ \t]+\S(?:,[^,\r\n]+|[^,\r\n]*)(?:,[^,\r\n]+)*)*/m,lookbehind:!0,greedy:!0}})}(Prism),function(e){var t="\\b(?:BASH|BASHOPTS|BASH_ALIASES|BASH_ARGC|BASH_ARGV|BASH_CMDS|BASH_COMPLETION_COMPAT_DIR|BASH_LINENO|BASH_REMATCH|BASH_SOURCE|BASH_VERSINFO|BASH_VERSION|COLORTERM|COLUMNS|COMP_WORDBREAKS|DBUS_SESSION_BUS_ADDRESS|DEFAULTS_PATH|DESKTOP_SESSION|DIRSTACK|DISPLAY|EUID|GDMSESSION|GDM_LANG|GNOME_KEYRING_CONTROL|GNOME_KEYRING_PID|GPG_AGENT_INFO|GROUPS|HISTCONTROL|HISTFILE|HISTFILESIZE|HISTSIZE|HOME|HOSTNAME|HOSTTYPE|IFS|INSTANCE|JOB|LANG|LANGUAGE|LC_ADDRESS|LC_ALL|LC_IDENTIFICATION|LC_MEASUREMENT|LC_MONETARY|LC_NAME|LC_NUMERIC|LC_PAPER|LC_TELEPHONE|LC_TIME|LESSCLOSE|LESSOPEN|LINES|LOGNAME|LS_COLORS|MACHTYPE|MAILCHECK|MANDATORY_PATH|NO_AT_BRIDGE|OLDPWD|OPTERR|OPTIND|ORBIT_SOCKETDIR|OSTYPE|PAPERSIZE|PATH|PIPESTATUS|PPID|PS1|PS2|PS3|PS4|PWD|RANDOM|REPLY|SECONDS|SELINUX_INIT|SESSION|SESSIONTYPE|SESSION_MANAGER|SHELL|SHELLOPTS|SHLVL|SSH_AUTH_SOCK|TERM|UID|UPSTART_EVENTS|UPSTART_INSTANCE|UPSTART_JOB|UPSTART_SESSION|USER|WINDOWID|XAUTHORITY|XDG_CONFIG_DIRS|XDG_CURRENT_DESKTOP|XDG_DATA_DIRS|XDG_GREETER_DATA_DIR|XDG_MENU_PREFIX|XDG_RUNTIME_DIR|XDG_SEAT|XDG_SEAT_PATH|XDG_SESSION_DESKTOP|XDG_SESSION_ID|XDG_SESSION_PATH|XDG_SESSION_TYPE|XDG_VTNR|XMODIFIERS)\\b",n={pattern:/(^(["']?)\w+\2)[ \t]+\S.*/,lookbehind:!0,alias:"punctuation",inside:null},r={bash:n,environment:{pattern:RegExp("\\$"+t),alias:"constant"},variable:[{pattern:/\$?\(\([\s\S]+?\)\)/,greedy:!0,inside:{variable:[{pattern:/(^\$\(\([\s\S]+)\)\)/,lookbehind:!0},/^\$\(\(/],number:/\b0x[\dA-Fa-f]+\b|(?:\b\d+(?:\.\d*)?|\B\.\d+)(?:[Ee]-?\d+)?/,operator:/--|\+\+|\*\*=?|<<=?|>>=?|&&|\|\||[=!+\-*/%<>^&|]=?|[?~:]/,punctuation:/\(\(?|\)\)?|,|;/}},{pattern:/\$\((?:\([^)]+\)|[^()])+\)|`[^`]+`/,greedy:!0,inside:{variable:/^\$\(|^`|\)$|`$/}},{pattern:/\$\{[^}]+\}/,greedy:!0,inside:{operator:/:[-=?+]?|[!\/]|##?|%%?|\^\^?|,,?/,punctuation:/[\[\]]/,environment:{pattern:RegExp("(\\{)"+t),lookbehind:!0,alias:"constant"}}},/\$(?:\w+|[#?*!@$])/],entity:/\\(?:[abceEfnrtv\\"]|O?[0-7]{1,3}|U[0-9a-fA-F]{8}|u[0-9a-fA-F]{4}|x[0-9a-fA-F]{1,2})/};e.languages.bash={shebang:{pattern:/^#!\s*\/.*/,alias:"important"},comment:{pattern:/(^|[^"{\\$])#.*/,lookbehind:!0},"function-name":[{pattern:/(\bfunction\s+)[\w-]+(?=(?:\s*\(?:\s*\))?\s*\{)/,lookbehind:!0,alias:"function"},{pattern:/\b[\w-]+(?=\s*\(\s*\)\s*\{)/,alias:"function"}],"for-or-select":{pattern:/(\b(?:for|select)\s+)\w+(?=\s+in\s)/,alias:"variable",lookbehind:!0},"assign-left":{pattern:/(^|[\s;|&]|[<>]\()\w+(?=\+?=)/,inside:{environment:{pattern:RegExp("(^|[\\s;|&]|[<>]\\()"+t),lookbehind:!0,alias:"constant"}},alias:"variable",lookbehind:!0},string:[{pattern:/((?:^|[^<])<<-?\s*)(\w+)\s[\s\S]*?(?:\r?\n|\r)\2/,lookbehind:!0,greedy:!0,inside:r},{pattern:/((?:^|[^<])<<-?\s*)(["'])(\w+)\2\s[\s\S]*?(?:\r?\n|\r)\3/,lookbehind:!0,greedy:!0,inside:{bash:n}},{pattern:/(^|[^\\](?:\\\\)*)"(?:\\[\s\S]|\$\([^)]+\)|\$(?!\()|`[^`]+`|[^"\\`$])*"/,lookbehind:!0,greedy:!0,inside:r},{pattern:/(^|[^$\\])'[^']*'/,lookbehind:!0,greedy:!0},{pattern:/\$'(?:[^'\\]|\\[\s\S])*'/,greedy:!0,inside:{entity:r.entity}}],environment:{pattern:RegExp("\\$?"+t),alias:"constant"},variable:r.variable,function:{pattern:/(^|[\s;|&]|[<>]\()(?:add|apropos|apt|apt-cache|apt-get|aptitude|aspell|automysqlbackup|awk|basename|bash|bc|bconsole|bg|bzip2|cal|cat|cfdisk|chgrp|chkconfig|chmod|chown|chroot|cksum|clear|cmp|column|comm|composer|cp|cron|crontab|csplit|curl|cut|date|dc|dd|ddrescue|debootstrap|df|diff|diff3|dig|dir|dircolors|dirname|dirs|dmesg|docker|docker-compose|du|egrep|eject|env|ethtool|expand|expect|expr|fdformat|fdisk|fg|fgrep|file|find|fmt|fold|format|free|fsck|ftp|fuser|gawk|git|gparted|grep|groupadd|groupdel|groupmod|groups|grub-mkconfig|gzip|halt|head|hg|history|host|hostname|htop|iconv|id|ifconfig|ifdown|ifup|import|install|ip|jobs|join|kill|killall|less|link|ln|locate|logname|logrotate|look|lpc|lpr|lprint|lprintd|lprintq|lprm|ls|lsof|lynx|make|man|mc|mdadm|mkconfig|mkdir|mke2fs|mkfifo|mkfs|mkisofs|mknod|mkswap|mmv|more|most|mount|mtools|mtr|mutt|mv|nano|nc|netstat|nice|nl|node|nohup|notify-send|npm|nslookup|op|open|parted|passwd|paste|pathchk|ping|pkill|pnpm|podman|podman-compose|popd|pr|printcap|printenv|ps|pushd|pv|quota|quotacheck|quotactl|ram|rar|rcp|reboot|remsync|rename|renice|rev|rm|rmdir|rpm|rsync|scp|screen|sdiff|sed|sendmail|seq|service|sftp|sh|shellcheck|shuf|shutdown|sleep|slocate|sort|split|ssh|stat|strace|su|sudo|sum|suspend|swapon|sync|tac|tail|tar|tee|time|timeout|top|touch|tr|traceroute|tsort|tty|umount|uname|unexpand|uniq|units|unrar|unshar|unzip|update-grub|uptime|useradd|userdel|usermod|users|uudecode|uuencode|v|vcpkg|vdir|vi|vim|virsh|vmstat|wait|watch|wc|wget|whereis|which|who|whoami|write|xargs|xdg-open|yarn|yes|zenity|zip|zsh|zypper)(?=$|[)\s;|&])/,lookbehind:!0},keyword:{pattern:/(^|[\s;|&]|[<>]\()(?:case|do|done|elif|else|esac|fi|for|function|if|in|select|then|until|while)(?=$|[)\s;|&])/,lookbehind:!0},builtin:{pattern:/(^|[\s;|&]|[<>]\()(?:\.|:|alias|bind|break|builtin|caller|cd|command|continue|declare|echo|enable|eval|exec|exit|export|getopts|hash|help|let|local|logout|mapfile|printf|pwd|read|readarray|readonly|return|set|shift|shopt|source|test|times|trap|type|typeset|ulimit|umask|unalias|unset)(?=$|[)\s;|&])/,lookbehind:!0,alias:"class-name"},boolean:{pattern:/(^|[\s;|&]|[<>]\()(?:false|true)(?=$|[)\s;|&])/,lookbehind:!0},"file-descriptor":{pattern:/\B&\d\b/,alias:"important"},operator:{pattern:/\d?<>|>\||\+=|=[=~]?|!=?|<<[<-]?|[&\d]?>>|\d[<>]&?|[<>][&=]?|&[>&]?|\|[&|]?/,inside:{"file-descriptor":{pattern:/^\d/,alias:"important"}}},punctuation:/\$?\(\(?|\)\)?|\.\.|[{}[\];\\]/,number:{pattern:/(^|\s)(?:[1-9]\d*|0)(?:[.,]\d+)?\b/,lookbehind:!0}},n.inside=e.languages.bash;for(var a=["comment","function-name","for-or-select","assign-left","string","environment","function","keyword","builtin","boolean","file-descriptor","operator","punctuation","number"],i=r.variable[1].inside,o=0;o<a.length;o++)i[a[o]]=e.languages.bash[a[o]];e.languages.shell=e.languages.bash}(Prism),Prism.languages.swift={comment:{pattern:/(^|[^\\:])(?:\/\/.*|\/\*(?:[^/*]|\/(?!\*)|\*(?!\/)|\/\*(?:[^*]|\*(?!\/))*\*\/)*\*\/)/,lookbehind:!0,greedy:!0},"string-literal":[{pattern:RegExp(/(^|[^"#])/.source+"(?:"+/"(?:\\(?:\((?:[^()]|\([^()]*\))*\)|\r\n|[^(])|[^\\\r\n"])*"/.source+"|"+/"""(?:\\(?:\((?:[^()]|\([^()]*\))*\)|[^(])|[^\\"]|"(?!""))*"""/.source+")"+/(?!["#])/.source),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\\()(?:[^()]|\([^()]*\))*(?=\))/,lookbehind:!0,inside:null},"interpolation-punctuation":{pattern:/^\)|\\\($/,alias:"punctuation"},punctuation:/\\(?=[\r\n])/,string:/[\s\S]+/}},{pattern:RegExp(/(^|[^"#])(#+)/.source+"(?:"+/"(?:\\(?:#+\((?:[^()]|\([^()]*\))*\)|\r\n|[^#])|[^\\\r\n])*?"/.source+"|"+/"""(?:\\(?:#+\((?:[^()]|\([^()]*\))*\)|[^#])|[^\\])*?"""/.source+")\\2"),lookbehind:!0,greedy:!0,inside:{interpolation:{pattern:/(\\#+\()(?:[^()]|\([^()]*\))*(?=\))/,lookbehind:!0,inside:null},"interpolation-punctuation":{pattern:/^\)|\\#+\($/,alias:"punctuation"},string:/[\s\S]+/}}],directive:{pattern:RegExp(/#/.source+"(?:"+/(?:elseif|if)\b/.source+"(?:[ \t]*"+/(?:![ \t]*)?(?:\b\w+\b(?:[ \t]*\((?:[^()]|\([^()]*\))*\))?|\((?:[^()]|\([^()]*\))*\))(?:[ \t]*(?:&&|\|\|))?/.source+")+|"+/(?:else|endif)\b/.source+")"),alias:"property",inside:{"directive-name":/^#\w+/,boolean:/\b(?:false|true)\b/,number:/\b\d+(?:\.\d+)*\b/,operator:/!|&&|\|\||[<>]=?/,punctuation:/[(),]/}},literal:{pattern:/#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\b/,alias:"constant"},"other-directive":{pattern:/#\w+\b/,alias:"property"},attribute:{pattern:/@\w+/,alias:"atrule"},"function-definition":{pattern:/(\bfunc\s+)\w+/,lookbehind:!0,alias:"function"},label:{pattern:/\b(break|continue)\s+\w+|\b[a-zA-Z_]\w*(?=\s*:\s*(?:for|repeat|while)\b)/,lookbehind:!0,alias:"important"},keyword:/\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\b/,boolean:/\b(?:false|true)\b/,nil:{pattern:/\bnil\b/,alias:"constant"},"short-argument":/\$\d+\b/,omit:{pattern:/\b_\b/,alias:"keyword"},number:/\b(?:[\d_]+(?:\.[\de_]+)?|0x[a-f0-9_]+(?:\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\b/i,"class-name":/\b[A-Z](?:[A-Z_\d]*[a-z]\w*)?\b/,function:/\b[a-z_]\w*(?=\s*\()/i,constant:/\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\b/,operator:/[-+*/%=!<>&|^~?]+|\.[.\-+*/%=!<>&|^~?]+/,punctuation:/[{}[\]();,.:\\]/},Prism.languages.swift["string-literal"].forEach((function(e){e.inside.interpolation.inside=Prism.languages.swift})),Prism.languages["visual-basic"]={comment:{pattern:/(?:['‘’]|REM\b)(?:[^\r\n_]|_(?:\r\n?|\n)?)*/i,inside:{keyword:/^REM/i}},directive:{pattern:/#(?:Const|Else|ElseIf|End|ExternalChecksum|ExternalSource|If|Region)(?:\b_[ \t]*(?:\r\n?|\n)|.)+/i,alias:"property",greedy:!0},string:{pattern:/\$?["“”](?:["“”]{2}|[^"“”])*["“”]C?/i,greedy:!0},date:{pattern:/#[ \t]*(?:\d+([/-])\d+\1\d+(?:[ \t]+(?:\d+[ \t]*(?:AM|PM)|\d+:\d+(?::\d+)?(?:[ \t]*(?:AM|PM))?))?|\d+[ \t]*(?:AM|PM)|\d+:\d+(?::\d+)?(?:[ \t]*(?:AM|PM))?)[ \t]*#/i,alias:"number"},number:/(?:(?:\b\d+(?:\.\d+)?|\.\d+)(?:E[+-]?\d+)?|&[HO][\dA-F]+)(?:[FRD]|U?[ILS])?/i,boolean:/\b(?:False|Nothing|True)\b/i,keyword:/\b(?:AddHandler|AddressOf|Alias|And(?:Also)?|As|Boolean|ByRef|Byte|ByVal|Call|Case|Catch|C(?:Bool|Byte|Char|Date|Dbl|Dec|Int|Lng|Obj|SByte|Short|Sng|Str|Type|UInt|ULng|UShort)|Char|Class|Const|Continue|Currency|Date|Decimal|Declare|Default|Delegate|Dim|DirectCast|Do|Double|Each|Else(?:If)?|End(?:If)?|Enum|Erase|Error|Event|Exit|Finally|For|Friend|Function|Get(?:Type|XMLNamespace)?|Global|GoSub|GoTo|Handles|If|Implements|Imports|In|Inherits|Integer|Interface|Is|IsNot|Let|Lib|Like|Long|Loop|Me|Mod|Module|Must(?:Inherit|Override)|My(?:Base|Class)|Namespace|Narrowing|New|Next|Not(?:Inheritable|Overridable)?|Object|Of|On|Operator|Option(?:al)?|Or(?:Else)?|Out|Overloads|Overridable|Overrides|ParamArray|Partial|Private|Property|Protected|Public|RaiseEvent|ReadOnly|ReDim|RemoveHandler|Resume|Return|SByte|Select|Set|Shadows|Shared|short|Single|Static|Step|Stop|String|Structure|Sub|SyncLock|Then|Throw|To|Try|TryCast|Type|TypeOf|U(?:Integer|Long|Short)|Until|Using|Variant|Wend|When|While|Widening|With(?:Events)?|WriteOnly|Xor)\b/i,operator:/[+\-*/\\^<=>&#@$%!]|\b_(?=[ \t]*[\r\n])/,punctuation:/[{}().,:?]/},Prism.languages.vb=Prism.languages["visual-basic"],Prism.languages.vba=Prism.languages["visual-basic"],Prism.languages.wasm={comment:[/\(;[\s\S]*?;\)/,{pattern:/;;.*/,greedy:!0}],string:{pattern:/"(?:\\[\s\S]|[^"\\])*"/,greedy:!0},keyword:[{pattern:/\b(?:align|offset)=/,inside:{operator:/=/}},{pattern:/\b(?:(?:f32|f64|i32|i64)(?:\.(?:abs|add|and|ceil|clz|const|convert_[su]\/i(?:32|64)|copysign|ctz|demote\/f64|div(?:_[su])?|eqz?|extend_[su]\/i32|floor|ge(?:_[su])?|gt(?:_[su])?|le(?:_[su])?|load(?:(?:8|16|32)_[su])?|lt(?:_[su])?|max|min|mul|neg?|nearest|or|popcnt|promote\/f32|reinterpret\/[fi](?:32|64)|rem_[su]|rot[lr]|shl|shr_[su]|sqrt|store(?:8|16|32)?|sub|trunc(?:_[su]\/f(?:32|64))?|wrap\/i64|xor))?|memory\.(?:grow|size))\b/,inside:{punctuation:/\./}},/\b(?:anyfunc|block|br(?:_if|_table)?|call(?:_indirect)?|data|drop|elem|else|end|export|func|get_(?:global|local)|global|if|import|local|loop|memory|module|mut|nop|offset|param|result|return|select|set_(?:global|local)|start|table|tee_local|then|type|unreachable)\b/],variable:/\$[\w!#$%&'*+\-./:<=>?@\\^`|~]+/,number:/[+-]?\b(?:\d(?:_?\d)*(?:\.\d(?:_?\d)*)?(?:[eE][+-]?\d(?:_?\d)*)?|0x[\da-fA-F](?:_?[\da-fA-F])*(?:\.[\da-fA-F](?:_?[\da-fA-D])*)?(?:[pP][+-]?\d(?:_?\d)*)?)\b|\binf\b|\bnan(?::0x[\da-fA-F](?:_?[\da-fA-D])*)?\b/,punctuation:/[()]/},dd.manual=!0;var gd={figure:"figure"},md=function(e){An(n,eu);var t=hd(n);function n(e){var r;e.externals;var a,i=e.config;(mn(this,n),r=t.call(this,{needCache:!0}),n.inlineCodeCache={},r.codeCache={},r.customLang=[],r.customParser={},r.wrap=i.wrap,r.lineNumber=i.lineNumber,r.copyCode=i.copyCode,r.editCode=i.editCode,r.changeLang=i.changeLang,r.selfClosing=i.selfClosing,r.mermaid=i.mermaid,r.indentedCodeBlock=void 0===i.indentedCodeBlock||i.indentedCodeBlock,r.INLINE_CODE_REGEX=/(`+)(.+?(?:\n.+?)*?)\1/g,i&&i.customRenderer)&&(r.customLang=Lc(a=oc(i.customRenderer)).call(a,(function(e){return e.toLowerCase()})),r.customParser=function(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?ac(n=pd(Object(a),!0)).call(n,(function(t){hi(e,t,a[t])})):gl?vl(e,gl(a)):ac(r=pd(Object(a))).call(r,(function(t){Us(e,t,dl(a,t))}))}return e}({},i.customRenderer));return r.customHighlighter=i.highlighter,r}return gn(n,[{key:"$codeCache",value:function(e,t){return e&&t&&(this.codeCache[e]=t),this.codeCache[e]?this.codeCache[e]:(this.codeCache.length>40&&(this.codeCache.length=0),!1)}},{key:"parseCustomLanguage",value:function(e,t,n){var r,a,i,o,s,c=this.customParser[e];if(!c||"function"!=typeof c.render)return!1;var l=c.render(t,n.sign,this.$engine,this.mermaid);if(!l)return!1;var u=gd[c.constructor.TYPE]||"div";return Vs(r=Vs(a=Vs(i=Vs(o=Vs(s="<".concat(u,' data-sign="')).call(s,n.sign,'" data-type="')).call(o,e,'" data-lines="')).call(i,n.lines,'">')).call(a,l,"</")).call(r,u,">")}},{key:"fillTag",value:function(e){var t=[];return Lc(e).call(e,(function(e){if(!e)return"";for(var n=e;t.length;){var r,a=t.pop();n=Vs(r="".concat(a)).call(r,n)}var i=n.match(/<span class="(.+?)">|<\/span>/g),o=0;if(!i)return n;for(;i.length;){var s=i.pop();/<\/span>/.test(s)?o+=1:o?o-=1:t.unshift(s.match(/<span class="(.+?)">/)[0])}for(var c=0;c<t.length;c++)n="".concat(n,"</span>");return n}))}},{key:"renderLineNumber",value:function(e){if(!this.lineNumber)return e;var t=e.split("\n");return t.pop(),t=this.fillTag(t),'<span class="code-line">'.concat(t.join('</span>\n<span class="code-line">'),"</span>")}},{key:"isInternalCustomLangCovered",value:function(e){var t;return-1!==Il(t=this.customLang).call(t,e)}},{key:"computeLines",value:function(e,t,n){var r=t,a=this.getLineCount(e,r);return{sign:this.$engine.md5(e.replace(/^\n+/,"")+a),lines:a}}},{key:"appendMermaid",value:function(e,t){var n=e,r=t;if(/^flow([ ](TD|LR))?$/i.test(r)&&!this.isInternalCustomLangCovered(r)){var a,i=r.match(/^flow(?:[ ](TD|LR))?$/i)||[];n=Vs(a="graph ".concat(i[1]||"TD","\n")).call(a,n),r="mermaid"}return/^seq$/i.test(r)&&!this.isInternalCustomLangCovered(r)&&(n="sequenceDiagram\n".concat(n),r="mermaid"),"mermaid"===r&&(n=n.replace(/(^[\s]*)stateDiagram-v2\n/,"$1stateDiagram\n")),[n,r]}},{key:"wrapCode",value:function(e,t){var n,r;return Vs(n=Vs(r='<code class="language-'.concat(t)).call(r,this.wrap?" wrap":"",'">')).call(n,e,"</code>")}},{key:"renderCodeBlock",value:function(e,t,n,r){var a,i,o,s,c,l,u,f=e,d=t;return this.customHighlighter?f=this.customHighlighter(f,d):(d&&dd.languages[d]||(d="javascript"),f=dd.highlight(f,dd.languages[d],d),f=this.renderLineNumber(f)),f=Vs(a=Vs(i=Vs(o=Vs(s=Vs(c=Vs(l=Vs(u='<div\n        data-sign="'.concat(n,'"\n        data-type="codeBlock"\n        data-lines="')).call(u,r,'" \n        data-edit-code="')).call(l,this.editCode,'" \n        data-copy-code="')).call(c,this.copyCode,'"\n        data-change-lang="')).call(s,this.changeLang,'"\n        data-lang="')).call(o,t,'"\n      >\n      <pre class="language-')).call(i,d,'">')).call(a,this.wrapCode(f,d),"</pre>\n    </div>")}},{key:"$getIndentedCodeReg",value:function(){return new RegExp("(?:^|\\n\\s*\\n)(?: {4}|\\t)"+"([\\s\\S]+?)"+"(?=$|\\n( {0,3}[^ \\t\\n]|\\n[^ \\t\\n]))","g")}},{key:"$getIndentCodeBlock",value:function(e){var t=this;return this.indentedCodeBlock?this.$recoverCodeInIndent(e).replace(this.$getIndentedCodeReg(),(function(e,n){var r,a,i=(e.match(/\n/g)||[]).length,o=t.$engine.md5(e),s=Vs(r=Vs(a='<pre data-sign="'.concat(o,'" data-lines="')).call(a,i,'"><code>')).call(r,Kl(n.replace(/\n( {4}|\t)/g,"\n")),"</code></pre>");return el(e,t.pushCache(s,o,i))})):e}},{key:"$replaceCodeInIndent",value:function(e){return this.indentedCodeBlock?e.replace(this.$getIndentedCodeReg(),(function(e){return e.replace(/`/g,"~~~IndentCode")})):e}},{key:"$recoverCodeInIndent",value:function(e){return this.indentedCodeBlock?e.replace(this.$getIndentedCodeReg(),(function(e){return e.replace(/~~~IndentCode/g,"`")})):e}},{key:"$dealUnclosingCode",value:function(e){var t=e.match(/(?:^|\n)(\n*((?:>[\t ]*)*)(?:[^\S\n]*))(`{3,})([^`]*?)(?=$|\n)/g);if(!t||t.length<=0)return e;var n=!1,r=fc(t).call(t,(function(e){return!1===n?(n=!0,!0):!/```[^`\s]+/.test(e)&&(n=!1,!0)}));if(r.length%2==1){var a,i=r[r.length-1].replace(/(`)[^`]+$/,"$1").replace(/\n+/,""),o=e.replace(/\n+$/,"").replace(/\n`{1,2}$/,"");return Vs(a="".concat(o,"\n")).call(a,i,"\n")}return e}},{key:"beforeMakeHtml",value:function(e,t,n){var r=this,a=e;return(this.selfClosing||this.$engine.globalConfig.flowSessionContext)&&(a=this.$dealUnclosingCode(a)),a=(a=this.$replaceCodeInIndent(a)).replace(this.RULE.reg,(function(e,t,n,a,i,o){var s,c,l;function u(e){if(n){var t=new RegExp("^\n*",""),r=e.match(t)[0];e=r+n+e.replace(t,(function(e){return""}))}return e}var f=o,d=r.computeLines(e,t,o),p=d.sign,h=d.lines,g=r.$codeCache(p);if(g&&""!==g)return u(r.getCacheWithSpace(r.pushCache(g,p,h),e));f=(f=(f=r.$recoverCodeInIndent(f)).replace(/~D/g,"$")).replace(/~T/g,"~");var m=null!==(s=null==t||null===(c=t.match(/[ ]/g))||void 0===c?void 0:c.length)&&void 0!==s?s:0;if(m>0){var b=new RegExp("(^|\\n)[ ]{1,".concat(m,"}"),"g");f=f.replace(b,"$1")}if(n){var v=new RegExp("(^|\\n)".concat(n),"g");f=f.replace(v,"$1")}var y=qc(i).call(i);if(/^(math|katex|latex)$/i.test(y)&&!r.isInternalCustomLangCovered(y)){var _,k=e.match(/^\s*/g);return Vs(_="".concat(k,"~D~D\n")).call(_,f,"~D~D")}var w=fd(r.appendMermaid(f,y),2);return f=w[0],y=w[1],-1!==Il(l=r.customLang).call(l,y.toLowerCase())&&(g=r.parseCustomLanguage(y,f,{lines:h,sign:p}))&&""!==g?(r.$codeCache(p,g),r.getCacheWithSpace(r.pushCache(g,p,h),e)):(f=f.replace(/~X/g,"\\`"),g=(g=r.renderCodeBlock(f,y,p,h)).replace(/\\/g,"\\\\"),g=r.$codeCache(p,g),u(r.getCacheWithSpace(r.pushCache(g,p,h),e)))})),a=a.replace(_f(!0),(function(e){var t;return Lc(t=e.split("|")).call(t,(function(e){return r.makeInlineCode(e)})).join("|").replace(/`/g,"\\`")})),a=this.makeInlineCode(a),a=this.$getIndentCodeBlock(a)}},{key:"makeInlineCode",value:function(e){var t=this,r=e;return this.INLINE_CODE_REGEX.test(r)&&(r=(r=r.replace(/\\`/g,"~~not~inlineCode")).replace(this.INLINE_CODE_REGEX,(function(e,r,a){if("`"===qc(a).call(a))return e;var i=a.replace(/~~not~inlineCode/g,"\\`");i=(i=t.$replaceSpecialChar(i)).replace(/\\/g,"\\\\");var o="<code>".concat(Kl(i),"</code>"),s=t.$engine.md5(o);return n.inlineCodeCache[s]=o,"~~CODE".concat(s,"$")})),r=r.replace(/~~not~inlineCode/g,"\\`")),r}},{key:"makeHtml",value:function(e){return e}},{key:"$replaceSpecialChar",value:function(e){var t=e.replace(/~Q/g,"\\~");return t=(t=(t=(t=t.replace(/~Y/g,"\\!")).replace(/~Z/g,"\\#")).replace(/~&/g,"\\&")).replace(/~K/g,"\\/")}},{key:"rule",value:function(){return(e={begin:/(?:^|\n)(\n*((?:>[\t ]*)*)(?:[^\S\n]*))(`{3,})([^`]*?)\n/,content:/([\w\W]*?)/,end:/[^\S\n]*\3[ \t]*(?=$|\n+)/,reg:new RegExp("")}).reg=new RegExp(e.begin.source+e.content.source+e.end.source,"g"),sf(sf({},e),{},{begin:e.begin.source,content:e.content.source,end:e.end.source});var e}},{key:"mounted",value:function(e){}}]),n}();function bd(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(md,"HOOK_NAME","codeBlock"),hi(md,"inlineCodeCache",{});var vd=function(e){An(n,eu);var t=bd(n);function n(){return mn(this,n),t.apply(this,arguments)}return gn(n,[{key:"makeHtml",value:function(e){return e}},{key:"afterMakeHtml",value:function(e){var t=e;return oc(md.inlineCodeCache).length>0&&(t=t.replace(/~~CODE([0-9a-zA-Z]+)\$/g,(function(e,t){return md.inlineCodeCache[t]})),md.inlineCodeCache={}),t}},{key:"rule",value:function(){var e={begin:"(`+)[ ]*",end:"[ ]*\\1",content:"(.+?(?:\\n.+?)*?)"};return e.reg=cf(e,"g"),e}}]),n}();hi(vd,"HOOK_NAME","inlineCode");var yd=t((function(e){!function(){var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,t){return e<<t|e>>>32-t},rotr:function(e,t){return e<<32-t|e>>>t},endian:function(e){if(e.constructor==Number)return 16711935&n.rotl(e,8)|4278255360&n.rotl(e,24);for(var t=0;t<e.length;t++)e[t]=n.endian(e[t]);return e},randomBytes:function(e){for(var t=[];e>0;e--)t.push(Math.floor(256*Math.random()));return t},bytesToWords:function(e){for(var t=[],n=0,r=0;n<e.length;n++,r+=8)t[r>>>5]|=e[n]<<24-r%32;return t},wordsToBytes:function(e){for(var t=[],n=0;n<32*e.length;n+=8)t.push(e[n>>>5]>>>24-n%32&255);return t},bytesToHex:function(e){for(var t=[],n=0;n<e.length;n++)t.push((e[n]>>>4).toString(16)),t.push((15&e[n]).toString(16));return t.join("")},hexToBytes:function(e){for(var t=[],n=0;n<e.length;n+=2)t.push(parseInt(e.substr(n,2),16));return t},bytesToBase64:function(e){for(var n=[],r=0;r<e.length;r+=3)for(var a=e[r]<<16|e[r+1]<<8|e[r+2],i=0;i<4;i++)8*r+6*i<=8*e.length?n.push(t.charAt(a>>>6*(3-i)&63)):n.push("=");return n.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],r=0,a=0;r<e.length;a=++r%4)0!=a&&n.push((t.indexOf(e.charAt(r-1))&Math.pow(2,-2*a+8)-1)<<2*a|t.indexOf(e.charAt(r))>>>6-2*a);return n}};e.exports=n}()})),_d={utf8:{stringToBytes:function(e){return _d.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(_d.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],n=0;n<e.length;n++)t.push(255&e.charCodeAt(n));return t},bytesToString:function(e){for(var t=[],n=0;n<e.length;n++)t.push(String.fromCharCode(e[n]));return t.join("")}}},kd=_d,wd=function(e){return null!=e&&(Ed(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&Ed(e.slice(0,0))}(e)||!!e._isBuffer)};function Ed(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}var Sd=t((function(e){!function(){var t=yd,n=kd.utf8,r=wd,a=kd.bin,i=function(e,o){e.constructor==String?e=o&&"binary"===o.encoding?a.stringToBytes(e):n.stringToBytes(e):r(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var s=t.bytesToWords(e),c=8*e.length,l=1732584193,u=-271733879,f=-1732584194,d=271733878,p=0;p<s.length;p++)s[p]=16711935&(s[p]<<8|s[p]>>>24)|4278255360&(s[p]<<24|s[p]>>>8);s[c>>>5]|=128<<c%32,s[14+(c+64>>>9<<4)]=c;var h=i._ff,g=i._gg,m=i._hh,b=i._ii;for(p=0;p<s.length;p+=16){var v=l,y=u,_=f,k=d;l=h(l,u,f,d,s[p+0],7,-680876936),d=h(d,l,u,f,s[p+1],12,-389564586),f=h(f,d,l,u,s[p+2],17,606105819),u=h(u,f,d,l,s[p+3],22,-1044525330),l=h(l,u,f,d,s[p+4],7,-176418897),d=h(d,l,u,f,s[p+5],12,1200080426),f=h(f,d,l,u,s[p+6],17,-1473231341),u=h(u,f,d,l,s[p+7],22,-45705983),l=h(l,u,f,d,s[p+8],7,1770035416),d=h(d,l,u,f,s[p+9],12,-1958414417),f=h(f,d,l,u,s[p+10],17,-42063),u=h(u,f,d,l,s[p+11],22,-1990404162),l=h(l,u,f,d,s[p+12],7,1804603682),d=h(d,l,u,f,s[p+13],12,-40341101),f=h(f,d,l,u,s[p+14],17,-1502002290),l=g(l,u=h(u,f,d,l,s[p+15],22,1236535329),f,d,s[p+1],5,-165796510),d=g(d,l,u,f,s[p+6],9,-1069501632),f=g(f,d,l,u,s[p+11],14,643717713),u=g(u,f,d,l,s[p+0],20,-373897302),l=g(l,u,f,d,s[p+5],5,-701558691),d=g(d,l,u,f,s[p+10],9,38016083),f=g(f,d,l,u,s[p+15],14,-660478335),u=g(u,f,d,l,s[p+4],20,-405537848),l=g(l,u,f,d,s[p+9],5,568446438),d=g(d,l,u,f,s[p+14],9,-1019803690),f=g(f,d,l,u,s[p+3],14,-187363961),u=g(u,f,d,l,s[p+8],20,1163531501),l=g(l,u,f,d,s[p+13],5,-1444681467),d=g(d,l,u,f,s[p+2],9,-51403784),f=g(f,d,l,u,s[p+7],14,1735328473),l=m(l,u=g(u,f,d,l,s[p+12],20,-1926607734),f,d,s[p+5],4,-378558),d=m(d,l,u,f,s[p+8],11,-2022574463),f=m(f,d,l,u,s[p+11],16,1839030562),u=m(u,f,d,l,s[p+14],23,-35309556),l=m(l,u,f,d,s[p+1],4,-1530992060),d=m(d,l,u,f,s[p+4],11,1272893353),f=m(f,d,l,u,s[p+7],16,-155497632),u=m(u,f,d,l,s[p+10],23,-1094730640),l=m(l,u,f,d,s[p+13],4,681279174),d=m(d,l,u,f,s[p+0],11,-358537222),f=m(f,d,l,u,s[p+3],16,-722521979),u=m(u,f,d,l,s[p+6],23,76029189),l=m(l,u,f,d,s[p+9],4,-640364487),d=m(d,l,u,f,s[p+12],11,-421815835),f=m(f,d,l,u,s[p+15],16,530742520),l=b(l,u=m(u,f,d,l,s[p+2],23,-995338651),f,d,s[p+0],6,-198630844),d=b(d,l,u,f,s[p+7],10,1126891415),f=b(f,d,l,u,s[p+14],15,-1416354905),u=b(u,f,d,l,s[p+5],21,-57434055),l=b(l,u,f,d,s[p+12],6,1700485571),d=b(d,l,u,f,s[p+3],10,-1894986606),f=b(f,d,l,u,s[p+10],15,-1051523),u=b(u,f,d,l,s[p+1],21,-2054922799),l=b(l,u,f,d,s[p+8],6,1873313359),d=b(d,l,u,f,s[p+15],10,-30611744),f=b(f,d,l,u,s[p+6],15,-1560198380),u=b(u,f,d,l,s[p+13],21,1309151649),l=b(l,u,f,d,s[p+4],6,-145523070),d=b(d,l,u,f,s[p+11],10,-1120210379),f=b(f,d,l,u,s[p+2],15,718787259),u=b(u,f,d,l,s[p+9],21,-343485551),l=l+v>>>0,u=u+y>>>0,f=f+_>>>0,d=d+k>>>0}return t.endian([l,u,f,d])};i._ff=function(e,t,n,r,a,i,o){var s=e+(t&n|~t&r)+(a>>>0)+o;return(s<<i|s>>>32-i)+t},i._gg=function(e,t,n,r,a,i,o){var s=e+(t&r|n&~r)+(a>>>0)+o;return(s<<i|s>>>32-i)+t},i._hh=function(e,t,n,r,a,i,o){var s=e+(t^n^r)+(a>>>0)+o;return(s<<i|s>>>32-i)+t},i._ii=function(e,t,n,r,a,i,o){var s=e+(n^(t|~r))+(a>>>0)+o;return(s<<i|s>>>32-i)+t},i._blocksize=16,i._digestsize=16,e.exports=function(e,n){if(null==e)throw new Error("Illegal argument "+e);var r=t.wordsToBytes(i(e,n));return n&&n.asBytes?r:n&&n.asString?a.bytesToString(r):t.bytesToHex(r)}}()})),Ad={},xd=/^cherry-inner:\/\/([0-9a-f]+)$/i;var Cd=function(){function e(){mn(this,e)}return gn(e,null,[{key:"isInnerLink",value:function(e){return xd.test(e)}},{key:"set",value:function(e){var t=Sd(e);return Ad[t]=e,"cherry-inner://".concat(t)}},{key:"get",value:function(e){var t,n=fd(null!==(t=e.match(xd))&&void 0!==t?t:[],2)[1];if(n)return Ad[n]}},{key:"replace",value:function(e,t){var n,r=fd(null!==(n=e.match(xd))&&void 0!==n?n:[],2)[1];if(r)return Ad[r]=t,e}},{key:"restoreAll",value:function(t){var n=t.replace(/cherry-inner:\/\/([0-9a-f]+)/gi,(function(t){return e.get(t)||t}));return n}},{key:"clear",value:function(){Ad={}}}]),e}();function Td(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var $d=function(e){An(n,Cc);var t=Td(n);function n(e){var r,a=e.config,i=e.globalConfig;return mn(this,n),(r=t.call(this,{config:a})).urlProcessor=i.urlProcessor,r.target=a.target?'target="'.concat(a.target,'"'):a.openNewPage?'target="_blank"':"",r.rel=a.rel?'rel="'.concat(a.rel,'"'):"",r}return gn(n,[{key:"checkBrackets",value:function(e){for(var t=[],n="[".concat(e,"]"),r=function(e){return 1&Kf(n).call(n,0,e).match(/\\*$/)[0].length},a=n.length-1;n[a]&&(a!==n.length-1||!r(a));a--)if("]"!==n[a]||r(a)||t.push("]"),"["===n[a]&&!r(a)&&(t.pop(),!t.length))return{isValid:!0,coreText:Kf(n).call(n,a+1,n.length-1),extraLeadingChar:Kf(n).call(n,0,a)};return{isValid:!1,coreText:e,extraLeadingChar:""}}},{key:"toHtml",value:function(e,t,n,r,a,i,o){var s=void 0===r?"ref":"url",c="";if("ref"===s)return e;if("url"===s){var l,u=this.checkBrackets(n),f=u.isValid,d=u.coreText,p=u.extraLeadingChar;if(!f)return e;c=a&&""!==qc(a).call(a)?' title="'.concat(Kl(a.replace(/["']/g,"")),'"'):"",o?c+=' target="'.concat(o.replace(/{target\s*=\s*(.*?)}/,"$1"),'"'):this.target&&(c+=" ".concat(this.target));var h,g,m,b,v=qc(r).call(r).replace(/~1D/g,"~D"),y=d.replace(/~1D/g,"~D");return Xl(v)?(v=Vl(v=this.urlProcessor(v,"link")),Vs(h=Vs(g=Vs(m=Vs(b="".concat(t+p,'<a href="')).call(b,Cd.set(v),'" ')).call(m,this.rel," ")).call(g,c,">")).call(h,y,"</a>")):Vs(l="".concat(t+p,"<span>")).call(l,n,"</span>")}return e}},{key:"toStdMarkdown",value:function(e){return e}},{key:"makeHtml",value:function(e){var t,n,r=e.replace(this.RULE.reg,(function(e){return e.replace(/~D/g,"~1D")}));lf()?r=r.replace(this.RULE.reg,Zs(t=this.toHtml).call(t,this)):r=Xf(r,this.RULE.reg,Zs(n=this.toHtml).call(n,this),!0,1);return r=r.replace(this.RULE.reg,(function(e){return e.replace(/~1D/g,"~D")})),r}},{key:"rule",value:function(){var e={begin:lf()?"((?<!\\\\))":"(^|[^\\\\])",content:["\\[([^\\n]*?)\\]","[ \\t]*","".concat("(?:\\(([^\\s)]+)(?:[ \\t]((?:\".*?\")|(?:'.*?')))?\\)|\\[(").concat(ff,")\\]")+")","(\\{target\\s*=\\s*(_blank|_parent|_self|_top)\\})?"].join(""),end:""};return e.reg=cf(e,"g"),e}}]),n}();hi($d,"HOOK_NAME","link");var Rd=i.RangeError;Ve({target:"String",proto:!0},{repeat:function(e){var t=Wn(P(this)),n="",r=Ct(e);if(r<0||r==1/0)throw Rd("Wrong number of repetitions");for(;r>0;(r>>>=1)&&(t+=t))1&r&&(n+=t);return n}});var Od=Ws("String").repeat,Pd=String.prototype,Ld=function(e){var t=e.repeat;return"string"==typeof e||e===Pd||D(Pd,e)&&t===Pd.repeat?Od:t};function Id(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var Nd=function(e){An(n,Cc);var t=Id(n);function n(){var e,r=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0}).config;return mn(this,n),e=t.call(this,{config:r}),r?(e.allowWhitespace=!!r.allowWhitespace,e):ui(e)}return gn(n,[{key:"makeHtml",value:function(e,t){var n=function(e,n,r,a){var i,o,s,c=r.length%2==1?"em":"strong",l=Math.floor(r.length/2),u=Ld("<strong>").call("<strong>",l),f=Ld("</strong>").call("</strong>",l);return"em"===c&&(u+="<em>",f="</em>".concat(f)),Vs(i=Vs(o=Vs(s="".concat(n)).call(s,u)).call(o,t(a).html.replace(/_/g,"~U"))).call(i,f)},r=e;return r=(r=this.allowWhitespace?(r=(r=r.replace(/(^|\n[\s]*)(\*)([^\s*](?:.*?)(?:(?:\n.*?)*?))\*/g,n)).replace(/(^|\n[\s]*)(\*{2,})((?:.*?)(?:(?:\n.*?)*?))\2/g,n)).replace(/([^\n*\\\s][ ]*)(\*+)((?:.*?)(?:(?:\n.*?)*?))\2/g,n):r.replace(this.RULE.asterisk.reg,n)).replace(this.RULE.underscore.reg,(function(e,n,r,a,i,o){var s,c,l;if(""===qc(a).call(a))return e;var u=r.length%2==1?"em":"strong",f=Math.floor(r.length/2),d=Ld("<strong>").call("<strong>",f),p=Ld("</strong>").call("</strong>",f),h=t(a).html;return"em"===u&&(d+="<em>",p="</em>".concat(p)),Vs(s=Vs(c=Vs(l="".concat(n)).call(l,d)).call(c,h)).call(s,p)})),r.replace(/~U/g,"_")}},{key:"test",value:function(e,t){return this.RULE[t].reg&&this.RULE[t].reg.test(e)}},{key:"rule",value:function(){var e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0}).config,t=!!e&&!!e.allowWhitespace,n=function(e,t){var n,r,a,i="[^".concat(t,"\\s]");return e?"(?:.*?)(?:(?:\\n.*?)*?)":Vs(n=Vs(r=Vs(a="(".concat(i,"|")).call(a,i,"(.*?(\n")).call(r,i,".*)*)")).call(n,i,")")},r={begin:"(^|[^\\\\])([*]+)",content:"(".concat(n(t,"*"),")"),end:"\\2"},a={begin:"(^|".concat(pf,")(_+)"),content:"(".concat(n(t,"_"),")"),end:"\\2(?=".concat(pf,"|$)")};return r.reg=cf(r,"g"),a.reg=cf(a,"g"),{asterisk:r,underscore:a}}}]),n}();function Md(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(Nd,"HOOK_NAME","fontEmphasis");var jd=function(e){An(n,eu);var t=Md(n);function n(e){var r;return mn(this,n),(r=t.call(this)).initBrReg(e.globalConfig.classicBr),r}return gn(n,[{key:"makeHtml",value:function(e,t){var n=this;return this.test(e)?e.replace(this.RULE.reg,(function(e,r,a){var i;if(n.isContainsCache(e,!0))return e;var o,s=function(e){var r,a,i,o,s,c;if(""===qc(e).call(e))return"";var l=t(e),u=l.sign,f=l.html,d="p";new RegExp("<(".concat(Wl,")[^>]*>"),"i").test(f)&&(d="div");var p=n.getLineCount(e,e);return Vs(r=Vs(a=Vs(i=Vs(o=Vs(s=Vs(c="<".concat(d,' data-sign="')).call(c,u)).call(s,p,'" data-type="')).call(o,d,'" data-lines="')).call(i,p,'">')).call(a,n.$cleanParagraph(f),"</")).call(r,d,">")};return n.isContainsCache(a)?n.makeExcludingCached(Vs(o="".concat(r)).call(o,a),s):s(Vs(i="".concat(r)).call(i,a))})):e}},{key:"rule",value:function(){var e={begin:"(?:^|\\n)(\\n*)",end:"(?=\\s*$|\\n\\n)",content:"([\\s\\S]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();hi(jd,"HOOK_NAME","normalParagraph");Ve({target:"Reflect",stat:!0},{get:function e(t,n){var r,a,i=arguments.length<3?t:arguments[2];return Fe(t)===i?t[n]:(r=Ce.f(t,n))?function(e){return void 0!==e&&(ce(e,"value")||ce(e,"writable"))}(r)?r.value:void 0===r.get?void 0:_(r.get,i):I(a=Ta(t))?e(a,n,i):void 0}});var Dd=N.Reflect.get,Bd=fl;function Fd(){return Fd="undefined"!=typeof Reflect&&Dd?Dd:function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=pi(e)););return e}(e,t);if(r){var a=Bd(r,t);return a.get?a.get.call(arguments.length<3?e:n):a.value}},Fd.apply(this,arguments)}function Hd(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var zd="atx",Ud="setext",Wd=/[\s\-_]/,qd=/[A-Za-z]/,Gd=/[0-9]/,Kd=function(e){An(n,eu);var t=Hd(n);function n(){var e,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0,externals:void 0};r.externals;var a=r.config;return mn(this,n),(e=t.call(this,{needCache:!0})).strict=!a||!!a.strict,e.RULE=e.rule(),e.headerIDCache=[],e.headerIDCounter={},e.config=a||{},e}return gn(n,[{key:"$parseTitleText",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return"string"!=typeof e?"":e.replace(/<.*?>/g,"").replace(/&#60;/g,"<").replace(/&#62;/g,">")}},{key:"$generateId",value:function(e){for(var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n=e.length,r="",a=0;a<n;a++){var i=e.charAt(a);if(qd.test(i))r+=t?i.toLowerCase():i;else if(Gd.test(i))r+=i;else if(Wd.test(i))r+=r.length<1||"-"!==r.charAt(r.length-1)?"-":"";else if(i.charCodeAt(0)>255)try{r+=encodeURIComponent(i)}catch(e){}}return r}},{key:"generateIDNoDup",value:function(e){var t,n=e.replace(/&#60;/g,"<").replace(/&#62;/g,">"),r=this.$generateId(n,!0),a=Il(t=this.headerIDCache).call(t,r);if(-1!==a)this.headerIDCounter[a]+=1,r+="-".concat(this.headerIDCounter[a]+1);else{var i=this.headerIDCache.push(r);this.headerIDCounter[i-1]=1}return r}},{key:"$wrapHeader",value:function(e,t,n,r){var a,i,o,s,c,l,u,f=r(qc(e).call(e)),d=f.html,p=d.match(/\s+\{#([A-Za-z0-9-]+)\}$/);null!==p&&(d=d.substring(0,p.index),u=fd(p,2)[1]);var h=this.$parseTitleText(d);if(!u){u=this.generateIDNoDup(h.replace(/~fn#([0-9]+)#/g,""))}var g="safe_".concat(u),m=this.$engine.md5(Vs(a=Vs(i=Vs(o="".concat(t,"-")).call(o,f.sign,"-")).call(i,u,"-")).call(a,n));return{html:[Vs(s=Vs(c=Vs(l="<h".concat(t,' id="')).call(l,g,'" data-sign="')).call(c,m,'" data-lines="')).call(s,n,'">'),this.$getAnchor(u),"".concat(d),"</h".concat(t,">")].join(""),sign:"".concat(m)}}},{key:"$getAnchor",value:function(e){return"none"===(this.config.anchorStyle||"default")?"":'<a class="anchor" href="#'.concat(e,'"></a>')}},{key:"beforeMakeHtml",value:function(e){var t=this,n=e;return this.$engine.globalConfig.flowSessionContext&&(n=n.replace(/(\n\s*-{1,})\s*$/,"$1 ")),this.test(n,zd)&&(n=n.replace(this.RULE[zd].reg,(function(e,n,r,a){return""===qc(a).call(a)?e:t.getCacheWithSpace(t.pushCache(e),e,!0)}))),this.test(n,Ud)&&(n=n.replace(this.RULE[Ud].reg,(function(e,n,r){return""===qc(r).call(r)||t.isContainsCache(r)?e:t.getCacheWithSpace(t.pushCache(e),e,!0)}))),n}},{key:"makeHtml",value:function(e,t){var n=this,r=this.restoreCache(e);return this.test(r,zd)&&(r=r.replace(this.RULE[zd].reg,(function(e,r,a,i){var o=tl(r,n.getLineCount(e.replace(/^\n+/,""))),s=i.replace(/\s+#+\s*$/,""),c=n.$wrapHeader(s,a.length,o,t),l=c.html,u=c.sign;return n.getCacheWithSpace(n.pushCache(l,u,o),e,!0)}))),this.test(r,Ud)&&(r=r.replace(this.RULE[Ud].reg,(function(e,r,a,i){if(n.isContainsCache(a))return e;var o=tl(r,n.getLineCount(e.replace(/^\n+/,""))),s="-"===i[0]?2:1,c=n.$wrapHeader(a,s,o,t),l=c.html,u=c.sign;return n.getCacheWithSpace(n.pushCache(l,u,o),e,!0)}))),r}},{key:"afterMakeHtml",value:function(e){var t=Fd(pi(n.prototype),"afterMakeHtml",this).call(this,e);return this.headerIDCache=[],this.headerIDCounter={},t}},{key:"test",value:function(e,t){return this.RULE[t].reg&&this.RULE[t].reg.test(e)}},{key:"rule",value:function(){var e={begin:"(?:^|\\n)(\\n*)",content:["(?:\\h*","(.+)",")\\n","(?:\\h*","([=]+|[-]+)",")"].join(""),end:"(?=$|\\n)"};e.reg=cf(e,"g",!0);var t={begin:"(?:^|\\n)(\\n*)(?:\\h*(#{1,6}))",content:"(.+?)",end:"(?=$|\\n)"};return this.strict&&(t.begin+="(?=\\h+)"),t.reg=cf(t,"g",!0),{setext:e,atx:t}}}]),n}();function Zd(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(Kd,"HOOK_NAME","header");var Yd=function(e){An(n,Cc);var t=Zd(n);function n(){return mn(this,n),t.apply(this,arguments)}return gn(n,[{key:"rule",value:function(){return{begin:"",content:"",end:"",reg:new RegExp("")}}},{key:"beforeMakeHtml",value:function(e){return e.replace(/\\\n/g,"\\ \n")}},{key:"afterMakeHtml",value:function(e){var t=e.replace(/~Q/g,"~");return t=(t=(t=(t=(t=t.replace(/~X/g,"`")).replace(/~Y/g,"!")).replace(/~Z/g,"#")).replace(/~&/g,"&")).replace(/~K/g,"/")}}]),n}();hi(Yd,"HOOK_NAME","transfer");var Xd=i.TypeError,Vd=function(e){return function(t,n,r,a){J(n);var i=oe(t),o=R(i),s=Lt(i),c=e?s-1:0,l=e?-1:1;if(r<2)for(;;){if(c in o){a=o[c],c+=l;break}if(c+=l,e?c<0:s<=c)throw Xd("Reduce of empty array with no initial value")}for(;e?c>=0:s>c;c+=l)c in o&&(a=n(a,o[c],c,i));return a}},Jd={left:Vd(!1),right:Vd(!0)},Qd="process"==C(i.process),ep=Jd.left,tp=Js("reduce");Ve({target:"Array",proto:!0,forced:!tp||!Qd&&W>79&&W<83},{reduce:function(e){var t=arguments.length;return ep(this,e,t,t>1?arguments[1]:void 0)}});var np=Ws("Array").reduce,rp=Array.prototype,ap=function(e){var t=e.reduce;return e===rp||D(rp,e)&&t===rp.reduce?np:t};function ip(e,t){var n=oc(e);if(sl){var r=sl(e);t&&(r=fc(r).call(r,(function(t){return dl(e,t).enumerable}))),n.push.apply(n,r)}return n}function op(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?ac(n=ip(Object(a),!0)).call(n,(function(t){hi(e,t,a[t])})):gl?vl(e,gl(a)):ac(r=ip(Object(a))).call(r,(function(t){Us(e,t,dl(a,t))}))}return e}function sp(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var cp="loose",lp="strict",up=function(e){An(n,eu);var t=sp(n);function n(e){var r,a=e.externals,i=e.config;mn(this,n),r=t.call(this,{needCache:!0});var o=i.enableChart,s=i.chartRenderEngine,c=i.externals,l=i.chartEngineOptions,u=void 0===l?{}:l;if(r.chartRenderEngine=null,!0===o)try{r.chartRenderEngine=new s(op(op({},a&&c instanceof Array&&ap(c).call(c,(function(e,t){return delete u[t],op(op({},e),{},hi({},t,a[t]))}),{})),{},{renderer:"svg",width:500,height:300},u))}catch(e){console.warn(e)}return r}return gn(n,[{key:"$extendColumns",value:function(e,t){var n=t-e.length;return n<1?e:Vs(e).call(e,Ld("&nbsp;|").call("&nbsp;|",n).split("|",n))}},{key:"$parseChartOptions",value:function(e){if(!this.chartRenderEngine)return null;var t=/^[ ]*:(\w+):(?:[ ]*{(.*?)}[ ]*)?$/;if(!t.test(e))return null;var n=fd(e.match(t),3),r=n[1],a=n[2];return{type:r,options:a?a.split(/\s*,\s*/):["x","y"]}}},{key:"$parseColumnAlignRules",value:function(e){var t=["U","L","R","C"];return{textAlignRules:Lc(e).call(e,(function(e){var n=qc(e).call(e),r=0;return/^:/.test(n)&&(r+=1),/:$/.test(n)&&(r+=2),t[r]})),COLUMN_ALIGN_MAP:{L:"left",R:"right",C:"center"}}}},{key:"$parseTable",value:function(e,t,n){var r,a,i,o,s,c,l=this,u=0,f=Lc(e).call(e,(function(e,t){var n=e.replace(/\\\|/g,"~CS").split("|");return""===n[0]&&n.shift(),""===n[n.length-1]&&n.pop(),1!==t&&(u=Math.max(u,n.length)),n})),d=this.$parseColumnAlignRules(f[1]),p=d.textAlignRules,h=d.COLUMN_ALIGN_MAP,g={header:[],rows:[],colLength:u,rowLength:f.length-2},m=this.$parseChartOptions(f[0][0]),b=this.$engine.md5(f[0][0]);m&&(f[0][0]="");var v=Lc(r=this.$extendColumns(f[0],u)).call(r,(function(e,n){var r,a;g.header.push(e.replace(/~CS/g,"\\|"));var i=t(qc(r=e.replace(/~CS/g,"\\|")).call(r)).html;return Vs(a="~CTH".concat(p[n]||"U"," ")).call(a,i," ~CTH$")})).join(""),y=ap(f).call(f,(function(e,n,r){var a;if(r<=1)return e;var i=r-2;g.rows[i]=[];var o=Lc(a=l.$extendColumns(n,u)).call(a,(function(e,n){var r,a;g.rows[i].push(e.replace(/~CS/g,"\\|"));var o=t(qc(r=e.replace(/~CS/g,"\\|")).call(r)).html;return Vs(a="~CTD".concat(p[n]||"U"," ")).call(a,o," ~CTD$")}));return e.push("~CTR".concat(o.join(""),"~CTR$")),e}),[]).join(""),_=this.$renderTable(h,v,y,n);if(!m)return _;var k=this.chartRenderEngine.render(m.type,m.options,g),w=Vs(a=Vs(i=Vs(o=Vs(s='<figure id="table_chart_'.concat(b,"_")).call(s,_.sign,'"\n      data-sign="table_chart_')).call(o,b,"_")).call(i,_.sign,'" data-lines="0">')).call(a,k,"</figure>");return{html:Vs(c="".concat(w)).call(c,_.html),sign:b+_.sign}}},{key:"$testHeadEmpty",value:function(e){var t=e.replace(/&nbsp;/g,"").replace(/\s/g,"").replace(/(~CTH\$|~CTHU|~CTHL|~CTHR|~CTHC)/g,"");return(null==t?void 0:t.length)>0}},{key:"$renderTable",value:function(e,t,n,r){var a,i,o,s,c=this.$testHeadEmpty(t)?Vs(a="~CTHD".concat(t,"~CTHD$~CTBD")).call(a,n,"~CTBD$"):"~CTBD".concat(n,"~CTBD$"),l=this.$engine.md5(c),u=c.replace(/~CTHD\$/g,"</thead>").replace(/~CTHD/g,"<thead>").replace(/~CTBD\$/g,"</tbody>").replace(/~CTBD/g,"</tbody>").replace(/~CTR\$/g,"</tr>").replace(/~CTR/g,"<tr>").replace(/[ ]?~CTH\$/g,"</th>").replace(/[ ]?~CTD\$/g,"</td>").replace(/~CT(D|H)(L|R|C|U)[ ]?/g,(function(t,n,r){var a="<t".concat(n);return a+="U"===r?">":' align="'.concat(e[r],'">')})).replace(/\\\|/g,"|");return{html:Vs(i=Vs(o=Vs(s='<div class="cherry-table-container" data-sign="'.concat(l)).call(s,r,'" data-lines="')).call(o,r,'">\n        <table class="cherry-table">')).call(i,u,"</table></div>"),sign:l}}},{key:"makeHtml",value:function(e,t){var n=this,r=e;return this.test(r,lp)&&(r=r.replace(this.RULE[lp].reg,(function(e,r){var a,i=n.getLineCount(e,r),o=Lc(a=qc(e).call(e).split(/\n/)).call(a,(function(e){var t;return qc(t=String(e)).call(t)})),s=n.$parseTable(o,t,i),c=s.html,l=s.sign;return n.getCacheWithSpace(n.pushCache(c,l,i),e)}))),this.test(r,cp)&&(r=r.replace(this.RULE[cp].reg,(function(e,r){var a,i=n.getLineCount(e,r),o=Lc(a=qc(e).call(e).split(/\n/)).call(a,(function(e){var t;return qc(t=String(e)).call(t)})),s=n.$parseTable(o,t,i),c=s.html,l=s.sign;return n.getCacheWithSpace(n.pushCache(c,l,i),e)}))),r}},{key:"test",value:function(e,t){return this.RULE[t].reg&&this.RULE[t].reg.test(e)}},{key:"rule",value:function(){return _f()}}]),n}();function fp(){return"object"===("undefined"==typeof window?"undefined":ci(window))}function dp(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(up,"HOOK_NAME","table");var pp=function(e){An(n,eu);var t=dp(n);function n(e){var r;return mn(this,n),(r=t.call(this,{needCache:!0})).classicBr=il("classicBr")?ol():e.globalConfig.classicBr,r}return gn(n,[{key:"beforeMakeHtml",value:function(e){var t=this;return this.test(e)?e.replace(this.RULE.reg,(function(e,n,r){var a,i;if(0===r)return e;var o,s,c=null!==(a=null===(i=n.match(/\n/g))||void 0===i?void 0:i.length)&&void 0!==a?a:0,l="br".concat(c),u="";fp()?u=t.classicBr?Vs(o='<span data-sign="'.concat(l,'" data-type="br" data-lines="')).call(o,c,'"></span>'):Vs(s='<p data-sign="'.concat(l,'" data-type="br" data-lines="')).call(s,c,'">&nbsp;</p>'):u=t.classicBr?"":"<br/>";var f=t.pushCache(u,l,c);return"\n\n".concat(f,"\n")})):e}},{key:"makeHtml",value:function(e,t){return e}},{key:"rule",value:function(){var e={begin:"(?:\\n)",end:"",content:"((?:\\h*\\n){2,})"};return e.reg=cf(e,"g",!0),e}}]),n}();function hp(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(pp,"HOOK_NAME","br");var gp=function(e){An(n,eu);var t=hp(n);function n(){return mn(this,n),t.call(this,{needCache:!0})}return gn(n,[{key:"beforeMakeHtml",value:function(e){var t=this;return e.replace(this.RULE.reg,(function(e,n){var r,a=(n.match(/\n/g)||[]).length+1,i="hr".concat(a);return el(e,t.pushCache(Vs(r='<hr data-sign="'.concat(i,'" data-lines="')).call(r,a,'" />'),i))}))}},{key:"makeHtml",value:function(e,t){return e}},{key:"rule",value:function(){var e={begin:"(?:^|\\n)(\\n*)[ ]*",end:"(?=$|\\n)",content:"((?:-[ \\t]*){3,}|(?:\\*[ \\t]*){3,}|(?:_[ \\t]*){3,})"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();hi(gp,"HOOK_NAME","hr");var mp={processExtendAttributesInAlt:function(e){var t=e.match(/#([0-9]+(px|em|pt|pc|in|mm|cm|ex|%)|auto)/g);if(!t)return"";var n="",r=fd(t,2),a=r[0],i=r[1];return a&&(n=' width="'.concat(a.replace(/[ #]*/g,""),'"')),i&&(n+=' height="'.concat(i.replace(/[ #]*/g,""),'"')),n},processExtendStyleInAlt:function(e){var t=this.$getAlignment(e),n="",r=e.match(/#(border|shadow|radius|B|S|R)/g);if(r)for(var a=0;a<r.length;a++)switch(r[a]){case"#border":case"#B":t+="border:1px solid #888888;padding: 2px;box-sizing: border-box;",n+=" cherry-img-border";break;case"#shadow":case"#S":t+="box-shadow:0 2px 15px -5px rgb(0 0 0 / 50%);",n+=" cherry-img-shadow";break;case"#radius":case"#R":t+="border-radius: 15px;",n+=" cherry-img-radius"}return{extendStyles:t,extendClasses:n}},$getAlignment:function(e){var t=e.match(/#(center|right|left|float-right|float-left)/i);if(!t)return"";switch(fd(t,2)[1]){case"center":return"transform:translateX(-50%);margin-left:50%;display:block;";case"right":return"transform:translateX(-100%);margin-left:100%;margin-right:-100%;display:block;";case"left":return"transform:translateX(0);margin-left:0;display:block;";case"float-right":return"float:right;transform:translateX(0);margin-left:0;display:block;";case"float-left":return"float:left;transform:translateX(0);margin-left:0;display:block;"}}};function bp(e,t){var n=oc(e);if(sl){var r=sl(e);t&&(r=fc(r).call(r,(function(t){return dl(e,t).enumerable}))),n.push.apply(n,r)}return n}function vp(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var yp=function(e,t,n,r,a,i,o,s,c){var l=void 0===a?"ref":"url",u="";if("ref"===l)return t;if("url"===l){var f,d,p,h,g,m,b,v,y=mp.processExtendAttributesInAlt(r),_=mp.processExtendStyleInAlt(r),k=_.extendStyles,w=_.extendClasses;k&&(k=' style="'.concat(k,'" ')),w&&(w=' class="'.concat(w,'" ')),u=i&&""!==qc(i).call(i)?' title="'.concat(Zl(i),'"'):"",o&&(u+=' poster="'.concat(Vl(o),'"'));var E=c.urlProcessor(a,e),S=Vs(f=Vs(d=Vs(p=Vs(h=Vs(g=Vs(m=Vs(b="<".concat(e,' src="')).call(b,Cd.set(Vl(E)),'"')).call(m,u," ")).call(g,y," ")).call(h,k," ")).call(p,w,' controls="controls">')).call(d,Zl(r||""),"</")).call(f,e,">");return Vs(v="".concat(n)).call(v,s.videoWrapper?s.videoWrapper(a):S)}return t},_p=function(e){An(n,Cc);var t=vp(n);function n(e){var r,a=e.config,i=e.globalConfig;return mn(this,n),(r=t.call(this,null)).urlProcessor=i.urlProcessor,r.extendMedia={tag:["video","audio"],replacer:{video:function(e,t,n,r,o,s){return yp("video",e,t,n,r,o,s,a,i)},audio:function(e,t,n,r,o,s){return yp("audio",e,t,n,r,o,s,a,i)}}},r.RULE=r.rule(r.extendMedia),r}return gn(n,[{key:"toHtml",value:function(e,t,n,r,a,i,o){var s=void 0===r?"ref":"url",c="";if("ref"===s)return e;if("url"===s){var l,u,f,d,p,h,g,m,b=mp.processExtendAttributesInAlt(n),v=mp.processExtendStyleInAlt(n),y=v.extendStyles,_=v.extendClasses;y&&(y=' style="'.concat(y,'" ')),_&&(_=' class="'.concat(_,'" ')),c=a&&""!==qc(a).call(a)?' title="'.concat(Zl(a.replace(/["']/g,"")),'"'):"";var k,w="src",E=this.$engine.$cherry.options;if(E.callback&&E.callback.beforeImageMounted){var S=E.callback.beforeImageMounted(w,r);w=S.srcProp||w,k=S.src||r}var A=o?o.replace(/[{}]/g,"").replace(/([^=\s]+)=([^\s]+)/g,'$1="$2"').replace(/&/g,"&amp;"):"";return Vs(l=Vs(u=Vs(f=Vs(d=Vs(p=Vs(h=Vs(g=Vs(m="".concat(t,"<img ")).call(m,w,'="')).call(g,Cd.set(Vl(this.urlProcessor(k,"image"))),'" ')).call(h,b," ")).call(p,y," ")).call(d,_,' alt="')).call(f,Zl(n||""),'"')).call(u,c," ")).call(l,A,"/>")}return e}},{key:"toMediaHtml",value:function(e,t,n,r,a,i,o,s,c){var l,u;if(!this.extendMedia.replacer[n])return e;for(var f=arguments.length,d=new Array(f>9?f-9:0),p=9;p<f;p++)d[p-9]=arguments[p];return(l=this.extendMedia.replacer[n]).call.apply(l,Vs(u=[this,e,t,r,a,i,c]).call(u,d))}},{key:"makeHtml",value:function(e){var t,n,r,a,i=e;this.test(i)&&(i=lf()?i.replace(this.RULE.reg,Zs(t=this.toHtml).call(t,this)):Xf(i,this.RULE.reg,Zs(n=this.toHtml).call(n,this),!0,1));this.testMedia(i)&&(i=lf()?i.replace(this.RULE.regExtend,Zs(r=this.toMediaHtml).call(r,this)):Xf(i,this.RULE.regExtend,Zs(a=this.toMediaHtml).call(a,this),!0,1));return i}},{key:"testMedia",value:function(e){return this.RULE.regExtend&&this.RULE.regExtend.test(e)}},{key:"rule",value:function(e){var t={begin:lf()?"((?<!\\\\))!":"(^|[^\\\\])!",content:["\\[([^\\n]*?)\\]","[ \\t]*","".concat('(?:\\(([^"][^\\s]+?)(?:[ \\t]((?:".*?")|(?:\'.*?\')))?\\)|\\[(').concat(ff,")\\]")+")"].join(""),end:"({[^{}]+?})?"};if(e){var n=function(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?ac(n=bp(Object(a),!0)).call(n,(function(t){hi(e,t,a[t])})):gl?vl(e,gl(a)):ac(r=bp(Object(a))).call(r,(function(t){Us(e,t,dl(a,t))}))}return e}({},t);n.begin=lf()?"((?<!\\\\))!(".concat(e.tag.join("|"),")"):"(^|[^\\\\])!(".concat(e.tag.join("|"),")"),n.end="({poster=(.*)})?",t.regExtend=cf(n,"g")}return t.reg=cf(t,"g"),t}}]),n}();function kp(e,t){var n=oc(e);if(sl){var r=sl(e);t&&(r=fc(r).call(r,(function(t){return dl(e,t).enumerable}))),n.push.apply(n,r)}return n}function wp(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?ac(n=kp(Object(a),!0)).call(n,(function(t){hi(e,t,a[t])})):gl?vl(e,gl(a)):ac(r=kp(Object(a))).call(r,(function(t){Us(e,t,dl(a,t))}))}return e}function Ep(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(_p,"HOOK_NAME","image");function Sp(e){var t;if("object"!==ci(e)&&oc(e).length<1)return"";var n=[""];return ac(t=oc(e)).call(t,(function(t){var r;n.push(Vs(r="".concat(t,'="')).call(r,e[t],'"'))})),n.join(" ")}function Ap(e,t){for(var n=/^(\t|[ ])/,r=e;n.test(r);)t.space+="\t"===r[0]?4:1,r=r.replace(n,"");return r}function xp(e,t){var n=/^((([*+-]|\d+[.]|[a-z]\.|[I一二三四五六七八九十]+\.)[ \t]+)([^\r]*?)($|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.]|[a-z]\.|[I一二三四五六七八九十]+\.)[ \t]+)))/;return n.test(e)?e.replace(n,(function(e,n,r,a,i){return t.type=r.search(/[*+-]/g)>-1?"ul":"ol",t.listStyle=function(e){return/^[a-z]/.test(e)?"lower-greek":/^[一二三四五六七八九十]/.test(e)?"cjk-ideographic":/^I/.test(e)?"upper-roman":/^\+/.test(e)?"circle":/^\*/.test(e)?"square":"default"}(r),t.start=Number(r.replace(".",""))?Number(r.replace(".","")):1,i})):(t.type="blank",e)}var Cp=gn((function e(){mn(this,e),this.index=0,this.space=0,this.type="",this.start=1,this.listStyle="",this.strs=[],this.children=[],this.lines=0})),Tp=function(e){An(n,eu);var t=Ep(n);function n(e){var r,a=e.config;return mn(this,n),(r=t.call(this,{needCache:!0})).config=a||{},r.tree=[],r.emptyLines=0,r.indentSpace=Math.max(r.config.indentSpace,2),r}return gn(n,[{key:"addNode",value:function(e,t,n,r){"blank"===e.type?this.tree[r].strs.push(e.strs[0]):(this.tree[n].children.push(t),this.tree[t]=wp(wp({},e),{},{parent:n}))}},{key:"buildTree",value:function(e,t){var n=e.split("\n");this.tree=[],n.unshift("");for(var r=e.match(/\n*$/g)[0].length,a=0;a<n.length-r;a++){var i=new Cp;if(n[a]=Ap(n[a],i),n[a]=xp(n[a],i),i.strs.push(t(n[a]).html),i.index=a,0!==a){for(var o=a-1;!this.tree[o];)o-=1;if("blank"===i.type)this.addNode(i,a,this.tree[o].parent,o);else{for(;!this.tree[o]||this.tree[o].space>i.space;)o-=1;var s=i.space,c=this.tree[o].space;s<c+this.indentSpace?this.config.listNested&&this.tree[o].type!==i.type?this.addNode(i,a,o):this.addNode(i,a,this.tree[o].parent):s<c+this.indentSpace+4?this.addNode(i,a,o):(i.type="blank",this.addNode(i,a,this.tree[o].parent,o))}}else i.space=-2,this.tree.push(i)}}},{key:"renderSubTree",value:function(e,t,n){var r,a,i,o=this,s=0,c={},l=ap(t).call(t,(function(t,n){var r,a,i,c=o.tree[n],l={class:"cherry-list-item"},u="<p>".concat(c.strs.join("<br>"),"</p>");c.lines+=o.getLineCount(c.strs.join("\n"));var f=c.children.length?o.renderTree(n):"";e.lines+=c.lines,s+=c.lines;return/<span class="ch-icon ch-icon-(square|check)"><\/span>/.test(u)&&(l.class+=" check-list-item"),Vs(r=Vs(a=Vs(i="".concat(t,"<li")).call(i,Sp(l),">")).call(a,u)).call(r,f,"</li>")}),"");return void 0===e.parent&&(c["data-lines"]=0===e.index?s+this.emptyLines:s,c["data-sign"]=this.sign),t[0]&&"ol"===n&&(c.start=this.tree[t[0]].start),c.class="cherry-list__".concat(this.tree[t[0]].listStyle),Vs(r=Vs(a=Vs(i="<".concat(n)).call(i,Sp(c),">")).call(a,l,"</")).call(r,n,">")}},{key:"renderTree",value:function(e){var t=this,n=0,r=this.tree[e],a=r.children;return ap(a).call(a,(function(e,i,o){if(0===o)return e;if(t.tree[a[o]].type===t.tree[a[o-1]].type)return e;var s=t.renderSubTree(r,Kf(a).call(a,n,o),t.tree[a[o-1]].type);return n=o,e+s}),"")+(a.length?this.renderSubTree(r,Kf(a).call(a,n,a.length),this.tree[a[a.length-1]].type):"")}},{key:"toHtml",value:function(e,t){var n,r;this.emptyLines=null!==(n=null===(r=e.match(/^\n\n/))||void 0===r?void 0:r.length)&&void 0!==n?n:0;var a=e.replace(/~0$/g,"").replace(/^\n+/,"");this.buildTree(function(e){return e.replace(/^((?:|[\t ]+)[*+-]\s+)\[(\s|x)\]/gm,(function(e,t,n){var r,a=/\s/.test(n)?'<span class="ch-icon ch-icon-square"></span>':'<span class="ch-icon ch-icon-check"></span>';return Vs(r="".concat(t)).call(r,a)}))}(a),t);var i=this.renderTree(0);return this.pushCache(i,this.sign,this.$getLineNum(e))}},{key:"$getLineNum",value:function(e){var t,n,r,a,i=null!==(t=null===(n=e.match(/^\n\n/))||void 0===n?void 0:n.length)&&void 0!==t?t:0;return null!==(r=null===(a=e.replace(/^\n+/,"").replace(/\n+$/,"\n").match(/\n/g))||void 0===a?void 0:a.length)&&void 0!==r?r:0+i}},{key:"makeHtml",value:function(e,t){var n=this,r="".concat(e,"~0");return this.test(r)&&(r=r.replace(this.RULE.reg,(function(e){return n.getCacheWithSpace(n.checkCache(e,t,n.$getLineNum(e)),e)}))),r=r.replace(/~0$/g,"")}},{key:"rule",value:function(){var e={begin:"(?:^|\n)(\n*)(([ ]{0,3}([*+-]|\\d+[.]|[a-z]\\.|[I一二三四五六七八九十]+\\.)[ \\t]+)",content:"([^\\r]+?)",end:"(~0|\\n{2,}(?=\\S)(?![ \\t]*(?:[*+-]|\\d+[.]|[a-z]\\.|[I一二三四五六七八九十]+\\.)[ \\t]+)))"};return e.reg=new RegExp(e.begin+e.content+e.end,"gm"),e}}]),n}();function $p(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}function Rp(e){for(var t=/^(\t|[ ]{1,4})/,n=e,r=0;t.test(n);)n=n.replace(/^(\t|[ ]{1,4})/g,""),r+=1;return r}hi(Tp,"HOOK_NAME","list");var Op=function(e){An(n,eu);var t=$p(n);function n(){return mn(this,n),t.call(this,{needCache:!0})}return gn(n,[{key:"handleMatch",value:function(e,t){var n=this;return e.replace(this.RULE.reg,(function(e,r,a){for(var i,o,s=t(a),c=s.sign,l=s.html,u=n.signWithCache(l)||c,f=n.getLineCount(e,r),d=/^(([ \t]{0,3}([*+-]|\d+[.]|[a-z]\.|[I一二三四五六七八九十]+\.)[ \t]+)([^\r]+?)($|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.]|[a-z]\.|[I一二三四五六七八九十]+\.)[ \t]+)))/,p=Rp(r),h=l.split("\n"),g=/^[>\s]+/,m=/>/g,b=1,v=0,y=Vs(i=Vs(o='<blockquote data-sign="'.concat(u,"_")).call(o,f,'" data-lines="')).call(i,f,'">'),_=0;h[_];_++){if(0!==_){var k=Rp(h[_]);if(k<=p&&d.test(h[_]))break;p=k}var w=h[_].replace(g,(function(e){var t=e.match(m);return v=t&&t.length>b?t.length:b,""}));if(b===v&&0!==_&&(y+="<br>"),b<v)y+=Ld("<blockquote>").call("<blockquote>",v-b),b=v;y+=w}return y+=Ld("</blockquote>").call("</blockquote>",b),n.getCacheWithSpace(n.pushCache(y,u,f),e)}))}},{key:"makeHtml",value:function(e,t){return this.test(e)?this.handleMatch(e,t):e}},{key:"rule",value:function(){var e={begin:"(?:^|\\n)(\\s*)",content:["(",">(?:.+?\\n(?![*+-]|\\d+[.]|[a-z]\\.))(?:>*.+?\\n(?![*+-]|\\d+[.]|[a-z]\\.))*(?:>*.+?)","|",">(?:.+?)",")"].join(""),end:"(?=(\\n)|$)"};return e.reg=cf(e,"g"),e}}]),n}();function Pp(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(Op,"HOOK_NAME","blockquote");var Lp=function(e){An(n,Cc);var t=Pp(n);function n(e){var r,a=e.config,i=e.globalConfig;return mn(this,n),(r=t.call(this,{config:a})).urlProcessor=i.urlProcessor,r.enableShortLink=!!a.enableShortLink,r.shortLinkLength=a.shortLinkLength,r.target=a.target?'target="'.concat(a.target,'"'):a.openNewPage?'target="_blank"':"",r.rel=a.rel?'rel="'.concat(a.rel,'"'):"",r}return gn(n,[{key:"isLinkInHtmlAttribute",value:function(e,t,n){for(var r,a=new RegExp(["<","([a-zA-Z][a-zA-Z0-9-]*)","(",["\\s+[a-zA-Z_:][a-zA-Z0-9_.:-]*","(",["\\s*=\\s*","(",["([^\\s\"'=<>`]+)","('[^']*')",'("[^"]*")'].join("|"),")"].join(""),")?"].join(""),")*","\\s*[/]?>"].join(""),"g");null!==(r=a.exec(e))&&!(r.index>t+n);)if(r.index<t&&r.index+r[0].length>=t+n)return!0;return!1}},{key:"isLinkInATag",value:function(e,t,n){for(var r,a=/<a.*>[^<]*<\/a>/g;null!==(r=a.exec(e))&&!(r.index>t+n);)if(r.index<t&&r.index+r[0].length>=t+n)return!0;return!1}},{key:"makeHtml",value:function(e,t){var n=this;return this.test(e)&&(hf.test(e)||mf.test(e))?e.replace(this.RULE.reg,(function(e,t,r,a,i,o,s){var c,l,u;if(n.isLinkInHtmlAttribute(s,o,r.length+a.length)||n.isLinkInATag(s,o,r.length+a.length))return e;var f=r.toLowerCase(),d="",p="",h=!0;if(("<"!==t&&"&#60;"!==t||">"!==i&&"&#62;"!==i)&&(d=t,p=i,h=!1),""===qc(a).call(a)||!h&&""===f&&!/www\./.test(a))return e;switch(f){case"javascript:":return e;case"mailto:":var g,m,b,v,y,_;return gf.test(a)?Vs(g=Vs(m=Vs(b=Vs(v=Vs(y="".concat(d,'<a href="')).call(y,Vl(Vs(_="".concat(f)).call(_,a)),'" ')).call(v,n.target," ")).call(b,n.rel,">")).call(m,Zl(a),"</a>")).call(g,p):e;case"":var k,w,E,S,A,x,C,T,$,R;if(d===p||!h)return gf.test(a)?Vs(k=Vs(w=Vs(E=Vs(S=Vs(A="".concat(d,'<a href="mailto:')).call(A,Vl(a),'" ')).call(S,n.target," ")).call(E,n.rel,">")).call(w,Zl(a),"</a>")).call(k,p):vf.test(a)?Vs(x=Vs(C="".concat(d)).call(C,n.renderLink("//".concat(a),a))).call(x,p):e;if(h)return gf.test(a)?Vs(T=Vs($=Vs(R='<a href="mailto:'.concat(Vl(a),'" ')).call(R,n.target," ")).call($,n.rel,">")).call(T,Zl(a),"</a>"):yf.test(a)||vf.test(a)?n.renderLink(a):e;default:return yf.test(a)?Vs(c=Vs(l="".concat(d)).call(l,n.renderLink(Vs(u="".concat(f)).call(u,a)))).call(c,p):e}return e})):e}},{key:"rule",value:function(){var e,t={begin:"(<?)",content:["((?:[a-z][a-z0-9+.-]{1,31}:)?)",Vs(e="((?:".concat(bf.source,")|(?:")).call(e,hf.source,"))")].join(""),end:"(>?)"};return t.reg=cf(t,"ig"),t}},{key:"renderLink",value:function(e,t){var r,a,i,o,s=t;if("string"!=typeof s)if(this.enableShortLink){var c,l=e.replace(/^https?:\/\//i,"");s=Vs(c="".concat(l.substring(0,this.shortLinkLength))).call(c,l.length>this.shortLinkLength?"...":"")}else s=e;var u=Vl(this.urlProcessor(e,"autolink")),f=Zl(e),d=fc(r=[this.target,this.rel]).call(r,Boolean).join(" ");return Vs(a=Vs(i=Vs(o='<a href="'.concat(n.escapePreservedSymbol(u),'" title="')).call(o,n.escapePreservedSymbol(f),'" ')).call(i,d,">")).call(a,n.escapePreservedSymbol(f),"</a>")}}]),n}();function Ip(){var e,t,n,r;fp()&&(this.katex=null!==(e=null===(t=this.externals)||void 0===t?void 0:t.katex)&&void 0!==e?e:window.katex,this.MathJax=null!==(n=null===(r=this.externals)||void 0===r?void 0:r.MathJax)&&void 0!==n?n:window.MathJax)}hi(Lp,"HOOK_NAME","autoLink"),hi(Lp,"escapePreservedSymbol",(function(e){return e.replace(/_/g,"&#x5f;").replace(/\*/g,"&#x2a;")}));var Np=["&","<",">",'"',"'"],Mp=function(e){var t=e.replace(new RegExp(df,"g"),(function(e){return-1!==Il(Np).call(Np,e)?Kl(e):"\\".concat(e)}));return t},jp=Fc.trim,Dp=m("".charAt),Bp=i.parseFloat,Fp=i.Symbol,Hp=Fp&&Fp.iterator,zp=1/Bp(Ic+"-0")!=-1/0||Hp&&!o((function(){Bp(Object(Hp))}))?function(e){var t=jp(Wn(e)),n=Bp(t);return 0===n&&"-"==Dp(t,0)?-0:n}:Bp;Ve({global:!0,forced:parseFloat!=zp},{parseFloat:zp}),N.parseFloat;var Up=Of,Wp=be("match"),qp=i.TypeError,Gp=function(e){if(function(e){var t;return I(e)&&(void 0!==(t=e[Wp])?!!t:"RegExp"==C(e))}(e))throw qp("The method doesn't accept regular expressions");return e},Kp=be("match"),Zp=function(e){var t=/./;try{"/./"[e](t)}catch(n){try{return t[Kp]=!1,"/./"[e](t)}catch(e){}}return!1};Ce.f;var Yp=m("".startsWith),Xp=m("".slice),Vp=Math.min,Jp=Zp("startsWith");Ve({target:"String",proto:!0,forced:!Jp},{startsWith:function(e){var t=Wn(P(this));Gp(e);var n=Pt(Vp(arguments.length>1?arguments[1]:void 0,t.length)),r=Wn(e);return Yp?Yp(t,r,n):Xp(t,n,n+r.length)===r}});var Qp=Ws("String").startsWith,eh=String.prototype,th=function(e){var t=e.startsWith;return"string"==typeof e||e===eh||D(eh,e)&&t===eh.startsWith?Qp:t};function nh(e,t){if(!e||!e.tagName)return"";var n,r,a=document.createElement("div");return a.appendChild(e.cloneNode(!1)),n=a.innerHTML,t&&(r=Il(n).call(n,">")+1,n=n.substring(0,r)+e.innerHTML+n.substring(r)),a=null,n}function rh(e){var t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=document.createElement(e);(a.className=n,void 0!==r)&&ac(t=oc(r)).call(t,(function(e){var t=r[e];if(th(e).call(e,"data-")){var n=e.replace(/^data-/,"");a.dataset[n]=t}else a.setAttribute(e,t)}));return a}function ah(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var ih=function(e){An(n,eu);var t=ah(n);function n(e){var r,a,i=e.config;return mn(this,n),hi(li(a=t.call(this,{needCache:!0})),"engine","MathJax"),hi(li(a),"katex",void 0),hi(li(a),"MathJax",void 0),a.engine=fp()?null!==(r=i.engine)&&void 0!==r?r:"MathJax":"node",a}return gn(n,[{key:"toHtml",value:function(e,t,n,r){var a;Zs(Ip).call(Ip,this)("engine");var i=e.replace(/^[ \f\r\t\v]*/,"").replace(/\s*$/,""),o=t.replace(/^[ \f\r\t\v]*\n/,""),s=this.$engine.md5(e),c=this.getLineCount(i,o);/\n/.test(t)||(c-=1),/\n\s*$/.test(e)||(c-=1),c=c>0?c:0;var l="";if("katex"===this.engine){var u,f,d=this.katex.renderToString(r,{throwOnError:!1,displayMode:!0});l=Vs(u=Vs(f='<div data-sign="'.concat(s,'" class="Cherry-Math" data-type="mathBlock"\n            data-lines="')).call(f,c,'">')).call(u,d,"</div>")}else if(null!==(a=this.MathJax)&&void 0!==a&&a.tex2svg){var p,h,g=nh(this.MathJax.tex2svg(r),!0);l=Vs(p=Vs(h='<div data-sign="'.concat(s,'" class="Cherry-Math" data-type="mathBlock"\n            data-lines="')).call(h,c,'">')).call(p,g,"</div>")}else{var m,b;l=Vs(m=Vs(b='<div data-sign="'.concat(s,'" class="Cherry-Math" data-type="mathBlock"\n      data-lines="')).call(b,c,'">$$')).call(m,Mp(r),"$$</div>")}return n+this.getCacheWithSpace(this.pushCache(l,s,c),e)}},{key:"beforeMakeHtml",value:function(e){var t,n;return lf()?e.replace(this.RULE.reg,Zs(n=this.toHtml).call(n,this)):Xf(e,this.RULE.reg,Zs(t=this.toHtml).call(t,this),!0,1)}},{key:"makeHtml",value:function(e){return e}},{key:"rule",value:function(){var e={begin:lf()?"(\\s*)((?<!\\\\))~D~D\\s*":"(\\s*)(^|[^\\\\])~D~D\\s*",content:"([\\w\\W]*?)",end:"(\\s*)~D~D(?:\\s{0,1})"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();function oh(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(ih,"HOOK_NAME","mathBlock");var sh=function(e){An(n,eu);var t=oh(n);function n(e){var r,a,i=e.config;return mn(this,n),hi(li(a=t.call(this,{needCache:!0})),"engine","MathJax"),hi(li(a),"katex",void 0),hi(li(a),"MathJax",void 0),a.engine=fp()?null!==(r=i.engine)&&void 0!==r?r:"MathJax":"node",a}return gn(n,[{key:"toHtml",value:function(e,t,n){var r,a;if(!n)return e;Zs(Ip).call(Ip,this)("engine");var i=n.match(/\n/g),o=i?i.length+2:2,s=this.$engine.md5(e),c="";if("katex"===this.engine&&null!==(r=this.katex)&&void 0!==r&&r.renderToString){var l,u,f=this.katex.renderToString(n,{throwOnError:!1});c=Vs(l=Vs(u="".concat(t,'<span class="Cherry-InlineMath" data-type="mathBlock" data-lines="')).call(u,o,'">')).call(l,f,"</span>")}else if(null!==(a=this.MathJax)&&void 0!==a&&a.tex2svg){var d,p,h=nh(this.MathJax.tex2svg(n,{em:12,ex:6,display:!1}),!0);c=Vs(d=Vs(p="".concat(t,'<span class="Cherry-InlineMath" data-type="mathBlock" data-lines="')).call(p,o,'">')).call(d,h,"</span>")}else{var g,m;c=Vs(g=Vs(m="".concat(t,'<span class="Cherry-InlineMath" data-type="mathBlock"\n        data-lines="')).call(m,o,'">$')).call(g,Mp(n),"$</span>")}return this.pushCache(c,eu.IN_PARAGRAPH_CACHE_KEY_PREFIX+s)}},{key:"beforeMakeHtml",value:function(e){var t=this,n=e;return n=n.replace(_f(!0),(function(e){var n;return Lc(n=e.split("|")).call(n,(function(e){return t.makeInlineMath(e)})).join("|").replace(/\\~D/g,"~D").replace(/~D/g,"\\~D")})),this.makeInlineMath(n)}},{key:"makeInlineMath",value:function(e){var t,n;return this.test(e)?lf()?e.replace(this.RULE.reg,Zs(n=this.toHtml).call(n,this)):Xf(e,this.RULE.reg,Zs(t=this.toHtml).call(t,this),!0,1):e}},{key:"makeHtml",value:function(e){return e}},{key:"rule",value:function(){var e={begin:lf()?"((?<!\\\\))~D\\n?":"(^|[^\\\\])~D\\n?",content:"(.*?)\\n?",end:"~D"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();hi(sh,"HOOK_NAME","inlineMath");Ve({target:"Array",proto:!0},{fill:function(e){for(var t=oe(this),n=Lt(t),r=arguments.length,a=Rt(r>1?arguments[1]:void 0,n),i=r>2?arguments[2]:void 0,o=void 0===i?n:Rt(i,n);o>a;)t[a++]=e;return t}});var ch=Ws("Array").fill,lh=Array.prototype,uh=function(e){var t=e.fill;return e===lh||D(lh,e)&&t===lh.fill?ch:t};function fh(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}function dh(e){return e}var ph={tocStyle:"plain",tocNodeClass:"toc-li",tocContainerClass:"toc",tocTitleClass:"toc-title",linkProcessor:dh},hh='<p data-sign="empty-toc" data-lines="1">&nbsp;</p>',gh=function(e){An(n,eu);var t=fh(n);function n(e){var r,a;e.externals;var i=e.config;return mn(this,n),hi(li(a=t.call(this,{needCache:!0})),"tocStyle","nested"),hi(li(a),"tocNodeClass","toc-li"),hi(li(a),"tocContainerClass","toc"),hi(li(a),"tocTitleClass","toc-title"),hi(li(a),"linkProcessor",dh),hi(li(a),"baseLevel",1),hi(li(a),"isFirstTocToken",!0),hi(li(a),"allowMultiToc",!1),ac(r=oc(ph)).call(r,(function(e){a[e]=i[e]||ph[e]})),a}return gn(n,[{key:"beforeMakeHtml",value:function(e){var t=this,n=e;return this.test(n,"extend")&&(n=n.replace(this.RULE.extend.reg,(function(e,n,r){var a;if(!t.allowMultiToc&&!t.isFirstTocToken)return Vs(a="\n".concat(n)).call(a,hh);var i=t.pushCache(e);return t.isFirstTocToken=!1,el(e,i)}))),this.test(n,"standard")&&(n=n.replace(this.RULE.standard.reg,(function(e,n,r){var a;return t.allowMultiToc||t.isFirstTocToken?(t.isFirstTocToken=!1,el(e,t.pushCache(e))):Vs(a="\n".concat(n)).call(a,hh)}))),n}},{key:"makeHtml",value:function(e){return e}},{key:"$makeLevel",value:function(e){for(var t="",n=this.baseLevel;n<e;n++)t+="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";return t}},{key:"$makeTocItem",value:function(e,t){var n,r,a,i,o,s=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],c="";t&&(c=this.$makeLevel(e.level));var l=this.linkProcessor("#".concat(e.id).replace(/safe_/g,""));return Vs(n=Vs(r=Vs(a=Vs(i=Vs(o='<li class="'.concat(this.tocNodeClass,'">')).call(o,c,'<a href="')).call(i,l,'" class="level-')).call(a,e.level,'">')).call(r,e.text,"</a>")).call(n,s?"</li>":"")}},{key:"$makePlainToc",value:function(e){var t=this;return Lc(e).call(e,(function(e){return t.$makeTocItem(e,!0)})).join("")}},{key:"$makeNestedToc",value:function(e){var t,n,r=this,a=0,i=uh(t=new Array(7)).call(t,!1),o=uh(n=new Array(7)).call(n,!1),s="";ac(e).call(e,(function(e){var t=e.level;if(0===a){for(var n=t;n>=r.baseLevel;n--)s+="<ul>",o[n]=!0;return s+=r.$makeTocItem(e,!1,!1),i[t]=!0,void(a=t)}if(t<a){for(var c=a;c>=t;c--)i[c]&&(s+="</li>",i[c]=!1),o[c]&&c>t&&(s+="</ul>",o[c]=!1);i[t]=!0,s+=r.$makeTocItem(e,!1,!1),a=t}else if(t===a)i[a]&&(s+="</li>"),s+=r.$makeTocItem(e,!1,!1),i[t]=!0,o[t]=!0;else{for(var l=a+1;l<=t;l++)s+="<ul>",o[l]=!0;i[t]=!0,s+=r.$makeTocItem(e,!1,!1),a=t}}));for(var c=a;c>=this.baseLevel;c--)i[c]&&(s+="</li>",i[c]=!1),o[c]&&(s+="</ul>",o[c]=!1);return s}},{key:"$makeToc",value:function(e,t,n){var r,a,i,o=tl(n,1),s=Vs(r=Vs(a=Vs(i='<div class="'.concat(this.tocContainerClass,'" data-sign="')).call(i,t,"-")).call(a,o,'" data-lines="')).call(r,o,'">');return s+='<p class="'.concat(this.tocTitleClass,'">目录</p>'),e.length<=0?"":(this.baseLevel=Math.min.apply(Math,Uf(Lc(e).call(e,(function(e){return e.level})))),"nested"===this.tocStyle?s+=this.$makeNestedToc(e):s+=this.$makePlainToc(e),s+="</div>")}},{key:"afterMakeHtml",value:function(e){var t=this,r=Fd(pi(n.prototype),"afterMakeHtml",this).call(this,e),a=[],i="";return r.replace(/<h([1-6])[^>]*? id="([^"]+?)"[^>]*?>(?:<a[^/]+?\/a>|)(.+?)<\/h\1>/g,(function(e,t,n,r){var o,s=r.replace(/~fn#[0-9]+#/g,"");a.push({level:+t,id:n,text:s}),i+=Vs(o="".concat(t)).call(o,n)})),i=this.$engine.md5(i),r=r.replace(/(?:^|\n)(\[\[|\[|【【)(toc|TOC)(\]\]|\]|】】)([<~])/,(function(e){return e.replace(/(\]\]|\]|】】)([<~])/,"$1\n$2")})),r=(r=r.replace(this.RULE.extend.reg,(function(e,n){return t.$makeToc(a,i,n)}))).replace(this.RULE.standard.reg,(function(e,n){return t.$makeToc(a,i,n)})),this.isFirstTocToken=!0,r}},{key:"test",value:function(e,t){return!!this.RULE[t].reg&&this.RULE[t].reg.test(e)}},{key:"rule",value:function(){var e={begin:"(?:^|\\n)(\\n*)",end:"(?=$|\\n)",content:"[ ]*((?:【【|\\[\\[)(?:toc|TOC)(?:\\]\\]|】】))[ ]*"};e.reg=new RegExp(e.begin+e.content+e.end,"g");var t={begin:"(?:^|\\n)(\\n*)",end:"(?=$|\\n)",content:"[ ]*(\\[(?:toc|TOC)\\])[ ]*"};return t.reg=new RegExp(t.begin+t.content+t.end,"g"),{extend:e,standard:t}}}]),n}();function mh(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(gh,"HOOK_NAME","toc");var bh=function(e){An(n,eu);var t=mh(n);function n(e){var r;return e.externals,e.config,mn(this,n),(r=t.call(this)).footnoteCache={},r.footnoteMap={},r.footnote=[],r}return gn(n,[{key:"$cleanCache",value:function(){this.footnoteCache={},this.footnoteMap={},this.footnote=[]}},{key:"pushFootnoteCache",value:function(e,t){this.footnoteCache[e]=t}},{key:"getFootnoteCache",value:function(e){return this.footnoteCache[e]||null}},{key:"pushFootNote",value:function(e,t){var n,r,a,i,o,s;if(this.footnoteMap[e])return this.footnoteMap[e];var c=this.footnote.length+1,l={};l.fn=Vs(n=Vs(r=Vs(a='<sup><a href="#fn:'.concat(c,'" id="fnref:')).call(a,c,'" title="')).call(r,e,'" class="footnote">[')).call(n,c,"]</a></sup>"),l.fnref=Vs(i=Vs(o=Vs(s='<a href="#fnref:'.concat(c,'" id="fn:')).call(s,c,'" title="')).call(o,e,'" class="footnote-ref">[')).call(i,c,"]</a>"),l.num=c,l.note=qc(t).call(t),this.footnote.push(l);var u="\0~fn#".concat(c-1,"#\0");return this.footnoteMap[e]=u,u}},{key:"getFootNote",value:function(){return this.footnote}},{key:"formatFootNote",value:function(){var e,t=this.getFootNote();if(t.length<=0)return"";var n=Lc(t).call(t,(function(e){var t;return Vs(t='<div class="one-footnote">\n'.concat(e.fnref)).call(t,e.note,"\n</div>")})).join(""),r=this.$engine.md5(n);return n=Vs(e='<div class="footnote" data-sign="'.concat(r,'" data-lines="0"><div class="footnote-title">脚注</div>')).call(e,n,"</div>")}},{key:"beforeMakeHtml",value:function(e){var t=this,n=e;return this.test(n)&&(n=n.replace(this.RULE.reg,(function(e,n,r,a){return t.pushFootnoteCache(r,a),(e.match(/\n/g)||[]).join("")})),n=n.replace(/\[\^([^\]]+?)\](?!:)/g,(function(e,n){var r=t.getFootnoteCache(n);return r?t.pushFootNote(n,r):e})),n+=this.formatFootNote()),n}},{key:"makeHtml",value:function(e,t){return e}},{key:"afterMakeHtml",value:function(e){var t=this.getFootNote(),n=e.replace(/\0~fn#([0-9]+)#\0/g,(function(e,n){return t[n].fn}));return this.$cleanCache(),n}},{key:"rule",value:function(){var e={begin:"(^|\\n)[ \t]*",content:["\\[\\^([^\\]]+?)\\]:\\h*","([\\s\\S]+?)"].join(""),end:"(?=\\s*$|\\n\\n)"};return e.reg=cf(e,"g",!0),e}}]),n}();function vh(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(bh,"HOOK_NAME","footnote");var yh=function(e){An(n,eu);var t=vh(n);function n(e){var r;return e.externals,e.config,mn(this,n),(r=t.call(this)).commentCache={},r}return gn(n,[{key:"$cleanCache",value:function(){this.commentCache={}}},{key:"pushCommentReferenceCache",value:function(e,t){var n,r=Gf(t.split(/[ ]+/g)),a=r[0],i=Kf(r).call(r,1),o=Cd.set(a);this.commentCache["".concat(e).toLowerCase()]=Vs(n=[o]).call(n,Uf(i)).join(" ")}},{key:"getCommentReferenceCache",value:function(e){return this.commentCache["".concat(e).toLowerCase()]||null}},{key:"beforeMakeHtml",value:function(e){var t=this,n=e;if(this.test(n)){n=n.replace(this.RULE.reg,(function(e,n,r,a){var i;return t.pushCommentReferenceCache(r,a),(null!==(i=e.match(/\n/g))&&void 0!==i?i:[]).join("")}));n=n.replace(/(\[[^\]\n]+?\])?(?:\[([^\]\n]+?)\])/g,(function(e,n,r){var a,i,o=t.getCommentReferenceCache(r);return o?n?Vs(i="".concat(n,"(")).call(i,o,")"):Vs(a="[".concat(r,"](")).call(a,o,")"):e})),this.$cleanCache()}return n}},{key:"makeHtml",value:function(e,t){return e}},{key:"afterMakeHtml",value:function(e){return Cd.restoreAll(e)}},{key:"rule",value:function(){var e={begin:"(^|\\n)[ \t]*",content:["\\[([^^][^\\]]*?)\\]:\\h*","([^\\n]+?)"].join(""),end:"(?=$|\\n)"};return e.reg=cf(e,"g",!0),e}}]),n}();hi(yh,"HOOK_NAME","commentReference");var _h=wr.some,kh=Js("some");Ve({target:"Array",proto:!0,forced:!kh},{some:function(e){return _h(this,e,arguments.length>1?arguments[1]:void 0)}});var wh=Ws("Array").some,Eh=Array.prototype,Sh=function(e){var t=e.some;return e===Eh||D(Eh,e)&&t===Eh.some?wh:t},Ah=t((function(e,t){e.exports=function(){const{entries:e,setPrototypeOf:t,isFrozen:n,getPrototypeOf:r,getOwnPropertyDescriptor:a}=Object;let{freeze:i,seal:o,create:s}=Object,{apply:c,construct:l}="undefined"!=typeof Reflect&&Reflect;i||(i=function(e){return e}),o||(o=function(e){return e}),c||(c=function(e,t,n){return e.apply(t,n)}),l||(l=function(e,t){return new e(...t)});const u=k(Array.prototype.forEach),f=k(Array.prototype.pop),d=k(Array.prototype.push),p=k(String.prototype.toLowerCase),h=k(String.prototype.toString),g=k(String.prototype.match),m=k(String.prototype.replace),b=k(String.prototype.indexOf),v=k(String.prototype.trim),y=k(RegExp.prototype.test),_=w(TypeError);function k(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return c(e,t,r)}}function w(e){return function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return l(e,n)}}function E(e,r){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:p;t&&t(e,null);let i=r.length;for(;i--;){let t=r[i];if("string"==typeof t){const e=a(t);e!==t&&(n(r)||(r[i]=e),t=e)}e[t]=!0}return e}function S(e){for(let t=0;t<e.length;t++)void 0===a(e,t)&&(e[t]=null);return e}function A(t){const n=s(null);for(const[r,i]of e(t))void 0!==a(t,r)&&(Array.isArray(i)?n[r]=S(i):i&&"object"==typeof i&&i.constructor===Object?n[r]=A(i):n[r]=i);return n}function x(e,t){for(;null!==e;){const n=a(e,t);if(n){if(n.get)return k(n.get);if("function"==typeof n.value)return k(n.value)}e=r(e)}function n(e){return console.warn("fallback value for",e),null}return n}const C=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),T=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),$=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),R=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),O=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),P=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),L=i(["#text"]),I=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),N=i(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),M=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),j=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),D=o(/\{\{[\w\W]*|[\w\W]*\}\}/gm),B=o(/<%[\w\W]*|[\w\W]*%>/gm),F=o(/\${[\w\W]*}/gm),H=o(/^data-[\-\w.\u00B7-\uFFFF]/),z=o(/^aria-[\-\w]+$/),U=o(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),W=o(/^(?:\w+script|data):/i),q=o(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),G=o(/^html$/i);var K=Object.freeze({__proto__:null,MUSTACHE_EXPR:D,ERB_EXPR:B,TMPLIT_EXPR:F,DATA_ATTR:H,ARIA_ATTR:z,IS_ALLOWED_URI:U,IS_SCRIPT_OR_DATA:W,ATTR_WHITESPACE:q,DOCTYPE_NAME:G});const Z=function(){return"undefined"==typeof window?null:window},Y=function(e,t){if("object"!=typeof e||"function"!=typeof e.createPolicy)return null;let n=null;const r="data-tt-policy-suffix";t&&t.hasAttribute(r)&&(n=t.getAttribute(r));const a="dompurify"+(n?"#"+n:"");try{return e.createPolicy(a,{createHTML:e=>e,createScriptURL:e=>e})}catch(e){return console.warn("TrustedTypes policy "+a+" could not be created."),null}};function X(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Z();const n=e=>X(e);if(n.version="3.0.8",n.removed=[],!t||!t.document||9!==t.document.nodeType)return n.isSupported=!1,n;let{document:r}=t;const a=r,o=a.currentScript,{DocumentFragment:c,HTMLTemplateElement:l,Node:k,Element:w,NodeFilter:S,NamedNodeMap:D=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:B,DOMParser:F,trustedTypes:H}=t,z=w.prototype,W=x(z,"cloneNode"),q=x(z,"nextSibling"),V=x(z,"childNodes"),J=x(z,"parentNode");if("function"==typeof l){const e=r.createElement("template");e.content&&e.content.ownerDocument&&(r=e.content.ownerDocument)}let Q,ee="";const{implementation:te,createNodeIterator:ne,createDocumentFragment:re,getElementsByTagName:ae}=r,{importNode:ie}=a;let oe={};n.isSupported="function"==typeof e&&"function"==typeof J&&te&&void 0!==te.createHTMLDocument;const{MUSTACHE_EXPR:se,ERB_EXPR:ce,TMPLIT_EXPR:le,DATA_ATTR:ue,ARIA_ATTR:fe,IS_SCRIPT_OR_DATA:de,ATTR_WHITESPACE:pe}=K;let{IS_ALLOWED_URI:he}=K,ge=null;const me=E({},[...C,...T,...$,...O,...L]);let be=null;const ve=E({},[...I,...N,...M,...j]);let ye=Object.seal(s(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),_e=null,ke=null,we=!0,Ee=!0,Se=!1,Ae=!0,xe=!1,Ce=!1,Te=!1,$e=!1,Re=!1,Oe=!1,Pe=!1,Le=!0,Ie=!1;const Ne="user-content-";let Me=!0,je=!1,De={},Be=null;const Fe=E({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let He=null;const ze=E({},["audio","video","img","source","image","track"]);let Ue=null;const We=E({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),qe="http://www.w3.org/1998/Math/MathML",Ge="http://www.w3.org/2000/svg",Ke="http://www.w3.org/1999/xhtml";let Ze=Ke,Ye=!1,Xe=null;const Ve=E({},[qe,Ge,Ke],h);let Je=null;const Qe=["application/xhtml+xml","text/html"],et="text/html";let tt=null,nt=null;const rt=r.createElement("form"),at=function(e){return e instanceof RegExp||e instanceof Function},it=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!nt||nt!==e){if(e&&"object"==typeof e||(e={}),e=A(e),Je=-1===Qe.indexOf(e.PARSER_MEDIA_TYPE)?et:e.PARSER_MEDIA_TYPE,tt="application/xhtml+xml"===Je?h:p,ge="ALLOWED_TAGS"in e?E({},e.ALLOWED_TAGS,tt):me,be="ALLOWED_ATTR"in e?E({},e.ALLOWED_ATTR,tt):ve,Xe="ALLOWED_NAMESPACES"in e?E({},e.ALLOWED_NAMESPACES,h):Ve,Ue="ADD_URI_SAFE_ATTR"in e?E(A(We),e.ADD_URI_SAFE_ATTR,tt):We,He="ADD_DATA_URI_TAGS"in e?E(A(ze),e.ADD_DATA_URI_TAGS,tt):ze,Be="FORBID_CONTENTS"in e?E({},e.FORBID_CONTENTS,tt):Fe,_e="FORBID_TAGS"in e?E({},e.FORBID_TAGS,tt):{},ke="FORBID_ATTR"in e?E({},e.FORBID_ATTR,tt):{},De="USE_PROFILES"in e&&e.USE_PROFILES,we=!1!==e.ALLOW_ARIA_ATTR,Ee=!1!==e.ALLOW_DATA_ATTR,Se=e.ALLOW_UNKNOWN_PROTOCOLS||!1,Ae=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,xe=e.SAFE_FOR_TEMPLATES||!1,Ce=e.WHOLE_DOCUMENT||!1,Re=e.RETURN_DOM||!1,Oe=e.RETURN_DOM_FRAGMENT||!1,Pe=e.RETURN_TRUSTED_TYPE||!1,$e=e.FORCE_BODY||!1,Le=!1!==e.SANITIZE_DOM,Ie=e.SANITIZE_NAMED_PROPS||!1,Me=!1!==e.KEEP_CONTENT,je=e.IN_PLACE||!1,he=e.ALLOWED_URI_REGEXP||U,Ze=e.NAMESPACE||Ke,ye=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&at(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(ye.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&at(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(ye.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(ye.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),xe&&(Ee=!1),Oe&&(Re=!0),De&&(ge=E({},L),be=[],!0===De.html&&(E(ge,C),E(be,I)),!0===De.svg&&(E(ge,T),E(be,N),E(be,j)),!0===De.svgFilters&&(E(ge,$),E(be,N),E(be,j)),!0===De.mathMl&&(E(ge,O),E(be,M),E(be,j))),e.ADD_TAGS&&(ge===me&&(ge=A(ge)),E(ge,e.ADD_TAGS,tt)),e.ADD_ATTR&&(be===ve&&(be=A(be)),E(be,e.ADD_ATTR,tt)),e.ADD_URI_SAFE_ATTR&&E(Ue,e.ADD_URI_SAFE_ATTR,tt),e.FORBID_CONTENTS&&(Be===Fe&&(Be=A(Be)),E(Be,e.FORBID_CONTENTS,tt)),Me&&(ge["#text"]=!0),Ce&&E(ge,["html","head","body"]),ge.table&&(E(ge,["tbody"]),delete _e.tbody),e.TRUSTED_TYPES_POLICY){if("function"!=typeof e.TRUSTED_TYPES_POLICY.createHTML)throw _('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!=typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw _('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');Q=e.TRUSTED_TYPES_POLICY,ee=Q.createHTML("")}else void 0===Q&&(Q=Y(H,o)),null!==Q&&"string"==typeof ee&&(ee=Q.createHTML(""));i&&i(e),nt=e}},ot=E({},["mi","mo","mn","ms","mtext"]),st=E({},["foreignobject","desc","title","annotation-xml"]),ct=E({},["title","style","font","a","script"]),lt=E({},[...T,...$,...R]),ut=E({},[...O,...P]),ft=function(e){let t=J(e);t&&t.tagName||(t={namespaceURI:Ze,tagName:"template"});const n=p(e.tagName),r=p(t.tagName);return!!Xe[e.namespaceURI]&&(e.namespaceURI===Ge?t.namespaceURI===Ke?"svg"===n:t.namespaceURI===qe?"svg"===n&&("annotation-xml"===r||ot[r]):Boolean(lt[n]):e.namespaceURI===qe?t.namespaceURI===Ke?"math"===n:t.namespaceURI===Ge?"math"===n&&st[r]:Boolean(ut[n]):e.namespaceURI===Ke?!(t.namespaceURI===Ge&&!st[r])&&!(t.namespaceURI===qe&&!ot[r])&&!ut[n]&&(ct[n]||!lt[n]):!("application/xhtml+xml"!==Je||!Xe[e.namespaceURI]))},dt=function(e){d(n.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},pt=function(e,t){try{d(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){d(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!be[e])if(Re||Oe)try{dt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},ht=function(e){let t=null,n=null;if($e)e="<remove></remove>"+e;else{const t=g(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===Je&&Ze===Ke&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const a=Q?Q.createHTML(e):e;if(Ze===Ke)try{t=(new F).parseFromString(a,Je)}catch(e){}if(!t||!t.documentElement){t=te.createDocument(Ze,"template",null);try{t.documentElement.innerHTML=Ye?ee:a}catch(e){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(r.createTextNode(n),i.childNodes[0]||null),Ze===Ke?ae.call(t,Ce?"html":"body")[0]:Ce?t.documentElement:i},gt=function(e){return ne.call(e.ownerDocument||e,e,S.SHOW_ELEMENT|S.SHOW_COMMENT|S.SHOW_TEXT,null)},mt=function(e){return e instanceof B&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof D)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes)},bt=function(e){return"function"==typeof k&&e instanceof k},vt=function(e,t,r){oe[e]&&u(oe[e],(e=>{e.call(n,t,r,nt)}))},yt=function(e){let t=null;if(vt("beforeSanitizeElements",e,null),mt(e))return dt(e),!0;const r=tt(e.nodeName);if(vt("uponSanitizeElement",e,{tagName:r,allowedTags:ge}),e.hasChildNodes()&&!bt(e.firstElementChild)&&y(/<[/\w]/g,e.innerHTML)&&y(/<[/\w]/g,e.textContent))return dt(e),!0;if(!ge[r]||_e[r]){if(!_e[r]&&kt(r)){if(ye.tagNameCheck instanceof RegExp&&y(ye.tagNameCheck,r))return!1;if(ye.tagNameCheck instanceof Function&&ye.tagNameCheck(r))return!1}if(Me&&!Be[r]){const t=J(e)||e.parentNode,n=V(e)||e.childNodes;if(n&&t)for(let r=n.length-1;r>=0;--r)t.insertBefore(W(n[r],!0),q(e))}return dt(e),!0}return e instanceof w&&!ft(e)?(dt(e),!0):"noscript"!==r&&"noembed"!==r&&"noframes"!==r||!y(/<\/no(script|embed|frames)/i,e.innerHTML)?(xe&&3===e.nodeType&&(t=e.textContent,u([se,ce,le],(e=>{t=m(t,e," ")})),e.textContent!==t&&(d(n.removed,{element:e.cloneNode()}),e.textContent=t)),vt("afterSanitizeElements",e,null),!1):(dt(e),!0)},_t=function(e,t,n){if(Le&&("id"===t||"name"===t)&&(n in r||n in rt))return!1;if(Ee&&!ke[t]&&y(ue,t));else if(we&&y(fe,t));else if(!be[t]||ke[t]){if(!(kt(e)&&(ye.tagNameCheck instanceof RegExp&&y(ye.tagNameCheck,e)||ye.tagNameCheck instanceof Function&&ye.tagNameCheck(e))&&(ye.attributeNameCheck instanceof RegExp&&y(ye.attributeNameCheck,t)||ye.attributeNameCheck instanceof Function&&ye.attributeNameCheck(t))||"is"===t&&ye.allowCustomizedBuiltInElements&&(ye.tagNameCheck instanceof RegExp&&y(ye.tagNameCheck,n)||ye.tagNameCheck instanceof Function&&ye.tagNameCheck(n))))return!1}else if(Ue[t]);else if(y(he,m(n,pe,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==b(n,"data:")||!He[e])if(Se&&!y(de,m(n,pe,"")));else if(n)return!1;return!0},kt=function(e){return e.indexOf("-")>0},wt=function(e){vt("beforeSanitizeAttributes",e,null);const{attributes:t}=e;if(!t)return;const r={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:be};let a=t.length;for(;a--;){const i=t[a],{name:o,namespaceURI:s,value:c}=i,l=tt(o);let d="value"===o?c:v(c);if(r.attrName=l,r.attrValue=d,r.keepAttr=!0,r.forceKeepAttr=void 0,vt("uponSanitizeAttribute",e,r),d=r.attrValue,r.forceKeepAttr)continue;if(pt(o,e),!r.keepAttr)continue;if(!Ae&&y(/\/>/i,d)){pt(o,e);continue}xe&&u([se,ce,le],(e=>{d=m(d,e," ")}));const p=tt(e.nodeName);if(_t(p,l,d)){if(!Ie||"id"!==l&&"name"!==l||(pt(o,e),d=Ne+d),Q&&"object"==typeof H&&"function"==typeof H.getAttributeType)if(s);else switch(H.getAttributeType(p,l)){case"TrustedHTML":d=Q.createHTML(d);break;case"TrustedScriptURL":d=Q.createScriptURL(d)}try{s?e.setAttributeNS(s,o,d):e.setAttribute(o,d),f(n.removed)}catch(e){}}}vt("afterSanitizeAttributes",e,null)},Et=function e(t){let n=null;const r=gt(t);for(vt("beforeSanitizeShadowDOM",t,null);n=r.nextNode();)vt("uponSanitizeShadowNode",n,null),yt(n)||(n.content instanceof c&&e(n.content),wt(n));vt("afterSanitizeShadowDOM",t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=null,i=null,o=null,s=null;if(Ye=!e,Ye&&(e="\x3c!--\x3e"),"string"!=typeof e&&!bt(e)){if("function"!=typeof e.toString)throw _("toString is not a function");if("string"!=typeof(e=e.toString()))throw _("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Te||it(t),n.removed=[],"string"==typeof e&&(je=!1),je){if(e.nodeName){const t=tt(e.nodeName);if(!ge[t]||_e[t])throw _("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof k)r=ht("\x3c!----\x3e"),i=r.ownerDocument.importNode(e,!0),1===i.nodeType&&"BODY"===i.nodeName||"HTML"===i.nodeName?r=i:r.appendChild(i);else{if(!Re&&!xe&&!Ce&&-1===e.indexOf("<"))return Q&&Pe?Q.createHTML(e):e;if(r=ht(e),!r)return Re?null:Pe?ee:""}r&&$e&&dt(r.firstChild);const l=gt(je?e:r);for(;o=l.nextNode();)yt(o)||(o.content instanceof c&&Et(o.content),wt(o));if(je)return e;if(Re){if(Oe)for(s=re.call(r.ownerDocument);r.firstChild;)s.appendChild(r.firstChild);else s=r;return(be.shadowroot||be.shadowrootmode)&&(s=ie.call(a,s,!0)),s}let f=Ce?r.outerHTML:r.innerHTML;return Ce&&ge["!doctype"]&&r.ownerDocument&&r.ownerDocument.doctype&&r.ownerDocument.doctype.name&&y(G,r.ownerDocument.doctype.name)&&(f="<!DOCTYPE "+r.ownerDocument.doctype.name+">\n"+f),xe&&u([se,ce,le],(e=>{f=m(f,e," ")})),Q&&Pe?Q.createHTML(f):f},n.setConfig=function(){it(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}),Te=!0},n.clearConfig=function(){nt=null,Te=!1},n.isValidAttribute=function(e,t,n){nt||it({});const r=tt(e),a=tt(t);return _t(r,a,n)},n.addHook=function(e,t){"function"==typeof t&&(oe[e]=oe[e]||[],d(oe[e],t))},n.removeHook=function(e){if(oe[e])return f(oe[e])},n.removeHooks=function(e){oe[e]&&(oe[e]=[])},n.removeAllHooks=function(){oe={}},n}var V=X();return V}()})),xh=Ah(window);function Ch(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var Th=["href","src"];xh.addHook("afterSanitizeAttributes",(function(e){ac(Th).call(Th,(function(t){if(e.hasAttribute(t)){var n=e.getAttribute(t);e.setAttribute(t,n.replace(/\\/g,"%5c"))}}))}));var $h=function(e){An(n,eu);var t=Ch(n);function n(){return mn(this,n),t.call(this,{needCache:!0})}return gn(n,[{key:"isAutoLinkTag",value:function(e){var t=[/^<([a-z][a-z0-9+.-]{1,31}:\/\/[^<> `]+)>$/i,/^<(mailto:[^<> `]+)>$/i,/^<([^()<>[\]:'@\\,"\s`]+@[^()<>[\]:'@\\,"\s`.]+\.[^()<>[\]:'@\\,"\s`]+)>$/i];return Sh(t).call(t,(function(t){return t.test(e)}))}},{key:"isHtmlComment",value:function(e){return/^<!--.*?-->$/.test(e)}},{key:"beforeMakeHtml",value:function(e,t){var n=this;this.$engine.htmlWhiteListAppend?(this.htmlWhiteListAppend=new RegExp("^(".concat(this.$engine.htmlWhiteListAppend,")( |$|/)"),"i"),this.htmlWhiteList=this.$engine.htmlWhiteListAppend.split("|")):(this.htmlWhiteListAppend=!1,this.htmlWhiteList=[]);var r=e;return r=function(e){if("string"!=typeof e)return"";var t=e.replace(/&(\w+);?/g,(function(e,t){return-1===Il(e).call(e,";")||-1===Il(Hl).call(Hl,t.toLowerCase())?e.replace(/&/g,"&amp;"):e}));return t=t.replace(/&#(?!x)(\d*);?/gi,(function(e,t){return zl(t)||-1===Il(e).call(e,";")||t.lenth>7||!Ul(t)?e.replace(/&/g,"&amp;"):e})),t=t.replace(/&#x([0-9a-f]*);?/gi,(function(e,t){if(zl(t))return e.replace(/&/g,"&amp;");var n="0x".concat(t),r=Qc(n,16);return isNaN(r)||-1===Il(e).call(e,";")||t.lenth>6||!Ul(n)?e.replace(/&/g,"&amp;"):e})),t}(r=Yl(r)),r=(r=(r=r.replace(/<[/]?(.*?)>/g,(function(e,t){return Gl.test(t)||n.isAutoLinkTag(e)||n.isHtmlComment(e)||!1!==n.htmlWhiteListAppend&&n.htmlWhiteListAppend.test(t)?e.replace(/</g,"$#60;").replace(/>/g,"$#62;"):e.replace(/</g,"&#60;").replace(/>/g,"&#62;")}))).replace(/<(?=\/?(\w|\n|$))/g,"&#60;")).replace(/\$#60;/g,"<").replace(/\$#62;/g,">")}},{key:"makeHtml",value:function(e,t){return e}},{key:"afterMakeHtml",value:function(e){var t=e,n={ALLOW_UNKNOWN_PROTOCOLS:!0,ADD_ATTR:["target"]};if(!1!==this.htmlWhiteListAppend){var r;if(n.ADD_TAGS=this.htmlWhiteList,(this.htmlWhiteListAppend.test("style")||this.htmlWhiteListAppend.test("ALL"))&&(t=t.replace(/<style(>| [^>]*>).*?<\/style>/gi,(function(e){return e.replace(/<br>/gi,"")}))),this.htmlWhiteListAppend.test("iframe")||this.htmlWhiteListAppend.test("ALL"))n.ADD_ATTR=Vs(r=n.ADD_ATTR).call(r,["align","frameborder","height","longdesc","marginheight","marginwidth","name","sandbox","scrolling","seamless","src","srcdoc","width"]),n.SANITIZE_DOM=!1,t=t.replace(/<iframe(>| [^>]*>).*?<\/iframe>/gi,(function(e){return e.replace(/<br>/gi,"").replace(/\n/g,"")}));if(this.htmlWhiteListAppend.test("script")||this.htmlWhiteListAppend.test("ALL"))return t=t.replace(/<script(>| [^>]*>).*?<\/script>/gi,(function(e){return e.replace(/<br>/gi,"")})),t}return fp()||(n.FORBID_ATTR=["data-sign","data-lines"]),xh.sanitize(t,n)}}]),n}();hi($h,"HOOK_NAME","htmlBlock");var Rh={"+1":"1f44d","-1":"1f44e",100:"1f4af",1234:"1f522","1st_place_medal":"1f947","2nd_place_medal":"1f948","3rd_place_medal":"1f949","8ball":"1f3b1",a:"1f170",ab:"1f18e",abacus:"1f9ee",abc:"1f524",abcd:"1f521",accept:"1f251",adhesive_bandage:"1fa79",adult:"1f9d1",aerial_tramway:"1f6a1",afghanistan:"1f1e6-1f1eb",airplane:"2708",aland_islands:"1f1e6-1f1fd",alarm_clock:"23f0",albania:"1f1e6-1f1f1",alembic:"2697",algeria:"1f1e9-1f1ff",alien:"1f47d",ambulance:"1f691",american_samoa:"1f1e6-1f1f8",amphora:"1f3fa",anchor:"2693",andorra:"1f1e6-1f1e9",angel:"1f47c",anger:"1f4a2",angola:"1f1e6-1f1f4",angry:"1f620",anguilla:"1f1e6-1f1ee",anguished:"1f627",ant:"1f41c",antarctica:"1f1e6-1f1f6",antigua_barbuda:"1f1e6-1f1ec",apple:"1f34e",aquarius:"2652",argentina:"1f1e6-1f1f7",aries:"2648",armenia:"1f1e6-1f1f2",arrow_backward:"25c0",arrow_double_down:"23ec",arrow_double_up:"23eb",arrow_down:"2b07",arrow_down_small:"1f53d",arrow_forward:"25b6",arrow_heading_down:"2935",arrow_heading_up:"2934",arrow_left:"2b05",arrow_lower_left:"2199",arrow_lower_right:"2198",arrow_right:"27a1",arrow_right_hook:"21aa",arrow_up:"2b06",arrow_up_down:"2195",arrow_up_small:"1f53c",arrow_upper_left:"2196",arrow_upper_right:"2197",arrows_clockwise:"1f503",arrows_counterclockwise:"1f504",art:"1f3a8",articulated_lorry:"1f69b",artificial_satellite:"1f6f0",artist:"1f9d1-1f3a8",aruba:"1f1e6-1f1fc",ascension_island:"1f1e6-1f1e8",asterisk:"002a-20e3",astonished:"1f632",astronaut:"1f9d1-1f680",athletic_shoe:"1f45f",atm:"1f3e7",atom_symbol:"269b",australia:"1f1e6-1f1fa",austria:"1f1e6-1f1f9",auto_rickshaw:"1f6fa",avocado:"1f951",axe:"1fa93",azerbaijan:"1f1e6-1f1ff",b:"1f171",baby:"1f476",baby_bottle:"1f37c",baby_chick:"1f424",baby_symbol:"1f6bc",back:"1f519",bacon:"1f953",badger:"1f9a1",badminton:"1f3f8",bagel:"1f96f",baggage_claim:"1f6c4",baguette_bread:"1f956",bahamas:"1f1e7-1f1f8",bahrain:"1f1e7-1f1ed",balance_scale:"2696",bald_man:"1f468-1f9b2",bald_woman:"1f469-1f9b2",ballet_shoes:"1fa70",balloon:"1f388",ballot_box:"1f5f3",ballot_box_with_check:"2611",bamboo:"1f38d",banana:"1f34c",bangbang:"203c",bangladesh:"1f1e7-1f1e9",banjo:"1fa95",bank:"1f3e6",bar_chart:"1f4ca",barbados:"1f1e7-1f1e7",barber:"1f488",baseball:"26be",basket:"1f9fa",basketball:"1f3c0",basketball_man:"26f9-2642",basketball_woman:"26f9-2640",bat:"1f987",bath:"1f6c0",bathtub:"1f6c1",battery:"1f50b",beach_umbrella:"1f3d6",bear:"1f43b",bearded_person:"1f9d4",bed:"1f6cf",bee:"1f41d",beer:"1f37a",beers:"1f37b",beetle:"1f41e",beginner:"1f530",belarus:"1f1e7-1f1fe",belgium:"1f1e7-1f1ea",belize:"1f1e7-1f1ff",bell:"1f514",bellhop_bell:"1f6ce",benin:"1f1e7-1f1ef",bento:"1f371",bermuda:"1f1e7-1f1f2",beverage_box:"1f9c3",bhutan:"1f1e7-1f1f9",bicyclist:"1f6b4",bike:"1f6b2",biking_man:"1f6b4-2642",biking_woman:"1f6b4-2640",bikini:"1f459",billed_cap:"1f9e2",biohazard:"2623",bird:"1f426",birthday:"1f382",black_circle:"26ab",black_flag:"1f3f4",black_heart:"1f5a4",black_joker:"1f0cf",black_large_square:"2b1b",black_medium_small_square:"25fe",black_medium_square:"25fc",black_nib:"2712",black_small_square:"25aa",black_square_button:"1f532",blond_haired_man:"1f471-2642",blond_haired_person:"1f471",blond_haired_woman:"1f471-2640",blonde_woman:"1f471-2640",blossom:"1f33c",blowfish:"1f421",blue_book:"1f4d8",blue_car:"1f699",blue_heart:"1f499",blue_square:"1f7e6",blush:"1f60a",boar:"1f417",boat:"26f5",bolivia:"1f1e7-1f1f4",bomb:"1f4a3",bone:"1f9b4",book:"1f4d6",bookmark:"1f516",bookmark_tabs:"1f4d1",books:"1f4da",boom:"1f4a5",boot:"1f462",bosnia_herzegovina:"1f1e7-1f1e6",botswana:"1f1e7-1f1fc",bouncing_ball_man:"26f9-2642",bouncing_ball_person:"26f9",bouncing_ball_woman:"26f9-2640",bouquet:"1f490",bouvet_island:"1f1e7-1f1fb",bow:"1f647",bow_and_arrow:"1f3f9",bowing_man:"1f647-2642",bowing_woman:"1f647-2640",bowl_with_spoon:"1f963",bowling:"1f3b3",boxing_glove:"1f94a",boy:"1f466",brain:"1f9e0",brazil:"1f1e7-1f1f7",bread:"1f35e",breast_feeding:"1f931",bricks:"1f9f1",bride_with_veil:"1f470",bridge_at_night:"1f309",briefcase:"1f4bc",british_indian_ocean_territory:"1f1ee-1f1f4",british_virgin_islands:"1f1fb-1f1ec",broccoli:"1f966",broken_heart:"1f494",broom:"1f9f9",brown_circle:"1f7e4",brown_heart:"1f90e",brown_square:"1f7eb",brunei:"1f1e7-1f1f3",bug:"1f41b",building_construction:"1f3d7",bulb:"1f4a1",bulgaria:"1f1e7-1f1ec",bullettrain_front:"1f685",bullettrain_side:"1f684",burkina_faso:"1f1e7-1f1eb",burrito:"1f32f",burundi:"1f1e7-1f1ee",bus:"1f68c",business_suit_levitating:"1f574",busstop:"1f68f",bust_in_silhouette:"1f464",busts_in_silhouette:"1f465",butter:"1f9c8",butterfly:"1f98b",cactus:"1f335",cake:"1f370",calendar:"1f4c6",call_me_hand:"1f919",calling:"1f4f2",cambodia:"1f1f0-1f1ed",camel:"1f42b",camera:"1f4f7",camera_flash:"1f4f8",cameroon:"1f1e8-1f1f2",camping:"1f3d5",canada:"1f1e8-1f1e6",canary_islands:"1f1ee-1f1e8",cancer:"264b",candle:"1f56f",candy:"1f36c",canned_food:"1f96b",canoe:"1f6f6",cape_verde:"1f1e8-1f1fb",capital_abcd:"1f520",capricorn:"2651",car:"1f697",card_file_box:"1f5c3",card_index:"1f4c7",card_index_dividers:"1f5c2",caribbean_netherlands:"1f1e7-1f1f6",carousel_horse:"1f3a0",carrot:"1f955",cartwheeling:"1f938",cat:"1f431",cat2:"1f408",cayman_islands:"1f1f0-1f1fe",cd:"1f4bf",central_african_republic:"1f1e8-1f1eb",ceuta_melilla:"1f1ea-1f1e6",chad:"1f1f9-1f1e9",chains:"26d3",chair:"1fa91",champagne:"1f37e",chart:"1f4b9",chart_with_downwards_trend:"1f4c9",chart_with_upwards_trend:"1f4c8",checkered_flag:"1f3c1",cheese:"1f9c0",cherries:"1f352",cherry_blossom:"1f338",chess_pawn:"265f",chestnut:"1f330",chicken:"1f414",child:"1f9d2",children_crossing:"1f6b8",chile:"1f1e8-1f1f1",chipmunk:"1f43f",chocolate_bar:"1f36b",chopsticks:"1f962",christmas_island:"1f1e8-1f1fd",christmas_tree:"1f384",church:"26ea",cinema:"1f3a6",circus_tent:"1f3aa",city_sunrise:"1f307",city_sunset:"1f306",cityscape:"1f3d9",cl:"1f191",clamp:"1f5dc",clap:"1f44f",clapper:"1f3ac",classical_building:"1f3db",climbing:"1f9d7",climbing_man:"1f9d7-2642",climbing_woman:"1f9d7-2640",clinking_glasses:"1f942",clipboard:"1f4cb",clipperton_island:"1f1e8-1f1f5",clock1:"1f550",clock10:"1f559",clock1030:"1f565",clock11:"1f55a",clock1130:"1f566",clock12:"1f55b",clock1230:"1f567",clock130:"1f55c",clock2:"1f551",clock230:"1f55d",clock3:"1f552",clock330:"1f55e",clock4:"1f553",clock430:"1f55f",clock5:"1f554",clock530:"1f560",clock6:"1f555",clock630:"1f561",clock7:"1f556",clock730:"1f562",clock8:"1f557",clock830:"1f563",clock9:"1f558",clock930:"1f564",closed_book:"1f4d5",closed_lock_with_key:"1f510",closed_umbrella:"1f302",cloud:"2601",cloud_with_lightning:"1f329",cloud_with_lightning_and_rain:"26c8",cloud_with_rain:"1f327",cloud_with_snow:"1f328",clown_face:"1f921",clubs:"2663",cn:"1f1e8-1f1f3",coat:"1f9e5",cocktail:"1f378",coconut:"1f965",cocos_islands:"1f1e8-1f1e8",coffee:"2615",coffin:"26b0",cold_face:"1f976",cold_sweat:"1f630",collision:"1f4a5",colombia:"1f1e8-1f1f4",comet:"2604",comoros:"1f1f0-1f1f2",compass:"1f9ed",computer:"1f4bb",computer_mouse:"1f5b1",confetti_ball:"1f38a",confounded:"1f616",confused:"1f615",congo_brazzaville:"1f1e8-1f1ec",congo_kinshasa:"1f1e8-1f1e9",congratulations:"3297",construction:"1f6a7",construction_worker:"1f477",construction_worker_man:"1f477-2642",construction_worker_woman:"1f477-2640",control_knobs:"1f39b",convenience_store:"1f3ea",cook:"1f9d1-1f373",cook_islands:"1f1e8-1f1f0",cookie:"1f36a",cool:"1f192",cop:"1f46e",copyright:"00a9",corn:"1f33d",costa_rica:"1f1e8-1f1f7",cote_divoire:"1f1e8-1f1ee",couch_and_lamp:"1f6cb",couple:"1f46b",couple_with_heart:"1f491",couple_with_heart_man_man:"1f468-2764-1f468",couple_with_heart_woman_man:"1f469-2764-1f468",couple_with_heart_woman_woman:"1f469-2764-1f469",couplekiss:"1f48f",couplekiss_man_man:"1f468-2764-1f48b-1f468",couplekiss_man_woman:"1f469-2764-1f48b-1f468",couplekiss_woman_woman:"1f469-2764-1f48b-1f469",cow:"1f42e",cow2:"1f404",cowboy_hat_face:"1f920",crab:"1f980",crayon:"1f58d",credit_card:"1f4b3",crescent_moon:"1f319",cricket:"1f997",cricket_game:"1f3cf",croatia:"1f1ed-1f1f7",crocodile:"1f40a",croissant:"1f950",crossed_fingers:"1f91e",crossed_flags:"1f38c",crossed_swords:"2694",crown:"1f451",cry:"1f622",crying_cat_face:"1f63f",crystal_ball:"1f52e",cuba:"1f1e8-1f1fa",cucumber:"1f952",cup_with_straw:"1f964",cupcake:"1f9c1",cupid:"1f498",curacao:"1f1e8-1f1fc",curling_stone:"1f94c",curly_haired_man:"1f468-1f9b1",curly_haired_woman:"1f469-1f9b1",curly_loop:"27b0",currency_exchange:"1f4b1",curry:"1f35b",cursing_face:"1f92c",custard:"1f36e",customs:"1f6c3",cut_of_meat:"1f969",cyclone:"1f300",cyprus:"1f1e8-1f1fe",czech_republic:"1f1e8-1f1ff",dagger:"1f5e1",dancer:"1f483",dancers:"1f46f",dancing_men:"1f46f-2642",dancing_women:"1f46f-2640",dango:"1f361",dark_sunglasses:"1f576",dart:"1f3af",dash:"1f4a8",date:"1f4c5",de:"1f1e9-1f1ea",deaf_man:"1f9cf-2642",deaf_person:"1f9cf",deaf_woman:"1f9cf-2640",deciduous_tree:"1f333",deer:"1f98c",denmark:"1f1e9-1f1f0",department_store:"1f3ec",derelict_house:"1f3da",desert:"1f3dc",desert_island:"1f3dd",desktop_computer:"1f5a5",detective:"1f575",diamond_shape_with_a_dot_inside:"1f4a0",diamonds:"2666",diego_garcia:"1f1e9-1f1ec",disappointed:"1f61e",disappointed_relieved:"1f625",diving_mask:"1f93f",diya_lamp:"1fa94",dizzy:"1f4ab",dizzy_face:"1f635",djibouti:"1f1e9-1f1ef",dna:"1f9ec",do_not_litter:"1f6af",dog:"1f436",dog2:"1f415",dollar:"1f4b5",dolls:"1f38e",dolphin:"1f42c",dominica:"1f1e9-1f1f2",dominican_republic:"1f1e9-1f1f4",door:"1f6aa",doughnut:"1f369",dove:"1f54a",dragon:"1f409",dragon_face:"1f432",dress:"1f457",dromedary_camel:"1f42a",drooling_face:"1f924",drop_of_blood:"1fa78",droplet:"1f4a7",drum:"1f941",duck:"1f986",dumpling:"1f95f",dvd:"1f4c0","e-mail":"1f4e7",eagle:"1f985",ear:"1f442",ear_of_rice:"1f33e",ear_with_hearing_aid:"1f9bb",earth_africa:"1f30d",earth_americas:"1f30e",earth_asia:"1f30f",ecuador:"1f1ea-1f1e8",egg:"1f95a",eggplant:"1f346",egypt:"1f1ea-1f1ec",eight:"0038-20e3",eight_pointed_black_star:"2734",eight_spoked_asterisk:"2733",eject_button:"23cf",el_salvador:"1f1f8-1f1fb",electric_plug:"1f50c",elephant:"1f418",elf:"1f9dd",elf_man:"1f9dd-2642",elf_woman:"1f9dd-2640",email:"2709",end:"1f51a",england:"1f3f4-e0067-e0062-e0065-e006e-e0067-e007f",envelope:"2709",envelope_with_arrow:"1f4e9",equatorial_guinea:"1f1ec-1f1f6",eritrea:"1f1ea-1f1f7",es:"1f1ea-1f1f8",estonia:"1f1ea-1f1ea",ethiopia:"1f1ea-1f1f9",eu:"1f1ea-1f1fa",euro:"1f4b6",european_castle:"1f3f0",european_post_office:"1f3e4",european_union:"1f1ea-1f1fa",evergreen_tree:"1f332",exclamation:"2757",exploding_head:"1f92f",expressionless:"1f611",eye:"1f441",eye_speech_bubble:"1f441-1f5e8",eyeglasses:"1f453",eyes:"1f440",face_with_head_bandage:"1f915",face_with_thermometer:"1f912",facepalm:"1f926",facepunch:"1f44a",factory:"1f3ed",factory_worker:"1f9d1-1f3ed",fairy:"1f9da",fairy_man:"1f9da-2642",fairy_woman:"1f9da-2640",falafel:"1f9c6",falkland_islands:"1f1eb-1f1f0",fallen_leaf:"1f342",family:"1f46a",family_man_boy:"1f468-1f466",family_man_boy_boy:"1f468-1f466-1f466",family_man_girl:"1f468-1f467",family_man_girl_boy:"1f468-1f467-1f466",family_man_girl_girl:"1f468-1f467-1f467",family_man_man_boy:"1f468-1f468-1f466",family_man_man_boy_boy:"1f468-1f468-1f466-1f466",family_man_man_girl:"1f468-1f468-1f467",family_man_man_girl_boy:"1f468-1f468-1f467-1f466",family_man_man_girl_girl:"1f468-1f468-1f467-1f467",family_man_woman_boy:"1f468-1f469-1f466",family_man_woman_boy_boy:"1f468-1f469-1f466-1f466",family_man_woman_girl:"1f468-1f469-1f467",family_man_woman_girl_boy:"1f468-1f469-1f467-1f466",family_man_woman_girl_girl:"1f468-1f469-1f467-1f467",family_woman_boy:"1f469-1f466",family_woman_boy_boy:"1f469-1f466-1f466",family_woman_girl:"1f469-1f467",family_woman_girl_boy:"1f469-1f467-1f466",family_woman_girl_girl:"1f469-1f467-1f467",family_woman_woman_boy:"1f469-1f469-1f466",family_woman_woman_boy_boy:"1f469-1f469-1f466-1f466",family_woman_woman_girl:"1f469-1f469-1f467",family_woman_woman_girl_boy:"1f469-1f469-1f467-1f466",family_woman_woman_girl_girl:"1f469-1f469-1f467-1f467",farmer:"1f9d1-1f33e",faroe_islands:"1f1eb-1f1f4",fast_forward:"23e9",fax:"1f4e0",fearful:"1f628",feet:"1f43e",female_detective:"1f575-2640",female_sign:"2640",ferris_wheel:"1f3a1",ferry:"26f4",field_hockey:"1f3d1",fiji:"1f1eb-1f1ef",file_cabinet:"1f5c4",file_folder:"1f4c1",film_projector:"1f4fd",film_strip:"1f39e",finland:"1f1eb-1f1ee",fire:"1f525",fire_engine:"1f692",fire_extinguisher:"1f9ef",firecracker:"1f9e8",firefighter:"1f9d1-1f692",fireworks:"1f386",first_quarter_moon:"1f313",first_quarter_moon_with_face:"1f31b",fish:"1f41f",fish_cake:"1f365",fishing_pole_and_fish:"1f3a3",fist:"270a",fist_left:"1f91b",fist_oncoming:"1f44a",fist_raised:"270a",fist_right:"1f91c",five:"0035-20e3",flags:"1f38f",flamingo:"1f9a9",flashlight:"1f526",flat_shoe:"1f97f",fleur_de_lis:"269c",flight_arrival:"1f6ec",flight_departure:"1f6eb",flipper:"1f42c",floppy_disk:"1f4be",flower_playing_cards:"1f3b4",flushed:"1f633",flying_disc:"1f94f",flying_saucer:"1f6f8",fog:"1f32b",foggy:"1f301",foot:"1f9b6",football:"1f3c8",footprints:"1f463",fork_and_knife:"1f374",fortune_cookie:"1f960",fountain:"26f2",fountain_pen:"1f58b",four:"0034-20e3",four_leaf_clover:"1f340",fox_face:"1f98a",fr:"1f1eb-1f1f7",framed_picture:"1f5bc",free:"1f193",french_guiana:"1f1ec-1f1eb",french_polynesia:"1f1f5-1f1eb",french_southern_territories:"1f1f9-1f1eb",fried_egg:"1f373",fried_shrimp:"1f364",fries:"1f35f",frog:"1f438",frowning:"1f626",frowning_face:"2639",frowning_man:"1f64d-2642",frowning_person:"1f64d",frowning_woman:"1f64d-2640",fu:"1f595",fuelpump:"26fd",full_moon:"1f315",full_moon_with_face:"1f31d",funeral_urn:"26b1",gabon:"1f1ec-1f1e6",gambia:"1f1ec-1f1f2",game_die:"1f3b2",garlic:"1f9c4",gb:"1f1ec-1f1e7",gear:"2699",gem:"1f48e",gemini:"264a",genie:"1f9de",genie_man:"1f9de-2642",genie_woman:"1f9de-2640",georgia:"1f1ec-1f1ea",ghana:"1f1ec-1f1ed",ghost:"1f47b",gibraltar:"1f1ec-1f1ee",gift:"1f381",gift_heart:"1f49d",giraffe:"1f992",girl:"1f467",globe_with_meridians:"1f310",gloves:"1f9e4",goal_net:"1f945",goat:"1f410",goggles:"1f97d",golf:"26f3",golfing:"1f3cc",golfing_man:"1f3cc-2642",golfing_woman:"1f3cc-2640",gorilla:"1f98d",grapes:"1f347",greece:"1f1ec-1f1f7",green_apple:"1f34f",green_book:"1f4d7",green_circle:"1f7e2",green_heart:"1f49a",green_salad:"1f957",green_square:"1f7e9",greenland:"1f1ec-1f1f1",grenada:"1f1ec-1f1e9",grey_exclamation:"2755",grey_question:"2754",grimacing:"1f62c",grin:"1f601",grinning:"1f600",guadeloupe:"1f1ec-1f1f5",guam:"1f1ec-1f1fa",guard:"1f482",guardsman:"1f482-2642",guardswoman:"1f482-2640",guatemala:"1f1ec-1f1f9",guernsey:"1f1ec-1f1ec",guide_dog:"1f9ae",guinea:"1f1ec-1f1f3",guinea_bissau:"1f1ec-1f1fc",guitar:"1f3b8",gun:"1f52b",guyana:"1f1ec-1f1fe",haircut:"1f487",haircut_man:"1f487-2642",haircut_woman:"1f487-2640",haiti:"1f1ed-1f1f9",hamburger:"1f354",hammer:"1f528",hammer_and_pick:"2692",hammer_and_wrench:"1f6e0",hamster:"1f439",hand:"270b",hand_over_mouth:"1f92d",handbag:"1f45c",handball_person:"1f93e",handshake:"1f91d",hankey:"1f4a9",hash:"0023-20e3",hatched_chick:"1f425",hatching_chick:"1f423",headphones:"1f3a7",health_worker:"1f9d1-2695",hear_no_evil:"1f649",heard_mcdonald_islands:"1f1ed-1f1f2",heart:"2764",heart_decoration:"1f49f",heart_eyes:"1f60d",heart_eyes_cat:"1f63b",heartbeat:"1f493",heartpulse:"1f497",hearts:"2665",heavy_check_mark:"2714",heavy_division_sign:"2797",heavy_dollar_sign:"1f4b2",heavy_exclamation_mark:"2757",heavy_heart_exclamation:"2763",heavy_minus_sign:"2796",heavy_multiplication_x:"2716",heavy_plus_sign:"2795",hedgehog:"1f994",helicopter:"1f681",herb:"1f33f",hibiscus:"1f33a",high_brightness:"1f506",high_heel:"1f460",hiking_boot:"1f97e",hindu_temple:"1f6d5",hippopotamus:"1f99b",hocho:"1f52a",hole:"1f573",honduras:"1f1ed-1f1f3",honey_pot:"1f36f",honeybee:"1f41d",hong_kong:"1f1ed-1f1f0",horse:"1f434",horse_racing:"1f3c7",hospital:"1f3e5",hot_face:"1f975",hot_pepper:"1f336",hotdog:"1f32d",hotel:"1f3e8",hotsprings:"2668",hourglass:"231b",hourglass_flowing_sand:"23f3",house:"1f3e0",house_with_garden:"1f3e1",houses:"1f3d8",hugs:"1f917",hungary:"1f1ed-1f1fa",hushed:"1f62f",ice_cream:"1f368",ice_cube:"1f9ca",ice_hockey:"1f3d2",ice_skate:"26f8",icecream:"1f366",iceland:"1f1ee-1f1f8",id:"1f194",ideograph_advantage:"1f250",imp:"1f47f",inbox_tray:"1f4e5",incoming_envelope:"1f4e8",india:"1f1ee-1f1f3",indonesia:"1f1ee-1f1e9",infinity:"267e",information_desk_person:"1f481",information_source:"2139",innocent:"1f607",interrobang:"2049",iphone:"1f4f1",iran:"1f1ee-1f1f7",iraq:"1f1ee-1f1f6",ireland:"1f1ee-1f1ea",isle_of_man:"1f1ee-1f1f2",israel:"1f1ee-1f1f1",it:"1f1ee-1f1f9",izakaya_lantern:"1f3ee",jack_o_lantern:"1f383",jamaica:"1f1ef-1f1f2",japan:"1f5fe",japanese_castle:"1f3ef",japanese_goblin:"1f47a",japanese_ogre:"1f479",jeans:"1f456",jersey:"1f1ef-1f1ea",jigsaw:"1f9e9",jordan:"1f1ef-1f1f4",joy:"1f602",joy_cat:"1f639",joystick:"1f579",jp:"1f1ef-1f1f5",judge:"1f9d1-2696",juggling_person:"1f939",kaaba:"1f54b",kangaroo:"1f998",kazakhstan:"1f1f0-1f1ff",kenya:"1f1f0-1f1ea",key:"1f511",keyboard:"2328",keycap_ten:"1f51f",kick_scooter:"1f6f4",kimono:"1f458",kiribati:"1f1f0-1f1ee",kiss:"1f48b",kissing:"1f617",kissing_cat:"1f63d",kissing_closed_eyes:"1f61a",kissing_heart:"1f618",kissing_smiling_eyes:"1f619",kite:"1fa81",kiwi_fruit:"1f95d",kneeling_man:"1f9ce-2642",kneeling_person:"1f9ce",kneeling_woman:"1f9ce-2640",knife:"1f52a",koala:"1f428",koko:"1f201",kosovo:"1f1fd-1f1f0",kr:"1f1f0-1f1f7",kuwait:"1f1f0-1f1fc",kyrgyzstan:"1f1f0-1f1ec",lab_coat:"1f97c",label:"1f3f7",lacrosse:"1f94d",lantern:"1f3ee",laos:"1f1f1-1f1e6",large_blue_circle:"1f535",large_blue_diamond:"1f537",large_orange_diamond:"1f536",last_quarter_moon:"1f317",last_quarter_moon_with_face:"1f31c",latin_cross:"271d",latvia:"1f1f1-1f1fb",laughing:"1f606",leafy_green:"1f96c",leaves:"1f343",lebanon:"1f1f1-1f1e7",ledger:"1f4d2",left_luggage:"1f6c5",left_right_arrow:"2194",left_speech_bubble:"1f5e8",leftwards_arrow_with_hook:"21a9",leg:"1f9b5",lemon:"1f34b",leo:"264c",leopard:"1f406",lesotho:"1f1f1-1f1f8",level_slider:"1f39a",liberia:"1f1f1-1f1f7",libra:"264e",libya:"1f1f1-1f1fe",liechtenstein:"1f1f1-1f1ee",light_rail:"1f688",link:"1f517",lion:"1f981",lips:"1f444",lipstick:"1f484",lithuania:"1f1f1-1f1f9",lizard:"1f98e",llama:"1f999",lobster:"1f99e",lock:"1f512",lock_with_ink_pen:"1f50f",lollipop:"1f36d",loop:"27bf",lotion_bottle:"1f9f4",lotus_position:"1f9d8",lotus_position_man:"1f9d8-2642",lotus_position_woman:"1f9d8-2640",loud_sound:"1f50a",loudspeaker:"1f4e2",love_hotel:"1f3e9",love_letter:"1f48c",love_you_gesture:"1f91f",low_brightness:"1f505",luggage:"1f9f3",luxembourg:"1f1f1-1f1fa",lying_face:"1f925",m:"24c2",macau:"1f1f2-1f1f4",macedonia:"1f1f2-1f1f0",madagascar:"1f1f2-1f1ec",mag:"1f50d",mag_right:"1f50e",mage:"1f9d9",mage_man:"1f9d9-2642",mage_woman:"1f9d9-2640",magnet:"1f9f2",mahjong:"1f004",mailbox:"1f4eb",mailbox_closed:"1f4ea",mailbox_with_mail:"1f4ec",mailbox_with_no_mail:"1f4ed",malawi:"1f1f2-1f1fc",malaysia:"1f1f2-1f1fe",maldives:"1f1f2-1f1fb",male_detective:"1f575-2642",male_sign:"2642",mali:"1f1f2-1f1f1",malta:"1f1f2-1f1f9",man:"1f468",man_artist:"1f468-1f3a8",man_astronaut:"1f468-1f680",man_cartwheeling:"1f938-2642",man_cook:"1f468-1f373",man_dancing:"1f57a",man_facepalming:"1f926-2642",man_factory_worker:"1f468-1f3ed",man_farmer:"1f468-1f33e",man_firefighter:"1f468-1f692",man_health_worker:"1f468-2695",man_in_manual_wheelchair:"1f468-1f9bd",man_in_motorized_wheelchair:"1f468-1f9bc",man_in_tuxedo:"1f935",man_judge:"1f468-2696",man_juggling:"1f939-2642",man_mechanic:"1f468-1f527",man_office_worker:"1f468-1f4bc",man_pilot:"1f468-2708",man_playing_handball:"1f93e-2642",man_playing_water_polo:"1f93d-2642",man_scientist:"1f468-1f52c",man_shrugging:"1f937-2642",man_singer:"1f468-1f3a4",man_student:"1f468-1f393",man_teacher:"1f468-1f3eb",man_technologist:"1f468-1f4bb",man_with_gua_pi_mao:"1f472",man_with_probing_cane:"1f468-1f9af",man_with_turban:"1f473-2642",mandarin:"1f34a",mango:"1f96d",mans_shoe:"1f45e",mantelpiece_clock:"1f570",manual_wheelchair:"1f9bd",maple_leaf:"1f341",marshall_islands:"1f1f2-1f1ed",martial_arts_uniform:"1f94b",martinique:"1f1f2-1f1f6",mask:"1f637",massage:"1f486",massage_man:"1f486-2642",massage_woman:"1f486-2640",mate:"1f9c9",mauritania:"1f1f2-1f1f7",mauritius:"1f1f2-1f1fa",mayotte:"1f1fe-1f1f9",meat_on_bone:"1f356",mechanic:"1f9d1-1f527",mechanical_arm:"1f9be",mechanical_leg:"1f9bf",medal_military:"1f396",medal_sports:"1f3c5",medical_symbol:"2695",mega:"1f4e3",melon:"1f348",memo:"1f4dd",men_wrestling:"1f93c-2642",menorah:"1f54e",mens:"1f6b9",mermaid:"1f9dc-2640",merman:"1f9dc-2642",merperson:"1f9dc",metal:"1f918",metro:"1f687",mexico:"1f1f2-1f1fd",microbe:"1f9a0",micronesia:"1f1eb-1f1f2",microphone:"1f3a4",microscope:"1f52c",middle_finger:"1f595",milk_glass:"1f95b",milky_way:"1f30c",minibus:"1f690",minidisc:"1f4bd",mobile_phone_off:"1f4f4",moldova:"1f1f2-1f1e9",monaco:"1f1f2-1f1e8",money_mouth_face:"1f911",money_with_wings:"1f4b8",moneybag:"1f4b0",mongolia:"1f1f2-1f1f3",monkey:"1f412",monkey_face:"1f435",monocle_face:"1f9d0",monorail:"1f69d",montenegro:"1f1f2-1f1ea",montserrat:"1f1f2-1f1f8",moon:"1f314",moon_cake:"1f96e",morocco:"1f1f2-1f1e6",mortar_board:"1f393",mosque:"1f54c",mosquito:"1f99f",motor_boat:"1f6e5",motor_scooter:"1f6f5",motorcycle:"1f3cd",motorized_wheelchair:"1f9bc",motorway:"1f6e3",mount_fuji:"1f5fb",mountain:"26f0",mountain_bicyclist:"1f6b5",mountain_biking_man:"1f6b5-2642",mountain_biking_woman:"1f6b5-2640",mountain_cableway:"1f6a0",mountain_railway:"1f69e",mountain_snow:"1f3d4",mouse:"1f42d",mouse2:"1f401",movie_camera:"1f3a5",moyai:"1f5ff",mozambique:"1f1f2-1f1ff",mrs_claus:"1f936",muscle:"1f4aa",mushroom:"1f344",musical_keyboard:"1f3b9",musical_note:"1f3b5",musical_score:"1f3bc",mute:"1f507",myanmar:"1f1f2-1f1f2",nail_care:"1f485",name_badge:"1f4db",namibia:"1f1f3-1f1e6",national_park:"1f3de",nauru:"1f1f3-1f1f7",nauseated_face:"1f922",nazar_amulet:"1f9ff",necktie:"1f454",negative_squared_cross_mark:"274e",nepal:"1f1f3-1f1f5",nerd_face:"1f913",netherlands:"1f1f3-1f1f1",neutral_face:"1f610",new:"1f195",new_caledonia:"1f1f3-1f1e8",new_moon:"1f311",new_moon_with_face:"1f31a",new_zealand:"1f1f3-1f1ff",newspaper:"1f4f0",newspaper_roll:"1f5de",next_track_button:"23ed",ng:"1f196",ng_man:"1f645-2642",ng_woman:"1f645-2640",nicaragua:"1f1f3-1f1ee",niger:"1f1f3-1f1ea",nigeria:"1f1f3-1f1ec",night_with_stars:"1f303",nine:"0039-20e3",niue:"1f1f3-1f1fa",no_bell:"1f515",no_bicycles:"1f6b3",no_entry:"26d4",no_entry_sign:"1f6ab",no_good:"1f645",no_good_man:"1f645-2642",no_good_woman:"1f645-2640",no_mobile_phones:"1f4f5",no_mouth:"1f636",no_pedestrians:"1f6b7",no_smoking:"1f6ad","non-potable_water":"1f6b1",norfolk_island:"1f1f3-1f1eb",north_korea:"1f1f0-1f1f5",northern_mariana_islands:"1f1f2-1f1f5",norway:"1f1f3-1f1f4",nose:"1f443",notebook:"1f4d3",notebook_with_decorative_cover:"1f4d4",notes:"1f3b6",nut_and_bolt:"1f529",o:"2b55",o2:"1f17e",ocean:"1f30a",octopus:"1f419",oden:"1f362",office:"1f3e2",office_worker:"1f9d1-1f4bc",oil_drum:"1f6e2",ok:"1f197",ok_hand:"1f44c",ok_man:"1f646-2642",ok_person:"1f646",ok_woman:"1f646-2640",old_key:"1f5dd",older_adult:"1f9d3",older_man:"1f474",older_woman:"1f475",om:"1f549",oman:"1f1f4-1f1f2",on:"1f51b",oncoming_automobile:"1f698",oncoming_bus:"1f68d",oncoming_police_car:"1f694",oncoming_taxi:"1f696",one:"0031-20e3",one_piece_swimsuit:"1fa71",onion:"1f9c5",open_book:"1f4d6",open_file_folder:"1f4c2",open_hands:"1f450",open_mouth:"1f62e",open_umbrella:"2602",ophiuchus:"26ce",orange:"1f34a",orange_book:"1f4d9",orange_circle:"1f7e0",orange_heart:"1f9e1",orange_square:"1f7e7",orangutan:"1f9a7",orthodox_cross:"2626",otter:"1f9a6",outbox_tray:"1f4e4",owl:"1f989",ox:"1f402",oyster:"1f9aa",package:"1f4e6",page_facing_up:"1f4c4",page_with_curl:"1f4c3",pager:"1f4df",paintbrush:"1f58c",pakistan:"1f1f5-1f1f0",palau:"1f1f5-1f1fc",palestinian_territories:"1f1f5-1f1f8",palm_tree:"1f334",palms_up_together:"1f932",panama:"1f1f5-1f1e6",pancakes:"1f95e",panda_face:"1f43c",paperclip:"1f4ce",paperclips:"1f587",papua_new_guinea:"1f1f5-1f1ec",parachute:"1fa82",paraguay:"1f1f5-1f1fe",parasol_on_ground:"26f1",parking:"1f17f",parrot:"1f99c",part_alternation_mark:"303d",partly_sunny:"26c5",partying_face:"1f973",passenger_ship:"1f6f3",passport_control:"1f6c2",pause_button:"23f8",paw_prints:"1f43e",peace_symbol:"262e",peach:"1f351",peacock:"1f99a",peanuts:"1f95c",pear:"1f350",pen:"1f58a",pencil:"1f4dd",pencil2:"270f",penguin:"1f427",pensive:"1f614",people_holding_hands:"1f9d1-1f91d-1f9d1",performing_arts:"1f3ad",persevere:"1f623",person_bald:"1f9d1-1f9b2",person_curly_hair:"1f9d1-1f9b1",person_fencing:"1f93a",person_in_manual_wheelchair:"1f9d1-1f9bd",person_in_motorized_wheelchair:"1f9d1-1f9bc",person_red_hair:"1f9d1-1f9b0",person_white_hair:"1f9d1-1f9b3",person_with_probing_cane:"1f9d1-1f9af",person_with_turban:"1f473",peru:"1f1f5-1f1ea",petri_dish:"1f9eb",philippines:"1f1f5-1f1ed",phone:"260e",pick:"26cf",pie:"1f967",pig:"1f437",pig2:"1f416",pig_nose:"1f43d",pill:"1f48a",pilot:"1f9d1-2708",pinching_hand:"1f90f",pineapple:"1f34d",ping_pong:"1f3d3",pirate_flag:"1f3f4-2620",pisces:"2653",pitcairn_islands:"1f1f5-1f1f3",pizza:"1f355",place_of_worship:"1f6d0",plate_with_cutlery:"1f37d",play_or_pause_button:"23ef",pleading_face:"1f97a",point_down:"1f447",point_left:"1f448",point_right:"1f449",point_up:"261d",point_up_2:"1f446",poland:"1f1f5-1f1f1",police_car:"1f693",police_officer:"1f46e",policeman:"1f46e-2642",policewoman:"1f46e-2640",poodle:"1f429",poop:"1f4a9",popcorn:"1f37f",portugal:"1f1f5-1f1f9",post_office:"1f3e3",postal_horn:"1f4ef",postbox:"1f4ee",potable_water:"1f6b0",potato:"1f954",pouch:"1f45d",poultry_leg:"1f357",pound:"1f4b7",pout:"1f621",pouting_cat:"1f63e",pouting_face:"1f64e",pouting_man:"1f64e-2642",pouting_woman:"1f64e-2640",pray:"1f64f",prayer_beads:"1f4ff",pregnant_woman:"1f930",pretzel:"1f968",previous_track_button:"23ee",prince:"1f934",princess:"1f478",printer:"1f5a8",probing_cane:"1f9af",puerto_rico:"1f1f5-1f1f7",punch:"1f44a",purple_circle:"1f7e3",purple_heart:"1f49c",purple_square:"1f7ea",purse:"1f45b",pushpin:"1f4cc",put_litter_in_its_place:"1f6ae",qatar:"1f1f6-1f1e6",question:"2753",rabbit:"1f430",rabbit2:"1f407",raccoon:"1f99d",racehorse:"1f40e",racing_car:"1f3ce",radio:"1f4fb",radio_button:"1f518",radioactive:"2622",rage:"1f621",railway_car:"1f683",railway_track:"1f6e4",rainbow:"1f308",rainbow_flag:"1f3f3-1f308",raised_back_of_hand:"1f91a",raised_eyebrow:"1f928",raised_hand:"270b",raised_hand_with_fingers_splayed:"1f590",raised_hands:"1f64c",raising_hand:"1f64b",raising_hand_man:"1f64b-2642",raising_hand_woman:"1f64b-2640",ram:"1f40f",ramen:"1f35c",rat:"1f400",razor:"1fa92",receipt:"1f9fe",record_button:"23fa",recycle:"267b",red_car:"1f697",red_circle:"1f534",red_envelope:"1f9e7",red_haired_man:"1f468-1f9b0",red_haired_woman:"1f469-1f9b0",red_square:"1f7e5",registered:"00ae",relaxed:"263a",relieved:"1f60c",reminder_ribbon:"1f397",repeat:"1f501",repeat_one:"1f502",rescue_worker_helmet:"26d1",restroom:"1f6bb",reunion:"1f1f7-1f1ea",revolving_hearts:"1f49e",rewind:"23ea",rhinoceros:"1f98f",ribbon:"1f380",rice:"1f35a",rice_ball:"1f359",rice_cracker:"1f358",rice_scene:"1f391",right_anger_bubble:"1f5ef",ring:"1f48d",ringed_planet:"1fa90",robot:"1f916",rocket:"1f680",rofl:"1f923",roll_eyes:"1f644",roll_of_paper:"1f9fb",roller_coaster:"1f3a2",romania:"1f1f7-1f1f4",rooster:"1f413",rose:"1f339",rosette:"1f3f5",rotating_light:"1f6a8",round_pushpin:"1f4cd",rowboat:"1f6a3",rowing_man:"1f6a3-2642",rowing_woman:"1f6a3-2640",ru:"1f1f7-1f1fa",rugby_football:"1f3c9",runner:"1f3c3",running:"1f3c3",running_man:"1f3c3-2642",running_shirt_with_sash:"1f3bd",running_woman:"1f3c3-2640",rwanda:"1f1f7-1f1fc",sa:"1f202",safety_pin:"1f9f7",safety_vest:"1f9ba",sagittarius:"2650",sailboat:"26f5",sake:"1f376",salt:"1f9c2",samoa:"1f1fc-1f1f8",san_marino:"1f1f8-1f1f2",sandal:"1f461",sandwich:"1f96a",santa:"1f385",sao_tome_principe:"1f1f8-1f1f9",sari:"1f97b",sassy_man:"1f481-2642",sassy_woman:"1f481-2640",satellite:"1f4e1",satisfied:"1f606",saudi_arabia:"1f1f8-1f1e6",sauna_man:"1f9d6-2642",sauna_person:"1f9d6",sauna_woman:"1f9d6-2640",sauropod:"1f995",saxophone:"1f3b7",scarf:"1f9e3",school:"1f3eb",school_satchel:"1f392",scientist:"1f9d1-1f52c",scissors:"2702",scorpion:"1f982",scorpius:"264f",scotland:"1f3f4-e0067-e0062-e0073-e0063-e0074-e007f",scream:"1f631",scream_cat:"1f640",scroll:"1f4dc",seat:"1f4ba",secret:"3299",see_no_evil:"1f648",seedling:"1f331",selfie:"1f933",senegal:"1f1f8-1f1f3",serbia:"1f1f7-1f1f8",service_dog:"1f415-1f9ba",seven:"0037-20e3",seychelles:"1f1f8-1f1e8",shallow_pan_of_food:"1f958",shamrock:"2618",shark:"1f988",shaved_ice:"1f367",sheep:"1f411",shell:"1f41a",shield:"1f6e1",shinto_shrine:"26e9",ship:"1f6a2",shirt:"1f455",poo:"1f4a9",shoe:"1f45e",shopping:"1f6cd",shopping_cart:"1f6d2",shorts:"1fa73",shower:"1f6bf",shrimp:"1f990",shrug:"1f937",shushing_face:"1f92b",sierra_leone:"1f1f8-1f1f1",signal_strength:"1f4f6",singapore:"1f1f8-1f1ec",singer:"1f9d1-1f3a4",sint_maarten:"1f1f8-1f1fd",six:"0036-20e3",six_pointed_star:"1f52f",skateboard:"1f6f9",ski:"1f3bf",skier:"26f7",skull:"1f480",skull_and_crossbones:"2620",skunk:"1f9a8",sled:"1f6f7",sleeping:"1f634",sleeping_bed:"1f6cc",sleepy:"1f62a",slightly_frowning_face:"1f641",slightly_smiling_face:"1f642",slot_machine:"1f3b0",sloth:"1f9a5",slovakia:"1f1f8-1f1f0",slovenia:"1f1f8-1f1ee",small_airplane:"1f6e9",small_blue_diamond:"1f539",small_orange_diamond:"1f538",small_red_triangle:"1f53a",small_red_triangle_down:"1f53b",smile:"1f604",smile_cat:"1f638",smiley:"1f603",smiley_cat:"1f63a",smiling_face_with_three_hearts:"1f970",smiling_imp:"1f608",smirk:"1f60f",smirk_cat:"1f63c",smoking:"1f6ac",snail:"1f40c",snake:"1f40d",sneezing_face:"1f927",snowboarder:"1f3c2",snowflake:"2744",snowman:"26c4",snowman_with_snow:"2603",soap:"1f9fc",sob:"1f62d",soccer:"26bd",socks:"1f9e6",softball:"1f94e",solomon_islands:"1f1f8-1f1e7",somalia:"1f1f8-1f1f4",soon:"1f51c",sos:"1f198",sound:"1f509",south_africa:"1f1ff-1f1e6",south_georgia_south_sandwich_islands:"1f1ec-1f1f8",south_sudan:"1f1f8-1f1f8",space_invader:"1f47e",spades:"2660",spaghetti:"1f35d",sparkle:"2747",sparkler:"1f387",sparkles:"2728",sparkling_heart:"1f496",speak_no_evil:"1f64a",speaker:"1f508",speaking_head:"1f5e3",speech_balloon:"1f4ac",speedboat:"1f6a4",spider:"1f577",spider_web:"1f578",spiral_calendar:"1f5d3",spiral_notepad:"1f5d2",sponge:"1f9fd",spoon:"1f944",squid:"1f991",sri_lanka:"1f1f1-1f1f0",st_barthelemy:"1f1e7-1f1f1",st_helena:"1f1f8-1f1ed",st_kitts_nevis:"1f1f0-1f1f3",st_lucia:"1f1f1-1f1e8",st_martin:"1f1f2-1f1eb",st_pierre_miquelon:"1f1f5-1f1f2",st_vincent_grenadines:"1f1fb-1f1e8",stadium:"1f3df",standing_man:"1f9cd-2642",standing_person:"1f9cd",standing_woman:"1f9cd-2640",star:"2b50",star2:"1f31f",star_and_crescent:"262a",star_of_david:"2721",star_struck:"1f929",stars:"1f320",station:"1f689",statue_of_liberty:"1f5fd",steam_locomotive:"1f682",stethoscope:"1fa7a",stew:"1f372",stop_button:"23f9",stop_sign:"1f6d1",stopwatch:"23f1",straight_ruler:"1f4cf",strawberry:"1f353",stuck_out_tongue:"1f61b",stuck_out_tongue_closed_eyes:"1f61d",stuck_out_tongue_winking_eye:"1f61c",student:"1f9d1-1f393",studio_microphone:"1f399",stuffed_flatbread:"1f959",sudan:"1f1f8-1f1e9",sun_behind_large_cloud:"1f325",sun_behind_rain_cloud:"1f326",sun_behind_small_cloud:"1f324",sun_with_face:"1f31e",sunflower:"1f33b",sunglasses:"1f60e",sunny:"2600",sunrise:"1f305",sunrise_over_mountains:"1f304",superhero:"1f9b8",superhero_man:"1f9b8-2642",superhero_woman:"1f9b8-2640",supervillain:"1f9b9",supervillain_man:"1f9b9-2642",supervillain_woman:"1f9b9-2640",surfer:"1f3c4",surfing_man:"1f3c4-2642",surfing_woman:"1f3c4-2640",suriname:"1f1f8-1f1f7",sushi:"1f363",suspension_railway:"1f69f",svalbard_jan_mayen:"1f1f8-1f1ef",swan:"1f9a2",swaziland:"1f1f8-1f1ff",sweat:"1f613",sweat_drops:"1f4a6",sweat_smile:"1f605",sweden:"1f1f8-1f1ea",sweet_potato:"1f360",swim_brief:"1fa72",swimmer:"1f3ca",swimming_man:"1f3ca-2642",swimming_woman:"1f3ca-2640",switzerland:"1f1e8-1f1ed",symbols:"1f523",synagogue:"1f54d",syria:"1f1f8-1f1fe",syringe:"1f489","t-rex":"1f996",taco:"1f32e",tada:"1f389",taiwan:"1f1f9-1f1fc",tajikistan:"1f1f9-1f1ef",takeout_box:"1f961",tanabata_tree:"1f38b",tangerine:"1f34a",tanzania:"1f1f9-1f1ff",taurus:"2649",taxi:"1f695",tea:"1f375",teacher:"1f9d1-1f3eb",technologist:"1f9d1-1f4bb",teddy_bear:"1f9f8",telephone:"260e",telephone_receiver:"1f4de",telescope:"1f52d",tennis:"1f3be",tent:"26fa",test_tube:"1f9ea",thailand:"1f1f9-1f1ed",thermometer:"1f321",thinking:"1f914",thought_balloon:"1f4ad",thread:"1f9f5",three:"0033-20e3",thumbsdown:"1f44e",thumbsup:"1f44d",ticket:"1f3ab",tickets:"1f39f",tiger:"1f42f",tiger2:"1f405",timer_clock:"23f2",timor_leste:"1f1f9-1f1f1",tipping_hand_man:"1f481-2642",tipping_hand_person:"1f481",tipping_hand_woman:"1f481-2640",tired_face:"1f62b",tm:"2122",togo:"1f1f9-1f1ec",toilet:"1f6bd",tokelau:"1f1f9-1f1f0",tokyo_tower:"1f5fc",tomato:"1f345",tonga:"1f1f9-1f1f4",tongue:"1f445",toolbox:"1f9f0",tooth:"1f9b7",top:"1f51d",tophat:"1f3a9",tornado:"1f32a",tr:"1f1f9-1f1f7",trackball:"1f5b2",tractor:"1f69c",traffic_light:"1f6a5",train:"1f68b",train2:"1f686",tram:"1f68a",triangular_flag_on_post:"1f6a9",triangular_ruler:"1f4d0",trident:"1f531",trinidad_tobago:"1f1f9-1f1f9",tristan_da_cunha:"1f1f9-1f1e6",triumph:"1f624",trolleybus:"1f68e",trophy:"1f3c6",tropical_drink:"1f379",tropical_fish:"1f420",truck:"1f69a",trumpet:"1f3ba",tshirt:"1f455",tulip:"1f337",tumbler_glass:"1f943",tunisia:"1f1f9-1f1f3",turkey:"1f983",turkmenistan:"1f1f9-1f1f2",turks_caicos_islands:"1f1f9-1f1e8",turtle:"1f422",tuvalu:"1f1f9-1f1fb",tv:"1f4fa",twisted_rightwards_arrows:"1f500",two:"0032-20e3",two_hearts:"1f495",two_men_holding_hands:"1f46c",two_women_holding_hands:"1f46d",u5272:"1f239",u5408:"1f234",u55b6:"1f23a",u6307:"1f22f",u6708:"1f237",u6709:"1f236",u6e80:"1f235",u7121:"1f21a",u7533:"1f238",u7981:"1f232",u7a7a:"1f233",uganda:"1f1fa-1f1ec",uk:"1f1ec-1f1e7",ukraine:"1f1fa-1f1e6",umbrella:"2614",unamused:"1f612",underage:"1f51e",unicorn:"1f984",united_arab_emirates:"1f1e6-1f1ea",united_nations:"1f1fa-1f1f3",unlock:"1f513",up:"1f199",upside_down_face:"1f643",uruguay:"1f1fa-1f1fe",us:"1f1fa-1f1f8",us_outlying_islands:"1f1fa-1f1f2",us_virgin_islands:"1f1fb-1f1ee",uzbekistan:"1f1fa-1f1ff",v:"270c",vampire:"1f9db",vampire_man:"1f9db-2642",vampire_woman:"1f9db-2640",vanuatu:"1f1fb-1f1fa",vatican_city:"1f1fb-1f1e6",venezuela:"1f1fb-1f1ea",vertical_traffic_light:"1f6a6",vhs:"1f4fc",vibration_mode:"1f4f3",video_camera:"1f4f9",video_game:"1f3ae",vietnam:"1f1fb-1f1f3",violin:"1f3bb",virgo:"264d",volcano:"1f30b",volleyball:"1f3d0",vomiting_face:"1f92e",vs:"1f19a",vulcan_salute:"1f596",waffle:"1f9c7",wales:"1f3f4-e0067-e0062-e0077-e006c-e0073-e007f",walking:"1f6b6",walking_man:"1f6b6-2642",walking_woman:"1f6b6-2640",wallis_futuna:"1f1fc-1f1eb",waning_crescent_moon:"1f318",waning_gibbous_moon:"1f316",warning:"26a0",wastebasket:"1f5d1",watch:"231a",water_buffalo:"1f403",water_polo:"1f93d",watermelon:"1f349",wave:"1f44b",wavy_dash:"3030",waxing_crescent_moon:"1f312",waxing_gibbous_moon:"1f314",wc:"1f6be",weary:"1f629",wedding:"1f492",weight_lifting:"1f3cb",weight_lifting_man:"1f3cb-2642",weight_lifting_woman:"1f3cb-2640",western_sahara:"1f1ea-1f1ed",whale:"1f433",whale2:"1f40b",wheel_of_dharma:"2638",wheelchair:"267f",white_check_mark:"2705",white_circle:"26aa",white_flag:"1f3f3",white_flower:"1f4ae",white_haired_man:"1f468-1f9b3",white_haired_woman:"1f469-1f9b3",white_heart:"1f90d",white_large_square:"2b1c",white_medium_small_square:"25fd",white_medium_square:"25fb",white_small_square:"25ab",white_square_button:"1f533",wilted_flower:"1f940",wind_chime:"1f390",wind_face:"1f32c",wine_glass:"1f377",wink:"1f609",wolf:"1f43a",woman:"1f469",woman_artist:"1f469-1f3a8",woman_astronaut:"1f469-1f680",woman_cartwheeling:"1f938-2640",woman_cook:"1f469-1f373",woman_dancing:"1f483",woman_facepalming:"1f926-2640",woman_factory_worker:"1f469-1f3ed",woman_farmer:"1f469-1f33e",woman_firefighter:"1f469-1f692",woman_health_worker:"1f469-2695",woman_in_manual_wheelchair:"1f469-1f9bd",woman_in_motorized_wheelchair:"1f469-1f9bc",woman_judge:"1f469-2696",woman_juggling:"1f939-2640",woman_mechanic:"1f469-1f527",woman_office_worker:"1f469-1f4bc",woman_pilot:"1f469-2708",woman_playing_handball:"1f93e-2640",woman_playing_water_polo:"1f93d-2640",woman_scientist:"1f469-1f52c",woman_shrugging:"1f937-2640",woman_singer:"1f469-1f3a4",woman_student:"1f469-1f393",woman_teacher:"1f469-1f3eb",woman_technologist:"1f469-1f4bb",woman_with_headscarf:"1f9d5",woman_with_probing_cane:"1f469-1f9af",woman_with_turban:"1f473-2640",womans_clothes:"1f45a",womans_hat:"1f452",women_wrestling:"1f93c-2640",womens:"1f6ba",woozy_face:"1f974",world_map:"1f5fa",worried:"1f61f",wrench:"1f527",wrestling:"1f93c",writing_hand:"270d",x:"274c",yarn:"1f9f6",yawning_face:"1f971",yellow_circle:"1f7e1",yellow_heart:"1f49b",yellow_square:"1f7e8",yemen:"1f1fe-1f1ea",yen:"1f4b4",yin_yang:"262f",yo_yo:"1fa80",yum:"1f60b",zambia:"1f1ff-1f1f2",zany_face:"1f92a",zap:"26a1",zebra:"1f993",zero:"0030-20e3",zimbabwe:"1f1ff-1f1fc",zipper_mouth_face:"1f910",zombie:"1f9df",zombie_man:"1f9df-2642",zombie_woman:"1f9df-2640",zzz:"1f4a4"};function Oh(e,t){var n=oc(e);if(sl){var r=sl(e);t&&(r=fc(r).call(r,(function(t){return dl(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ph(e){for(var t=1;t<arguments.length;t++){var n,r,a=null!=arguments[t]?arguments[t]:{};t%2?ac(n=Oh(Object(a),!0)).call(n,(function(t){hi(e,t,a[t])})):gl?vl(e,gl(a)):ac(r=Oh(Object(a))).call(r,(function(t){Us(e,t,dl(a,t))}))}return e}function Lh(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}function Ih(){for(var e=[],t=0,n="",r=0,a=arguments.length;r!==a;++r){var i=+(r<0||arguments.length<=r?void 0:arguments[r]);if(!(i<1114111&&i>>>0===i))throw new RangeError("Invalid code point: ".concat(i));i<=65535?t=e.push(i):(i-=65536,t=e.push(55296+(i>>10),i%1024+56320)),t>=16383&&(n+=String.fromCharCode.apply(null,e),e.length=0)}return n+String.fromCharCode.apply(null,e)}var Nh=function(e){An(n,Cc);var t=Lh(n);function n(){var e,r=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{config:void 0}).config;if(mn(this,n),(e=t.call(this,{config:r})).options={useUnicode:!0,upperCase:!1,customHandled:!1,resourceURL:"https://github.githubassets.com/images/icons/emoji/unicode/${code}.png?v8",emojis:Ph({},Rh)},"object"!==ci(r))return ui(e);var a=r.useUnicode,i=r.customResourceURL,o=r.customRenderer,s=r.upperCase;return e.options.useUnicode="boolean"==typeof a?a:e.options.useUnicode,e.options.upperCase="boolean"==typeof s?s:e.options.upperCase,!1===a&&"string"==typeof i&&(e.options.resourceURL=i),"function"==typeof o&&(e.options.customHandled=!0,e.options.customRenderer=o),e}return gn(n,[{key:"makeHtml",value:function(e,t){var n=this;return this.test(e)?e.replace(this.RULE.reg,(function(e,t){var r;if(n.options.customHandled&&"function"==typeof n.options.customRenderer)return n.options.customRenderer(t);var a=n.options.emojis[t];if("string"!=typeof a)return e;if(n.options.useUnicode){var i,o=Lc(i=a.split("-")).call(i,(function(e){return"0x".concat(e)}));return Ih.apply(void 0,Uf(o))}n.options.upperCase&&(a=a.toUpperCase());var s=n.options.resourceURL.replace(/\$\{code\}/g,a);return Vs(r='<img class="emoji" src="'.concat(s,'" alt="')).call(r,Zl(t),'" />')})):e}},{key:"rule",value:function(){var e={begin:":",content:"([a-zA-Z0-9+_]+?)",end:":"};return e.reg=cf(e,"g"),e}}]),n}();function Mh(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(Nh,"HOOK_NAME","emoji");var jh=function(e){An(n,Cc);var t=Mh(n);function n(){return mn(this,n),t.apply(this,arguments)}return gn(n,[{key:"makeHtml",value:function(e){return this.test(e)?e.replace(this.RULE.reg,'$1<span style="text-decoration: underline;">$2</span>$3'):e}},{key:"rule",value:function(){var e={begin:"(^| )\\/",end:"\\/( |$)",content:"([^\\n]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();function Dh(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(jh,"HOOK_NAME","underline");var Bh=function(e){An(n,Cc);var t=Dh(n);function n(){return mn(this,n),t.apply(this,arguments)}return gn(n,[{key:"makeHtml",value:function(e){return this.test(e)?e.replace(this.RULE.reg,"$1<mark>$2</mark>$3"):e}},{key:"rule",value:function(){var e={begin:"(^| )==",end:"==( |$|\\n)",content:"([^\\n]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();hi(Bh,"HOOK_NAME","highLight");var Fh=Xa;N.JSON||(N.JSON={stringify:JSON.stringify});var Hh=function(e,t,n){return f(N.JSON.stringify,null,arguments)},zh=Nt.includes,Uh=o((function(){return!Array(1).includes()}));Ve({target:"Array",proto:!0,forced:Uh},{includes:function(e){return zh(this,e,arguments.length>1?arguments[1]:void 0)}});var Wh=Ws("Array").includes,qh=m("".indexOf);Ve({target:"String",proto:!0,forced:!Zp("includes")},{includes:function(e){return!!~qh(Wn(P(this)),Wn(Gp(e)),arguments.length>1?arguments[1]:void 0)}});var Gh=Ws("String").includes,Kh=Array.prototype,Zh=String.prototype,Yh=function(e){var t=e.includes;return e===Kh||D(Kh,e)&&t===Kh.includes?Wh:"string"==typeof e||e===Zh||D(Zh,e)&&t===Zh.includes?Gh:t},Xh=i.TypeError,Vh=/MSIE .\./.test(B),Jh=i.Function,Qh=function(e){return Vh?function(t,n){var r=function(e,t){if(e<t)throw Xh("Not enough arguments");return e}(arguments.length,1)>2,a=b(t)?t:Jh(t),i=r?Je(arguments,2):void 0;return e(r?function(){f(a,this,i)}:a,n)}:e},eg={setTimeout:Qh(i.setTimeout),setInterval:Qh(i.setInterval)},tg=eg.setInterval;Ve({global:!0,bind:!0,forced:i.setInterval!==tg},{setInterval:tg});var ng=eg.setTimeout;Ve({global:!0,bind:!0,forced:i.setTimeout!==ng},{setTimeout:ng});var rg=N.setTimeout;var ag=function(e,t){for(var n=-1,r=null==e?0:e.length,a=Array(r);++n<r;)a[n]=t(e[n],n,e);return a};var ig=function(e){return"symbol"==typeof e||Wo(e)&&"[object Symbol]"==Hi(e)},og=Pi?Pi.prototype:void 0,sg=og?og.toString:void 0;var cg=function e(t){if("string"==typeof t)return t;if(Xo(t))return ag(t,e)+"";if(ig(t))return sg?sg.call(t):"";var n=t+"";return"0"==n&&1/t==-Infinity?"-0":n};var lg=function(e){return null==e?"":cg(e)},ug=/[\\^$.*+?()[\]{}|]/g,fg=RegExp(ug.source);var dg=function(e){return(e=lg(e))&&fg.test(e)?e.replace(ug,"\\$&"):e},pg="/·￥、：“”【】（）《》".concat("#"),hg=[{icon:"h1",label:"H1 Heading",keyword:"head1",value:"# "},{icon:"h2",label:"H2  Heading",keyword:"head2",value:"## "},{icon:"h3",label:"H3  Heading",keyword:"head3",value:"### "},{icon:"table",label:"Table",keyword:"table",value:"| Header | Header | Header |\n| --- | --- | --- |\n| Content | Content | Content |\n"},{icon:"code",label:"Code",keyword:"code",value:"```\n\n```\n"},{icon:"link",label:"Link",keyword:"link",value:"[title](https://url)",selection:{from:19,to:14}},{icon:"checklist",label:"Checklist",keyword:"checklist",value:"- [ ] item\n- [x] item"},{icon:"tips",label:"Panel",keyword:"panel tips info warning danger success",value:"::: primary title\ncontent\n:::\n"},{icon:"insertFlow",label:"Detail",keyword:"detail",value:"+++ 点击展开更多\n内容\n++- 默认展开\n内容\n++ 默认收起\n内容\n+++\n"}],gg=[{icon:"FullWidth",label:"`",keyword:"···",value:"`"},{icon:"FullWidth",label:"$",keyword:"￥",value:"$"},{icon:"FullWidth",label:"/",keyword:"、",value:"/"},{icon:"FullWidth",label:"\\",keyword:"、",value:"\\"},{icon:"FullWidth",label:'"',keyword:"“",value:'"'},{icon:"FullWidth",label:'"',keyword:"”",value:'"'},{icon:"FullWidth",label:"[",keyword:"【",value:"["},{icon:"FullWidth",label:"]",keyword:"】",value:"]"},{icon:"FullWidth",label:"(",keyword:"（",value:"("},{icon:"FullWidth",label:")",keyword:"）",value:")"},{icon:"FullWidth",label:"<",keyword:"《",value:"<"},{icon:"FullWidth",label:">",keyword:"》",value:">"}],mg=[{icon:"FullWidth",label:"[]",keyword:"【】",value:"[]",goLeft:1},{icon:"FullWidth",label:"【】",keyword:"【",value:"【】",goLeft:1},{icon:"link",label:"Link",keyword:"【】",value:"[title](https://url)",selection:{from:19,to:14}},{icon:"FullWidth",label:"()",keyword:"（",value:"()",goLeft:1},{icon:"FullWidth",label:"（）",keyword:"（",value:"（）",goLeft:1},{icon:"FullWidth",label:"<>",keyword:"《》",value:"<>",goLeft:1},{icon:"FullWidth",label:"《》",keyword:"《》",value:"《》",goLeft:1},{icon:"FullWidth",label:'""',keyword:"“”",value:'""',goLeft:1},{icon:"FullWidth",label:"“”",keyword:"“”",value:"”“",goLeft:1}],bg=Vs(gg).call(gg,mg);var vg=vn,yg=function(){return"CodeMirror.Pass"};function _g(e,t){var n=void 0!==Fh&&Ef(e)||e["@@iterator"];if(!n){if(rl(e)||(n=function(e,t){var n;if(!e)return;if("string"==typeof e)return kg(e,t);var r=Kf(n=Object.prototype.toString.call(e)).call(n,8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Up(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return kg(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return o=e.done,e},e:function(e){s=!0,i=e},f:function(){try{o||null==n.return||n.return()}finally{if(s)throw i}}}}function kg(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function wg(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var Eg=function(e){An(n,Cc);var t=wg(n);function n(e){var r,a=e.config,i=e.cherry;return mn(this,n),(r=t.call(this,{needCache:!0})).config=a,r.RULE=r.rule(),r.$cherry=i,r.suggesterPanel=new Sg(i),r}return gn(n,[{key:"afterInit",value:function(e){fp()&&("function"==typeof e&&e(),this.initConfig(this.config))}},{key:"initConfig",value:function(e){var t=this,n=e.suggester;this.suggester={};var r,a=[],i=_g(pg);try{var o=function(){var e=r.value;a.push({keyword:e,suggestList:function(t,n){var r=t.toLowerCase(),a=function(e,t){var n,r,a,i=Vs(n=[]).call(n,hg),o=Vs(r=[]).call(r,bg);return ac(i).call(i,(function(e){e.label=t?t[e.label]:e.label})),ac(o).call(o,(function(e){e.label=t?t[e.label]:e.label})),("/"===e[0]||"、"===e[0]||Yh("#").call("#",e[0]))&&ac(i).call(i,(function(t){t.keyword="".concat(e[0],t.keyword)})),fc(a=Vs(o).call(o,i)).call(a,(function(t){var n;return th(n=t.keyword).call(n,e[0])}))}(e,this.$locale);if(/^\s$/.test(r))n(!1);else{var i=r.replace(/\s+/g,"").replace(new RegExp("^".concat(e),"g"),"").split("").join(".*?"),o=new RegExp("^.*?".concat(i,".*?$"),"i"),s=fc(a).call(a,(function(e){return!r||o.test(e.keyword)}));n(0!==s.length&&s)}}})};for(i.s();!(r=i.n()).done;)o()}catch(e){i.e(e)}finally{i.f()}n=n?Vs(a).call(a,n):a,ac(n).call(n,(function(e){e.suggestList?(e.keyword||(e.keyword="@"),t.suggester[e.keyword]=e):console.warn("[cherry-suggester]: the suggestList of config is missing.")})),this.suggesterPanel.hasEditor()&&(this.suggesterPanel.editor=null)}},{key:"makeHtml",value:function(e){var t,n;if(!this.RULE.reg)return e;if(!this.suggesterPanel.hasEditor()&&fp()){var r=this.$engine.$cherry.editor;this.suggesterPanel.setEditor(r),this.suggesterPanel.setSuggester(this.suggester),this.suggesterPanel.bindEvent()}return lf()?e.replace(this.RULE.reg,Zs(n=this.toHtml).call(n,this)):Xf(e,this.RULE.reg,Zs(t=this.toHtml).call(t,this),!0,1)}},{key:"toHtml",value:function(e,t,n,r){var a,i,o,s,c;return r?(null===(i=this.suggester[n])||void 0===i||null===(o=i.echo)||void 0===o?void 0:o.call(this,r))||Vs(s=Vs(c="".concat(t,'<span class="cherry-suggestion">')).call(c,n)).call(s,r,"</span>"):!1===(null===(a=this.suggester[n])||void 0===a?void 0:a.echo)?"".concat(t):this.suggester[n]?r?t+r:"".concat(t):t+r}},{key:"rule",value:function(){var e,t,n;if(!this.suggester||oc(this.suggester).length<=0)return{};var r=Lc(e=oc(this.suggester)).call(e,(function(e){return dg(e)})).join("|");return{reg:new RegExp(Vs(t=Vs(n="".concat(lf()?"((?<!\\\\))[ ]":"(^|[^\\\\])[ ]","(")).call(n,r,")(([^")).call(t,r,"\\s])+)"),"g")}}},{key:"mounted",value:function(){if(!this.suggesterPanel.hasEditor()&&fp()){var e=this.$engine.$cherry.editor;this.suggesterPanel.setEditor(e),this.suggesterPanel.setSuggester(this.suggester),this.suggesterPanel.bindEvent()}}}]),n}();hi(Eg,"HOOK_NAME","suggester");var Sg=function(){function e(t){mn(this,e),hi(this,"panelWrap",'<div class="cherry-suggester-panel"></div>'),this.searchCache=!1,this.searchKeyCache=[],this.optionList=[],this.cursorMove=!0,this.suggesterConfig={},this.$cherry=t}return gn(e,[{key:"tryCreatePanel",value:function(){!this.$suggesterPanel&&fp()&&document&&(this.$cherry.wrapperDom.appendChild(this.createDom(this.panelWrap)),this.$suggesterPanel=this.$cherry.wrapperDom.querySelector(".cherry-suggester-panel"))}},{key:"hasEditor",value:function(){return!!this.editor&&!!this.editor.editor.display&&!!this.editor.editor.display.wrapper}},{key:"setEditor",value:function(e){this.editor=e}},{key:"setSuggester",value:function(e){this.suggesterConfig=e}},{key:"bindEvent",value:function(){var e=this,t=!1;this.editor.editor.on("change",(function(n,r){t=!0,e.onCodeMirrorChange(n,r)})),this.editor.editor.on("keydown",(function(n,r){t=!0,e.enableRelate()&&e.onKeyDown(n,r)})),this.editor.editor.on("cursorActivity",(function(){t||e.stopRelate(),t=!1}));var n=this.editor.editor.getOption("extraKeys"),r=["Up","Down","Enter"];ac(r).call(r,(function(t){if("function"==typeof n[t]){var r=n[t];n[t]=function(t){if(e.cursorMove){var n=r.call(t,t);if(n)return n}}}else if(n[t]){if("string"==typeof n[t]){var a=n[t];n[t]=function(t){e.cursorMove&&e.editor.editor.execCommand(a)}}}else n[t]=function(){if(e.cursorMove)return yg()}})),this.editor.editor.setOption("extraKeys",n),this.editor.editor.on("scroll",(function(t,n){e.searchCache&&e.relocatePanel(e.editor.editor)})),this.onClickPanelItem()}},{key:"onClickPanelItem",value:function(){var e=this;this.tryCreatePanel(),this.$suggesterPanel.addEventListener("click",(function(t){var n,r,a,i,o=(n=e.$suggesterPanel,r=t.target,i=-1,ac(a=n.childNodes).call(a,(function(e,t){return e===r?i=t:""})),i);o>-1&&e.pasteSelectResult(o),e.stopRelate()}),!1)}},{key:"showSuggesterPanel",value:function(e){var t=e.left,n=e.top,r=e.items;this.tryCreatePanel(),!this.$suggesterPanel&&fp()&&(this.$cherry.wrapperDom.appendChild(this.createDom(this.panelWrap)),this.$suggesterPanel=this.$cherry.wrapperDom.querySelector(".cherry-suggester-panel")),this.updatePanel(r),this.$suggesterPanel.style.left="".concat(t,"px"),this.$suggesterPanel.style.top="".concat(n,"px"),this.$suggesterPanel.style.display="block",this.$suggesterPanel.style.position="absolute",this.$suggesterPanel.style.zIndex="100"}},{key:"hideSuggesterPanel",value:function(){this.tryCreatePanel(),this.$suggesterPanel&&(this.$suggesterPanel.style.display="none")}},{key:"updatePanel",value:function(e){var t=this;this.tryCreatePanel();var n=Lc(e).call(e,(function(e,n){if("object"===ci(e)&&null!==e){var r,a=e.label;if(null!=e&&e.icon)a=Vs(r='<i class="ch-icon ch-icon-'.concat(e.icon,'"></i>')).call(r,a);return t.renderPanelItem(a,!1)}return t.renderPanelItem(e,!1)})).join(""),r=this.suggesterConfig[this.keyword];r&&"function"==typeof r.suggestListRender&&(n=r.suggestListRender.call(this,e)||n),this.$suggesterPanel.innerHTML="","string"==typeof n?this.$suggesterPanel.innerHTML=n:rl(n)&&n.length>0?ac(n).call(n,(function(e){t.$suggesterPanel.appendChild(e)})):"object"===ci(n)&&1===n.nodeType&&this.$suggesterPanel.appendChild(n)}},{key:"renderPanelItem",value:function(e,t){return t?'<div class="cherry-suggester-panel__item cherry-suggester-panel__item--selected">'.concat(e,"</div>"):'<div class="cherry-suggester-panel__item">'.concat(e,"</div>")}},{key:"createDom",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";this.template||(this.template=document.createElement("div")),this.template.innerHTML=qc(e).call(e);var t=document.createDocumentFragment();return Lc(Array.prototype).call(this.template.childNodes,(function(e,n){t.appendChild(e)})),t}},{key:"relocatePanel",value:function(e){var t=this.$cherry.wrapperDom.querySelector(".CodeMirror-cursors .CodeMirror-cursor");if(t||(t=this.$cherry.wrapperDom.querySelector(".CodeMirror-selected")),!t)return!1;var n=this.$cherry.wrapperDom.getBoundingClientRect(),r=t.getBoundingClientRect(),a=r.top+r.height+5-n.top,i=r.left-n.left;this.showSuggesterPanel({left:i,top:a,items:this.optionList})}},{key:"getCursorPos",value:function(e){var t=document.querySelector(".CodeMirror-cursors .CodeMirror-cursor");if(!t)return null;var n=e.getCursor(),r=e.lineInfo(n.line).handle.height,a=t.getBoundingClientRect(),i=a.top+r;return{left:a.left,top:i}}},{key:"startRelate",value:function(e,t,n){this.cursorFrom=n,this.keyword=t,this.searchCache=!0,this.relocatePanel(e)}},{key:"stopRelate",value:function(){this.hideSuggesterPanel(),this.cursorFrom=null,this.cursorTo=null,this.keyword="",this.searchKeyCache=[],this.searchCache=!1,this.cursorMove=!0,this.optionList=[]}},{key:"pasteSelectResult",value:function(e,t){if(this.cursorTo&&this.cursorTo!==this.cursorFrom||(this.cursorTo=JSON.parse(Hh(this.cursorFrom))),this.cursorTo){this.cursorTo.ch+=1;var n=this.cursorFrom,r=this.cursorTo;if(this.optionList[e]){var a="";if("object"===ci(this.optionList[e])&&null!==this.optionList[e]&&"string"==typeof this.optionList[e].value)a=this.optionList[e].value;else if("object"===ci(this.optionList[e])&&null!==this.optionList[e]&&"function"==typeof this.optionList[e].value)a=this.optionList[e].value();else{var i;a=Vs(i=" ".concat(this.keyword)).call(i,this.optionList[e]," ")}if(a&&this.editor.editor.replaceRange(a,n,r),this.optionList[e].goLeft){var o=this.editor.editor.getCursor();this.editor.editor.setCursor(o.line,o.ch-this.optionList[e].goLeft)}if(this.optionList[e].selection){var s=this.editor.editor.getCursor().line,c=this.editor.editor.getCursor().ch;this.editor.editor.setSelection({line:s,ch:c-this.optionList[e].selection.from},{line:s,ch:c-this.optionList[e].selection.to})}}}}},{key:"findSelectedItemIndex",value:function(){return bc(Array.prototype).call(this.$suggesterPanel.childNodes,(function(e){return e.classList.contains("cherry-suggester-panel__item--selected")}))}},{key:"enableRelate",value:function(){return this.searchCache}},{key:"onCodeMirrorChange",value:function(e,t){var n=this,r=t.text,a=t.from,i=t.to,o=t.origin,s=1===r.length?r[0]:"";if(!this.enableRelate()&&this.suggesterConfig[s]&&this.startRelate(e,s,a),this.enableRelate()&&(s||"+delete"===o)){var c;if(this.cursorTo=i,s)this.searchKeyCache.push(s);else if("+delete"===o&&(this.searchKeyCache.pop(),0===this.searchKeyCache.length))return void this.stopRelate();"function"==typeof(null===(c=this.suggesterConfig[this.keyword])||void 0===c?void 0:c.suggestList)&&this.suggesterConfig[this.keyword].suggestList(this.searchKeyCache.join(""),(function(e){!1!==e?(n.optionList=e&&e.length?e:[],n.updatePanel(n.optionList)):n.stopRelate()}))}}},{key:"onKeyDown",value:function(e,t){var n,r=this;if(this.tryCreatePanel(),!this.$suggesterPanel)return!1;var a=t.keyCode;if(Yh(n=[38,40]).call(n,a)){if(0===this.optionList.length)return void rg((function(){r.stopRelate()}),0);this.cursorMove=!1;var i=this.$suggesterPanel.querySelector(".cherry-suggester-panel__item--selected")||this.$suggesterPanel.querySelector(".cherry-suggester-panel__item:last-child"),o=null;38!==a||i.previousElementSibling?40!==a||i.nextElementSibling?38===a?o=i.previousElementSibling:40===a&&(o=i.nextElementSibling):o=this.$suggesterPanel.firstElementChild:o=this.$suggesterPanel.lastElementChild,i.classList.remove("cherry-suggester-panel__item--selected"),o.classList.add("cherry-suggester-panel__item--selected")}else if(13===a){var s=this.findSelectedItemIndex();s>=0&&(t.stopPropagation(),this.cursorMove=!1,this.pasteSelectResult(s,t),e.focus()),rg((function(){r.stopRelate()}),0)}else 27!==a&&37!==a&&39!==a||(t.stopPropagation(),e.focus(),rg((function(){r.stopRelate()}),0))}}]),e}();function Ag(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var xg=function(e){An(n,Cc);var t=Ag(n);function n(){return mn(this,n),t.apply(this,arguments)}return gn(n,[{key:"makeHtml",value:function(e){return this.test(e)?e.replace(this.RULE.reg,"$1<ruby>$2<rt>$3</rt></ruby>$4"):e}},{key:"rule",value:function(){var e={begin:"(^| )\\{",end:"\\}( |$)",content:"([^\n]+?)\\|([^\n]+?)"};return e.reg=new RegExp(e.begin+e.content+e.end,"g"),e}}]),n}();function Cg(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(xg,"HOOK_NAME","ruby");var Tg=function(e){An(n,eu);var t=Cg(n);function n(e){var r;return mn(this,n),(r=t.call(this,{needCache:!0})).initBrReg(e.globalConfig.classicBr),r}return gn(n,[{key:"makeHtml",value:function(e,t){var n=this;return e.replace(this.RULE.reg,(function(e,r,a,i){var o,s,c,l,u,f=n.getLineCount(e,r),d=n.$engine.md5(e),p=n.$getPanelInfo(a,i,t),h=p.title,g=p.body,m=p.appendStyle,b=p.className;return el(e,n.pushCache(Vs(o=Vs(s=Vs(c=Vs(l=Vs(u='<div class="'.concat(b,'" data-sign="')).call(u,d,'" data-lines="')).call(l,f,'" ')).call(c,m,">")).call(s,h)).call(o,g,"</div>"),d,f))}))}},{key:"$getClassByType",value:function(e){return/(left|right|center)/i.test(e)?"cherry-text-align cherry-text-align__".concat(e):"cherry-panel cherry-panel__".concat(e)}},{key:"$getPanelInfo",value:function(e,t,n){var r,a=this,i={type:this.$getTargetType(e),title:n(this.$getTitle(e)).html,body:t,appendStyle:"",className:""};i.className=this.$getClassByType(i.type),/(left|right|center)/i.test(i.type)&&(i.appendStyle='style="text-align:'.concat(i.type,';"')),i.title=Vs(r='<div class="cherry-panel--title '.concat(i.title?"cherry-panel--title__not-empty":"",'">')).call(r,i.title,"</div>");var o=function(e){var t,r;if(""===qc(e).call(e))return"";var i=n(e).html,o="p";return new RegExp("<(".concat(Wl,")[^>]*>"),"i").test(i)&&(o="div"),Vs(t=Vs(r="<".concat(o,">")).call(r,a.$cleanParagraph(i),"</")).call(t,o,">")},s="";return s=this.isContainsCache(i.body)?this.makeExcludingCached(i.body,o):o(i.body),i.body='<div class="cherry-panel--body">'.concat(s,"</div>"),i}},{key:"$getTitle",value:function(e){var t=qc(e).call(e);return/\s/.test(t)?t.replace(/[^\s]+\s/,""):""}},{key:"$getTargetType",value:function(e){var t=/\s/.test(qc(e).call(e))?qc(e).call(e).replace(/\s.*$/,""):e;switch(qc(t).call(t).toLowerCase()){case"primary":case"p":default:return"primary";case"info":case"i":return"info";case"warning":case"w":return"warning";case"danger":case"d":return"danger";case"success":case"s":return"success";case"right":case"r":return"right";case"center":case"c":return"center";case"left":case"l":return"left"}}},{key:"rule",value:function(){return(e={begin:/(?:^|\n)(\n*(?:[^\S\n]*)):::([^:][^\n]+?)\s*\n/,content:/([\w\W]*?)/,end:/\n[ \t]*:::[ \t]*(?=$|\n+)/}).reg=new RegExp(e.begin.source+e.content.source+e.end.source,"g"),e;var e}}]),n}();function $g(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}hi(Tg,"HOOK_NAME","panel");var Rg=function(e){An(n,eu);var t=$g(n);function n(){return mn(this,n),t.call(this,{needCache:!0})}return gn(n,[{key:"makeHtml",value:function(e,t){var n=this;return e.replace(this.RULE.reg,(function(e,r,a,i,o){var s,c,l,u=n.getLineCount(e,r),f=n.$engine.md5(e),d=n.$getDetailInfo(a,i,o,t),p=d.type,h=d.html;return el(e,n.pushCache(Vs(s=Vs(c=Vs(l='<div class="cherry-detail cherry-detail__'.concat(p,'" data-sign="')).call(l,f,'" data-lines="')).call(c,u,'" >')).call(s,h,"</div>"),f,u))}))}},{key:"$getDetailInfo",value:function(e,t,n,r){var a=this,i=/\n\s*(\+\+|\+\+-)\s*[^\n]+\n/.test(n)?"multiple":"single",o=n.split(/\n\s*(\+\+[-]{0,1}\s*[^\n]+)\n/),s="-"===e,c=t,l="";return"multiple"===i?ac(o).call(o,(function(e){if(/^\s*\+\+/.test(e))return s=/^\s*\+\+-/.test(e),c=e.replace(/\+\+[-]{0,1}\s*([^\n]+)$/,"$1"),!0;l+=a.$getDetailHtml(s,c,e,r)})):l=this.$getDetailHtml(s,c,n,r),{type:i,html:l}}},{key:"$getDetailHtml",value:function(e,t,n,r){var a=this,i="<details ".concat(e?"open":"",">"),o=function(e){var t,n;if(""===qc(e).call(e))return"";var i=r(e).html,o="p";return new RegExp("<(".concat(Wl,")[^>]*>"),"i").test(i)&&(o="div"),Vs(t=Vs(n="<".concat(o,">")).call(n,a.$cleanParagraph(i),"</")).call(t,o,">")};i+="<summary>".concat(r(t).html,"</summary>");var s="";return s=this.isContainsCache(n)?this.makeExcludingCached(n,o):o(n),i+='<div class="cherry-detail-body">'.concat(s,"</div>"),i+="</details>"}},{key:"rule",value:function(){return(e={begin:/(?:^|\n)(\n*(?:[^\S\n]*))\+\+\+([-]{0,1})\s+([^\n]+)\n/,content:/([\w\W]+?)/,end:/\n[ \t]*\+\+\+[ \t]*(?=$|\n+)/}).reg=new RegExp(e.begin.source+e.content.source+e.end.source,"g"),e;var e}}]),n}();hi(Rg,"HOOK_NAME","detail");var Og=[md,vd,ih,sh,$h,bh,yh,Yd,pp,up,Op,gh,Kd,gp,Tp,Rg,Tg,jd,Nh,_p,$d,Lp,Nd,ed,Jf,nd,ud,cd,xg,od,jh,Bh,Eg],Pg={run:function(e){var t,n="<div>".concat(e,"</div>");this.tagParser.formatEngine=this.mdFormatEngine,n=n.replace(/<!--[\s\S]*?-->/g,"");var r=this.htmlParser.parseHtml(n);return r=this.paragraphStyleClear(r),qc(t=this.$dealHtml(r).replace(/\n{3,}/g,"\n\n\n").replace(/&gt;/g,">").replace(/&lt;/g,"<").replace(/&amp;/g,"&")).call(t,"\n")},$dealHtml:function(e){for(var t="",n=0;n<e.length;n++){var r=e[n];"tag"===r.type?t=this.$handleTagObject(r,t):"text"===r.type&&r.content.length>0&&(t+=r.content.replace(/&nbsp;/g," ").replace(/[\n]+/g,"\n").replace(/^[ \t\n]+\n\s*$/,"\n"))}return t},$handleTagObject:function(e,t){var n,r=t;e.attrs.class&&/(ch-icon-square|ch-icon-check)/.test(e.attrs.class)?Il(n=e.attrs.class).call(n,"ch-icon-check")>=0?r+="[x]":r+="[ ]":e.attrs.class&&/cherry-code-preview-lang-select/.test(e.attrs.class)?r+="":r+=this.$dealTag(e);return r},$dealTag:function(e){var t=this,n="";return e.children&&(n=t.$dealHtml(e.children)),/(style|meta|link|script)/.test(e.name)?"":"code"===e.name||"pre"===e.name?t.tagParser.codeParser(e,t.$dealCodeTag(e),"pre"===e.name):"function"==typeof t.tagParser["".concat(e.name,"Parser")]?t.tagParser["".concat(e.name,"Parser")](e,n):n},$dealCodeTag:function(e){if(e.children.length<0)return"";for(var t="",n=0;n<e.children.length;n++){var r=e.children[n];"text"!==r.type?("li"===r.name&&(t+="\n"),"br"===r.name&&(t+="\n"),t+=this.$dealCodeTag(r)):t+=r.content}return t},htmlParser:{attrRE:/([\w-]+)|['"]{1}([^'"]*)['"]{1}/g,lookup:{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,video:!0,input:!0,keygen:!0,link:!0,menuitem:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},tagRE:/<(?:"[^"]*"['"]*|'[^']*'['"]*|[^'">])+>/g,empty:vg?vg(null):{},parseTags:function(e){var t,n=this,r=0,a={type:"tag",name:"",voidElement:!1,attrs:{},children:[]};return e.replace(this.attrRE,(function(i){r%2?t=i:0===r?((n.lookup[i]||"/"===e.charAt(e.length-2))&&(a.voidElement=!0),a.name=i):a.attrs[t]=i.replace(/['"]/g,""),r+=1})),a},parseHtml:function(e,t){var n=this,r=t||{};r.components||(r.components=this.empty);var a,i=[],o=-1,s=[],c={},l=!1;return e.replace(this.tagRE,(function(t,u){if(l){if(t!=="</".concat(a.name,">"))return;l=!1}var f,d="/"!==t.charAt(1),p=u+t.length,h=e.charAt(p);d&&(o+=1,"tag"===(a=n.parseTags(t)).type&&r.components[a.name]&&(a.type="component",l=!0),a.voidElement||l||!h||"<"===h||a.children.push({type:"text",content:Kf(e).call(e,p,Il(e).call(e,"<",p))}),c[a.tagName]=a,0===o&&i.push(a),(f=s[o-1])&&f.children.push(a),s[o]=a),d&&!a.voidElement||(o-=1,!l&&"<"!==h&&h&&s[o]&&s[o].children.push({type:"text",content:Kf(e).call(e,p,Il(e).call(e,"<",p))}))})),i}},tagParser:{formatEngine:{},pParser:function(e,t){var n=t;return/\n$/.test(n)?n:"".concat(n,"\n")},divParser:function(e,t){var n=t;return/\n$/.test(n)?n:"".concat(n,"\n")},spanParser:function(e,t){var n=t.replace(/\t/g,"").replace(/\n/g," ");return e.attrs&&e.attrs.style,n},codeParser:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return this.formatEngine.convertCode(t,n)},brParser:function(e,t){return this.formatEngine.convertBr(t,"\n")},imgParser:function(e,t){return e.attrs&&"tapd-graph"===e.attrs["data-control"]?this.formatEngine.convertGraph(e.attrs.title,e.attrs.src,e.attrs["data-origin-xml"],e):e.attrs&&e.attrs.src?this.formatEngine.convertImg(e.attrs.alt,e.attrs.src):void 0},videoParser:function(e,t){if(e.attrs&&e.attrs.src)return this.formatEngine.convertVideo(t,e.attrs.src,e.attrs.poster,e.attrs.title)},bParser:function(e,t){for(var n=t.split("\n"),r=[],a=0;a<n.length;a++)r.push(this.formatEngine.convertB(n[a]));return r.join("\n")},iParser:function(e,t){for(var n=t.split("\n"),r=[],a=0;a<n.length;a++)r.push(this.formatEngine.convertI(n[a]));return r.join("\n")},strikeParser:function(e,t){for(var n=t.split("\n"),r=[],a=0;a<n.length;a++)r.push(this.formatEngine.convertStrike(n[a]));return r.join("\n")},delParser:function(e,t){for(var n=t.split("\n"),r=[],a=0;a<n.length;a++)r.push(this.formatEngine.convertDel(n[a]));return r.join("\n")},uParser:function(e,t){for(var n=t.split("\n"),r=[],a=0;a<n.length;a++)r.push(this.formatEngine.convertU(n[a]));return r.join("\n")},aParser:function(e,t){return e.attrs&&e.attrs.href?this.formatEngine.convertA(t,e.attrs.href):""},supParser:function(e,t){return this.formatEngine.convertSup(t)},subParser:function(e,t){return this.formatEngine.convertSub(t)},tdParser:function(e,t){return this.formatEngine.convertTd(t)},trParser:function(e,t){return this.formatEngine.convertTr(t)},thParser:function(e,t){return this.formatEngine.convertTh(t)},theadParser:function(e,t){return this.formatEngine.convertThead(t)},tableParser:function(e,t){return this.formatEngine.convertTable(t)},liParser:function(e,t){return this.formatEngine.convertLi(t)},ulParser:function(e,t){return this.formatEngine.convertUl(t)},olParser:function(e,t){return this.formatEngine.convertOl(t)},strongParser:function(e,t){return this.formatEngine.convertStrong(t)},hrParser:function(e,t){return this.formatEngine.convertHr(t)},h1Parser:function(e,t){return this.formatEngine.convertH1(t)},h2Parser:function(e,t){return this.formatEngine.convertH2(t)},h3Parser:function(e,t){return this.formatEngine.convertH3(t)},h4Parser:function(e,t){return this.formatEngine.convertH4(t)},h5Parser:function(e,t){return this.formatEngine.convertH5(t)},h6Parser:function(e,t){return this.formatEngine.convertH6(t)},blockquoteParser:function(e,t){return this.formatEngine.convertBlockquote(t.replace(/\n+/g,"\n"))},addressParser:function(e,t){return this.formatEngine.convertAddress(t.replace(/\n+/g,"\n"))},styleParser:{colorAttrParser:function(e){var t=e.match(/color:\s*(#[a-zA-Z0-9]{3,6});/);return t&&t[1]?t[1]:""},sizeAttrParser:function(e){var t=e.match(/font-size:\s*([a-zA-Z0-9-]+?);/);if(t&&t[1]){var n,r=0;if(/[0-9]+px/.test(t[1]))r=qc(n=t[1].replace(/px/,"")).call(n);else switch(t[1]){case"x-small":r=10;break;case"small":r=12;break;case"medium":r=16;break;case"large":r=18;break;case"x-large":r=24;break;case"xx-large":r=32;break;default:r=""}return r>0?r:""}return""},bgColorAttrParser:function(e){var t=e.match(/background-color:\s*([^;]+?);/);if(t&&t[1]){var n="";if(/rgb\([ 0-9]+,[ 0-9]+,[ 0-9]+\)/.test(t[1])){var r,a,i,o,s,c=t[1].match(/rgb\(([ 0-9]+),([ 0-9]+),([ 0-9]+)\)/);if(c[1]&&c[2]&&c[3])c[1]=Qc(qc(r=c[1]).call(r),10),c[2]=Qc(qc(a=c[2]).call(a),10),c[3]=Qc(qc(i=c[3]).call(i),10),n=Vs(o=Vs(s="#".concat(c[1].toString(16))).call(s,c[2].toString(16))).call(o,c[3].toString(16))}else{n=fd(t,2)[1]}return n}return""}}},mdFormatEngine:{convertColor:function(e,t){var n,r=qc(e).call(e);return!r||/\n/.test(r)?r:t?Vs(n="!!".concat(t," ")).call(n,r,"!!"):r},convertSize:function(e,t){var n,r=qc(e).call(e);return!r||/\n/.test(r)?r:t?Vs(n="!".concat(t," ")).call(n,r,"!"):r},convertBgColor:function(e,t){var n,r=qc(e).call(e);return!r||/\n/.test(r)?r:t?Vs(n="!!!".concat(t," ")).call(n,r,"!!!"):r},convertBr:function(e,t){return e+t},convertCode:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return/\n/.test(e)||t?"```\n".concat(e.replace(/\n+$/,""),"\n```"):"`".concat(e.replace(/`/g,"\\`"),"`")},convertB:function(e){return/^\s*$/.test(e)?"":"**".concat(e,"**")},convertI:function(e){return/^\s*$/.test(e)?"":"*".concat(e,"*")},convertU:function(e){return/^\s*$/.test(e)?"":" /".concat(e,"/ ")},convertImg:function(e,t){var n,r=e&&e.length>0?e:"image";return Vs(n="![".concat(r,"](")).call(n,t,")")},convertGraph:function(e,t,n,r){var a,i,o,s=e&&e.length>0?e:"graph",c="";if(r)try{var l,u=r.attrs;ac(l=oc(u)).call(l,(function(e){var t;Object.prototype.hasOwnProperty.call(u,e)&&(Il(e).call(e,"data-graph-")>=0&&u[e]&&(c+=Vs(t=" ".concat(e,"=")).call(t,u[e])))}))}catch(e){}return Vs(a=Vs(i=Vs(o="![".concat(s,"](")).call(o,t,"){data-control=tapd-graph data-origin-xml=")).call(i,n)).call(a,c,"}")},convertVideo:function(e,t,n,r){var a,i,o=r&&r.length>0?r:"video";return Vs(a=Vs(i="!video[".concat(o,"](")).call(i,t,"){poster=")).call(a,n,"}")},convertA:function(e,t){var n;if(e===t)return"".concat(e," ");var r=qc(e).call(e);return r?Vs(n="[".concat(r,"](")).call(n,t,")"):r},convertSup:function(e){return"^".concat(qc(e).call(e).replace(/\^/g,"\\^"),"^")},convertSub:function(e){return"^^".concat(qc(e).call(e).replace(/\^\^/g,"\\^\\^"),"^^")},convertTd:function(e){return"~|".concat(qc(e).call(e).replace(/\n{1,}/g,"<br>").replace(/ /g,"~s~")," ~|")},convertTh:function(e){return/^\s*$/.test(e)?"":"~|".concat(qc(e).call(e).replace(/\n{1,}/g,"<br>")," ~|")},convertTr:function(e){return/^\s*$/.test(e)?"":"".concat(qc(e).call(e).replace(/\n/g,""),"\n")},convertThead:function(e){var t,n="".concat(e.replace(/[ \t]+/g,"").replace(/~\|~\|/g,"~|").replace(/~\|/g,"|"),"\n"),r=n.match(/\|/g).length-1;return Vs(t="".concat(n,"|")).call(t,Ld(":-:|").call(":-:|",r),"\n")},convertTable:function(e){var t="\n".concat(e.replace(/[ \t]+/g,"").replace(/~\|~\|/g,"~|").replace(/~\|/g,"|"),"\n").replace(/\n{2,}/g,"\n").replace(/\n[ \t]+\n/g,"\n").replace(/~s~/g," ");if(!/\|:-:\|/.test(t)){var n,r,a=t.match(/^\n[^\n]+\n/)[0].match(/\|/g).length-1;t=Vs(n=Vs(r="\n|".concat(Ld(" |").call(" |",a),"\n|")).call(r,Ld(":-:|").call(":-:|",a))).call(n,t)}return t},convertLi:function(e){return"- ".concat(e.replace(/^\n/,"").replace(/\n+$/,"").replace(/\n+/g,"\n\t"),"\n")},convertUl:function(e){return"".concat(e,"\n")},convertOl:function(e){for(var t=e.split("\n"),n=1,r=0;r<t.length;r++)/^- /.test(t[r])&&(t[r]=t[r].replace(/^- /,"".concat(n,". ")),n+=1);var a=t.join("\n");return"".concat(a,"\n")},convertStrong:function(e){return/^\s*$/.test(e)?"":"**".concat(e,"**")},convertStrike:function(e){return/^\s*$/.test(e)?"":"~~".concat(e,"~~")},convertDel:function(e){return/^\s*$/.test(e)?"":"~~".concat(e,"~~")},convertHr:function(e){return/^\s*$/.test(e)?"\n\n----\n":"\n\n----\n".concat(e)},convertH1:function(e){return"# ".concat(qc(e).call(e).replace(/\n+$/,""),"\n\n")},convertH2:function(e){return"## ".concat(qc(e).call(e).replace(/\n+$/,""),"\n\n")},convertH3:function(e){return"### ".concat(qc(e).call(e).replace(/\n+$/,""),"\n\n")},convertH4:function(e){return"#### ".concat(qc(e).call(e).replace(/\n+$/,""),"\n\n")},convertH5:function(e){return"##### ".concat(qc(e).call(e).replace(/\n+$/,""),"\n\n")},convertH6:function(e){return"###### ".concat(qc(e).call(e).replace(/\n+$/,""),"\n\n")},convertBlockquote:function(e){return">".concat(qc(e).call(e),"\n\n")},convertAddress:function(e){return">".concat(qc(e).call(e),"\n\n")}},paragraphStyleClear:function(e){for(var t=0;t<e[0].children.length;t++){for(var n=[e[0].children[t]],r=[];n.length;){var a=n.shift(),i=this.notEmptyTagCount(a);if(1===i)r.push(a);else if(i>1)for(var o=0;o<a.children.length;o++)n.push(a.children[o]);else 1===r.length&&this.clearChildColorAttrs(r.pop()),r=[]}1===r.length&&this.clearChildColorAttrs(r.pop())}return e},notEmptyTagCount:function(e){if(!e||e.voidElement||"tag"===e.type&&!e.children.length||"text"===e.type&&!e.content.replace(/(\r|\n|\s)+/g,""))return 0;if(e.children&&e.children.length){for(var t=0,n=0;n<e.children.length;n++)t+=this.notEmptyTagCount(e.children[n]);return t}return 1},clearChildColorAttrs:function(e){var t=this;this.forEachHtmlParsedItems(e,(function(e){t.clearSelfNodeColorAttrs(e)}))},clearSelfNodeColorAttrs:function(e){if(e.attrs&&e.attrs.style){for(var t=e.attrs.style.split(";"),n=[],r=0;r<t.length;r++){var a;t[r]&&-1===Il(a=t[r]).call(a,"color")&&n.push(t[r])}n.length?e.attrs.style="".concat(n.join(";"),";"):delete e.attrs.style}},forEachHtmlParsedItems:function(e,t){if(e&&(t(e),e.children&&e.children.length))for(var n=0;n<e.children.length;n++)this.forEachHtmlParsedItems(e.children[n],t)}},Lg=Pg,Ig=function(){function e(t,n){mn(this,e),this.$cherry=n,Us(this,"_cherry",{get:function(){return Ju.warn("`_engine._cherry` is deprecated. Use `$engine.$cherry` instead."),this.$cherry}}),this.initMath(t),this.$configInit(t),this.hookCenter=new af(Og,t,n),this.hooks=this.hookCenter.getHookList(),this.md5Cache={},this.md5StrMap={},this.markdownParams=t,this.currentStrMd5=[],this.globalConfig=t.engine.global,this.htmlWhiteListAppend=this.globalConfig.htmlWhiteList}return gn(e,[{key:"initMath",value:function(e){var t=e.externals,n=e.engine.syntax,r=n.mathBlock.plugins;if(fp()&&(n.mathBlock.src||n.inlineMath.src)&&!t.MathJax&&!window.MathJax){!function(e){if(fp()){var t=e?["input/asciimath","[tex]/noerrors","[tex]/cancel","[tex]/color","[tex]/boldsymbol","ui/safe"]:["ui/safe"];window.MathJax={startup:{elements:[".Cherry-Math",".Cherry-InlineMath"],typeset:!0},tex:{inlineMath:[["$","$"],["\\(","\\)"]],displayMath:[["$$","$$"],["\\[","\\]"]],tags:"ams",packages:{"[+]":["noerrors","cancel","color"]},macros:{bm:["{\\boldsymbol{#1}}",1]}},options:{skipHtmlTags:["script","noscript","style","textarea","pre","code","a"],ignoreHtmlClass:"tex2jax_ignore",processHtmlClass:"tex2jax_process",enableMenu:!1},loader:{load:t}}}}(r);var a=document.createElement("script");a.src=n.mathBlock.src?n.mathBlock.src:n.inlineMath.src,a.async=!0,a.src&&document.head.appendChild(a)}}},{key:"$configInit",value:function(e){if(e.hooksConfig&&Zu(e.hooksConfig.hooksList,Array))for(var t=0;t<e.hooksConfig.hooksList.length;t++){var n=e.hooksConfig.hooksList[t];try{"sentence"===n.getType()&&Yu(n,Cc),"paragraph"===n.getType()&&Yu(n,eu),Xu(n),Og.push(n)}catch(e){throw new Error("the hook does not correctly inherit")}}}},{key:"$beforeMakeHtml",value:function(e){var t=e.replace(/~/g,"~T");return"\n"!==(t=(t=(t=t.replace(/\$/g,"~D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n"))[t.length-1]&&(t+="\n"),t=this.$fireHookAction(t,"sentence","beforeMakeHtml"),t=this.$fireHookAction(t,"paragraph","beforeMakeHtml")}},{key:"$afterMakeHtml",value:function(e){var t=this.$fireHookAction(e,"paragraph","afterMakeHtml");return t=(t=(t=(t=t.replace(/~D/g,"$")).replace(/~T/g,"~")).replace(/\\<\//g,"\\ </")).replace(new RegExp("\\\\(".concat(df,")"),"g"),(function(e,t){return"&"===t?e:Kl(t)})).replace(/\\&(?!(amp|lt|gt|quot|apos);)/,(function(){return"&amp;"})),t=(t=t.replace(/\\ <\//g,"\\</")).replace(/id="safe_(?=.*?")/g,'id="'),t=Cd.restoreAll(t)}},{key:"$dealSentenceByCache",value:function(e){var t=this;return this.$checkCache(e,(function(e){return t.$dealSentence(e)}))}},{key:"$dealSentence",value:function(e){var t;return this.$fireHookAction(e,"sentence","makeHtml",Zs(t=this.$dealSentenceByCache).call(t,this))}},{key:"$fireHookAction",value:function(e,t,n,r){var a=this,i=e,o="afterMakeHtml"===n?"reduceRight":"reduce";if(!this.hooks&&!this.hooks[t]&&!this.hooks[t][o])return i;try{i=this.hooks[t][o]((function(e,t){return t.$engine||(t.$engine=a,Us(t,"_engine",{get:function(){return Ju.warn("`this._engine` is deprecated. Use `this.$engine` instead."),this.$engine}})),t[n]?t[n](e,r,a.markdownParams):e}),i)}catch(e){throw new Vu(e)}return i}},{key:"md5",value:function(e){return this.md5StrMap[e]||(this.md5StrMap[e]=Sd(e)),this.md5StrMap[e]}},{key:"$checkCache",value:function(e,t){var n=this.md5(e);return void 0===this.md5Cache[n]&&(this.md5Cache[n]=t(e)),{sign:n,html:this.md5Cache[n]}}},{key:"$dealParagraph",value:function(e){var t;return this.$fireHookAction(e,"paragraph","makeHtml",Zs(t=this.$dealSentenceByCache).call(t,this))}},{key:"makeHtml",value:function(e){var t=this.$beforeMakeHtml(e);return t=this.$dealParagraph(t),t=this.$afterMakeHtml(t)}},{key:"mounted",value:function(){this.$fireHookAction("","sentence","mounted"),this.$fireHookAction("","paragraph","mounted")}},{key:"makeMarkdown",value:function(e){return Lg.run(e)}}]),e}();function Ng(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"absolute",n=e.getBoundingClientRect();return"fixed"===t?n:"sidebar"===t?{left:Mg.getTargetParentByButton(e).offsetLeft-130+n.width,top:e.offsetTop+n.height/2,width:n.width,height:n.height}:{left:e.offsetLeft,top:e.offsetTop,width:n.width,height:n.height}}var Mg=function(){function e(t){mn(this,e),hi(this,"_onClick",void 0),this.$cherry=t,this.bubbleMenu=!1,this.subMenu=null,this.name="",this.editor=t.editor,this.locale=t.locale,this.dom=null,this.updateMarkdown=!0,this.subMenuConfig=[],this.noIcon=!1,this.cacheOnce=!1,this.positionModel="absolute","function"==typeof this._onClick&&(Ju.warn("`MenuBase._onClick` is deprecated. Override `fire` instead"),this.fire=this._onClick)}return gn(e,[{key:"getSubMenuConfig",value:function(){return this.subMenuConfig}},{key:"setName",value:function(e,t){this.name=e,this.iconName=t}},{key:"setCacheOnce",value:function(e){this.cacheOnce=e}},{key:"getAndCleanCacheOnce",value:function(){this.updateMarkdown=!0;var e=this.cacheOnce;return this.cacheOnce=!1,e}},{key:"hasCacheOnce",value:function(){return!1!==this.cacheOnce}},{key:"createBtn",value:function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=rh("span",e?"cherry-dropdown-item":"cherry-toolbar-button cherry-toolbar-".concat(this.iconName?this.iconName:this.name),{title:this.locale[this.name]||Zl(this.name)});if(this.iconName&&!this.noIcon){var n=rh("i","ch-icon ch-icon-".concat(this.iconName));t.appendChild(n)}return(e||this.noIcon)&&(t.innerHTML+=this.locale[this.name]||Zl(this.name)),e||this.dom||(this.dom=t),t}},{key:"createSubBtnByConfig",value:function(e){var t=e.name,n=e.iconName,r=e.icon,a=e.onclick,i=rh("span","cherry-dropdown-item",{title:this.locale[t]||Zl(t)});if(n){var o=rh("i","ch-icon ch-icon-".concat(n));i.appendChild(o)}else if(r){var s=rh("img","ch-icon",{src:r,style:"width: 16px; height: 16px; vertical-align: sub;"});i.appendChild(s)}return i.innerHTML+=this.locale[t]||Zl(t),i.addEventListener("click",a,!1),i}},{key:"fire",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(null==e||e.stopPropagation(),"function"==typeof this.onClick){var r=this.editor.editor.getSelections();this.isSelections=r.length>1;var a=Lc(r).call(r,(function(r,a,i){return t.onClick(r,n,e)||i[a]}));!this.bubbleMenu&&this.updateMarkdown&&(this.editor.editor.replaceSelections(a,"around"),this.editor.editor.focus(),this.$afterClick())}}},{key:"$getSelectionRange",value:function(){var e=this.editor.editor.listSelections()[0],t=e.anchor,n=e.head;return t.line===n.line&&t.ch>n.ch||t.line>n.line?{begin:n,end:t}:{begin:t,end:n}}},{key:"registerAfterClickCb",value:function(e){this.afterClickCb=e}},{key:"$afterClick",value:function(){"function"!=typeof this.afterClickCb||this.isSelections||(this.afterClickCb(),this.afterClickCb=null)}},{key:"setLessSelection",value:function(e,t){var n,r,a,i,o=this.editor.editor,s=this.$getSelectionRange(),c=s.begin,l=s.end,u={line:(null===(n=e.match(/\n/g))||void 0===n?void 0:n.length)>0?c.line+e.match(/\n/g).length:c.line,ch:(null===(r=e.match(/\n/g))||void 0===r?void 0:r.length)>0?e.replace(/^[\s\S]*?\n([^\n]*)$/,"$1").length:c.ch+e.length},f=(null===(a=t.match(/\n/g))||void 0===a?void 0:a.length)>0?l.line-t.match(/\n/g).length:l.line,d={line:f,ch:(null===(i=t.match(/\n/g))||void 0===i?void 0:i.length)>0?o.getLine(f).length:l.ch-t.length};o.setSelection(u,d)}},{key:"getMoreSelection",value:function(e,t,n){var r=this.editor.editor,a=this.$getSelectionRange(),i=a.begin,o=a.end,s=/\n/.test(e)?0:i.ch-e.length;s=s<0?0:s;var c,l=/\n/.test(e)?i.line-e.match(/\n/g).length:i.line,u={line:l=l<0?0:l,ch:s},f=o.line,d=o.ch;/\n/.test(t)?(f=o.line+t.match(/\n/g).length,d=null===(c=r.getLine(f))||void 0===c?void 0:c.length):d=r.getLine(o.line).length<o.ch+t.length?r.getLine(o.line).length:o.ch+t.length;var p={line:f,ch:d};r.setSelection(u,p),!1===n()&&r.setSelection(i,o)}},{key:"getSelection",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"word",n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],r=this.editor.editor;if(this.isSelections)return e;if(e&&!n)return e;if("line"===t){var a=this.$getSelectionRange(),i=a.begin,o=a.end;return r.setSelection({line:i.line,ch:0},{line:o.line,ch:r.getLine(o.line).length}),r.getSelection()}if("word"===t){var s=r.findWordAt(r.getCursor()),c=s.anchor,l=s.head;return r.setSelection(c,l),r.getSelection()}}},{key:"bindSubClick",value:function(e,t){return this.fire(null,e)}},{key:"onClick",value:function(e,t,n){return e}},{key:"shortcutKeys",get:function(){return[]}},{key:"getMenuPosition",value:function(){var t=e.getTargetParentByButton(this.dom),n=/cherry-sidebar/.test(t.className);return/cherry-bubble/.test(t.className)||/cherry-floatmenu/.test(t.className)?this.positionModel="fixed":this.positionModel=n?"sidebar":"absolute",Ng(this.dom,this.positionModel)}}],[{key:"getTargetParentByButton",value:function(e){var t=e.parentElement;return/toolbar-(left|right)/.test(t.className)&&(t=t.parentElement),t}}]),e}();function jg(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}function Dg(e,t,n){var r,a={};return ac(r=oc(e)).call(r,(function(r){-1!==Il(t).call(t,r)&&("object"===ci(n)?"string"==typeof n[r]?ci(e[r])===n[r]&&(a[r]=e[r]):e[r]instanceof n[r]&&(a[r]=e[r]):"string"==typeof n&&ci(e[r])===n&&(a[r]=e[r]))})),a}var Bg={HOOKS_TYPE_LIST:xc},Fg=[];fp()||ac(Fg).call(Fg,(function(e){}));var Hg=function(){function e(){mn(this,e)}return gn(e,null,[{key:"usePlugin",value:function(t){var n;if(this===e)throw new Error("`usePlugin` is not allowed to called through CherryStatic class.");if(this.initialized)throw new Error("The function `usePlugin` should be called before Cherry is instantiated.");if(!0!==t.$cherry$mounted){for(var r=arguments.length,a=new Array(r>1?r-1:0),i=1;i<r;i++)a[i-1]=arguments[i];t.install.apply(t,Vs(n=[this.config.defaults]).call(n,a)),t.$cherry$mounted=!0}}}]),e}();hi(Hg,"createSyntaxHook",(function(e,t,n){var r,a=t===xc.PAR?eu:Cc,i=Dg(n,["beforeMakeHtml","makeHtml","afterMakeHtml","rule","test"],"function"),o={needCache:n.needCache,defaultCache:n.defaultCache};return r=function(e){An(r,e);var n=jg(r);function r(){var e,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return mn(this,r),(e=t===xc.PAR?n.call(this,{needCache:!!o.needCache,defaultCache:o.defaultCache}):n.call(this)).config=a.config,ui(e)}return gn(r,[{key:"beforeMakeHtml",value:function(){for(var e,t,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return i.beforeMakeHtml?i.beforeMakeHtml.apply(this,a):(e=Fd(pi(r.prototype),"beforeMakeHtml",this)).call.apply(e,Vs(t=[this]).call(t,a))}},{key:"makeHtml",value:function(){for(var e,t,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return i.makeHtml?i.makeHtml.apply(this,a):(e=Fd(pi(r.prototype),"makeHtml",this)).call.apply(e,Vs(t=[this]).call(t,a))}},{key:"afterMakeHtml",value:function(){for(var e,t,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return i.afterMakeHtml?i.afterMakeHtml.apply(this,a):(e=Fd(pi(r.prototype),"afterMakeHtml",this)).call.apply(e,Vs(t=[this]).call(t,a))}},{key:"test",value:function(){for(var e,t,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return i.test?i.test.apply(this,a):(e=Fd(pi(r.prototype),"test",this)).call.apply(e,Vs(t=[this]).call(t,a))}},{key:"rule",value:function(){for(var e,t,n=arguments.length,a=new Array(n),o=0;o<n;o++)a[o]=arguments[o];return i.rule?i.rule.apply(this,a):(e=Fd(pi(r.prototype),"rule",this)).call.apply(e,Vs(t=[this]).call(t,a))}}]),r}(a),hi(r,"HOOK_NAME",e),r})),hi(Hg,"createMenuHook",(function(e,t){var n=Dg(t,["subMenuConfig","onClick","shortcutKeys","iconName"],{subMenuConfig:Array,onClick:"function",shortcutKeys:Array,iconName:"string"});return function(t){An(a,Mg);var r=jg(a);function a(t){var i;return mn(this,a),i=r.call(this,t),n.iconName||(i.noIcon=!0),i.setName(e,n.iconName),i.subMenuConfig=n.subMenuConfig||[],i}return gn(a,[{key:"onClick",value:function(){for(var e,t,r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];return n.onClick?n.onClick.apply(this,i):(e=Fd(pi(a.prototype),"onClick",this)).call.apply(e,Vs(t=[this]).call(t,i))}},{key:"shortcutKeys",get:function(){return n.shortcutKeys?n.shortcutKeys:[]}}]),a}()})),hi(Hg,"constants",Bg),hi(Hg,"VERSION","0.8.40");var zg=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r&&!1!==t(e[n],n,e););return e},Ug=Bo(Object.keys,Object),Wg=Object.prototype.hasOwnProperty;var qg=function(e){if(!zo(e))return Ug(e);var t=[];for(var n in Object(e))Wg.call(e,n)&&"constructor"!=n&&t.push(n);return t};var Gg=function(e){return Jo(e)?ws(e):qg(e)};var Kg=function(e,t){return e&&bs(t,Gg(t),e)};var Zg=function(e,t){return e&&bs(t,xs(t),e)};var Yg=function(e,t){for(var n=-1,r=null==e?0:e.length,a=0,i=[];++n<r;){var o=e[n];t(o,n,e)&&(i[a++]=o)}return i};var Xg=function(){return[]},Vg=Object.prototype.propertyIsEnumerable,Jg=Object.getOwnPropertySymbols,Qg=Jg?function(e){return null==e?[]:(e=Object(e),Yg(Jg(e),(function(t){return Vg.call(e,t)})))}:Xg,em=Qg;var tm=function(e,t){return bs(e,em(e),t)};var nm=function(e,t){for(var n=-1,r=t.length,a=e.length;++n<r;)e[a+n]=t[n];return e},rm=Object.getOwnPropertySymbols?function(e){for(var t=[];e;)nm(t,em(e)),e=Fo(e);return t}:Xg;var am=function(e,t){return bs(e,rm(e),t)};var im=function(e,t,n){var r=t(e);return Xo(e)?r:nm(r,n(e))};var om=function(e){return im(e,Gg,em)};var sm=function(e){return im(e,xs,rm)},cm=ro(Oi,"DataView"),lm=ro(Oi,"Promise"),um=ro(Oi,"Set"),fm=ro(Oi,"WeakMap"),dm="[object Map]",pm="[object Promise]",hm="[object Set]",gm="[object WeakMap]",mm="[object DataView]",bm=Zi(cm),vm=Zi(ao),ym=Zi(lm),_m=Zi(um),km=Zi(fm),wm=Hi;(cm&&wm(new cm(new ArrayBuffer(1)))!=mm||ao&&wm(new ao)!=dm||lm&&wm(lm.resolve())!=pm||um&&wm(new um)!=hm||fm&&wm(new fm)!=gm)&&(wm=function(e){var t=Hi(e),n="[object Object]"==t?e.constructor:void 0,r=n?Zi(n):"";if(r)switch(r){case bm:return mm;case vm:return dm;case ym:return pm;case _m:return hm;case km:return gm}return t});var Em=wm,Sm=Object.prototype.hasOwnProperty;var Am=function(e){var t=e.length,n=new e.constructor(t);return t&&"string"==typeof e[0]&&Sm.call(e,"index")&&(n.index=e.index,n.input=e.input),n};var xm=function(e,t){var n=t?Io(e.buffer):e.buffer;return new e.constructor(n,e.byteOffset,e.byteLength)},Cm=/\w*$/;var Tm=function(e){var t=new e.constructor(e.source,Cm.exec(e));return t.lastIndex=e.lastIndex,t},$m=Pi?Pi.prototype:void 0,Rm=$m?$m.valueOf:void 0;var Om=function(e){return Rm?Object(Rm.call(e)):{}};var Pm=function(e,t,n){var r=e.constructor;switch(t){case"[object ArrayBuffer]":return Io(e);case"[object Boolean]":case"[object Date]":return new r(+e);case"[object DataView]":return xm(e,n);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return No(e,n);case"[object Map]":case"[object Set]":return new r;case"[object Number]":case"[object String]":return new r(e);case"[object RegExp]":return Tm(e);case"[object Symbol]":return Om(e)}};var Lm=function(e){return Wo(e)&&"[object Map]"==Em(e)},Im=fs&&fs.isMap,Nm=Im?us(Im):Lm;var Mm=function(e){return Wo(e)&&"[object Set]"==Em(e)},jm=fs&&fs.isSet,Dm=jm?us(jm):Mm,Bm="[object Arguments]",Fm="[object Function]",Hm="[object Object]",zm={};zm[Bm]=zm["[object Array]"]=zm["[object ArrayBuffer]"]=zm["[object DataView]"]=zm["[object Boolean]"]=zm["[object Date]"]=zm["[object Float32Array]"]=zm["[object Float64Array]"]=zm["[object Int8Array]"]=zm["[object Int16Array]"]=zm["[object Int32Array]"]=zm["[object Map]"]=zm["[object Number]"]=zm[Hm]=zm["[object RegExp]"]=zm["[object Set]"]=zm["[object String]"]=zm["[object Symbol]"]=zm["[object Uint8Array]"]=zm["[object Uint8ClampedArray]"]=zm["[object Uint16Array]"]=zm["[object Uint32Array]"]=!0,zm["[object Error]"]=zm[Fm]=zm["[object WeakMap]"]=!1;var Um=function e(t,n,r,a,i,o){var s,c=1&n,l=2&n,u=4&n;if(r&&(s=i?r(t,a,i,o):r(t)),void 0!==s)return s;if(!zi(t))return t;var f=Xo(t);if(f){if(s=Am(t),!c)return Mo(t,s)}else{var d=Em(t),p=d==Fm||"[object GeneratorFunction]"==d;if(ts(t))return Po(t,c);if(d==Hm||d==Bm||p&&!i){if(s=l||p?{}:Uo(t),!c)return l?am(t,Zg(s,t)):tm(t,Kg(s,t))}else{if(!zm[d])return i?t:{};s=Pm(t,d,c)}}o||(o=new Co);var h=o.get(t);if(h)return h;o.set(t,s),Dm(t)?t.forEach((function(a){s.add(e(a,n,r,a,t,o))})):Nm(t)&&t.forEach((function(a,i){s.set(i,e(a,n,r,i,t,o))}));var g=f?void 0:(u?l?sm:om:l?xs:Gg)(t);return zg(g||t,(function(a,i){g&&(a=t[i=a]),ms(s,i,e(a,n,r,i,t,o))})),s};var Wm=function(e){return Um(e,5)},qm={urlProcessor:function(e,t){return e},fileUpload:function(e,t){if(/video/i.test(e.type))t("images/demo-dog.png",{name:"".concat(e.name.replace(/\.[^.]+$/,"")),poster:"images/demo-dog.png?poster=true",isBorder:!0,isShadow:!0,isRadius:!0});else if(/image/i.test(e.type)){var n=new FileReader;n.onload=function(n){var r=n.target.result;t(r,{name:"".concat(e.name.replace(/\.[^.]+$/,"")),isShadow:!0,width:"60%",height:"auto"})},n.readAsDataURL(e)}else t("images/demo-dog.png")},afterChange:function(e,t){},afterInit:function(e,t){},beforeImageMounted:function(e,t){return{srcProp:e,src:t}},onClickPreview:function(e){},onCopyCode:function(e,t){return t},changeString2Pinyin:function(e){return e}},Gm=Wm({externals:{},openai:{apiKey:"",ignoreError:!1},engine:{global:{classicBr:!1,urlProcessor:qm.urlProcessor,htmlWhiteList:"",flowSessionContext:!0},syntax:{link:{target:"",rel:""},autoLink:{target:"",rel:"",enableShortLink:!0,shortLinkLength:20},list:{listNested:!1,indentSpace:2},table:{enableChart:!1},inlineCode:{theme:"red"},codeBlock:{theme:"dark",wrap:!0,lineNumber:!0,copyCode:!0,editCode:!0,changeLang:!0,selfClosing:!0,customRenderer:{},mermaid:{svg2img:!1},indentedCodeBlock:!0},emoji:{useUnicode:!0},fontEmphasis:{allowWhitespace:!1},strikethrough:{needWhitespace:!1},mathBlock:{engine:"MathJax",src:"",plugins:!0},inlineMath:{engine:"MathJax",src:""},toc:{allowMultiToc:!1},header:{anchorStyle:"default"}}},editor:{id:"code",name:"code",autoSave2Textarea:!1,theme:"default",height:"100%",defaultModel:"edit&preview",convertWhenPaste:!0,codemirror:{autofocus:!0},writingStyle:"normal",keepDocumentScrollAfterInit:!1},toolbars:{theme:"dark",showToolbar:!0,toolbar:["bold","italic","strikethrough","|","color","header","ruby","|","list","panel","detail",{insert:["image","audio","video","link","hr","br","code","formula","toc","table","line-table","bar-table","pdf","word"]},"graph","settings"],toolbarRight:[],sidebar:[],bubble:["bold","italic","underline","strikethrough","sub","sup","quote","|","size","color"],float:["h1","h2","h3","|","checklist","quote","table","code"],toc:!1,shortcutKey:{},config:{formula:{showLatexLive:!0,templateConfig:!1}}},drawioIframeUrl:"",fileUpload:qm.fileUpload,fileTypeLimitMap:{video:"video/*",audio:"audio/*",image:"image/*",word:".doc,.docx",pdf:".pdf",file:"*"},callback:{afterChange:qm.afterChange,afterInit:qm.afterInit,beforeImageMounted:qm.beforeImageMounted,onClickPreview:qm.onClickPreview,onCopyCode:qm.onCopyCode,changeString2Pinyin:qm.changeString2Pinyin},previewer:{dom:!1,className:"cherry-markdown",enablePreviewerBubble:!0,lazyLoadImg:{loadingImgPath:"",maxNumPerTime:2,noLoadImgNum:5,autoLoadImgNum:5,maxTryTimesPerSrc:2,beforeLoadOneImgCallback:function(e){return!0},failLoadOneImgCallback:function(e){},afterLoadOneImgCallback:function(e){},afterLoadAllImgCallback:function(){}}},theme:[{className:"default",label:"默认"},{className:"dark",label:"暗黑"},{className:"light",label:"明亮"},{className:"green",label:"清新"},{className:"red",label:"热情"},{className:"violet",label:"淡雅"},{className:"blue",label:"清幽"}],themeNameSpace:"cherry",isPreviewOnly:!1,autoScrollByCursor:!0,forceAppend:!0,locale:"en_US",autoScrollByHashAfterInit:!1});function Km(e){var t=function(){if("undefined"==typeof Reflect||!ln)return!1;if(ln.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(ln(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=pi(e);if(t){var a=pi(this).constructor;n=ln(r,arguments,a)}else n=r.apply(this,arguments);return ui(this,n)}}var Zm=function(e){An(n,Hg);var t=Km(n);function n(e){var r;mn(this,n),r=t.call(this),n.initialized=!0;var a,i=Wm(n.config.defaults),o=zs({},i,e,al);return"function"==typeof o.engine.global.urlProcessor&&(o.engine.global.urlProcessor=(a=o.engine.global.urlProcessor,function(e,t){if(Cd.isInnerLink(e)){var n=a(Cd.get(e),t);return Cd.replace(e,n)}return a(e,t)})),ui(r,new Ig(o,{options:o}))}return gn(n)}();hi(Zm,"initialized",!1),hi(Zm,"config",{defaults:Gm});var Ym=Zm;export{Mg as MenuHookBase,Cc as SyntaxHookBase,Ym as default};