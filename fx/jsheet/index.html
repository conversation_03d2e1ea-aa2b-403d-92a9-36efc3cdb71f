<fx-jsheet id="editor"></fx-v>

<script type="module">
    import './jsheet.js';
    editor.src = `
{
    worksheets: [
        {
            data: [
                ['Jazz', 'Honda', '2019-02-12', '', true, '$ 2.000,00', '#777700'],
                ['Civic', 'Honda', '2018-07-11', '', true, '$ 4.000,01', '#007777'],
            ],
            columns: [
                { type: 'text', title: 'Car', width: 120 },
                { type: 'dropdown', title: 'Make', width: 200, source: ["Alfa Romeo", "Audi", "Bmw", "Honda"] },
                { type: 'calendar', title: 'Available', width: 200 },
                { type: 'image', title: 'Photo', width: 160 },
                { type: 'checkbox', title: 'Stock', width: 80 },
                { type: 'numeric', title: 'Price', width: 100, mask: '$ #.##,00', decimal: ',' },
                { type: 'color', width: 100, title: 'Color', render: 'square', }
            ]
        },
        {
            data: [
                ['Crayons Crayola only (No Rose Art)', 2, 5.01, 0.01, '=B1*C1*(1-D1)'],
                ['Colored Pencils Crayola only', 2, 4.41, 0.02, '=B2*C2*(1-D2)'],
                ['Expo Dry-erase Markers Wide', 4, 3.00, 0.1, '=B3*C3*(1-D3)'],
                ['Index Cards Unlined', 3, 6.00, 0.03, '=B4*C4*(1-D4)'],
                ['Tissues', 10, 1.90, 0.01, '=B5*C5*(1-D5)'],
                ['Ziploc Sandwich-size Bags', 5, 1.00, 0.01, '=B6*C6*(1-D6)'],
                ['Thin Markers Crayola only', 2, 3.00, 0.02, '=B7*C7*(1-D7)'],
                ['Highlighter', 4, 1.20, 0.01, '=B8*C8*(1-D8)'],
                ['Total', '=SUM(B1:B8)', '=ROUND(SUM(C1:C8), 2)', '', '=SUM(E1:E8)'],
            ],
            columns: [
                { type: 'text', title: 'Product', width: '300' },
                { type: 'text', title: 'Qtd', width: '80', mask: '#.##0' },
                { type: 'text', title: 'Price', width: '100px', mask: '$ #.##0,00' },
                { type: 'text', title: 'Discount', mask: '0.00%' },
                {
                    type: 'number',
                    title: 'Total',
                    width: '100px',
                    format: 'US #.##0,00',
                },
            ]
        }
    ],
    tabs: true,
    toolbar: false
}
        `
</script>