<meta charset="UTF-8">
<meta name="color-scheme" content="light dark" />

<fx-jtmp id="fx_tmp"></fx-jtmp>

<script type="module">
    import './jtmp.js';
    fx_tmp.srcdoc = () => { 
        return `
<style>
    body {
        margin: 0;
        padding: 20px;
        font-family: Arial, sans-serif;
    }
    #container {
        width: 100%;
        height: calc(100vh - 64px);
        border: 1px solid #ccc;
    }
</style>

<div id="container"></div>
<button id="toggleTheme" style="margin-top: 4px;">Сменить тему</button>

<script src="https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.40.0/min/vs/loader.min.js"><\/script>
<script>
    const defaultCode = "";
    let currentCode = "";
    require.config({ paths: { 'vs': 'https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/0.40.0/min/vs' } });
    require(['vs/editor/editor.main'], function () {
        const diffEditor = monaco.editor.createDiffEditor(document.getElementById('container'), {
            theme: 'vs',
            readOnly: false,
            renderSideBySide: true,
            enableSplitViewResizing: false,
            originalEditable: true,
        })
        const originalModel = monaco.editor.createModel(defaultCode, 'html');
        const modifiedModel = monaco.editor.createModel(currentCode, 'html');
        diffEditor.setModel({ original: originalModel, modified: modifiedModel });
    })
    let isDarkTheme = false;
    document.getElementById('toggleTheme').addEventListener('click', () => {
        isDarkTheme = !isDarkTheme;
        const newTheme = isDarkTheme ? 'vs-dark' : 'vs';
        monaco.editor.setTheme(newTheme);
    })
<\/script>
`
    }
    fx_tmp.setIframe();
</script>
