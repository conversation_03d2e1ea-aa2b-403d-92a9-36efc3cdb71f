import { css } from '/fx.js';

export const $styles = {
    main: css`
        #panel::-webkit-scrollbar { width: 0px; height: 0px; }
        .header {
            background: var(--fxs-surface);
            color: var(--fxs-txt-primary);
        }
        .row {
            /* color: gray; */
            margin: 4px;
            padding: 4px;
            /* border: 1px solid lightgray; */
            /* flex: 1; */
            width: 100%;
            /* border-radius: 2px; */
            /* outline-color: gray; */
            /* outline-width: thin; */
            outline: none;
        }
        .icon {
            opacity: .3;
            cursor: pointer;
        }
        .icon:hover {
            opacity: 1!important;
        }
        .sets {
            opacity: .3;
        }
        .sets:hover {
            opacity: 1!important;
        }
        input {
            color: var(--fxs-txt-primary);
            background-color: transparent!important;
        }
    `
}
