import { FxElement, html, css } from '../../../fx.js';

import '../~/konva/konva.min.js';
import '../icon/icon.js';

customElements.define('fx-events-weeks', class FxEventsWeeks extends FxElement {
    static properties = {
        maxYears: { type: Number, default: 120 },
        timeStart: { type: Number },
        dateStart: { type: Object },
        item: { type: Object }
    }
    get start() { return this.item?.doc?.dateStart || this._start || '2024-01-01' }
    get startInfo() {
        return this.start.split('T')[0].split('-').reverse().join('.');
    }
    get startYear() { return new Date(this.start).getFullYear() }
    get end() { return this.item?.doc?.dateEnd || '' }
    get endInfo() {
        return this.end.split('T')[0].split('-').reverse().join('.');
    }
    get endYear() { return new Date(this.end)?.getFullYear() }
    get width() { return this.blockWidth * 52 }
    get years() { return [...Array(this.maxYears).keys()] }
    get weeks() { return [...Array(52).keys()] }
    get blockWidth() {
        let w = (this.offsetWidth - 83) / 52;
        w = w < 6 ? 6 : w;
        return w;
    }

    async firstUpdated() {
        super.firstUpdated();
        await new Promise((r) => setTimeout(r, 0));
        this.listen('get-items-end', async (e) => {
            console.log(e)
            await new Promise((r) => setTimeout(r, 50));
            if (this.main?.panelSimple)
                this.main.panelSimple.idx = this.idxSimplePanelMain;
        })
        this.$update();
        setTimeout(() => {
            const resizeObserver = new ResizeObserver(() => {
                FX.throttle('resize', () => {
                    this.initCanvas();
                    this.$update();
                }, 20)
            })
            resizeObserver.observe(this.$qs('.row'));
        }, 500)
    }

    async initCanvas(y, w) {
        if (!this.start) return;
        if (this.endYear) {
            this.maxYears = this.endYear - this.startYear + 1;
        } else {
            this.maxYears = 120;
        }
        const container = this.$qs('#container');
        if (!container) return;
        const width = this.width + 1;
        const blockWidth = this.blockWidth;
        // console.log('blockWidth - ', blockWidth)
        const height = blockWidth * this.maxYears + 1;

        const stage = new Konva.Stage({
            container: this.$qs('#container'),
            width: width,
            height: height
        })
        const grid = new Konva.Layer();
        for (var i = 0; i <= 52; i++) {
            grid.add(new Konva.Line({
                points: [Math.round(i * blockWidth) + 0.5, 0, Math.round(i * blockWidth) + 0.5, height],
                stroke: '#ddd',
                strokeWidth: 1,
            }))
        }
        grid.add(new Konva.Line({ points: [0, 0, 10, 10] }));
        for (var y = 0; y < this.maxYears + 1; y++) {
            grid.add(new Konva.Line({
                points: [0, Math.round(y * blockWidth), width, Math.round(y * blockWidth)],
                stroke: '#ddd',
                strokeWidth: 1,
            }))
        }

        const gridLayer = this.gridLayer = new Konva.Layer();
        stage.add(grid, gridLayer);
        stage.on('click tap', (e) => {
            this.on_click(e)
        })

        const start = new Date(this.start);
        let startYear = this.startYear;
        // const timeStart = start.getTime() || 0;
        const end = this.end || '';
        // const timeEnd = new Date(end)?.getTime() || 0;
        let weekTime = 1000 * 60 * 60 * 24 * 7
        let fnXY = (d) => {
            d = new Date(d);
            let dYear = d.getFullYear();
            let y = dYear - startYear;
            let dTimeStartYear = start.setFullYear(dYear);
            let time = d.getTime() - dTimeStartYear;
            let x = time / weekTime;
            if (x < 0) {
                y = y - 1;
                x = 52.15 + x;
            }
            x = x > 51.55 ? 51.55 : x;
            return { x, y }
        }
        let fn = () => {
            for (let l = 0; l < events.length; l++) {
                const i = events[l];
                let d = fnXY(i.date1);
                let x = d.x, y = d.y;
                // console.log(y, x);
                if (i.isPeriod) {
                    let y1 = y, x1 = x;
                    y = y * blockWidth;
                    x = x * blockWidth;
                    let rect = {
                        x, y, i,
                        width: blockWidth * 52,
                        height: blockWidth,
                        fill: i.color,
                        opacity: .4
                    }
                    let d = fnXY(i.date2 || FX.dates(new Date()).short);
                    x = d.x;
                    y = d.y;
                    let y2 = y;
                    let rect1;
                    let y3 = y2 - y1;
                    if (y3 > 1) {
                        y3 -= 1;
                        y1 += 1;
                        y1 = y1 * blockWidth;
                        rect1 = new Konva.Rect({
                            x: 0, y: y1, i,
                            width: blockWidth * 52,
                            height: blockWidth * y3,
                            fill: i.color,
                            opacity: .4
                        })
                        gridLayer.add(rect1);
                    }
                    y = y * blockWidth;
                    x = x * blockWidth;
                    x = x < blockWidth / 2 ? blockWidth / 2 : x;
                    let width = x;
                    if (y3 === 0) {
                        rect.width = (d.x - x1) * blockWidth;
                    } else {
                        x = 0;
                        let rect2 = new Konva.Rect({
                            x, y, i,
                            width,
                            height: blockWidth,
                            fill: i.color,
                            opacity: .4
                        })
                        gridLayer.add(rect2);
                    }
                    rect = new Konva.Rect(rect);
                    gridLayer.add(rect);
                } else {
                    y = y * blockWidth + blockWidth / 2;
                    x = x * blockWidth;
                    x = x < blockWidth / 2 ? blockWidth / 2 : x;
                    let circle = new Konva.Circle({
                        x, y, i,
                        radius: blockWidth / 2.5,
                        fill: i.color,
                        opacity: .7
                    })
                    gridLayer.add(circle);
                }
            }
        }
        const _events = FX.sortBy(this.item?.doc.events?.filter(i => i && !i._deleted), 'date1');
        let events = _events.filter(i => i.isPeriod);
        if (events?.length) fn();
        events = _events.filter(i => !i.isPeriod);
        if (events?.length) fn();
        if (this.item?.doc.genderType === 'событие') {
            i = { date1: this.start, group: 'Начало', label: 'Дата старта', isOk: true };
        } else {
            i = { date1: this.start, group: 'ДР', label: 'День рождения', isOk: true };
        }
        gridLayer.add(new Konva.Rect({ x: 1, y: 1, width: blockWidth - 1, height: blockWidth - 1, fill: 'transparent', stroke: 'red', strokeWidth: 1, opacity: .9, i }));
        let d;
        if (this.end) {
            d = fnXY(this.end);
            if (this.item.doc.genderType === 'событие') {
                i = { date1: this.start, group: 'Конец', label: 'Дата оконания', isOk: true };
            } else {
                i = { date1: this.end, group: 'ДС', label: 'День смерти', isOk: true };
            }
            let x = d.x * blockWidth - blockWidth / 2;
            x = x < 0 ? 0 : x;
            gridLayer.add(new Konva.Rect({ x, y: d.y * blockWidth, width: blockWidth - 1, height: blockWidth - 1, fill: 'transparent', stroke: 'black', strokeWidth: 1, opacity: .9, i }));
        }
        let newYear = this.startYear + 1 + '-01-01';
        d = fnXY(newYear);
        gridLayer.add(new Konva.Line({
            points: [d.x * blockWidth, 0, d.x * blockWidth, height],
            stroke: 'red',
            strokeWidth: 1,
            opacity: .8
        }))
        let current = FX.dates().short;
        d = fnXY(current);
        gridLayer.add(new Konva.Line({
            points: [d.x * blockWidth, 0, d.x * blockWidth, height],
            stroke: 'blue',
            strokeWidth: 1,
            opacity: .6
        }))
        this.$update();
        setTimeout(() => {
            this.style.opacity = 1;
            this.$update();
        }, 200)
    }

    async on_click(e) {
        this._label = '';
        this.$update();
        if (!e.target.attrs.i) return;
        let y = e.evt.clientY || e.evt.layerY,
            h = this.$qs('#container').offsetHeight,
            r = this.$root.offsetHeight;
        this.$update()
        if (y < (h + (r - h)) / 2) {
            this._bottom = '2px';
        } else {
            this._bottom = '';
        }
        let label = '', i = e.target.attrs.i;
        let isPeriod = i.isPeriod;
        let _d1 = new Date(FX.dates(i.date1).short), _d2;
        _d2 = i.date2 ? new Date(FX.dates(i.date2).short) : '';
        let _isOk = i.isOk;
        let item = i;
        let arr;
        if (_isOk) {
            arr = [{ date1: new Date() }];
        }
        arr = arr || this.item.doc.events || [];
        arr = FX.sortBy(arr, 'date1');
        arr.filter(i => !i._deleted).map(i => {
            let d1 = new Date(FX.dates(i.date1).short);
            let d2 = i.date2 ? new Date(FX.dates(i.date2).short) : new Date(FX.dates().short);
            let isOk = false;
            if (_isOk) {
                isOk = true;
                i = item;
                _isOk = false;
            } else if (!isPeriod && !i.isPeriod) {
                isOk = _d1 === d1;
            } else if (!isPeriod && i.isPeriod) {
                isOk = _d1 >= d1 && _d1 <= d2;
            } else if (isPeriod && i.isPeriod) {
                isOk = d1 >= _d1 && d1 <= _d2 && d2 >= _d1 && d2 <= _d2;
                isOk = _d1 >= d1 && _d1 <= d2 && _d2 >= d1 && _d2 <= d2;
            }
            if (isOk || FX.dates(_d1).short === FX.dates(d1).short) {
                d1 = new Date(i.date1);
                d2 = i.date2 ? new Date(i.date2) : new Date();
                let diff = Math.abs(d2.getTime() - d1.getTime());
                let days = (diff / 1000 / 60 / 60 / 24).toFixed(2);
                diff = (diff / 1000 / 60 / 60 / 24 / 365.25).toFixed(2);
                d1 = d1.toLocaleDateString();
                d2 = d2.toLocaleDateString();
                label += `
<div style="border-bottom: 1px solid lightgray; padding: 6px; min-width: 100%;">${i?.group || ''}</div>
<strong>${i?.label || ''}</strong>
<br>
<strong>${d1}${i.isPeriod ? ' - ' + d2 : ''}</strong>
<div style="font-size: 14px">( ${days} <span style="font-size: 10px"> days</span> )</div>
<div style="font-size: 14px">( ${diff} <span style="font-size: 10px"> year</span> )</div>
<hr style="width: 100%;">
                `
            }
        })
        // if (document.exitFullscreen || document.webkitExitFullscreen || document.msExitFullscreen) {
        // if (FX.isMobile) {
        //     this._label = label;
        //     this.$update();
        // } else {
        let newDiv = document.createElement("div");
        if (label) {
            newDiv.style.fontSize = '16px';
            newDiv.style.color = 'gray';
            newDiv.style.padding = '8px';
            newDiv.style.background = 'white';
            newDiv.style.textAlign = 'center';
            newDiv.style.border = '1px solid gray';
            newDiv.style.boxShadow = '3px 3px 3px rgba(0, 0, 0, .7)';
            newDiv.innerHTML = label;
        }
        await FX.show('dropdown', newDiv, {}, { align: 'left', showHeader: true, label: 'Событие', intersect: true, draggable: true, resizable: false, btnCloseOnly: false });
        // }
    }
    static styles = css`
        :host {
            width: 100%;
            display: flex;
            flex-direction: column;
            flex: 1;
            position: relative;
            opacity: 0;
        }
        .row {
            position: sticky;
            top: 0;
            background: white;
            z-index: 2;
            height: 20px;
            /* border-bottom: 1px solid #ddd; */
            text-align: center;
            color: #777;
            max-height: 20px;
            width: 100%;
        }
        .col {
            position: sticky;
            left: 0;
            background: white;
            z-index: 1;
            max-width: 80px;
            min-width: 80px;
            /* border-right: 1px solid #ddd; */
        }
        .year {
            text-align: center;
            color: #777;
        }
    `

    render() {
        return html`
            <style>
                .year { max-height: ${this.blockWidth + .005}px; font-size: ${this.blockWidth < 10 ? 10 : 12}px; }
                .week { min-width: ${this.blockWidth}px; }
                .row { min-width: ${this.width + 80}px; font-size: ${this.blockWidth < 14 ? 10 : 12}px; }
            </style>
            <div class="vertical flex box ${this.start ? '' : 'hidden'}">
                <div class="row row2 horizontal flex box">
                    <div class="col pt2 brr" style="text-align: center; color: red; font-size: 13px">${this.startInfo}</div>
                    ${this.weeks.map(i => html`
                        <div class="week center vertical box">${i % 2 ? i + 1 : ''}</div>
                    `)}
                </div>
                <div class="row horizontal flex box" style="top: 17px;">
                    <div class="col pt2 brb brr" style="text-align: center; color: black; font-size: 13px">${this.endInfo}</div>
                    ${this.weeks.map(i => html`
                        <div class="week center vertical box brb">${i % 2 === 0 ? i + 1 : ''}</div>
                    `)}
                </div>
                <div class="horizontal flex box">
                    <div class="col vertical box mt4">
                        ${this.years.map(i => html`
                            <div class="year center horizontal box ml4 mr4 flex">${this.blockWidth > 18 || (this.blockWidth < 18 && i % 2 === 0) ? i + this.startYear + '-' + (i + this.startYear + 1 + '').slice(2) : ''}<span class="flex"></span>${this.blockWidth > 18 || (this.blockWidth < 18 && i % 2 === 0) ? i : ''}</div>
                        `)}
                    </div>
                    <div id="container" class="ml1 mb8 mt4"></div>
                </div>
            </div>
            <div .hidden=${!this._label} class="vertical flex center" style="right: 2px; position: fixed; bottom: ${this._bottom}; background: transparent; z-index: 999;pointer-events: none">
                <div class="flex"></div>
                <div id="info" class="vertical flex center border shadow" innerHTML=${this._label || ''} style="cursor: pointer;background: lightyellow; text-align: center;  padding: 4px 16px" @click=${e => { this._label = false; this.$update(); }}></div>
                <div class="flex"></div>
            </div>
       `
    }

})
