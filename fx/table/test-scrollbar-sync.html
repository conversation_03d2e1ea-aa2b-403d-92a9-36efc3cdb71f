<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест синхронизации скролла таблицы</title>
    <script type="module" src="../fx.js"></script>
    <script type="module" src="./table.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .container {
            width: 600px;
            height: 400px;
            margin: 0 auto;
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow: hidden;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .info {
            padding: 10px;
            background: #e3f2fd;
            margin-bottom: 20px;
            border-radius: 4px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>Тест синхронизации скролла в таблице</h1>
    
    <div class="info">
        <strong>Тест:</strong> При появлении вертикального скролла в таблице, колонки шапки и подвала должны точно совпадать с колонками содержимого.
        <br><strong>Ожидаемый результат:</strong> Вертикальные линии колонок должны быть выровнены между шапкой, содержимым и подвалом.
    </div>

    <div class="container">
        <fx-table id="testTable"></fx-table>
    </div>

    <script>
        // Создаем тестовые данные с большим количеством строк для появления скролла
        const generateTestData = (count) => {
            const data = [];
            for (let i = 1; i <= count; i++) {
                data.push({
                    id: i,
                    name: `Элемент ${i}`,
                    description: `Описание для элемента номер ${i}`,
                    value: Math.floor(Math.random() * 1000),
                    status: i % 3 === 0 ? 'Активный' : i % 3 === 1 ? 'Неактивный' : 'Ожидание'
                });
            }
            return data;
        };

        // Настройка колонок
        const columns = [
            { field: 'id', header: 'ID', width: 60 },
            { field: 'name', header: 'Название', width: 150 },
            { field: 'description', header: 'Описание', width: 200 },
            { field: 'value', header: 'Значение', width: 100 },
            { field: 'status', header: 'Статус', width: 120 }
        ];

        // Настройка подвала с вычислениями
        const footerCalculations = {
            value: { type: 'sum', decimals: 0 },
            id: { type: 'count' }
        };

        // Инициализация таблицы
        const table = document.getElementById('testTable');
        table.data = generateTestData(100); // 100 строк для гарантированного появления скролла
        table.columns = columns;
        table.footerCalculations = footerCalculations;
        table.showVerticalLines = true;
        table.headerHeight = 40;
        table.rowHeight = 32;
        table.footerHeight = 40;

        // Добавляем кнопки для тестирования
        const controls = document.createElement('div');
        controls.style.cssText = 'text-align: center; margin-top: 20px;';
        controls.innerHTML = `
            <button onclick="changeDataSize(5)">5 строк (без скролла)</button>
            <button onclick="changeDataSize(50)">50 строк</button>
            <button onclick="changeDataSize(100)">100 строк</button>
            <button onclick="changeDataSize(200)">200 строк</button>
            <button onclick="toggleVerticalLines()">Переключить вертикальные линии</button>
            <button onclick="resizeContainer()">Изменить размер контейнера</button>
        `;
        document.body.appendChild(controls);

        window.changeDataSize = (count) => {
            table.data = generateTestData(count);
            console.log(`Изменено количество строк на ${count}`);
        };

        window.toggleVerticalLines = () => {
            table.showVerticalLines = !table.showVerticalLines;
            console.log('Вертикальные линии:', table.showVerticalLines ? 'включены' : 'выключены');
        };

        let containerExpanded = false;
        window.resizeContainer = () => {
            const container = document.querySelector('.container');
            if (containerExpanded) {
                container.style.width = '600px';
                container.style.height = '400px';
                containerExpanded = false;
                console.log('Контейнер уменьшен');
            } else {
                container.style.width = '800px';
                container.style.height = '300px';
                containerExpanded = true;
                console.log('Контейнер увеличен');
            }
        };

        // Логирование для отладки
        console.log('Таблица инициализирована с', table.data.length, 'строками');

        // Добавляем обработчик для отслеживания изменений скролла
        setTimeout(() => {
            const bodyScrollable = table.shadowRoot?.querySelector('.body-scrollable') ||
                                 table.querySelector('.body-scrollable');
            const headerScrollable = table.shadowRoot?.querySelector('.header-scrollable') ||
                                   table.querySelector('.header-scrollable');
            const footerScrollable = table.shadowRoot?.querySelector('.footer-scrollable') ||
                                   table.querySelector('.footer-scrollable');
            const tableContainer = table.shadowRoot?.querySelector('.table-container') ||
                                 table.querySelector('.table-container');

            if (bodyScrollable) {
                console.log('Body - offsetWidth:', bodyScrollable.offsetWidth, 'clientWidth:', bodyScrollable.clientWidth);
                console.log('Ширина скролла:', bodyScrollable.offsetWidth - bodyScrollable.clientWidth, 'px');
            }
            if (headerScrollable) {
                console.log('Header - offsetWidth:', headerScrollable.offsetWidth, 'clientWidth:', headerScrollable.clientWidth);
            }
            if (footerScrollable) {
                console.log('Footer - offsetWidth:', footerScrollable.offsetWidth, 'clientWidth:', footerScrollable.clientWidth);
            }
            if (tableContainer) {
                console.log('Table container sync-scrollbar class:', tableContainer.classList.contains('sync-scrollbar'));
                console.log('Table container classes:', tableContainer.className);
            }

            // Проверяем стили скролла
            if (headerScrollable) {
                console.log('Header overflow-y:', getComputedStyle(headerScrollable).overflowY);
            }
            if (footerScrollable) {
                console.log('Footer overflow-y:', getComputedStyle(footerScrollable).overflowY);
            }
        }, 1000);
    </script>
</body>
</html>
