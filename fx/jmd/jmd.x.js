import { css } from '/fx.js';

export const $styles = css`
    :host { overflow: hidden; overflow-y: auto; font-family: Robot<PERSON>, Noto, sans-serif; }
    table { border-collapse: collapse; width: 100%; margin-bottom: 16px; }
    th { background-color: lightgray; border: 1px solid darkgray; padding: 2px; }
    td { border: 1px solid lightgray; padding: 2px; }
    img { max-width: 96%; height: auto; }
    blockquote { border-left: 10px solid #ccc; background: #f9f9f9; padding: 0.5em 10px; margin: 1.5em 10px; }
    .md { display: block; padding: 4px; }
    .hljs { display: block; padding: 8px 4px 6px 10px; }
    .hljs-comment, .hljs-quote { color: #93a1a1; }
    .hljs-keyword, .hljs-selector-tag, .hljs-addition { color: #859900; }
    .hljs-number, .hljs-string, hljs-meta .hljs-meta-string, .hljs-literal, .hljs-doctag, .hljs-regexp { color: #2aa198; }
    .hljs-title, .hljs-section, .hljs-name, .hljs-selector-id, .hljs-selector-class { color: #268bd2; }
    .hljs-attribute, .hljs-attr, .hljs-variable, .hljs-template-variable, .hljs-class .hljs-title, .hljs-type { color: #b58900; }
    .hljs-symbol, .hljs-bullet, .hljs-subst, .hljs-meta, .hljs-meta .hljs-keyword, .hljs-selector-attr, .hljs-selector-pseudo, .hljs-link { color: #cb4b16; }
    .hljs-built_in, .hljs-deletion { color: #dc322f; }
    .hljs-formula { background: #eee8d5; }
    .hljs-emphasis { font-style: italic; }
    .hljs-strong { font-weight: bold; }
    .hjln { min-width:34px; color:gray; border-right:.1em solid; counter-reset: l; cursor:default; float:left; padding:8px 0; margin:0 0.5em 0 0; text-align:right; -moz-user-select:none; -webkit-user-select:none }
    .hjln span { counter-increment:l; display:block;padding:0 .5em 0 1em }
    .hjln span:before { content:counter(l) }
    .light { background-color: #ddd; display: inline-block;}
    .icon-info { display: flex; }
    .katex-html { display: none; }
`
