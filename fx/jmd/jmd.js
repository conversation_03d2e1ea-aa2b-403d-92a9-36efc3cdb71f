import { FxElement, html, css, unsafeHTML } from '../../fx.js';
import { $styles } from './jmd.x.js';
import './src/showdown.min.js';
import './src/decodeHTML.min.js';
import './src/highlight.min.js';
import './src/showdown-youtube.min.js';
import './src/showdown-katex.js'; // https://github.com/obedm503/showdown-katex

customElements.define('fx-jmd', class FxJMD extends FxElement {
    static properties = {
        src: { type: String, default: '' },
        optionsDefault: {
            type: Object,
            default: {
                omitExtraWLInCodeBlocks: false, //'Omit the default extra whiteline added to code blocks',
                noHeaderId: false, //'Turn on/off generated header id',
                prefixHeaderId: false, //'Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic \'section-\' prefix',
                rawPrefixHeaderId: false, //'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',
                ghCompatibleHeaderId: false, //'Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)',
                rawHeaderId: false, //'Remove only spaces, \' and " from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids',
                headerLevelStart: false, //'The header blocks level start', type: 'integer'
                parseImgDimensions: false, //'Turn on/off image dimension parsing',
                simplifiedAutoLink: false, //'Turn on/off GFM autolink style',
                literalMidWordUnderscores: false, //'Parse midword underscores as literal underscores',
                literalMidWordAsterisks: false, //'Parse midword asterisks as literal asterisks',
                strikethrough: false, //'Turn on/off strikethrough support',
                tables: true, //'Turn on/off tables support',
                tablesHeaderId: false, //'Add an id to table headers',
                ghCodeBlocks: true, //'Turn on/off GFM fenced code blocks support',
                tasklists: false, //'Turn on/off GFM tasklist support',
                smoothLivePreview: false, //'Prevents weird effects in live previews due to incomplete input',
                smartIndentationFix: false, //'Tries to smartly fix indentation in es6 strings',
                disableForced4SpacesIndentedSublists: false, //'Disables the requirement of indenting nested sublists by 4 spaces',
                simpleLineBreaks: false, //'Parses simple line breaks as <br> (GFM Style)',
                requireSpaceBeforeHeadingText: false, //'Makes adding a space between `#` and the header text mandatory (GFM Style)',
                ghMentions: false, //'Enables github @mentions',
                ghMentionsLink: 'https://github.com/{u}', //'Changes the link generated by @mentions. Only applies if ghMentions option is enabled.',
                encodeEmails: true, //'Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities',
                openLinksInNewWindow: true,//'Open all links in new windows',
                backslashEscapesHTMLTags: true, //'Support for HTML Tag escaping. ex: \<div>foo\</div>',
                emoji: true, //'Enable emoji support. Ex: `this is a :smile: emoji`'
                underline: false, //'Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`',
                completeHTMLDocument: false, //Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags',
                metadata: false, //'Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).',
                splitAdjacentBlockquotes: false, //'Split adjacent blockquote blocks',
            }
        },
        options: {
            type: Object,
            default: {}
        },
        html: {
            type: String,
            default: ''
        },
        mediaPath: { type: String, default: '', local: true },
    }

    set src(val) {
        let oldVal = this._src
        if (val !== oldVal) {
            this._src = val;
            this.requestUpdate('src', oldVal);
            this._setHTML(val);
        }
    }
    get src() { return this._src; }

    firstUpdated() {
        super.firstUpdated();
        this.async(() => {
            this.mdShowdown = new showdown.Converter({
                ...this.optionsDefault, ...this.options,
                extensions: ['youtube', showdownKatex({
                    delimiters: [
                        { left: "$", right: "$", display: false },
                        { left: '~', right: '~', display: false, asciimath: true },
                    ],
                }), () => {
                    return [{
                        type: "output",
                        filter(text) {
                            let left = "<pre><code\\b[^>]*>", right = "</code></pre>", flags = "g";
                            const replacement = (wholeMatch, match, left) => {
                                let lang = (left.match(/class=\"([^ \"]+)/) || [])[1];
                                let html = lang && hljs.getLanguage(lang) ? hljs.highlight(lang, htmlDecode(match)).value : hljs.highlightAuto(htmlDecode(match)).value;
                                return `
                                    <pre class="hljs" style="display: flex; border: .5px solid lightgray; border-radius: 2px; min-height:32px; background: #FFE5; overflow: auto;">
                                        <code class="hljs" style="outline: 0px solid transparent; font-size: 125%; line-height: 1.2; white-space: pre-wrap;">${html}</code>
                                    </pre>`;
                            };
                            return showdown.helper.replaceRecursiveRegExp(text, replacement, left, right, flags);
                        }
                    }];
                }]
            })
            this._setHTML();
        })
    }

    async _setHTML(s = this.src) {
        if (!this.mdShowdown) return;
        let url = new URL(window.location),
            src = url.searchParams.get("src") || s;
        if (!src) return;
        if (src.endsWith('.md') || src.startsWith('blob:')) {
            src = await fetch(s);
            src = src && src.ok ? await src.text() : s;
        }
        src = src.replace(/\$\$(.*)\$\$/g, `<h2 style="text-align: center;">~~~~~~$1~~~~~~</h2>`);
        src = src.replaceAll('~~~~~~', '$');
        src = src.replace(/(```\S*|~~~\S*)( +)/g, '$1' + '_');
        this.html = html`${unsafeHTML(this.mdShowdown?.makeHtml(src))}`;
    }

    static styles = $styles

    render() { return html`${this.html}` }
})
