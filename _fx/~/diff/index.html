<div style="display: flex; width: 100%; height: calc(100vh - 16px); display: flex; flex: 1; position: relative;">
    <div style="display: flex; flex-direction: column; width: 50%; position: relative;">
        <div id="editor1" style="width: 98%; height: 60%; margin: 8px; border: 1px solid lightgray;"></div>
        <div id="editor2" style="width: 98%; height: 40%; margin: 8px; border: 1px solid lightgray;"></div>
    </div>
    <div id="editor3" style="width: 50%; height: calc(100% - 30x); margin: 8px; border: 1px solid lightgray;"></div>
</div>
<button id="btn" style="margin: 8px; position: absolute; top: 2px; left: calc(50vw - 48px); z-index: 10;"
    onclick="fn()">refresh</button>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/ace/1.23.4/ace.js"></script>

<script type="module">
    const editor1 = ace.edit("editor1")
    editor1.setTheme("ace/theme/chrome")
    editor1.session.setMode("ace/mode/javascript")
    editor1.setValue(`export default {
   ctor: "$host",
   id: "f651193e2ddef57",
   tmp_public: 1000, 
   create_time: "2024-12-20T07:00:15.831Z",
   KEY: "62cbf5017278f568e7912423018aa2b789a2bca5d771c5021ac878e1021399a861627beeeec32e54b0721d19bbff3891c135bdcabdbcd85c64061f720f781013",
   $public:{
      host_self: 'ok'
   }
}

class MyClass extends Reactor {
    constructor() {
        super();
    }
    get $public() {
        return{
            get a() {
                return 100;
            }
        }
    }
    get b() {
        return this.a * 2;
    }
} 
`)
    editor1.clearSelection()

    const editor2 = ace.edit("editor2")
    editor2.setTheme("ace/theme/chrome")
    editor2.session.setMode("ace/mode/javascript")
    editor2.setValue(`export default{
    $public:{
        host_item:"ok"
    }
}

function test() {
    console.log('test')
}

class MyClass extends Reactor {
    get b() {
        return this.a * 4;
    }
    get c() {
        return this._c;
    }
    set c(v) {
        this._c = v;
    }
}
`)
    editor2.clearSelection();

    const editor3 = ace.edit("editor3")
    editor3.setTheme("ace/theme/chrome")
    editor3.session.setMode("ace/mode/javascript")
    editor3.setReadOnly(true)

    function merge(text1, text2) {
        // Парсинг текстов
        const obj1 = parseJavaScript(text1);
        const obj2 = parseJavaScript(text2);
        
        // Объединение объектов
        const merged = mergeObjects(obj1, obj2);
        
        // Преобразование обратно в текст
        return generateCode(merged);
    }

    // Простой парсер JavaScript
    function parseJavaScript(text) {
        const result = {
            defaultExport: null,
            classes: {},
            functions: {}
        };
        
        // Разделяем текст на блоки
        const blocks = text.split(/(?=export default|class\s+\w+\s+extends|function\s+\w+)/g);
        
        blocks.forEach(block => {
            block = block.trim();
            
            // Обработка export default
            if (block.startsWith('export default')) {
                const objectMatch = block.match(/export default\s*({[\s\S]*?})/);
                if (objectMatch) {
                    try {
                        // Преобразуем текст в объект
                        const objText = objectMatch[1].replace(/'/g, '"')
                            .replace(/(\w+):/g, '"$1":')
                            .replace(/\$public/g, '"$public"');
                        
                        // Безопасный парсинг
                        let parsed;
                        try {
                            parsed = Function(`return ${objText}`)();
                        } catch (e) {
                            // Упрощенный парсинг для сложных объектов
                            parsed = parseSimpleObject(objectMatch[1]);
                        }
                        
                        result.defaultExport = parsed;
                    } catch (e) {
                        console.error("Ошибка парсинга объекта:", e);
                    }
                }
            }
            
            // Обработка классов
            const classMatch = block.match(/class\s+(\w+)\s+extends\s+(\w+)\s*{([\s\S]*?)}/);
            if (classMatch) {
                const className = classMatch[1];
                const extendsName = classMatch[2];
                const classBody = classMatch[3];
                
                result.classes[className] = {
                    extends: extendsName,
                    methods: {},
                    getters: {},
                    setters: {}
                };
                
                // Парсинг методов, геттеров и сеттеров
                const methodsMatch = classBody.match(/(?:constructor|get|set|\w+)\s*\([^)]*\)\s*{[\s\S]*?}/g) || [];
                
                methodsMatch.forEach(methodText => {
                    if (methodText.startsWith('constructor')) {
                        result.classes[className].constructor = methodText;
                    } else if (methodText.startsWith('get')) {
                        const getterMatch = methodText.match(/get\s+(\w+)/);
                        if (getterMatch) {
                            result.classes[className].getters[getterMatch[1]] = methodText;
                        }
                    } else if (methodText.startsWith('set')) {
                        const setterMatch = methodText.match(/set\s+(\w+)/);
                        if (setterMatch) {
                            result.classes[className].setters[setterMatch[1]] = methodText;
                        }
                    } else {
                        const methodMatch = methodText.match(/(\w+)\s*\(/);
                        if (methodMatch) {
                            result.classes[className].methods[methodMatch[1]] = methodText;
                        }
                    }
                });
            }
            
            // Обработка функций
            const functionMatch = block.match(/function\s+(\w+)\s*\([^)]*\)\s*{([\s\S]*?)}/);
            if (functionMatch) {
                const functionName = functionMatch[1];
                const functionBody = functionMatch[2];
                result.functions[functionName] = {
                    name: functionName,
                    body: functionBody,
                    fullText: block
                };
            }
        });
        
        return result;
    }

    // Упрощенный парсер для объектов
    function parseSimpleObject(text) {
        const result = {};
        
        // Извлекаем $public если есть
        const publicMatch = text.match(/\$public\s*:\s*{([^}]*)}/);
        if (publicMatch) {
            result.$public = {};
            const publicProps = publicMatch[1].split(',').map(p => p.trim()).filter(p => p);
            
            publicProps.forEach(prop => {
                const [key, value] = prop.split(':').map(p => p.trim());
                if (key && value) {
                    result.$public[key.replace(/['"]/g, '')] = value.replace(/['"]/g, '');
                }
            });
        }
        
        // Извлекаем остальные свойства верхнего уровня
        const lines = text.replace(/\$public\s*:\s*{[^}]*}/, '').split(',');
        lines.forEach(line => {
            line = line.trim();
            if (!line || line === '{' || line === '}') return;
            
            const [key, value] = line.split(':').map(p => p.trim());
            if (key && value) {
                result[key.replace(/['"]/g, '')] = value.replace(/['"]/g, '');
            }
        });
        
        return result;
    }

    // Объединение объектов
    function mergeObjects(obj1, obj2) {
        const result = {
            defaultExport: null,
            classes: {},
            functions: {}
        };
        
        // Объединение export default
        if (obj1.defaultExport || obj2.defaultExport) {
            result.defaultExport = { ...(obj1.defaultExport || {}), ...(obj2.defaultExport || {}) };
            
            // Специальная обработка для $public
            if (obj1.defaultExport && obj1.defaultExport.$public && 
                obj2.defaultExport && obj2.defaultExport.$public) {
                result.defaultExport.$public = {
                    ...obj1.defaultExport.$public,
                    ...obj2.defaultExport.$public
                };
            }
        }
        
        // Объединение классов
        const allClassNames = [...new Set([
            ...Object.keys(obj1.classes || {}),
            ...Object.keys(obj2.classes || {})
        ])];
        
        allClassNames.forEach(className => {
            const class1 = obj1.classes[className];
            const class2 = obj2.classes[className];
            
            if (class1 && class2) {
                // Объединяем классы, если они есть в обоих объектах
                result.classes[className] = {
                    extends: class2.extends || class1.extends,
                    constructor: class2.constructor || class1.constructor,
                    methods: { ...(class1.methods || {}), ...(class2.methods || {}) },
                    getters: { ...(class1.getters || {}), ...(class2.getters || {}) },
                    setters: { ...(class1.setters || {}), ...(class2.setters || {}) }
                };
            } else {
                // Если класс есть только в одном объекте, берем его
                result.classes[className] = class1 || class2;
            }
        });
        
        // Объединение функций (приоритет у obj2)
        result.functions = { ...(obj1.functions || {}), ...(obj2.functions || {}) };
        
        return result;
    }

    // Генерация кода из объединенного объекта
    function generateCode(merged) {
        let code = '';
        
        // Генерация export default
        if (merged.defaultExport) {
            code += 'export default {\n';
            
            // Добавляем все свойства кроме $public
            Object.entries(merged.defaultExport)
                .filter(([key]) => key !== '$public')
                .forEach(([key, value]) => {
                    if (typeof value === 'string' && !value.startsWith('{') && !value.startsWith('[')) {
                        value = `'${value}'`;
                    }
                    code += `   ${key}: ${value},\n`;
                });
            
            // Добавляем $public если есть
            if (merged.defaultExport.$public) {
                code += '   $public:{\n';
                Object.entries(merged.defaultExport.$public).forEach(([key, value]) => {
                    if (typeof value === 'string' && !value.startsWith('{') && !value.startsWith('[')) {
                        value = `'${value}'`;
                    }
                    code += `      ${key}: ${value},\n`;
                });
                code += '   }\n';
            } else {
                // Удаляем последнюю запятую
                code = code.replace(/,\n$/, '\n');
            }
            
            code += '}\n\n';
        }
        
        // Генерация функций
        Object.values(merged.functions).forEach(func => {
            code += `function ${func.name}() {\n    ${func.body.trim()}\n}\n\n`;
        });
        
        // Генерация классов
        Object.entries(merged.classes).forEach(([className, classData]) => {
            code += `class ${className} extends ${classData.extends} {\n`;
            
            // Конструктор
            if (classData.constructor) {
                code += `    ${typeof classData.constructor === 'string' ? classData.constructor.trim() : classData.constructor}\n`;
            }
            
            // Геттеры
            Object.values(classData.getters || {}).forEach(getter => {
                code += `    ${getter.trim()}\n`;
            });
            
            // Сеттеры
            Object.values(classData.setters || {}).forEach(setter => {
                code += `    ${setter.trim()}\n`;
            });
            
            // Методы
            Object.values(classData.methods || {}).forEach(method => {
                code += `    ${method.trim()}\n`;
            });
            
            code += '}\n';
        });
        
        return code;
    }

    window.fn = () => {
        const text1 = editor1.getValue();
        const text2 = editor2.getValue();

        const mergedText = merge(text1, text2);

        editor3.setValue(merge(editor1.getValue(), editor2.getValue()));
        editor3.clearSelection();
    }

    fn()
</script>
