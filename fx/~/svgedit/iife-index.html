<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge, chrome=1" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="mobile-web-app-capable" content="yes" />
  <!-- No-op until loaded dynamically (could make configurable) -->
  <link rel="icon" href="images/logo.svg">
  <!-- STYLESHEETS (others loaded dynamically) -->
  <style id="styleoverrides" media="screen"></style>
  <link href="./svgedit.css" rel="stylesheet" media="all">
  </link>
  <!-- Lacking browser support -->
  <script type="module" src="./browser-not-supported.js"></script>
  <title>SVG-edit</title>
</head>

<body style="margin:0">
  <!-- svgedit container can be positionned anywhere in the DOM
       but it must have a width and a height -->
  <!--
  <h1>SVGEdit</h1>
  <div id="container" style="width:100%;height:550px"></div>
  <h1>End</h1>
  -->
  <div id="container" style="width:100%;height:100vh"></div>
</body>
 <!-- If you do not wish to add extensions by URL, you can add calls
      within the following file to svgEditor.setConfig -->
      <script src="./iife-Editor.js"></script><script>
        /* import Editor from './xdomain-Editor.js' */
/* for available options see the file `docs/tutorials/ConfigOptions.md */
const svgEditor = new Editor(document.getElementById('container'))
svgEditor.init()
svgEditor.setConfig({
          allowInitialUserOverride: true,
          extensions: [],
          noDefaultExtensions: false,
          userExtensions: [/* { pathName: './react-extensions/react-test/dist/react-test.js' } */]
        })
// Variable XDOMAIN below is created by Rollup for the Xdomain build (see rollup.config.js)
/* globals XDOMAIN */
try { // try clause to avoid js to complain if XDOMAIN undefined
          if (XDOMAIN) {
            svgEditor.setConfig({
              canvasName: 'xdomain', // Namespace this
              allowedOrigins: [ '*' ]
            })
    console.info('xdomain config activated')
  }
        } catch (error) { /* empty fn */ }
      </script>
</html>
