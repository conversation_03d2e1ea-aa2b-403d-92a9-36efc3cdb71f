/**
 * @file ext-polystar.js
 *
 *
 * @copyright 2010 CloudCanvas, Inc. All rights reserved
 * @copyright 2021 Optimistik SAS, Inc. All rights reserved
 * @license MIT
 *
 */
const t="polystar",loadExtensionTranslation=async function(e){let s;const a=e.configObj.pref("lang");try{s=await function __variableDynamicImportRuntime0__(t){switch(t){case"./locale/en.js":return Promise.resolve().then((function(){return o}));case"./locale/fr.js":return Promise.resolve().then((function(){return l}));case"./locale/tr.js":return Promise.resolve().then((function(){return n}));case"./locale/zh-CN.js":return Promise.resolve().then((function(){return i}));default:return new Promise((function(e,o){("function"==typeof queueMicrotask?queueMicrotask:setTimeout)(o.bind(null,new Error("Unknown variable dynamic import: "+t)))}))}}("./locale/".concat(a,".js"))}catch(e){console.warn("Missing translation (".concat(a,") for ").concat(t," - using 'en'")),s=await Promise.resolve().then((function(){return o}))}e.i18next.addResourceBundle(a,t,s.default)};var e={name:t,async init(){const e=this,{svgCanvas:o}=e,{ChangeElementCommand:l}=o.history,addToHistory=t=>{o.undoMgr.addCommandToHistory(t)},{$id:n,$click:i}=o;let s,a,r;await loadExtensionTranslation(e);const showPanel=(t,e)=>{t?n("".concat(e,"_panel")).style.removeProperty("display"):n("".concat(e,"_panel")).style.display="none"},setAttr=(t,e)=>{o.changeSelectedAttribute(t,e),o.call("changed",s)},cot=t=>1/Math.tan(t),sec=t=>1/Math.cos(t);return{name:e.i18next.t("".concat(t,":name")),callback(){const e="".concat(t,":title"),a="".concat(t,":buttons.0.title"),r="".concat(t,":buttons.1.title"),u='\n            <se-flyingbutton id="tools_polygon" title="'.concat(e,'">\n              <se-button id="tool_star" title="').concat(a,'" src="star.svg">\n              </se-button>\n              <se-button id="tool_polygon" title="').concat(r,'" src="polygon.svg">\n              </se-button>\n            </se-flyingbutton>\n          ');o.insertChildAtIndex(n("tools_left"),u,10),i(n("tool_star"),(()=>{this.leftPanel.updateLeftPanel("tool_star")&&(o.setMode("star"),showPanel(!0,"star"),showPanel(!1,"polygon"))})),i(n("tool_polygon"),(()=>{this.leftPanel.updateLeftPanel("tool_polygon")&&(o.setMode("polygon"),showPanel(!0,"polygon"),showPanel(!1,"star"))}));const c="".concat(t,":contextTools.0.label"),d="".concat(t,":contextTools.0.title"),g="".concat(t,":contextTools.1.label"),b="".concat(t,":contextTools.1.title"),p="".concat(t,":contextTools.2.label"),h="".concat(t,":contextTools.2.title"),m="".concat(t,":contextTools.3.label"),f="".concat(t,":contextTools.3.title"),y=document.createElement("template");y.innerHTML='\n          <div id="star_panel">\n            <se-spin-input id="starNumPoints" label="'.concat(c,'" min=1 step=1 value=5 title="').concat(d,'">\n            </se-spin-input>\n            <se-spin-input id="RadiusMultiplier" label="').concat(g,'" min=1 step=2.5 value=3 title="').concat(b,'">\n            </se-spin-input>\n            <se-spin-input id="radialShift" min=0 step=1 value=0 label="').concat(p,'" title="').concat(h,'">\n            </se-spin-input>\n          </div>\n          <div id="polygon_panel">\n            <se-spin-input size="3" id="polySides" min=1 step=1 value=5 label="').concat(m,'" title="').concat(f,'">\n            </se-spin-input>\n          </div>\n        '),n("tools_top").appendChild(y.content.cloneNode(!0)),showPanel(!1,"star"),showPanel(!1,"polygon"),n("starNumPoints").addEventListener("change",(t=>{setAttr("point",t.target.value);const e=t.target.value;let o=s.length;for(;o--;){const t=s[o];if(t.hasAttribute("r")){const o=t.getAttribute("point"),n=t.getAttribute("points"),i=t.getAttribute("radialshift");let s=0,a=0;if(t.points){const r=t.points,u=r.numberOfItems;for(let t=0;t<u;++t){const e=r.getItem(t);s+=parseFloat(e.x),a+=parseFloat(e.y)}const c=s/u,d=a/u,g=Number(t.getAttribute("r")),b=g/t.getAttribute("starRadiusMultiplier");let p="";for(let t=0;e>=t;t++){let o=2*Math.PI*(t/e);o-=Math.PI/2;let l=g*Math.cos(o)+c,n=g*Math.sin(o)+d;p+=l+","+n+" ",isNaN(b)||(o=2*Math.PI*(t/e)+Math.PI/e,o-=Math.PI/2,o+=i,l=b*Math.cos(o)+c,n=b*Math.sin(o)+d,p+=l+","+n+" ")}t.setAttribute("points",p),addToHistory(new l(t,{point:o,points:n}))}}}})),n("RadiusMultiplier").addEventListener("change",(t=>{setAttr("starRadiusMultiplier",t.target.value)})),n("radialShift").addEventListener("change",(t=>{setAttr("radialshift",t.target.value)})),n("polySides").addEventListener("change",(t=>{setAttr("sides",t.target.value);const e=t.target.value;let o=s.length;for(;o--;){const t=s[o];if(t.hasAttribute("edge")){const o=t.getAttribute("sides"),n=t.getAttribute("points");let i=0,s=0;if(t.points){const a=t.points,r=a.numberOfItems;for(let t=0;t<r;++t){const e=a.getItem(t);i+=parseFloat(e.x),s+=parseFloat(e.y)}const u=i/r,c=s/r,d=t.getAttribute("edge")/2*cot(Math.PI/e)*sec(Math.PI/e);let g="";for(let t=0;e>=t;t++){const o=2*Math.PI*t/e;g+=d*Math.cos(o)+u+","+(d*Math.sin(o)+c)+" "}t.setAttribute("points",g),addToHistory(new l(t,{sides:o,points:n}))}}}}))},mouseDown(t){if("star"===o.getMode()){const e=o.getColor("fill"),l=o.getColor("stroke"),i=o.getStrokeWidth();return a=!0,r=o.addSVGElementsFromJson({element:"polygon",attr:{cx:t.start_x,cy:t.start_y,id:o.getNextId(),shape:"star",point:n("starNumPoints").value,r:0,radialshift:n("radialShift").value,r2:0,orient:"point",fill:e,stroke:l,"stroke-width":i}}),{started:!0}}if("polygon"===o.getMode()){const e=o.getColor("fill"),l=o.getColor("stroke"),i=o.getStrokeWidth();return a=!0,r=o.addSVGElementsFromJson({element:"polygon",attr:{cx:t.start_x,cy:t.start_y,id:o.getNextId(),shape:"regularPoly",sides:n("polySides").value,orient:"x",edge:0,fill:e,stroke:l,"stroke-width":i}}),{started:!0}}},mouseMove(t){if(a){if("star"===o.getMode()){const e=Number(r.getAttribute("cx")),o=Number(r.getAttribute("cy")),l=Number(r.getAttribute("point")),n=r.getAttribute("orient"),i=r.getAttribute("fill"),s=r.getAttribute("stroke"),a=Number(r.getAttribute("stroke-width")),u=Number(r.getAttribute("radialshift"));let c=t.mouse_x,d=t.mouse_y;const g=Math.sqrt((c-e)*(c-e)+(d-o)*(d-o))/1.5,b=document.getElementById("RadiusMultiplier").value,p=g/b;r.setAttribute("r",g),r.setAttribute("r2",p),r.setAttribute("starRadiusMultiplier",b);let h="";for(let t=0;l>=t;t++){let i=2*Math.PI*(t/l);"point"===n?i-=Math.PI/2:"edge"===n&&(i=i+Math.PI/l-Math.PI/2),c=g*Math.cos(i)+e,d=g*Math.sin(i)+o,h+=c+","+d+" ",isNaN(p)||(i=2*Math.PI*(t/l)+Math.PI/l,"point"===n?i-=Math.PI/2:"edge"===n&&(i=i+Math.PI/l-Math.PI/2),i+=u,c=p*Math.cos(i)+e,d=p*Math.sin(i)+o,h+=c+","+d+" ")}return r.setAttribute("points",h),r.setAttribute("fill",i),r.setAttribute("stroke",s),r.setAttribute("stroke-width",a),r.getAttribute("shape"),{started:!0}}if("polygon"===o.getMode()){const e=Number(r.getAttribute("cx")),o=Number(r.getAttribute("cy")),l=Number(r.getAttribute("sides")),n=r.getAttribute("fill"),i=r.getAttribute("stroke"),s=Number(r.getAttribute("stroke-width"));let a=t.mouse_x,u=t.mouse_y;const c=Math.sqrt((a-e)*(a-e)+(u-o)*(u-o))/1.5;r.setAttribute("edge",c);const d=c/2*cot(Math.PI/l)*sec(Math.PI/l);let g="";for(let t=0;l>=t;t++){const n=2*Math.PI*t/l;a=d*Math.cos(n)+e,u=d*Math.sin(n)+o,g+=a+","+u+" "}return r.setAttribute("points",g),r.setAttribute("fill",n),r.setAttribute("stroke",i),r.setAttribute("stroke-width",s),{started:!0}}}},mouseUp(){if("star"===o.getMode()){return{keep:"0"!==r.getAttribute("r"),element:r}}if("polygon"===o.getMode()){return{keep:"0"!==r.getAttribute("edge"),element:r}}},selectedChanged(t){s=t.elems;let e=s.length;if(!e)return showPanel(!1,"star"),void showPanel(!1,"polygon");for(;e--;){const o=s[e];"star"===(null==o?void 0:o.getAttribute("shape"))?t.selectedElement&&!t.multiselected?(n("starNumPoints").value=o.getAttribute("point"),n("radialShift").value=o.getAttribute("radialshift"),showPanel(!0,"star")):showPanel(!1,"star"):"regularPoly"===(null==o?void 0:o.getAttribute("shape"))?t.selectedElement&&!t.multiselected?(n("polySides").value=o.getAttribute("sides"),showPanel(!0,"polygon")):showPanel(!1,"polygon"):(showPanel(!1,"star"),showPanel(!1,"polygon"))}}}}},o=Object.freeze({__proto__:null,default:{name:"star",title:"Polygone/Star Tool",buttons:[{title:"Star Tool"},{title:"Polygon Tool"}],contextTools:[{title:"Number of Sides",label:"points"},{title:"Pointiness",label:"Pointiness"},{title:"Twists the star",label:"Radial Shift"},{title:"Number of Sides",label:"sides"}]}}),l=Object.freeze({__proto__:null,default:{name:"etoile",title:"Outil Polygone/Etoile",buttons:[{title:"Outil Etoile"},{title:"Outil Polygone"}],contextTools:[{title:"Nombre de côtés",label:"points"},{title:"Précision",label:"Précision"},{title:"Torsion Etoile",label:"Décalage Radial"},{title:"Nombre de côtés",label:"côtés"}]}}),n=Object.freeze({__proto__:null,default:{name:"yıldız",title:"Çokgen/Yıldız Aracı",buttons:[{title:"Yıldız Aracı"},{title:"Çokgen Aracı"}],contextTools:[{title:"Kenar Sayısı",label:"noktalar"},{title:"Sivrilik",label:"Sivrilik"},{title:"Yıldızı Kıvır",label:"Döngüsel Kaydırma"},{title:"Kenar Sayısı",label:"kenarlar"}]}}),i=Object.freeze({__proto__:null,default:{name:"星形",title:"Polygone/Star Tool",buttons:[{title:"星形工具"},{title:"多边形工具"}],contextTools:[{title:"顶点",label:"顶点"},{title:"钝度",label:"钝度"},{title:"径向",label:"径向"},{title:"边数",label:"边数"}]}});export{e as default};
//# sourceMappingURL=ext-polystar.js.map
