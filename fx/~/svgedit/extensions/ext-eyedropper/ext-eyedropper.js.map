{"version": 3, "file": "ext-eyedropper.js", "sources": ["../../../../src/editor/extensions/ext-eyedropper/ext-eyedropper.js", "../../../../src/editor/extensions/ext-eyedropper/locale/en.js", "../../../../src/editor/extensions/ext-eyedropper/locale/fr.js", "../../../../src/editor/extensions/ext-eyedropper/locale/tr.js", "../../../../src/editor/extensions/ext-eyedropper/locale/zh-CN.js"], "sourcesContent": ["/**\n * @file ext-eyedropper.js\n *\n * @license MIT\n *\n * @copyright 2010 <PERSON>\n * @copyright 2021 OptimistikSAS\n *\n */\n\nconst name = 'eyedropper'\n\nconst loadExtensionTranslation = async function (svgEditor) {\n  let translationModule\n  const lang = svgEditor.configObj.pref('lang')\n  try {\n    translationModule = await import(`./locale/${lang}.js`)\n  } catch (_error) {\n    console.warn(`Missing translation (${lang}) for ${name} - using 'en'`)\n    translationModule = await import('./locale/en.js')\n  }\n  svgEditor.i18next.addResourceBundle(lang, name, translationModule.default)\n}\n\nexport default {\n  name,\n  async init () {\n    const svgEditor = this\n    const { svgCanvas } = svgEditor\n    await loadExtensionTranslation(svgEditor)\n    const { ChangeElementCommand } = svgCanvas.history\n    // svgdoc = S.svgroot.parentNode.ownerDocument,\n    const addToHistory = (cmd) => { svgCanvas.undoMgr.addCommandToHistory(cmd) }\n    const currentStyle = {\n      fillPaint: 'red',\n      fillOpacity: 1.0,\n      strokePaint: 'black',\n      strokeOpacity: 1.0,\n      strokeWidth: 5,\n      strokeDashArray: null,\n      opacity: 1.0,\n      strokeLinecap: 'butt',\n      strokeLinejoin: 'miter'\n    }\n    const { $id, $click } = svgCanvas\n\n    /**\n     *\n     * @param {module:svgcanvas.SvgCanvas#event:ext_selectedChanged|module:svgcanvas.SvgCanvas#event:ext_elementChanged} opts\n     * @returns {void}\n     */\n    const getStyle = (opts) => {\n      // if we are in eyedropper mode, we don't want to disable the eye-dropper tool\n      const mode = svgCanvas.getMode()\n      if (mode === 'eyedropper') { return }\n\n      const tool = $id('tool_eyedropper')\n      // enable-eye-dropper if one element is selected\n      let elem = null\n      if (!opts.multiselected && opts.elems[0] &&\n        !['svg', 'g', 'use'].includes(opts.elems[0].nodeName)\n      ) {\n        elem = opts.elems[0]\n        tool.classList.remove('disabled')\n        // grab the current style\n        currentStyle.fillPaint = elem.getAttribute('fill') || 'black'\n        currentStyle.fillOpacity = elem.getAttribute('fill-opacity') || 1.0\n        currentStyle.strokePaint = elem.getAttribute('stroke')\n        currentStyle.strokeOpacity = elem.getAttribute('stroke-opacity') || 1.0\n        currentStyle.strokeWidth = elem.getAttribute('stroke-width')\n        currentStyle.strokeDashArray = elem.getAttribute('stroke-dasharray')\n        currentStyle.strokeLinecap = elem.getAttribute('stroke-linecap')\n        currentStyle.strokeLinejoin = elem.getAttribute('stroke-linejoin')\n        currentStyle.opacity = elem.getAttribute('opacity') || 1.0\n        // disable eye-dropper tool\n      } else {\n        tool.classList.add('disabled')\n      }\n    }\n\n    return {\n      name: svgEditor.i18next.t(`${name}:name`),\n      callback () {\n        // Add the button and its handler(s)\n        const title = `${name}:buttons.0.title`\n        const key = `${name}:buttons.0.key`\n        const buttonTemplate = `\n        <se-button id=\"tool_eyedropper\" title=\"${title}\" src=\"eye_dropper.svg\" shortcut=${key}></se-button>\n        `\n        svgCanvas.insertChildAtIndex($id('tools_left'), buttonTemplate, 12)\n        $click($id('tool_eyedropper'), () => {\n          if (this.leftPanel.updateLeftPanel('tool_eyedropper')) {\n            svgCanvas.setMode('eyedropper')\n          }\n        })\n      },\n      // if we have selected an element, grab its paint and enable the eye dropper button\n      selectedChanged: getStyle,\n      elementChanged: getStyle,\n      mouseDown (opts) {\n        const mode = svgCanvas.getMode()\n        if (mode === 'eyedropper') {\n          const e = opts.event\n          const { target } = e\n          if (!['svg', 'g', 'use'].includes(target.nodeName)) {\n            const changes = {}\n\n            const change = function (elem, attrname, newvalue) {\n              changes[attrname] = elem.getAttribute(attrname)\n              elem.setAttribute(attrname, newvalue)\n            }\n\n            if (currentStyle.fillPaint) { change(target, 'fill', currentStyle.fillPaint) }\n            if (currentStyle.fillOpacity) { change(target, 'fill-opacity', currentStyle.fillOpacity) }\n            if (currentStyle.strokePaint) { change(target, 'stroke', currentStyle.strokePaint) }\n            if (currentStyle.strokeOpacity) { change(target, 'stroke-opacity', currentStyle.strokeOpacity) }\n            if (currentStyle.strokeWidth) { change(target, 'stroke-width', currentStyle.strokeWidth) }\n            if (currentStyle.strokeDashArray) { change(target, 'stroke-dasharray', currentStyle.strokeDashArray) }\n            if (currentStyle.opacity) { change(target, 'opacity', currentStyle.opacity) }\n            if (currentStyle.strokeLinecap) { change(target, 'stroke-linecap', currentStyle.strokeLinecap) }\n            if (currentStyle.strokeLinejoin) { change(target, 'stroke-linejoin', currentStyle.strokeLinejoin) }\n\n            addToHistory(new ChangeElementCommand(target, changes))\n          }\n        }\n      }\n    }\n  }\n}\n", "export default {\n  name: 'eyedropper',\n  buttons: [\n    {\n      title: 'Eye Dropper Tool',\n      key: 'I'\n    }\n  ]\n}\n", "export default {\n  name: 'pipette',\n  buttons: [\n    {\n      title: 'Outil pipette',\n      key: 'I'\n    }\n  ]\n}\n", "export default {\n  name: 're<PERSON><PERSON><PERSON><PERSON>',\n  buttons: [\n    {\n      title: '<PERSON><PERSON> Seçim <PERSON>',\n      key: 'I'\n    }\n  ]\n}\n", "export default {\n  name: '滴管',\n  buttons: [\n    {\n      title: '滴管工具',\n      key: 'I'\n    }\n  ]\n}\n"], "names": ["name", "loadExtensionTranslation", "async", "svgEditor", "translationModule", "lang", "config<PERSON><PERSON><PERSON>", "pref", "__variableDynamicImportRuntime0__", "concat", "_error", "console", "warn", "Promise", "resolve", "then", "en$1", "i18next", "addResourceBundle", "default", "extEyedropper", "this", "svgCanvas", "ChangeElementCommand", "history", "currentStyle", "<PERSON><PERSON><PERSON><PERSON>", "fillOpacity", "<PERSON><PERSON><PERSON><PERSON>", "strokeOpacity", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "opacity", "strokeLinecap", "strokeLinejoin", "$id", "$click", "getStyle", "opts", "getMode", "tool", "elem", "multiselected", "elems", "includes", "nodeName", "classList", "add", "remove", "getAttribute", "t", "callback", "title", "key", "buttonTemplate", "insertChildAtIndex", "leftPanel", "updateLeftPanel", "setMode", "<PERSON><PERSON><PERSON><PERSON>", "elementChanged", "mouseDown", "e", "event", "target", "changes", "change", "attrname", "newvalue", "setAttribute", "cmd", "undoMgr", "addCommandToHistory", "buttons"], "mappings": ";;;;;;;;;AAUA,MAAMA,EAAO,aAEPC,yBAA2BC,eAAgBC,GAC/C,IAAIC,EACJ,MAAMC,EAAOF,EAAUG,UAAUC,KAAK,QACtC,IACEH,0hBAA0BI,CAAM,YAAAC,OAAaJ,EAA7C,QACA,MAAOK,GACPC,QAAQC,KAA6BP,wBAAAA,OAAAA,mBAAaL,EAAlD,kBACAI,QAA0BS,QAAAC,UAAAC,MAAA,WAAA,OAAAC,KAE5Bb,EAAUc,QAAQC,kBAAkBb,EAAML,EAAMI,EAAkBe,UAGpE,IAAeC,EAAA,CACbpB,KAAAA,EACAE,aACE,MAAMC,EAAYkB,MACZC,UAAEA,GAAcnB,QAChBF,yBAAyBE,GAC/B,MAAMoB,qBAAEA,GAAyBD,EAAUE,QAGrCC,EAAe,CACnBC,UAAW,MACXC,YAAa,EACbC,YAAa,QACbC,cAAe,EACfC,YAAa,EACbC,gBAAiB,KACjBC,QAAS,EACTC,cAAe,OACfC,eAAgB,UAEZC,IAAEA,EAAFC,OAAOA,GAAWd,EAOlBe,SAAYC,IAGhB,GAAa,eADAhB,EAAUiB,UACM,OAE7B,MAAMC,EAAOL,EAAI,mBAEjB,IAAIM,EAAO,KACNH,EAAKI,gBAAiBJ,EAAKK,MAAM,IACnC,CAAC,MAAO,IAAK,OAAOC,SAASN,EAAKK,MAAM,GAAGE,UAgB5CL,EAAKM,UAAUC,IAAI,aAdnBN,EAAOH,EAAKK,MAAM,GAClBH,EAAKM,UAAUE,OAAO,YAEtBvB,EAAaC,UAAYe,EAAKQ,aAAa,SAAW,QACtDxB,EAAaE,YAAcc,EAAKQ,aAAa,iBAAmB,EAChExB,EAAaG,YAAca,EAAKQ,aAAa,UAC7CxB,EAAaI,cAAgBY,EAAKQ,aAAa,mBAAqB,EACpExB,EAAaK,YAAcW,EAAKQ,aAAa,gBAC7CxB,EAAaM,gBAAkBU,EAAKQ,aAAa,oBACjDxB,EAAaQ,cAAgBQ,EAAKQ,aAAa,kBAC/CxB,EAAaS,eAAiBO,EAAKQ,aAAa,mBAChDxB,EAAaO,QAAUS,EAAKQ,aAAa,YAAc,IAO3D,MAAO,CACLjD,KAAMG,EAAUc,QAAQiC,EAAlB,GAAAzC,OAAuBT,EADxB,UAELmD,WAEE,MAAMC,EAAWpD,GAAAA,OAAAA,EAAjB,oBACMqD,EAASrD,GAAAA,OAAAA,EAAf,kBACMsD,EAAc,oDAAA7C,OACqB2C,EADrB,qCAAA3C,OAC8D4C,EADlF,2BAGA/B,EAAUiC,mBAAmBpB,EAAI,cAAemB,EAAgB,IAChElB,EAAOD,EAAI,oBAAoB,KACzBd,KAAKmC,UAAUC,gBAAgB,oBACjCnC,EAAUoC,QAAQ,kBAKxBC,gBAAiBtB,SACjBuB,eAAgBvB,SAChBwB,UAAWvB,GAET,GAAa,eADAhB,EAAUiB,UACI,CACzB,MAAMuB,EAAIxB,EAAKyB,OACTC,OAAEA,GAAWF,EACnB,IAAK,CAAC,MAAO,IAAK,OAAOlB,SAASoB,EAAOnB,UAAW,CAClD,MAAMoB,EAAU,GAEVC,OAAS,SAAUzB,EAAM0B,EAAUC,GACvCH,EAAQE,GAAY1B,EAAKQ,aAAakB,GACtC1B,EAAK4B,aAAaF,EAAUC,IAG1B3C,EAAaC,WAAawC,OAAOF,EAAQ,OAAQvC,EAAaC,WAC9DD,EAAaE,aAAeuC,OAAOF,EAAQ,eAAgBvC,EAAaE,aACxEF,EAAaG,aAAesC,OAAOF,EAAQ,SAAUvC,EAAaG,aAClEH,EAAaI,eAAiBqC,OAAOF,EAAQ,iBAAkBvC,EAAaI,eAC5EJ,EAAaK,aAAeoC,OAAOF,EAAQ,eAAgBvC,EAAaK,aACxEL,EAAaM,iBAAmBmC,OAAOF,EAAQ,mBAAoBvC,EAAaM,iBAChFN,EAAaO,SAAWkC,OAAOF,EAAQ,UAAWvC,EAAaO,SAC/DP,EAAaQ,eAAiBiC,OAAOF,EAAQ,iBAAkBvC,EAAaQ,eAC5ER,EAAaS,gBAAkBgC,OAAOF,EAAQ,kBAAmBvC,EAAaS,gBAxFpEoC,EA0FD,IAAI/C,EAAqByC,EAAQC,GA1FtB3C,EAAUiD,QAAQC,oBAAoBF,IAAhDA,IAAAA,8CChCX,CACbtE,KAAM,aACNyE,QAAS,CACP,CACErB,MAAO,mBACPC,IAAK,iDCLI,CACbrD,KAAM,UACNyE,QAAS,CACP,CACErB,MAAO,gBACPC,IAAK,iDCLI,CACbrD,KAAM,aACNyE,QAAS,CACP,CACErB,MAAO,mBACPC,IAAK,iDCLI,CACbrD,KAAM,KACNyE,QAAS,CACP,CACErB,MAAO,OACPC,IAAK"}