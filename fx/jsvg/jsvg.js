import { FxElement, html, css } from '../../fx.js';

import '../button/button.js';
import '../dropdown/dropdown.js';
import '/fx/~/svgedit/Editor.js';

customElements.define('fx-jsvg', class FxJSVG extends FxElement {
    static properties = {
        readOnly: { type: Boolean, local: true },
        editMode: { type: Boolean, default: false, notify: true },
        source: { type: String, default: '' },
        cell: { type: Object, default: {}, notify: true },
        h: { type: Number, default: '' },
        showBtnSettings: { type: Boolean, default: false }
    }

    firstUpdated() {
        super.firstUpdated();
        this.listen('editMode-changed', () => {
            this.showSettings(!this.editMode);
        })
    }

    showSettings(editMode, setEditMode) {
        if (editMode) {
            this.source = this.editor?.svgCanvas.svgCanvasToString() || this.source || this.cell?.source || '';
            if (this.cell?.source !== this.source) {
                this.cell.source = this.source;
                setTimeout(() => this.fire('change', this.source), 50);
            }
        } else {
            const _int = setInterval(async () => {
                this.editor = this.$qs('iframe')?.contentDocument._svgEditor;
                if (this.editor) {
                    clearInterval(_int);
                    await this.sleep(100);
                    this.editor.loadFromString(this.cell?.source || this.source || `<svg width="640" height="480" xmlns="http://www.w3.org/2000/svg" xmlns:svg="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"></svg>`)
                }
            }, 100)
        }
        if (setEditMode) this.editMode = !this.editMode;
    }

    static styles = css`
        :host { 
            position: relative; 
            display: flex;
            flex-direction: column;
            width: 100%; 
            height: 100%;
            overflow: hidden;  
        }
        #btn-settings {
            position: absolute; 
            top: 8px; 
            right: 6px; 
            opacity: .3;
            z-index: 100;
        }
        #btn-settings:hover {
            opacity: .7
        }
    `

    render() {
        return html`
        ${this.readOnly || (!this.readOnly && !this.editMode) ? html`
            <div style="display: flex; flex-direction: column; width: 100%; height: 100%; min-height: 12px;"> 
                <div .innerHTML=${this.source} style="width: 100%; height: 100%; overflow: auto;"></div>
            </div>
        ` : html`
            <iframe src="/fx/~/svgedit/index.html" style="width: 100%; height: 100%; border: none;"></iframe>
        `}
        ${this.showBtnSettings && !this.readOnly ? html`
            <fx-button id="btn-settings" name="cb-settings" @click=${() => this.showSettings(this.editMode, true)}></fx-button>
        ` : html``}
    `
    }

})
