let e=!1;const t=[],o=void 0!==window.ontouchstart;
/**
 * @file ext-overview_window.js
 *
 * @license MIT
 *
 * @copyright 2013 <PERSON>
 *
 */
var i={name:"overview_window",init(i){const l=this,{$id:n,$click:r}=l.svgCanvas,p={};n("sidepanel_content").insertAdjacentHTML("beforeend",'<div id="overview_window_content_pane" style="width:100%; word-wrap:break-word;  display:inline-block; margin-top:20px;"><div id="overview_window_content" style="position:relative; padding-left:15px; top:0px;"><div style="background-color:#A0A0A0; display:inline-block; overflow:visible;"><svg id="overviewMiniView" width="132" height="100" x="0" y="0" viewBox="0 0 4800 3600" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><use x="0" y="0" xlink:href="#svgroot"> </use></svg><div id="overview_window_view_box" style="min-width:50px; min-height:50px; position:absolute; top:30px; left:30px; z-index:5; background-color:rgba(255,0,102,0.3);"></div></div></div></div>');const updateViewBox=()=>{const{workarea:e}=l,t=parseFloat(getComputedStyle(e,null).height.replace("px","")),o=parseFloat(getComputedStyle(e,null).width.replace("px","")),i=e.scrollLeft,r=e.scrollTop,p=parseFloat(getComputedStyle(n("svgcanvas"),null).width.replace("px","")),a=parseFloat(getComputedStyle(n("svgcanvas"),null).height.replace("px","")),s=parseFloat(getComputedStyle(n("overviewMiniView"),null).width.replace("px","")),w=parseFloat(getComputedStyle(n("overviewMiniView"),null).height.replace("px","")),v=i/p*s,d=r/a*w,u=o/p*s,c=t/a*w;n("overview_window_view_box").style.minWidth=u+"px",n("overview_window_view_box").style.minHeight=c+"px",n("overview_window_view_box").style.top=d+"px",n("overview_window_view_box").style.left=v+"px"};n("workarea").addEventListener("scroll",(()=>{p.viewBoxDragging||updateViewBox()})),n("workarea").addEventListener("resize",updateViewBox),updateViewBox();const updateViewDimensions=function(){const e=parseFloat(getComputedStyle(n("svgroot"),null).width.replace("px","")),t=parseFloat(getComputedStyle(n("svgroot"),null).height.replace("px","")),o=t/e*parseFloat(getComputedStyle(n("overviewMiniView"),null).width.replace("px",""));n("overviewMiniView").setAttribute("viewBox","640 480 "+e+" "+t),n("overviewMiniView").setAttribute("height",o),updateViewBox()};updateViewDimensions(),p.viewBoxDragging=!1;const updateViewPortFromViewBox=function(){const e=parseFloat(getComputedStyle(n("svgcanvas"),null).width.replace("px","")),t=parseFloat(getComputedStyle(n("svgcanvas"),null).height.replace("px","")),o=parseFloat(getComputedStyle(n("overviewMiniView"),null).width.replace("px","")),i=parseFloat(getComputedStyle(n("overviewMiniView"),null).height.replace("px","")),l=parseFloat(getComputedStyle(n("overview_window_view_box"),null).getPropertyValue("left").replace("px",""))/o*e,r=parseFloat(getComputedStyle(n("overview_window_view_box"),null).getPropertyValue("top").replace("px",""))/i*t;n("workarea").scrollLeft=l,n("workarea").scrollTop=r},a=document.querySelector("#overview_window_view_box");return function(i,l,n,r,p,a){e||document.addEventListener(o?"touchmove":"mousemove",(function(e){let o=e;e.touches&&(o=e.touches[0]);for(let e=0;e<t.length;e++)t[e](o.clientX,o.clientY)})),e=!0;let s=!1,w=!1,v=0,d=0,u=0,c=0;l.addEventListener(o?"touchstart":"mousedown",(function(e){if(e.stopPropagation(),e.preventDefault(),"false"===i.dataset.dragEnabled)return;let t=e;e.touches&&(t=e.touches[0]),s=!0,v=i.offsetLeft-t.clientX,d=i.offsetTop-t.clientY})),document.addEventListener(o?"touchend":"mouseup",(function(){p&&w&&p(i,n,parseInt(i.style.left),parseInt(i.style.top)),s=!1,w=!1})),document.addEventListener(o?"touchmove":"mousemove",(function(){a&&w&&a(i,parseInt(i.style.left),parseInt(i.style.top))})),t.push((function move(e,t){if(s){if(w||(w=!0,r&&r(i,u,c)),u=e+v,c=t+d,"true"===i.dataset.dragBoundary){if(u<1||u>=window.innerWidth-i.offsetWidth)return;if(c<1||c>=window.innerHeight-i.offsetHeight)return}i.style.left=u+"px",i.style.top=c+"px"}}))}(a,a,document.querySelector("#overviewMiniView"),(()=>{p.viewBoxDragging=!0,updateViewPortFromViewBox()}),((e,t,o,i)=>{e.offsetLeft+e.offsetWidth>parseFloat(getComputedStyle(t,null).width.replace("px",""))?e.style.left=parseFloat(getComputedStyle(t,null).width.replace("px",""))-e.offsetWidth+"px":e.offsetLeft<0&&(e.style.left="0px"),e.offsetTop+e.offsetHeight>parseFloat(getComputedStyle(t,null).height.replace("px",""))?e.style.top=parseFloat(getComputedStyle(t,null).height.replace("px",""))-e.offsetHeight+"px":e.offsetTop<0&&(e.style.top="0px"),p.viewBoxDragging=!1,updateViewPortFromViewBox()}),(function(){updateViewPortFromViewBox()})),r(n("overviewMiniView"),(e=>{const t=e.offsetX||e.originalEvent.layerX,o=e.offsetY||e.originalEvent.layerY,i=parseFloat(getComputedStyle(n("overviewMiniView"),null).width.replace("px","")),l=parseFloat(getComputedStyle(n("overviewMiniView"),null).height.replace("px","")),r=parseFloat(getComputedStyle(n("overview_window_view_box"),null).getPropertyValue("min-width").replace("px","")),p=parseFloat(getComputedStyle(n("overview_window_view_box"),null).getPropertyValue("min-height").replace("px",""));let a=t-.5*r,s=o-.5*p;a<0&&(a=0),s<0&&(s=0),a+r>i&&(a=i-r),s+p>l&&(s=l-p),n("overview_window_view_box").style.top=s+"px",n("overview_window_view_box").style.left=a+"px",updateViewPortFromViewBox()})),{name:"overview window",canvasUpdated:updateViewDimensions,workareaResized:updateViewBox}}};export{i as default};
//# sourceMappingURL=ext-overview_window.js.map
