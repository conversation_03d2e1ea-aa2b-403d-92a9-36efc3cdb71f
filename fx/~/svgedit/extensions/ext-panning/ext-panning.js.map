{"version": 3, "file": "ext-panning.js", "sources": ["../../../../src/editor/extensions/ext-panning/ext-panning.js", "../../../../src/editor/extensions/ext-panning/locale/en.js", "../../../../src/editor/extensions/ext-panning/locale/tr.js", "../../../../src/editor/extensions/ext-panning/locale/zh-CN.js"], "sourcesContent": ["/**\n * @file ext-panning.js\n *\n * @license MIT\n *\n * @copyright 2013 <PERSON>\n *\n */\n/*\n  This is a very basic SVG-Edit extension to let tablet/mobile devices pan without problem\n*/\n\nconst name = 'panning'\n\nconst loadExtensionTranslation = async function (svgEditor) {\n  let translationModule\n  const lang = svgEditor.configObj.pref('lang')\n  try {\n    translationModule = await import(`./locale/${lang}.js`)\n  } catch (_error) {\n    console.warn(`Missing translation (${lang}) for ${name} - using 'en'`)\n    translationModule = await import('./locale/en.js')\n  }\n  svgEditor.i18next.addResourceBundle(lang, name, translationModule.default)\n}\n\nexport default {\n  name,\n  async init () {\n    const svgEditor = this\n    await loadExtensionTranslation(svgEditor)\n    const {\n      svgCanvas\n    } = svgEditor\n    const { $id, $click } = svgCanvas\n    const insertAfter = (referenceNode, newNode) => {\n      referenceNode.parentNode.insertBefore(newNode, referenceNode.nextSibling)\n    }\n    return {\n      name: svgEditor.i18next.t(`${name}:name`),\n      callback () {\n        const btitle = `${name}:buttons.0.title`\n        // Add the button and its handler(s)\n        const buttonTemplate = document.createElement('template')\n        buttonTemplate.innerHTML = `\n        <se-button id=\"ext-panning\" title=\"${btitle}\" src=\"panning.svg\"></se-button>\n        `\n        insertAfter($id('tool_zoom'), buttonTemplate.content.cloneNode(true))\n        $click($id('ext-panning'), () => {\n          if (this.leftPanel.updateLeftPanel('ext-panning')) {\n            svgCanvas.setMode('ext-panning')\n          }\n        })\n      },\n      mouseDown () {\n        if (svgCanvas.getMode() === 'ext-panning') {\n          svgEditor.setPanning(true)\n          return {\n            started: true\n          }\n        }\n        return undefined\n      },\n      mouseUp () {\n        if (svgCanvas.getMode() === 'ext-panning') {\n          svgEditor.setPanning(false)\n          return {\n            keep: false,\n            element: null\n          }\n        }\n        return undefined\n      }\n    }\n  }\n}\n", "export default {\n  name: 'Extension Panning',\n  buttons: [\n    {\n      title: 'Panning'\n    }\n  ]\n}\n", "export default {\n  name: '<PERSON><PERSON><PERSON><PERSON> ',\n  buttons: [\n    {\n      title: '<PERSON><PERSON><PERSON><PERSON>'\n    }\n  ]\n}\n", "export default {\n  name: '移动',\n  buttons: [\n    {\n      title: '移动'\n    }\n  ]\n}\n"], "names": ["name", "loadExtensionTranslation", "async", "svgEditor", "translationModule", "lang", "config<PERSON><PERSON><PERSON>", "pref", "__variableDynamicImportRuntime0__", "concat", "_error", "console", "warn", "Promise", "resolve", "then", "en$1", "i18next", "addResourceBundle", "default", "extPanning", "this", "svgCanvas", "$id", "$click", "t", "callback", "btitle", "buttonTemplate", "document", "createElement", "referenceNode", "newNode", "innerHTML", "content", "cloneNode", "parentNode", "insertBefore", "nextS<PERSON>ling", "leftPanel", "updateLeftPanel", "setMode", "mouseDown", "getMode", "setPanning", "started", "mouseUp", "keep", "element", "buttons", "title"], "mappings": ";;;;;;;;AAYA,MAAMA,EAAO,UAEPC,yBAA2BC,eAAgBC,GAC/C,IAAIC,EACJ,MAAMC,EAAOF,EAAUG,UAAUC,KAAK,QACtC,IACEH,+cAA0BI,CAAM,YAAAC,OAAaJ,EAA7C,QACA,MAAOK,GACPC,QAAQC,KAA6BP,wBAAAA,OAAAA,mBAAaL,EAAlD,kBACAI,QAA0BS,QAAAC,UAAAC,MAAA,WAAA,OAAAC,KAE5Bb,EAAUc,QAAQC,kBAAkBb,EAAML,EAAMI,EAAkBe,UAGpE,IAAeC,EAAA,CACbpB,KAAAA,EACAE,aACE,MAAMC,EAAYkB,WACZpB,yBAAyBE,GAC/B,MAAMmB,UACJA,GACEnB,GACEoB,IAAEA,EAAFC,OAAOA,GAAWF,EAIxB,MAAO,CACLtB,KAAMG,EAAUc,QAAQQ,EAAlB,GAAAhB,OAAuBT,EADxB,UAEL0B,WACE,MAAMC,EAAM,GAAAlB,OAAMT,EAAN,oBAEN4B,EAAiBC,SAASC,cAAc,YAR9B,IAACC,EAAeC,EAShCJ,EAAeK,UAAf,gDAAAxB,OACqCkB,EADrC,8CATiBI,EAYLR,EAAI,aAZgBS,EAYFJ,EAAeM,QAAQC,WAAU,GAXjEJ,EAAcK,WAAWC,aAAaL,EAASD,EAAcO,aAY3Dd,EAAOD,EAAI,gBAAgB,KACrBF,KAAKkB,UAAUC,gBAAgB,gBACjClB,EAAUmB,QAAQ,mBAIxBC,YACE,GAA4B,gBAAxBpB,EAAUqB,UAEZ,OADAxC,EAAUyC,YAAW,GACd,CACLC,SAAS,IAKfC,UACE,GAA4B,gBAAxBxB,EAAUqB,UAEZ,OADAxC,EAAUyC,YAAW,GACd,CACLG,MAAM,EACNC,QAAS,kDCpEN,CACbhD,KAAM,oBACNiD,QAAS,CACP,CACEC,MAAO,uDCJE,CACblD,KAAM,kBACNiD,QAAS,CACP,CACEC,MAAO,wDCJE,CACblD,KAAM,KACNiD,QAAS,CACP,CACEC,MAAO"}