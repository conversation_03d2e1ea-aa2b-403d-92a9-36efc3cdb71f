```html
<div style="position:relative;padding-bottom:48%; margin:10px">
    <iframe src="https://www.youtube.com/embed/cQy2BQAVtVc" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen 
    	style="position:absolute;width:100%;height:100%;"></iframe>
</div>

```

### Showdown's Wiki pages 
[Окрыть wiki в новом окне](https://github.com/showdownjs/showdown/wiki)

$x=\frac{ -b\pm\sqrt{ b^2-4ac } } {2a}$

$$x=\frac{ -b\pm\sqrt{ b^2-4ac } } {2a}$$


<h1 style="text-align: center;">$x^n + y^n = z^n$</h1>


##$E=mc^2$

**Имя** компонента обязательно должно содержать хотя бы один дефис в соответствии с требованиями [стандарта HTML](https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name)   


```javascript
import { FxElement, html, css } from '../../fx.js';

import '../button/button.js';

customElements.define('<fx-layout-panel', class LiLayoutPanel extends FxElement {
    static get properties() {
        return {

        }
    }

    connectedCallback() {
        super.connectedCallback();

    }

    static get styles() {
        return css`

        `;
    }

    render() {
        return html`
            
        `;
    }
});

```

Для примера в шаблоне данного компонента задана кнопка, в обработчике нажатия **\_onTap** которой значение 
счетчика **\_count** увеличивается на единицу. В свойстве **text** объявлен метод с именем **get**, который и является геттером. Этот метод будет вызываться не только при обращение к свойству для чтения, как это указано в шаблоне компонента с помощью директивы **{{ }}**, но и при любом изменении связанного с ним свойства из-за механизма реактивности.

Начальное значение свойства **text**, указанное в параметре **default**, будет присвоено свойству только при создании компонента. После этого значение свойства будет формироваться уже геттером. При этом его начальное значение будет потеряно в любом случае, так как при первом обращении к свойству оно будет переписано геттером.

Значение выражения в двойных фигурных скобках подставляется как простой текст, а не как HTML-код. 
Если необходимо, чтобы вместо текста выводился сырой HTML-код необходимо использовать специальную директиву **~html**.

```xml
<style>
    body {
        padding: 0px;
        margin: 0px;
        background: lightyellow
    }

</style>
```

```html
<div>
    <<fx-dropdown id="drop">
        <div id="content" style="border:1px solid red;text-align:center;background: lightyellow;display: inline-block;visibility: hidden;display: flex;flex-direction: column">
            <section class="section-buttons">
                <h2>Buttons icon</h2>
                <lit-button class="primary icon" aria-label="search">
                    <lit-icon icon="search" aria-hidden="true">OK</lit-icon>
                </lit-button>
                <lit-button class="primary icon" aria-label="menu">
                    <lit-icon icon="menu" aria-hidden="true"></lit-icon>
                </lit-button>
                <lit-button class="primary icon">
                    <lit-icon icon="crop" aria-label="crop"></lit-icon>
                </lit-button>
            </section>

            <section class="section-buttons section-max-width" style="font-size: 62.5%;">
                <h2>Buttons block</h2>
                <lit-button class="primary block margin-bottom">Primary block</lit-button>
                <lit-button class="secondary block margin-bottom">Secondary block</lit-button>
                <lit-button class="success block">Success block</lit-button>
            </section>

            <lit-button id="ok" class="primary block" style="padding: 10px">OK</lit-button>

        </div>
    </<fx-dropdown>
</div>
```

Содержимое второго тега **p** в данном примере будет заменено значением свойства **text**, интерпретированного как обычный HTML. В первом теге **p** свойство **text** интерпретируется как обычный текст.

Важно помнить html-коде все внутренние привязки игнорируются.

``` info_nocopy_md
Директиву **HTML** нельзя использовать для ~~вложения~~ шаблонов друг в друга. Вместо этого нужно использовать компоненты, позволяющие объединять и повторно использовать элементы UI.
```

``` warning_nocopy_md
Динамическая отрисовка произвольного HTML-кода на сайте крайне опасна, так как может легко привести к XSS-уязвимостям. Используйте интерполяцию HTML только для доверенного кода, и никогда не подставляйте туда содержимое, создаваемое пользователями.
```


``` warning_nocopy_md
This is WARNING message ...
This is WARNING message ...
This is WARNING message ...
This is WARNING message ...
```

``` error_nocopy_md
This is ERROR message ...
```

``` success_nocopy_md
This is SUCCESS message ...
```

``` info_nocopy_md
This is INFO message ...
```

``` help_nocopy_md
This is HELP message ...
```

``` like_nocopy_md
This is LIKE message ...
```

``` faq_nocopy_md
This is FAQ message ...
```

# H1 Demo
## H2 Demo
### H3 Demo
#### H4 Demo
##### H5 Demo
###### H6 Demo

Sub-heading
-----------

Paragraphs are separated
by a blank line.

Two spaces at the end of a line  
produces a line break.

Text attributes _italic_, 
**bold**, `monospace`.

Horizontal rule:

---

Strikethrough:
~~strikethrough~~

Bullet list:

  * apples
  * oranges
  * pears

Numbered list:

  1. lather
  2. rinse
  3. repeat

An [example](http://example.com).


[ ZIP-архив](https://xxx.org/web/xxx/dist/xxx.zip)

CDN
~~~html
<script type="module" src="https://cdn.jsdelivr.net/gh/xxx/xxx-framework@master/xxx.js"></script>
~~~
~~~html
<script type="module" src="https://unpkg.com/browse/xxx-framework@0.0.1/xxx.json"></script>
~~~

NPM

~~~
 npm i xxx-framework
~~~

#  Сравнение СУБД: MS SQL и xxx

В таблице приведены ряд критериев, по которым можно сравнить xxx с реляционной СУБД MS SQL. Ниже — комментарии по некоторым вопросам.

| № | Характеристика | Реализация в xxx | Реализация в MS SQL |
|:---:|:---|:---:|:---:|
| **1** | **Универсальные свойства СУБД** | | | 
| 1.1 | Транзакционность | В терминах реляционной базы данных нет. Развернутый комментарий ниже. | Есть |
| 1.2 | Целостность | Есть. Развернутый комментарий ниже. | Есть |
| 1.3 | Ключи | Есть | Есть |
| 1.4 | Индексы | Есть | Есть |
| 1.5 | Язык запросов | XQuery | T-SQL |
| 1.6 | Процедуры | Есть. Реализуются в виде классов, которые предоставляют свои методы другим классам. | Есть |
| 1.7 | Функции | Есть. Реализуются в виде классов, которые предоставляют свои методы другим классам. | Есть |
| 1.8 | Job | Есть. Реализуется в виде классов, код методов которых выполняется на сервере. Запуск выполнения по событиям возможен. | Есть |
| 1.9 | Контроль uid ссылок. \(при изменении источника кеш не обновляется в зависимых объектах\) | Ссылка, аналогичная uid ссылке в реляционной базе данных, не используется в xxx по идеологическим причинам, хотя технически она реализуема. В xxx используется понятие связи, у которой другое поведение. | Есть |
| **2** | **Администрирование, инструменты разработки** | | | 
| 2.1 | Трекинг запросов | Есть. Изменение базы данных происходит ВСЕГДА через изменение объектов. Отслеживание изменений объектов реализовано. | Есть |
| 2.2 | План запроса | Нет | Есть |
| 2.3 | Управление доступом к таблицам/классам | Есть | Есть |
| 2.4 | Шифрование, анонимизация данных в БД | Техническая возможность есть, необходимо получение лицензии | Есть |
| 2.5 | Редактор запросов | Есть | Есть |
| 2.6 | Автоматическое резервное копирование | Есть | Есть |
| 2.7 | Автоматический Shrink | Нет. Не требуется, связано с архитектурой базы данных. База всегда оптимизирована по размеру. | Есть |
| **3** | **Интеграция с другими СУБД** | | | 
| 3.1 | Подключение к другим СУБД | Реализуется на прикладном уровне. Сейчас есть сервисный класс для подключения к другим базам данных через ODBC, с возможностью импорта. Также возможен импорт из других источников: Excel, csv файлы и др. | Есть |
| 3.2 | Выполнение выборки данных из другой СУБД | При выполнении импорта с использованием ODBC драйвера возможно выполнение выборки с помощью SQL запросов. | T-SQL |
| **4** | **Использование различных приложений** | | | 
| 4.1 | Доступ к данным из приложений | Через коннектор, разработанный для данного приложения. Можно использовать уже существующие коннекторы \(например, для 1С\). | Через стандартные драйвера |
| **5** | **Объектная модель работы с данными** | | | 
| 5.1 | Наследование | Есть | Нет |
| 5.2 | Иерархическая БД | Есть | Нет |
| 5.3 | Простое построение и работа со сложными структурами данных. Возможность отображать сложные сущности внешнего мира в одном классе. | Есть | Нет |
| 5.4 | Общий репозиторий методов | Есть | Нет |
| 5.5 | Распределенные хранение и обработка данных | Есть | Нет |
| 5.6 | Глобальная идентификация сущностей | Есть | Нет |
| 5.7 | Создание модели данных, которая включает в себя зависимости между данными | Есть | Нет |
| 5.8 | Возможность обмена \(продажи\) между разработчиками частями базы данных \(классов\). Возможность построение базы данных из частей базы данных \(классов\), созданных другими разработчиками. | Есть | Нет |
| 5.9 | Возможность использовать при построении базы данных сервисы с данными \(например, поле адреса\), которые поставляются другими разработчиками. | Есть | Нет |
| 5.10 | Возможность синхронизации моделей данных через общие репозитарии моделей данных между различными разработчиками | Есть | Нет |

## Транзакционность

>При изменении данных БД должна переходить от одного целостного состояния к другому. Однако, в процессе обновления данных возможны ситуации, когда состояние целостности нарушается. Во избежание таких ситуаций в СУБД вводится понятие транзакции — атомарного действия над БД, переводящего ее из одного целостного состояния в другое целостное состояние. Другими словами, транзакция — это последовательность операций, которые должны быть или все выполнены или все не выполнены \(все или ничего\).

>Понятие транзакции было разработано и реализовано в эпоху табличных реляционных баз данных. В таких БД прикладные сущности, как правило, не могут быть реализованы в одной таблице, а располагаются сразу в нескольких связанных таблицах. В этом случае операция в БД с одной прикладной сущностью требует обращения сразу к нескольким таблицам. При этом могут возникать различные проблемы, связанные как с прерыванием операций, так и со случаями параллельной работы нескольких пользователей с одними и теми же сущностями.

>В объектной базе данных xxx прикладная сущность описывается одним классом, а действие производится над объектом класса. В xxx сервер контролирует целостность сущности — объекта — при операциях с ним. Можно говорить о том, что механизм транзакций, который обеспечивает целостность БД, реализован на уровне сервера, не требуя дополнительного управления на уровне программиста. При этом механизма транзакций, привычного специалистам по работе с SQL СУБД — нет.

### Целостность объектной БД — это целостность объектов, которая контролируются сервером. Для полноценной работы этого механизма необходимо построение правильной структуры данных.

У программистов, которые ранее работали только с табличными реляционными БД, возникает проблема при переходе на объектную СУБД xxx. Они в процессе разработки пытаются реализовать структуру данных аналогичную той, которую они применяют в РСУБД. И при работе с этой структурой у них возникает потребность в транзакционности. Данная проблема решается формированием правильной структуры данных и идеологией построения приложений.

## Контроль uid ссылок

Еще одно понятие, которое присуще идеологии реляционных табличных БД. Рассмотрим простой пример.

[![](http://csc.xxx.org/wp-content/uploads/2018/07/sch.jpg "")](http://csc.xxx.org/wp-content/uploads/2018/07/sch.jpg "")

В таблице «Прайс-лист» информация о продавце товара дана только в виде кода продавца \(uid\). А все остальное — в таблице «Продавцы». Если нужно получить прайс-лист, в который будет вставлена информация о продавце, выполняется SQL запрос, которые объединит данные таблиц «Прайс-лист» и «Продавцы».

Подобная реализация в xxx возможна, но обычно не применяется. В xxx в прайс-листе атрибут, связанный с продавцом, будет включать в себя необходимое описание этого продавца \(как минимум — название, может быть адрес, телефон, а может — полные реквизиты\). При этом можно установить связь этого атрибута с классом «Продавцы», и в этой связи будет информация по uid продавца. Но поведение этой связи сложное и настраивается в зависимости от прикладной задачи. Связь бывает статической или динамической, можно настроить, какая часть информации из объекта «Продавец» будет импортироваться.

Контроль ссылочной целостности в реляционных БД подразумевает следующее. Если мы попытаемся удалить запись в таблице «Продавцы», на которую ссылается запись в таблице «Прайс-лист», то SQL эту операцию заблокирует. Этот контроль нам необходим, так как данные о продавце есть только в записи таблицы «Продавцы». Но в xxx все не так. Данные о продавце есть в объекте «Прайс-лист». И в случае удаления объекта из класса «Продавцы» данные этого продавца останутся. Поэтому контроля ссылочной целостности в том виде, как это реализовано в табличных БД, в xxx нет.

### Еще раз отметим, если мы захотим, то сможем с помощью XQuery получить результат как при выполнении SQL запроса.

this is a \:smile\: :smile: emoji   
Other emoji:

1	:+1:	   
-1	:-1:	   
100	:100:	   
1234	:1234:	   
1st_place_medal	:1st_place_medal:	   
2nd_place_medal	:2nd_place_medal:	   
3rd_place_medal	:3rd_place_medal:	   
8ball	:8ball:	   
a	:a:	   
ab	:ab:	   
abc	:abc:	   
abcd	:abcd:	   
accept	:accept:
...	