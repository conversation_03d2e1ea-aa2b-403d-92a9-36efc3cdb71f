import { FxElement, html, css } from '/fx.js';
import { $styles } from './ftmp.x.js';
import '../button/button.js';
import '../checkbox/checkbox.js';
import '../property-grid/property-grid.js';
import '../table/table.js';
import '../tree/tree.js';
import '../splitter/splitter.js';

export class FxFtmp extends FxElement {

    static get properties() {
        return {
            item: { type: Object }
        }
    }
    get flat() { return FX.flatItems(this.item.fields[0]) }

    static styles = $styles.ftmp

    render() {
        return html`
            <div class="form horizontal flex w100 h100 relative" @click=${this.formClick}>
                <fx-tree id="ftmp-tree" .item=${this.item.fields[0]} style="min-width: 240px;  width: 240px;"></fx-tree>
                <fx-splitter color="gray" vertical size="1"></fx-splitter>
                <div class="vertical flex h100 relative">
                    <div class="w100 brb horizontal mh36 p8 center no-flex ${this.label || 'Label' ? '' : 'hidden'} ${this.doc?.labelClass || ''}" style=${this.doc?.labelStyle || ''}>${this.label || 'Label'}</div>
                    <fx-ftmp-field class="w100 h100 relative" .selectedField=${this.selectedField} .main=${this} .field=${this.item.fields[0]} style="overflow-y: auto"></fx-ftmp-field>
                </div>
            </div>
    `
    }

    firstUpdated() {
        super.firstUpdated();
        setTimeout(() => {
            this.isReady = true;
            this.style.opacity = 1;
            this.$update();
            // console.log(this.item, this.flat)
        }, 100)
    }
}
customElements.define('fx-ftmp', FxFtmp);

customElements.define('fx-ftmp-field', class FxFtmpField extends FxElement {
    static styles = css`
        :host {
            display: flex;
            /* height: 100%; */
            position: relative;
            flex-wrap: wrap;
        }
        .selected {
            outline: 4px solid violet;
            border-radius: 4px;
            /* background: lightyellow; */
            outline-offset: -4px;
            z-index: 99;
        }
    `

    _isRow(i, doc) {
        return html`
            <div class="horizontal w100 wrap ${this.isSelected(doc)}">
                ${i.items?.length ? html`  
                    ${i.items.map(c => {
            const docc = this.item?.$flatFields?.[c._id]?.doc;
            return html`
                            <fx-ftmp-container .main=${this.main} .selectedField=${this.selectedField} class="${this.main?.doc?.fieldsClass || ''} ${docc?.fieldClass || ''} ${this.isSelected(docc)}" .field=${c} style="${this.main?.doc?.fieldsStyle || ''} ${docc?.fieldStyle || ''}"></fx-ftmp-container>
                        `
        })}
                ` : html`      
                    <div class="w100 ${i.items?.length ? 'min-w100' : ''} ${doc?.fieldClass || ''} ${this.isSelected(doc)}" style="${this.main?.doc?.fieldsStyle || ''} ${doc?.fieldStyle || ''}"></div>
                `}
            </div>
        `
    }
    _isTabs(i, doc) {
        return html`
            <div class="vertical w100 relative ${doc?.fieldClass || ''}" style="${this.main?.doc?.fieldsStyle || ''} ${doc?.fieldStyle || ''}">
                <div class="horizontal w100 brb mt4 sticky ${this.isSelected(i)}" style="z-index: 1; top:0; background: #eee;">
                    ${(i.items || []).filter(d => !d._deleted).map((t, tidx) => {
            const doct = this.item?.$flatFields?.[t._id]?.doc;
            return html`
                            <div class="m2 p4 br pointer ${this.isSelected(t)}" style="background: white; color: #333; border-bottom: 0; margin-bottom: 0; border-radius: 4px 4px 0 0; background: ${tidx === (this.tidx || 0) ? 'lightyellow' : ''}; color: ${tidx === (this.tidx || 0) ? 'blue' : ''}" @click=${e => { this.tidx = tidx; this.$update(); }}>
                                ${t.label}
                            </div>
                        `
        })}
                </div>
                <div class="w100 vertical">
                    <fx-ftmp-field .main=${this.main} class="ml16" .field=${i.items[this.tidx || 0]} .selectedField=${this.selectedField}></fx-ftmp-field>
                </div>
            </div>
        `
    }
    _isLabel(i, doc) {
        return html`
            <div class="w100 ${i.items?.length ? 'min-w100' : ''} ${doc?.fieldClass || ''} ${this.isSelected(doc)}" style="${this.main?.doc?.fieldsStyle || ''} ${doc?.fieldStyle || ''}">${i.label}</div>
        `
    }
    _isIcon(i, doc) {
        return html`
            <fx-icon url=${doc?.iconName || ''} size=${doc?.iconSize || 24} class="${doc?.fieldClass || ''} ${this.isSelected(doc)}" style="${this.main?.doc?.fieldsStyle || ''} ${doc?.fieldStyle || ''}" an="btn" @click=${e => this._fieldClick(doc)} fill=${doc.iconColor || ''} svg=${doc?.iconSVG || ''} scale=${doc?.iconScale || ''} title=${doc?.iconTitle || ''}></fx-icon>
        `
    }
    _isTable(i, doc) {
        this._tableData ||= {};
        this._tableData[doc.ulid] ||= this.tableData(doc);
        let data = this._tableData?.[doc.ulid];
        return html`
            <div class="horizontal relative w100">  
                <fx-table .id=${doc.tableID} .data=${data} class="w100 ${i.items?.length ? 'min-w100' : ''} ${doc?.fieldClass || ''} ${this.isSelected(doc)}" style="${this.main?.doc?.fieldsStyle || ''} ${doc?.fieldStyle || ''}">${i.label}></fx-table>
            </div>
        `
    }
    _isInput(i, doc) {
        return html`
            <fx-ftmp-container  .selectedField=${this.selectedField} .main=${this.main} class="${i.items?.length ? 'min-w100' : ''} ${this.main?.doc?.fieldsClass || ''} ${doc?.fieldClass || ''} ${this.isSelected(doc)}" .field=${i} style="${this.main?.doc?.fieldsStyle || ''} ${doc?.fieldStyle || ''}">
            </fx-ftmp-container>
    `
    }

    render() {
        return html`
            ${this.field?.items?.map(i => {
            const doc = i; // this.main?.flat?.[i._id]?.doc;
            return i._deleted ? html`` : html` 
                    ${doc?.fieldIs === 'row' ? this._isRow(i, doc)
                    : doc?.fieldIs === 'tabs' ? this._isTabs(i, doc)
                        : doc?.fieldIs === 'label' ? this._isLabel(i, doc)
                            : doc?.fieldIs === 'icon' ? this._isIcon(i, doc)
                                : doc?.fieldIs === 'table' ? this._isTable(i, doc)
                                    : this._isInput(i, doc)
                }
                `
        })}
        `
    }

    static properties = {
        main: { type: Object },
        item: { type: Object, local: true },
        field: { type: Object },
        selectedField: { type: Object },
        useSelected: { type: Boolean, default: false, local: true },
        action: { type: Object, global: true },
    }

    // get item() { return $00?.bsSelected }

    firstUpdated() {
        super.firstUpdated();
        setTimeout(() => {
            this.isReady = true;
            this.$update();
        }, 100);
    }
    isSelected(doc) {
        if (this.useSelected)
            return this.selectedField?.ulid === doc?.ulid ? 'selected' : ''
        return false;
    }
    tableData(doc) {
        let d;
        try {
            d = JSON.parse(doc?.tableData || '{}')
        } catch (error) {
            console.log(error);
        }
        d ||= {};
        return d;
    }

    _fieldClick(doc) {
        let act = doc.iconAction,
            item = this.item,
            table,
            docTable,
            data;
        if (act) {
            table = this.$qs('#' + doc.iconTableID);
            docTable = Object.values(item.$flatFields || {}).find(i => i.doc.tableID === doc.iconTableID)?.doc;
            data = this._tableData[docTable.ulid];
            // console.log(table);
            if (!table || !docTable)
                return;
        } else
            return;
        const acts = {
            'addRowTable': () => {
                data.rows ||= [];
                data.rows.push({});
                this.action = { id: docTable.tableID, fn: '_setRows', scrollToEnd: true };
                docTable.tableData = JSON.stringify(data);
            },
            'deleteRowTable': () => {
                // docTable.tableData = '';
                this.action = { id: docTable.tableID, fn: '_deleteRow' };
                data.rows = table._rows;
                this.async(() => {
                    docTable.tableData = JSON.stringify(data);
                }, 100)
            },
            'clearTable': () => {
                data.rows = [];
                docTable.tableData = JSON.stringify(data);
            },
        }
        acts[act] && acts[act]();
        this.$update();
    }
})

customElements.define('fx-ftmp-container', class FxFtmpContainerr extends FxElement {
    static get styles() {
        return css`
            :host {
                display: flex;
                flex-direction: column;
                /* box-shadow: -1px -1px 0 0 lightgray; */
                position: relative;
                flex: 1;
                min-width: 240px;
            }
            .row {
                min-height: 32px;
            }
            .show {
                visibility: visible;
            }
            .hide {
                visibility: hidden;
            }
            .fieldset {
                border-radius: 4px;
            }
        `;
    }
    render() {
        return html`

            <fieldset class="fieldset br p0 m6 ${this.main?.doc?.fieldsetsClass || ''} ${this.doc?.fieldsetClass || ''}" style="${this.main?.doc?.fieldsetsStyle || ''} ${this.doc?.fieldsetStyle || ''}">
                <legend>${this.field?.label}</legend>
                <div class="row horizontal align w100 h100 m0 p0">
                    <fx-button class="${this.hasChildren ? 'show' : 'hide'}" name="cb-chevron-right" toggledClass="right90" .toggled="${this.field.expanded}" size="18" back="transparent" border=0 @click=${this._toggleExpand}></fx-button>
                    <input type=${this.doc?.fieldType || 'text'} class="w100 h100 inp fm ${this.main?.doc?.inputsClass || ''} ${this.doc?.inputClass || ''}" style="${this.main?.doc?.inputsStyle || ''} ${this.doc?.inputStyle || ''}" @change=${this._change}>
                </div>
            </fieldset>
            ${this.hasChildren && this.field.expanded ? html`
                <fx-ftmp-field .main=${this.main} class="ml16" .field=${this.field} .selectedField=${this.selectedField}></fx-ftmp-field>
            ` : html``}
        `
    }

    static get properties() {
        return {
            main: { type: Object },
            item: { type: Object, local: true },
            field: { type: Object },
            selectedField: { type: Object }
        }
    }

    // get item() { return $00?.bsSelected }
    get doc() { return this.item?.$flatFields?.[this.field._id]?.doc }
    get hasChildren() { return this.field?.items?.length }

    _toggleExpand(e) {
        this.field.expanded = e.target.toggled;
        this.$update();
    }

    _change(e) {
        console.log($00.form.item._id, this.field._id, e)
    }
})
