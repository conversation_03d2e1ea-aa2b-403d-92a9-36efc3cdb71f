{"version": 3, "file": "ext-shapes.js", "sources": ["../../../../src/editor/extensions/ext-shapes/ext-shapes.js", "../../../../src/editor/extensions/ext-shapes/locale/en.js", "../../../../src/editor/extensions/ext-shapes/locale/fr.js", "../../../../src/editor/extensions/ext-shapes/locale/tr.js", "../../../../src/editor/extensions/ext-shapes/locale/zh-CN.js"], "sourcesContent": ["/**\n * @file ext-shapes.js\n *\n * @license MIT\n *\n * @copyright 2010 <PERSON>, 2010 <PERSON>\n *\n */\nconst name = 'shapes'\n\nconst loadExtensionTranslation = async function (svgEditor) {\n  let translationModule\n  const lang = svgEditor.configObj.pref('lang')\n  try {\n    translationModule = await import(`./locale/${lang}.js`)\n  } catch (_error) {\n    console.warn(`Missing translation (${lang}) for ${name} - using 'en'`)\n    translationModule = await import('./locale/en.js')\n  }\n  svgEditor.i18next.addResourceBundle(lang, name, translationModule.default)\n}\n\nexport default {\n  name,\n  async init () {\n    const svgEditor = this\n    const canv = svgEditor.svgCanvas\n    const { $id, $click } = canv\n    const svgroot = canv.getSvgRoot()\n    let lastBBox = {}\n    await loadExtensionTranslation(svgEditor)\n\n    const modeId = 'shapelib'\n    const startClientPos = {}\n\n    let curShape\n    let startX\n    let startY\n\n    return {\n      callback () {\n        if ($id('tool_shapelib') === null) {\n          const extPath = svgEditor.configObj.curConfig.extPath\n          const buttonTemplate = `\n          <se-explorerbutton id=\"tool_shapelib\" title=\"${svgEditor.i18next.t(`${name}:buttons.0.title`)}\" lib=\"${extPath}/ext-shapes/shapelib/\"\n          src=\"shapelib.svg\"></se-explorerbutton>\n          `\n          canv.insertChildAtIndex($id('tools_left'), buttonTemplate, 9)\n          $click($id('tool_shapelib'), () => {\n            if (this.leftPanel.updateLeftPanel('tool_shapelib')) {\n              canv.setMode(modeId)\n            }\n          })\n        }\n      },\n      mouseDown (opts) {\n        const mode = canv.getMode()\n        if (mode !== modeId) { return undefined }\n\n        const currentD = document.getElementById('tool_shapelib').dataset.draw\n        startX = opts.start_x\n        const x = startX\n        startY = opts.start_y\n        const y = startY\n        const curStyle = canv.getStyle()\n\n        startClientPos.x = opts.event.clientX\n        startClientPos.y = opts.event.clientY\n\n        curShape = canv.addSVGElementsFromJson({\n          element: 'path',\n          curStyles: true,\n          attr: {\n            d: currentD,\n            id: canv.getNextId(),\n            opacity: curStyle.opacity / 2,\n            style: 'pointer-events:none'\n          }\n        })\n\n        curShape.setAttribute('transform', 'translate(' + x + ',' + y + ') scale(0.005) translate(' + -x + ',' + -y + ')')\n\n        canv.recalculateDimensions(curShape)\n\n        lastBBox = curShape.getBBox()\n\n        return {\n          started: true\n        }\n      },\n      mouseMove (opts) {\n        const mode = canv.getMode()\n        if (mode !== modeId) { return }\n\n        const zoom = canv.getZoom()\n        const evt = opts.event\n\n        const x = opts.mouse_x / zoom\n        const y = opts.mouse_y / zoom\n\n        const tlist = curShape.transform.baseVal\n        const box = curShape.getBBox()\n        const left = box.x; const top = box.y\n\n        const newbox = {\n          x: Math.min(startX, x),\n          y: Math.min(startY, y),\n          width: Math.abs(x - startX),\n          height: Math.abs(y - startY)\n        }\n\n        let sx = (newbox.width / lastBBox.width) || 1\n        let sy = (newbox.height / lastBBox.height) || 1\n\n        // Not perfect, but mostly works...\n        let tx = 0\n        if (x < startX) {\n          tx = lastBBox.width\n        }\n        let ty = 0\n        if (y < startY) {\n          ty = lastBBox.height\n        }\n\n        // update the transform list with translate,scale,translate\n        const translateOrigin = svgroot.createSVGTransform()\n        const scale = svgroot.createSVGTransform()\n        const translateBack = svgroot.createSVGTransform()\n\n        translateOrigin.setTranslate(-(left + tx), -(top + ty))\n        if (!evt.shiftKey) {\n          const max = Math.min(Math.abs(sx), Math.abs(sy))\n\n          sx = max * (sx < 0 ? -1 : 1)\n          sy = max * (sy < 0 ? -1 : 1)\n        }\n        scale.setScale(sx, sy)\n\n        translateBack.setTranslate(left + tx, top + ty)\n        tlist.appendItem(translateBack)\n        tlist.appendItem(scale)\n        tlist.appendItem(translateOrigin)\n\n        canv.recalculateDimensions(curShape)\n\n        lastBBox = curShape.getBBox()\n      },\n      mouseUp (opts) {\n        const mode = canv.getMode()\n        if (mode !== modeId) { return undefined }\n\n        const keepObject = (opts.event.clientX !== startClientPos.x && opts.event.clientY !== startClientPos.y)\n\n        return {\n          keep: keepObject,\n          element: curShape,\n          started: false\n        }\n      }\n    }\n  }\n}\n", "export default {\n  loading: 'Loading...',\n  categories: {\n    basic: 'Basic',\n    object: 'Objects',\n    symbol: 'Symbols',\n    arrow: 'Arrows',\n    flowchart: 'Flowchart',\n    animal: 'Animals',\n    game: 'Cards & Chess',\n    dialog_balloon: 'Dialog balloons',\n    electronics: 'Electronics',\n    math: 'Mathematical',\n    music: 'Music',\n    misc: 'Miscellaneous',\n    raphael_1: 'raphaeljs.com set 1',\n    raphael_2: 'raphaeljs.com set 2'\n  },\n  buttons: [\n    {\n      title: 'Shape library'\n    }\n  ]\n}\n", "export default {\n  loading: 'Chargement...',\n  categories: {\n    basic: 'Basique',\n    object: 'Objets',\n    symbol: 'Symboles',\n    arrow: 'Flèches',\n    flowchart: 'Flowchart',\n    animal: 'Animaux',\n    game: 'Cartes & Echecs',\n    dialog_balloon: 'Dialog balloons',\n    electronics: 'Electronique',\n    math: 'Mathematiques',\n    music: 'Musique',\n    misc: 'Divers',\n    raphael_1: 'raphaeljs.com set 1',\n    raphael_2: 'raphaeljs.com set 2'\n  },\n  buttons: [\n    {\n      title: \"Bibliothèque d'images\"\n    }\n  ]\n}\n", "export default {\n  loading: 'Yü<PERSON><PERSON>yor...',\n  categories: {\n    basic: '<PERSON><PERSON>',\n    object: '<PERSON><PERSON><PERSON><PERSON>',\n    symbol: 'Semboller',\n    arrow: '<PERSON><PERSON>',\n    flowchart: '<PERSON><PERSON><PERSON><PERSON> Şemaları',\n    animal: '<PERSON><PERSON><PERSON>',\n    game: 'Kartlar & Satranç',\n    dialog_balloon: '<PERSON>yalog baloncukları',\n    electronics: 'Elektronikler',\n    math: '<PERSON><PERSON>tiks<PERSON>',\n    music: '<PERSON><PERSON><PERSON><PERSON>',\n    misc: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    raphael_1: 'raphaeljs.com set 1',\n    raphael_2: 'raphaeljs.com set 2'\n  },\n  buttons: [\n    {\n      title: '<PERSON><PERSON><PERSON>ü<PERSON>ü<PERSON>'\n    }\n  ]\n}\n", "export default {\n  loading: '正在加载...',\n  categories: {\n    basic: '基本',\n    object: '对象',\n    symbol: '符号',\n    arrow: '箭头',\n    flowchart: '工作流',\n    animal: '动物',\n    game: '棋牌',\n    dialog_balloon: '会话框',\n    electronics: '电子',\n    math: '数学',\n    music: '音乐',\n    misc: '其他',\n    raphael_1: 'raphaeljs.com 集合 1',\n    raphael_2: 'raphaeljs.com 集合 2'\n  },\n  buttons: [\n    {\n      title: '图元库'\n    }\n  ]\n}\n"], "names": ["name", "loadExtensionTranslation", "async", "svgEditor", "translationModule", "lang", "config<PERSON><PERSON><PERSON>", "pref", "__variableDynamicImportRuntime0__", "concat", "_error", "console", "warn", "Promise", "resolve", "then", "en$1", "i18next", "addResourceBundle", "default", "extShapes", "this", "canv", "svgCanvas", "$id", "$click", "svgroot", "getSvgRoot", "lastBBox", "modeId", "startClientPos", "curShape", "startX", "startY", "callback", "extPath", "curConfig", "buttonTemplate", "t", "insertChildAtIndex", "leftPanel", "updateLeftPanel", "setMode", "mouseDown", "opts", "getMode", "currentD", "document", "getElementById", "dataset", "draw", "start_x", "x", "start_y", "y", "curStyle", "getStyle", "event", "clientX", "clientY", "addSVGElementsFrom<PERSON>son", "element", "curStyles", "attr", "d", "id", "getNextId", "opacity", "style", "setAttribute", "recalculateDimensions", "getBBox", "started", "mouseMove", "zoom", "getZoom", "evt", "mouse_x", "mouse_y", "tlist", "transform", "baseVal", "box", "left", "top", "newbox", "Math", "min", "abs", "sx", "width", "sy", "height", "tx", "ty", "<PERSON><PERSON><PERSON><PERSON>", "createSVGTransform", "scale", "translateBack", "setTranslate", "shift<PERSON>ey", "max", "setScale", "appendItem", "mouseUp", "keep", "loading", "categories", "basic", "object", "symbol", "arrow", "flowchart", "animal", "game", "dialog_balloon", "electronics", "math", "music", "misc", "rap<PERSON>el_1", "<PERSON><PERSON><PERSON>_2", "buttons", "title"], "mappings": ";;;;;;;;AAQA,MAAMA,EAAO,SAEPC,yBAA2BC,eAAgBC,GAC/C,IAAIC,EACJ,MAAMC,EAAOF,EAAUG,UAAUC,KAAK,QACtC,IACEH,0hBAA0BI,CAAM,YAAAC,OAAaJ,EAA7C,QACA,MAAOK,GACPC,QAAQC,KAA6BP,wBAAAA,OAAAA,mBAAaL,EAAlD,kBACAI,QAA0BS,QAAAC,UAAAC,MAAA,WAAA,OAAAC,KAE5Bb,EAAUc,QAAQC,kBAAkBb,EAAML,EAAMI,EAAkBe,UAGpE,IAAeC,EAAA,CACbpB,KAAAA,EACAE,aACE,MAAMC,EAAYkB,KACZC,EAAOnB,EAAUoB,WACjBC,IAAEA,EAAFC,OAAOA,GAAWH,EAClBI,EAAUJ,EAAKK,aACrB,IAAIC,EAAW,SACT3B,yBAAyBE,GAE/B,MAAM0B,EAAS,WACTC,EAAiB,GAEvB,IAAIC,EACAC,EACAC,EAEJ,MAAO,CACLC,WACE,GAA6B,OAAzBV,EAAI,iBAA2B,CACjC,MAAMW,EAAUhC,EAAUG,UAAU8B,UAAUD,QACxCE,EAAc,4DAAA5B,OAC2BN,EAAUc,QAAQqB,EAAlB,GAAA7B,OAAuBT,EAAvB,qBAAwDmC,WAAAA,OAAAA,EADvG,yFAIAb,EAAKiB,mBAAmBf,EAAI,cAAea,EAAgB,GAC3DZ,EAAOD,EAAI,kBAAkB,KACvBH,KAAKmB,UAAUC,gBAAgB,kBACjCnB,EAAKoB,QAAQb,QAKrBc,UAAWC,GAET,GADatB,EAAKuB,YACLhB,EAAU,OAEvB,MAAMiB,EAAWC,SAASC,eAAe,iBAAiBC,QAAQC,KAClElB,EAASY,EAAKO,QACd,MAAMC,EAAIpB,EACVC,EAASW,EAAKS,QACd,MAAMC,EAAIrB,EACJsB,EAAWjC,EAAKkC,WAsBtB,OApBA1B,EAAesB,EAAIR,EAAKa,MAAMC,QAC9B5B,EAAewB,EAAIV,EAAKa,MAAME,QAE9B5B,EAAWT,EAAKsC,uBAAuB,CACrCC,QAAS,OACTC,WAAW,EACXC,KAAM,CACJC,EAAGlB,EACHmB,GAAI3C,EAAK4C,YACTC,QAASZ,EAASY,QAAU,EAC5BC,MAAO,yBAIXrC,EAASsC,aAAa,YAAa,aAAejB,EAAI,IAAME,EAAI,6BAA+BF,EAAI,KAAOE,EAAI,KAE9GhC,EAAKgD,sBAAsBvC,GAE3BH,EAAWG,EAASwC,UAEb,CACLC,SAAS,IAGbC,UAAW7B,GAET,GADatB,EAAKuB,YACLhB,EAAU,OAEvB,MAAM6C,EAAOpD,EAAKqD,UACZC,EAAMhC,EAAKa,MAEXL,EAAIR,EAAKiC,QAAUH,EACnBpB,EAAIV,EAAKkC,QAAUJ,EAEnBK,EAAQhD,EAASiD,UAAUC,QAC3BC,EAAMnD,EAASwC,UACfY,EAAOD,EAAI9B,EAASgC,EAAMF,EAAI5B,EAE9B+B,GACDC,KAAKC,IAAIvD,EAAQoB,GACjBkC,KAAKC,IAAItD,EAAQqB,GACbgC,KAAKE,IAAIpC,EAAIpB,IAHhBqD,EAIIC,KAAKE,IAAIlC,EAAIrB,GAGvB,IAAIwD,EAAMJ,EAAezD,EAAS8D,OAAU,EACxCC,EAAMN,EAAgBzD,EAASgE,QAAW,EAG1CC,EAAK,EACLzC,EAAIpB,IACN6D,EAAKjE,EAAS8D,OAEhB,IAAII,EAAK,EACLxC,EAAIrB,IACN6D,EAAKlE,EAASgE,QAIhB,MAAMG,EAAkBrE,EAAQsE,qBAC1BC,EAAQvE,EAAQsE,qBAChBE,EAAgBxE,EAAQsE,qBAG9B,GADAD,EAAgBI,eAAehB,EAAOU,KAAOT,EAAMU,KAC9ClB,EAAIwB,SAAU,CACjB,MAAMC,EAAMf,KAAKC,IAAID,KAAKE,IAAIC,GAAKH,KAAKE,IAAIG,IAE5CF,EAAKY,GAAOZ,EAAK,GAAK,EAAI,GAC1BE,EAAKU,GAAOV,EAAK,GAAK,EAAI,GAE5BM,EAAMK,SAASb,EAAIE,GAEnBO,EAAcC,aAAahB,EAAOU,EAAIT,EAAMU,GAC5Cf,EAAMwB,WAAWL,GACjBnB,EAAMwB,WAAWN,GACjBlB,EAAMwB,WAAWR,GAEjBzE,EAAKgD,sBAAsBvC,GAE3BH,EAAWG,EAASwC,WAEtBiC,QAAS5D,GAEP,GADatB,EAAKuB,YACLhB,EAAU,OAIvB,MAAO,CACL4E,KAHkB7D,EAAKa,MAAMC,UAAY5B,EAAesB,GAAKR,EAAKa,MAAME,UAAY7B,EAAewB,EAInGO,QAAS9B,EACTyC,SAAS,+CC5JJ,CACbkC,QAAS,aACTC,WAAY,CACVC,MAAO,QACPC,OAAQ,UACRC,OAAQ,UACRC,MAAO,SACPC,UAAW,YACXC,OAAQ,UACRC,KAAM,gBACNC,eAAgB,kBAChBC,YAAa,cACbC,KAAM,eACNC,MAAO,QACPC,KAAM,gBACNC,UAAW,sBACXC,UAAW,uBAEbC,QAAS,CACP,CACEC,MAAO,6DCpBE,CACbjB,QAAS,gBACTC,WAAY,CACVC,MAAO,UACPC,OAAQ,SACRC,OAAQ,WACRC,MAAO,UACPC,UAAW,YACXC,OAAQ,UACRC,KAAM,kBACNC,eAAgB,kBAChBC,YAAa,eACbC,KAAM,gBACNC,MAAO,UACPC,KAAM,SACNC,UAAW,sBACXC,UAAW,uBAEbC,QAAS,CACP,CACEC,MAAO,qECpBE,CACbjB,QAAS,gBACTC,WAAY,CACVC,MAAO,QACPC,OAAQ,WACRC,OAAQ,YACRC,MAAO,QACPC,UAAW,gBACXC,OAAQ,YACRC,KAAM,oBACNC,eAAgB,uBAChBC,YAAa,gBACbC,KAAM,kBACNC,MAAO,QACPC,KAAM,YACNC,UAAW,sBACXC,UAAW,uBAEbC,QAAS,CACP,CACEC,MAAO,iECpBE,CACbjB,QAAS,UACTC,WAAY,CACVC,MAAO,KACPC,OAAQ,KACRC,OAAQ,KACRC,MAAO,KACPC,UAAW,MACXC,OAAQ,KACRC,KAAM,KACNC,eAAgB,MAChBC,YAAa,KACbC,KAAM,KACNC,MAAO,KACPC,KAAM,KACNC,UAAW,qBACXC,UAAW,sBAEbC,QAAS,CACP,CACEC,MAAO"}