{"version": 3, "file": "ext-storage.js", "sources": ["../../../../src/editor/extensions/ext-storage/storageDialog.js", "../../../../src/editor/extensions/ext-storage/ext-storage.js"], "sourcesContent": ["/* globals svgEditor */\nimport storageDialogHTML from './storageDialog.html'\n\nconst template = document.createElement('template')\ntemplate.innerHTML = storageDialogHTML\n/**\n * @class SeStorageDialog\n */\nexport class SeStorageDialog extends HTMLElement {\n  /**\n    * @function constructor\n    */\n  constructor () {\n    super()\n    // create the shadowDom and insert the template\n    this._shadowRoot = this.attachShadow({ mode: 'open' })\n    this._shadowRoot.append(template.content.cloneNode(true))\n    this.$dialog = this._shadowRoot.querySelector('#dialog_box')\n    this.$storage = this._shadowRoot.querySelector('#js-storage')\n    this.$okBtn = this._shadowRoot.querySelector('#storage_ok')\n    this.$cancelBtn = this._shadowRoot.querySelector('#storage_cancel')\n    this.$storageInput = this._shadowRoot.querySelector('#se-storage-pref')\n    this.$rememberInput = this._shadowRoot.querySelector('#se-remember')\n  }\n\n  /**\n   * @function init\n   * @param {any} name\n   * @returns {void}\n   */\n  init (i18next) {\n    this.setAttribute('common-ok', i18next.t('common.ok'))\n    this.setAttribute('common-cancel', i18next.t('common.cancel'))\n    this.setAttribute('notify-editor_pref_msg', i18next.t('notification.editorPreferencesMsg'))\n    this.setAttribute('properties-prefs_and_content', i18next.t('properties.prefs_and_content'))\n    this.setAttribute('properties-prefs_only', i18next.t('properties.prefs_only'))\n    this.setAttribute('properties-no_prefs_or_content', i18next.t('properties.no_prefs_or_content'))\n    this.setAttribute('tools-remember_this_choice', i18next.t('tools.remember_this_choice'))\n    this.setAttribute('tools-remember_this_choice_title', i18next.t('tools.remember_this_choice_title'))\n  }\n\n  /**\n   * @function observedAttributes\n   * @returns {any} observed\n   */\n  static get observedAttributes () {\n    return ['dialog', 'storage', 'common-ok', 'common-cancel', 'notify-editor_pref_msg', 'properties-prefs_and_content', 'tools-remember_this_choice', 'tools-remember_this_choice_title', 'properties-prefs_only', 'properties-no_prefs_or_content']\n  }\n\n  /**\n   * @function attributeChangedCallback\n   * @param {string} name\n   * @param {string} oldValue\n   * @param {string} newValue\n   * @returns {void}\n   */\n  attributeChangedCallback (name, oldValue, newValue) {\n    let node\n    switch (name) {\n      case 'dialog':\n        if (newValue === 'open') {\n          this.$dialog.open()\n        } else {\n          this.$dialog.close()\n        }\n        break\n      case 'storage':\n        if (newValue === 'true') {\n          this.$storageInput.options[0].disabled = false\n        } else {\n          this.$storageInput.options[0].disabled = true\n        }\n        break\n      case 'common-ok':\n        this.$okBtn.textContent = newValue\n        break\n      case 'common-cancel':\n        this.$cancelBtn.textContent = newValue\n        break\n      case 'notify-editor_pref_msg':\n        node = this._shadowRoot.querySelector('#notificationNote')\n        node.textContent = newValue\n        break\n      case 'properties-prefs_and_content':\n        node = this._shadowRoot.querySelector('#prefsAndContent')\n        node.textContent = newValue\n        break\n      case 'properties-prefs_only':\n        node = this._shadowRoot.querySelector('#prefsOnly')\n        node.textContent = newValue\n        break\n      case 'properties-no_prefs_or_content':\n        node = this._shadowRoot.querySelector('#noPrefsOrContent')\n        node.textContent = newValue\n        break\n      case 'tools-remember_this_choice':\n        node = this._shadowRoot.querySelector('#se-remember-title')\n        node.prepend(newValue)\n        break\n      case 'tools-remember_this_choice_title':\n        node = this._shadowRoot.querySelector('#se-remember-title')\n        node.setAttribute('title', newValue)\n        break\n      default:\n        // super.attributeChangedCallback(name, oldValue, newValue);\n        break\n    }\n  }\n\n  /**\n   * @function get\n   * @returns {any}\n   */\n  get dialog () {\n    return this.getAttribute('dialog')\n  }\n\n  /**\n   * @function set\n   * @returns {void}\n   */\n  set dialog (value) {\n    this.setAttribute('dialog', value)\n  }\n\n  /**\n   * @function connectedCallback\n   * @returns {void}\n   */\n  connectedCallback () {\n    const onSubmitHandler = (e, action) => {\n      const triggerEvent = new CustomEvent('change',\n        {\n          detail: {\n            trigger: action,\n            select: this.$storageInput.value,\n            checkbox: this.$rememberInput.checked\n          }\n        })\n      this.dispatchEvent(triggerEvent)\n    }\n    svgEditor.$click(this.$okBtn, (evt) => onSubmitHandler(evt, 'ok'))\n    svgEditor.$click(this.$cancelBtn, (evt) => onSubmitHandler(evt, 'cancel'))\n  }\n}\n\n// Register\ncustomElements.define('se-storage-dialog', SeStorageDialog)\n", "/**\n * @file ext-storage.js\n *\n * This extension allows automatic saving of the SVG canvas contents upon\n *  page unload (which can later be automatically retrieved upon future\n *  editor loads).\n *\n *  The functionality was originally part of the SVG Editor, but moved to a\n *  separate extension to make the setting behavior optional, and adapted\n *  to inform the user of its setting of local data.\n *\n * @license MIT\n *\n * @copyright 2010 <PERSON>\n * @todo Revisit on whether to use `svgEditor.pref` over directly setting\n * `curConfig` in all extensions for a more public API (not only for `extPath`\n * and `imagePath`, but other currently used config in the extensions)\n * @todo We might provide control of storage settings through the UI besides the\n *   initial (or URL-forced) dialog. *\n */\nimport './storageDialog.js'\n\n/**\n * Expire the storage cookie.\n * @returns {void}\n */\nconst removeStoragePrefCookie = () => {\n  expireCookie('svgeditstore')\n}\n/**\n * Set the cookie to expire.\n * @param {string} cookie\n * @returns {void}\n */\nconst expireCookie = cookie => {\n  document.cookie =\n    encodeURIComponent(cookie) + '=; expires=Thu, 01 Jan 1970 00:00:00 GMT'\n}\n\n/**\n * Replace `storagePrompt` parameter within URL.\n * @param {string} val\n * @returns {void}\n * @todo Replace the string manipulation with `searchParams.set`\n */\nconst replaceStoragePrompt = val => {\n  val = val ? 'storagePrompt=' + val : ''\n  const loc = top.location // Allow this to work with the embedded editor as well\n  if (loc.href.includes('storagePrompt=')) {\n    loc.href = loc.href.replace(/([&?])storagePrompt=[^&]*(&?)/, function (\n      n0,\n      n1,\n      amp\n    ) {\n      return (val ? n1 : '') + val + (!val && amp ? n1 : amp || '')\n    })\n  } else {\n    loc.href += (loc.href.includes('?') ? '&' : '?') + val\n  }\n}\n\nexport default {\n  name: 'storage',\n  init () {\n    const svgEditor = this\n    const { svgCanvas, storage } = svgEditor\n\n    // We could empty any already-set data for users when they decline storage,\n    //  but it would be a risk for users who wanted to store but accidentally\n    // said \"no\"; instead, we'll let those who already set it, delete it themselves;\n    // to change, set the \"emptyStorageOnDecline\" config setting to true\n    // in svgedit-config-iife.js/svgedit-config-es.js.\n    const {\n      // When the code in svg-editor.js prevents local storage on load per\n      //  user request, we also prevent storing on unload here so as to\n      //  avoid third-party sites making XSRF requests or providing links\n      // which would cause the user's local storage not to load and then\n      // upon page unload (such as the user closing the window), the storage\n      //  would thereby be set with an empty value, erasing any of the\n      // user's prior work. To change this behavior so that no use of storage\n      // or adding of new storage takes place regardless of settings, set\n      // the \"noStorageOnLoad\" config setting to true in svgedit-config-*.js.\n      noStorageOnLoad,\n      forceStorage,\n      canvasName\n    } = svgEditor.configObj.curConfig\n\n    // LOAD STORAGE CONTENT IF ANY\n    if (\n      storage && // Cookies do not have enough available memory to hold large documents\n      (forceStorage ||\n        (!noStorageOnLoad &&\n          /(?:^|;\\s*)svgeditstore=prefsAndContent/.test(document.cookie)))\n    ) {\n      const key = 'svgedit-' + canvasName\n      const cached = storage.getItem(key)\n      if (cached) {\n        svgEditor.loadFromString(cached)\n        const name = storage.getItem(`title-${key}`) ?? 'untitled.svg'\n        svgEditor.topPanel.updateTitle(name)\n        svgEditor.layersPanel.populateLayers()\n      }\n    }\n\n    // storageDialog added to DOM\n    const storageBox = document.createElement('se-storage-dialog')\n    storageBox.setAttribute('id', 'se-storage-dialog')\n    svgEditor.$container.append(storageBox)\n    storageBox.init(svgEditor.i18next)\n\n    // manage the change in the storageDialog\n\n    storageBox.addEventListener('change', e => {\n      storageBox.setAttribute('dialog', 'close')\n      if (e?.detail?.trigger === 'ok') {\n        if (e?.detail?.select !== 'noPrefsOrContent') {\n          const storagePrompt = new URL(top.location).searchParams.get(\n            'storagePrompt'\n          )\n          document.cookie =\n            'svgeditstore=' +\n            encodeURIComponent(e.detail.select) +\n            '; expires=Fri, 31 Dec 9999 23:59:59 GMT'\n          if (storagePrompt === 'true' && e?.detail?.checkbox) {\n            replaceStoragePrompt()\n            return\n          }\n        } else {\n          removeStoragePrefCookie()\n          if (\n            svgEditor.configObj.curConfig.emptyStorageOnDecline &&\n            e?.detail?.checkbox\n          ) {\n            setSvgContentStorage('')\n            Object.keys(svgEditor.curPrefs).forEach(name => {\n              name = 'svg-edit-' + name\n              if (svgEditor.storage) {\n                svgEditor.storage.removeItem(name)\n              }\n              expireCookie(name)\n            })\n          }\n          if (e?.detail?.select && e?.detail?.checkbox) {\n            replaceStoragePrompt('false')\n            return\n          }\n        }\n      } else if (e?.detail?.trigger === 'cancel') {\n        removeStoragePrefCookie()\n      }\n      setupBeforeUnloadListener()\n      svgEditor.storagePromptState = 'closed'\n      svgEditor.updateCanvas(true)\n    })\n\n    /**\n     * Sets SVG content as a string with \"svgedit-\" and the current\n     *   canvas name as namespace.\n     * @param {string} svgString\n     * @returns {void}\n     */\n    const setSvgContentStorage = svgString => {\n      const name = `svgedit-${svgEditor.configObj.curConfig.canvasName}`\n      if (!svgString) {\n        storage.removeItem(name)\n        storage.removeItem(`${name}-title`)\n      } else {\n        storage.setItem(name, svgString)\n        storage.setItem(`title-${name}`, svgEditor.title)\n      }\n    }\n\n    /**\n     * Listen for unloading: If and only if opted in by the user, set the content\n     *   document and preferences into storage:\n     * 1. Prevent save warnings (since we're automatically saving unsaved\n     *       content into storage)\n     * 2. Use localStorage to set SVG contents (potentially too large to allow in cookies)\n     * 3. Use localStorage (where available) or cookies to set preferences.\n     * @returns {void}\n     */\n    const setupBeforeUnloadListener = () => {\n      window.addEventListener('beforeunload', function () {\n        // Don't save anything unless the user opted in to storage\n        if (\n          !/(?:^|;\\s*)svgeditstore=(?:prefsAndContent|prefsOnly)/.test(\n            document.cookie\n          )\n        ) {\n          return\n        }\n        if (/(?:^|;\\s*)svgeditstore=prefsAndContent/.test(document.cookie)) {\n          setSvgContentStorage(svgCanvas.getSvgString())\n        }\n\n        svgEditor.setConfig({ no_save_warning: true }) // No need for explicit saving at all once storage is on\n\n        const { curPrefs } = svgEditor.configObj\n\n        Object.entries(curPrefs).forEach(([key, val]) => {\n          const store = val !== undefined\n          key = 'svg-edit-' + key\n          if (!store) {\n            return\n          }\n          if (storage) {\n            storage.setItem(key, val)\n          } else if (window.widget) {\n            window.widget.setPreferenceForKey(val, key)\n          } else {\n            val = encodeURIComponent(val)\n            document.cookie =\n              encodeURIComponent(key) +\n              '=' +\n              val +\n              '; expires=Fri, 31 Dec 9999 23:59:59 GMT'\n          }\n        })\n      })\n    }\n\n    let loaded = false\n    return {\n      name: 'storage',\n      callback () {\n        const storagePrompt = new URL(top.location).searchParams.get(\n          'storagePrompt'\n        )\n        // No need to run this one-time dialog again just because the user\n        //   changes the language\n        if (loaded) {\n          return\n        }\n        loaded = true\n\n        // Note that the following can load even if \"noStorageOnLoad\" is\n        //   set to false; to avoid any chance of storage, avoid this\n        //   extension! (and to avoid using any prior storage, set the\n        //   config option \"noStorageOnLoad\" to true).\n        if (\n          !forceStorage &&\n          // If the URL has been explicitly set to always prompt the\n          //  user (e.g., so one can be pointed to a URL where one\n          // can alter one's settings, say to prevent future storage)...\n          (storagePrompt === 'true' ||\n            // ...or...if the URL at least doesn't explicitly prevent a\n            //  storage prompt (as we use for users who\n            // don't want to set cookies at all but who don't want\n            // continual prompts about it)...\n            (storagePrompt !== 'false' &&\n              // ...and this user hasn't previously indicated a desire for storage\n              !/(?:^|;\\s*)svgeditstore=(?:prefsAndContent|prefsOnly)/.test(\n                document.cookie\n              )))\n          // ...then show the storage prompt.\n        ) {\n          const options = Boolean(storage)\n          // Open select-with-checkbox dialog\n          // From svg-editor.js\n          svgEditor.storagePromptState = 'waiting'\n          const $storageDialog = document.getElementById('se-storage-dialog')\n          $storageDialog.setAttribute('dialog', 'open')\n          $storageDialog.setAttribute('storage', options)\n        } else if (!noStorageOnLoad || forceStorage) {\n          setupBeforeUnloadListener()\n        }\n      }\n    }\n  }\n}\n"], "names": ["template", "document", "createElement", "innerHTML", "SeStorageDialog", "HTMLElement", "constructor", "super", "this", "_shadowRoot", "attachShadow", "mode", "append", "content", "cloneNode", "$dialog", "querySelector", "$storage", "$okBtn", "$cancelBtn", "$storageInput", "$rememberInput", "init", "i18next", "setAttribute", "t", "observedAttributes", "attributeChangedCallback", "name", "oldValue", "newValue", "node", "open", "close", "options", "disabled", "textContent", "prepend", "dialog", "getAttribute", "value", "connectedCallback", "onSubmitHandler", "e", "action", "triggerEvent", "CustomEvent", "detail", "trigger", "select", "checkbox", "checked", "dispatchEvent", "svgEditor", "$click", "evt", "customElements", "define", "removeStoragePrefCookie", "expire<PERSON><PERSON><PERSON>", "cookie", "encodeURIComponent", "replaceStoragePrompt", "val", "loc", "top", "location", "href", "includes", "replace", "n0", "n1", "amp", "extStorage", "svgCanvas", "storage", "noStorageOnLoad", "forceStorage", "canvasName", "config<PERSON><PERSON><PERSON>", "curConfig", "test", "key", "cached", "getItem", "_storage$getItem", "loadFromString", "topPanel", "updateTitle", "layersPanel", "populateLayers", "storageBox", "$container", "addEventListener", "_e$detail", "_e$detail7", "_e$detail2", "_e$detail3", "storagePrompt", "URL", "searchParams", "get", "_e$detail4", "_e$detail5", "_e$detail6", "emptyStorageOnDecline", "setSvgContentStorage", "Object", "keys", "curPrefs", "for<PERSON>ach", "removeItem", "setupBeforeUnloadListener", "storagePromptState", "updateCanvas", "svgString", "setItem", "concat", "title", "window", "getSvgString", "setConfig", "no_save_warning", "entries", "_ref", "undefined", "widget", "setPreferenceForKey", "loaded", "callback", "Boolean", "$storageDialog", "getElementById"], "mappings": "AAGA,MAAMA,EAAWC,SAASC,cAAc,YACxCF,EAASG,43DAIF,MAAMC,wBAAwBC,YAInCC,cACEC,QAEAC,KAAKC,YAAcD,KAAKE,aAAa,CAAEC,KAAM,SAC7CH,KAAKC,YAAYG,OAAOZ,EAASa,QAAQC,WAAU,IACnDN,KAAKO,QAAUP,KAAKC,YAAYO,cAAc,eAC9CR,KAAKS,SAAWT,KAAKC,YAAYO,cAAc,eAC/CR,KAAKU,OAASV,KAAKC,YAAYO,cAAc,eAC7CR,KAAKW,WAAaX,KAAKC,YAAYO,cAAc,mBACjDR,KAAKY,cAAgBZ,KAAKC,YAAYO,cAAc,oBACpDR,KAAKa,eAAiBb,KAAKC,YAAYO,cAAc,gBAQvDM,KAAMC,GACJf,KAAKgB,aAAa,YAAaD,EAAQE,EAAE,cACzCjB,KAAKgB,aAAa,gBAAiBD,EAAQE,EAAE,kBAC7CjB,KAAKgB,aAAa,yBAA0BD,EAAQE,EAAE,sCACtDjB,KAAKgB,aAAa,+BAAgCD,EAAQE,EAAE,iCAC5DjB,KAAKgB,aAAa,wBAAyBD,EAAQE,EAAE,0BACrDjB,KAAKgB,aAAa,iCAAkCD,EAAQE,EAAE,mCAC9DjB,KAAKgB,aAAa,6BAA8BD,EAAQE,EAAE,+BAC1DjB,KAAKgB,aAAa,mCAAoCD,EAAQE,EAAE,qCAOvDC,gCACT,MAAO,CAAC,SAAU,UAAW,YAAa,gBAAiB,yBAA0B,+BAAgC,6BAA8B,mCAAoC,wBAAyB,kCAUlNC,yBAA0BC,EAAMC,EAAUC,GACxC,IAAIC,EACJ,OAAQH,GACN,IAAK,SACc,SAAbE,EACFtB,KAAKO,QAAQiB,OAEbxB,KAAKO,QAAQkB,QAEf,MACF,IAAK,UAEDzB,KAAKY,cAAcc,QAAQ,GAAGC,SADf,SAAbL,EAKJ,MACF,IAAK,YACHtB,KAAKU,OAAOkB,YAAcN,EAC1B,MACF,IAAK,gBACHtB,KAAKW,WAAWiB,YAAcN,EAC9B,MACF,IAAK,yBACHC,EAAOvB,KAAKC,YAAYO,cAAc,qBACtCe,EAAKK,YAAcN,EACnB,MACF,IAAK,+BACHC,EAAOvB,KAAKC,YAAYO,cAAc,oBACtCe,EAAKK,YAAcN,EACnB,MACF,IAAK,wBACHC,EAAOvB,KAAKC,YAAYO,cAAc,cACtCe,EAAKK,YAAcN,EACnB,MACF,IAAK,iCACHC,EAAOvB,KAAKC,YAAYO,cAAc,qBACtCe,EAAKK,YAAcN,EACnB,MACF,IAAK,6BACHC,EAAOvB,KAAKC,YAAYO,cAAc,sBACtCe,EAAKM,QAAQP,GACb,MACF,IAAK,mCACHC,EAAOvB,KAAKC,YAAYO,cAAc,sBACtCe,EAAKP,aAAa,QAASM,IAY7BQ,aACF,OAAO9B,KAAK+B,aAAa,UAOvBD,WAAQE,GACVhC,KAAKgB,aAAa,SAAUgB,GAO9BC,oBACE,MAAMC,gBAAkB,CAACC,EAAGC,KAC1B,MAAMC,EAAe,IAAIC,YAAY,SACnC,CACEC,OAAQ,CACNC,QAASJ,EACTK,OAAQzC,KAAKY,cAAcoB,MAC3BU,SAAU1C,KAAKa,eAAe8B,WAGpC3C,KAAK4C,cAAcP,IAErBQ,UAAUC,OAAO9C,KAAKU,QAASqC,GAAQb,gBAAgBa,EAAK,QAC5DF,UAAUC,OAAO9C,KAAKW,YAAaoC,GAAQb,gBAAgBa,EAAK,aAKpEC,eAAeC,OAAO,oBAAqBrD;;;;;;;;;;;;;;;;;;;;;ACzH3C,MAAMsD,wBAA0B,KAC9BC,aAAa,iBAOTA,aAAeC,IACnB3D,SAAS2D,OACPC,mBAAmBD,GAAU,4CAS3BE,qBAAuBC,IAC3BA,EAAMA,EAAM,iBAAmBA,EAAM,GACrC,MAAMC,EAAMC,IAAIC,SACZF,EAAIG,KAAKC,SAAS,kBACpBJ,EAAIG,KAAOH,EAAIG,KAAKE,QAAQ,iCAAiC,SAC3DC,EACAC,EACAC,GAEA,OAAQT,EAAMQ,EAAK,IAAMR,IAAQA,GAAOS,EAAMD,EAAKC,GAAO,OAG5DR,EAAIG,OAASH,EAAIG,KAAKC,SAAS,KAAO,IAAM,KAAOL,GAIvD,IAAeU,EAAA,CACb7C,KAAM,UACNN,OACE,MAAM+B,EAAY7C,MACZkE,UAAEA,EAAFC,QAAaA,GAAYtB,GAOzBuB,gBAUJA,EAVIC,aAWJA,EAXIC,WAYJA,GACEzB,EAAU0B,UAAUC,UAGxB,GACEL,IACCE,IACGD,GACA,yCAAyCK,KAAKhF,SAAS2D,SAC3D,CACA,MAAMsB,EAAM,WAAaJ,EACnBK,EAASR,EAAQS,QAAQF,GAC/B,GAAIC,EAAQ,CAAA,IAAAE,EACVhC,EAAUiC,eAAeH,GACzB,MAAMvD,EAAI,UAAG+C,EAAQS,QAAiBF,SAAAA,OAAAA,WAA5B,IAAAG,EAAAA,EAAsC,eAChDhC,EAAUkC,SAASC,YAAY5D,GAC/ByB,EAAUoC,YAAYC,kBAK1B,MAAMC,EAAa1F,SAASC,cAAc,qBAC1CyF,EAAWnE,aAAa,KAAM,qBAC9B6B,EAAUuC,WAAWhF,OAAO+E,GAC5BA,EAAWrE,KAAK+B,EAAU9B,SAI1BoE,EAAWE,iBAAiB,UAAUlD,IAAK,IAAAmD,EAAAC,EAERC,EAAjC,GADAL,EAAWnE,aAAa,SAAU,SACP,QAAvBmB,MAAAA,GAAA,QAAAmD,EAAAnD,EAAGI,cAAH,IAAA+C,OAAA,EAAAA,EAAW9C,SACb,GAA0B,sBAAtBL,MAAAA,GAAA,QAAAqD,EAAArD,EAAGI,cAAH,IAAAiD,OAAA,EAAAA,EAAW/C,QAA+B,CAAA,IAAAgD,EAC5C,MAAMC,EAAgB,IAAIC,IAAIlC,IAAIC,UAAUkC,aAAaC,IACvD,iBAMF,GAJApG,SAAS2D,OACP,gBACAC,mBAAmBlB,EAAEI,OAAOE,QAC5B,0CACoB,SAAlBiD,GAAAA,MAA4BvD,GAAA,QAAAA,EAAAA,EAAGI,cAAH,IAAAkD,GAAAA,EAAW/C,SAEzC,YADAY,2BAGG,CAAA,IAAAwC,EAAAC,EAAAC,EAeL,GAdA9C,0BAEEL,EAAU0B,UAAUC,UAAUyB,uBAA9BpD,MACAV,GAAA,UAAAA,EAAGI,cAAH,IAAAuD,GAAAA,EAAWpD,WAEXwD,qBAAqB,IACrBC,OAAOC,KAAKvD,EAAUwD,UAAUC,SAAQlF,IACtCA,EAAO,YAAcA,EACjByB,EAAUsB,SACZtB,EAAUsB,QAAQoC,WAAWnF,GAE/B+B,aAAa/B,OAGbe,MAAAA,GAAWM,QAAXN,EAAAA,EAAGI,cAAQE,IAAAA,GAAAA,EAAAA,QAAXN,MAAqBA,GAArB,QAAqBA,EAAAA,EAAGI,cAAxB,IAAAyD,GAAqBA,EAAWtD,SAElC,YADAY,qBAAqB,aAIO,YAAvBnB,MAAAA,GAAA,QAAAA,EAAAA,EAAGI,cAAH,IAAAgD,OAAApD,EAAAoD,EAAW/C,UACpBU,0BAEFsD,4BACA3D,EAAU4D,mBAAqB,SAC/B5D,EAAU6D,cAAa,MASzB,MAAMR,qBAAuBS,IAC3B,MAAMvF,oBAAkByB,EAAU0B,UAAUC,UAAUF,YACjDqC,GAIHxC,EAAQyC,QAAQxF,EAAMuF,GACtBxC,EAAQyC,QAAR,SAAAC,OAAyBzF,GAAQyB,EAAUiE,SAJ3C3C,EAAQoC,WAAWnF,GACnB+C,EAAQoC,WAAR,GAAAM,OAAsBzF,EAAtB,aAgBEoF,0BAA4B,KAChCO,OAAO1B,iBAAiB,gBAAgB,WAEtC,IACG,uDAAuDZ,KACtDhF,SAAS2D,QAGX,OAEE,yCAAyCqB,KAAKhF,SAAS2D,SACzD8C,qBAAqBhC,EAAU8C,gBAGjCnE,EAAUoE,UAAU,CAAEC,iBAAiB,IAEvC,MAAMb,SAAEA,GAAaxD,EAAU0B,UAE/B4B,OAAOgB,QAAQd,GAAUC,SAAQc,IAAgB,IAAd1C,EAAKnB,GAAS6D,EAE/C1C,EAAM,YAAcA,OADE2C,IAAR9D,IAKVY,EACFA,EAAQyC,QAAQlC,EAAKnB,GACZwD,OAAOO,OAChBP,OAAOO,OAAOC,oBAAoBhE,EAAKmB,IAEvCnB,EAAMF,mBAAmBE,GACzB9D,SAAS2D,OACPC,mBAAmBqB,GACnB,IACAnB,EACA,mDAMV,IAAIiE,GAAS,EACb,MAAO,CACLpG,KAAM,UACNqG,WACE,MAAM/B,EAAgB,IAAIC,IAAIlC,IAAIC,UAAUkC,aAAaC,IACvD,iBAIF,IAAI2B,EASJ,GANAA,GAAS,EAONnD,GAIkB,SAAlBqB,IAKoB,UAAlBA,GAEE,uDAAuDjB,KACtDhF,SAAS2D,SAWLgB,IAAmBC,GAC7BmC,gCATA,CACA,MAAM9E,EAAUgG,QAAQvD,GAGxBtB,EAAU4D,mBAAqB,UAC/B,MAAMkB,EAAiBlI,SAASmI,eAAe,qBAC/CD,EAAe3G,aAAa,SAAU,QACtC2G,EAAe3G,aAAa,UAAWU"}