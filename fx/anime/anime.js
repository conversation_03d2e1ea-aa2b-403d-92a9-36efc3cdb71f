import { FxElement } from '/fx.js';

customElements.define('fx-anime', class FxAnime extends FxElement {
    static properties = {
        animemax: { type: Number, default: 15 },
        animecolor: { type: Array, default: [''] }, // ['#b9dff5', '#7fc7ff', '#7fb1ff', '#7fc7ff', '#b9dff5', 'blue', 'white'] },
        animetype: { type: Array, default: ['Times'] },
        animeletter: { type: String, default: '*' }, // '*', '&#10052;' 🌷😍💖💕💝🧙🏻‍♀️👼🏻🏄🏼‍♂️🧘🏻🧑🏻‍❤️‍💋‍🧑🏼🌼🌻🌹🌺🌿💐🌸⭐🌟⛄🎄🎉✨🇷🇺🥞🦋🐲🥂🧞‍♂️🌧
        sinkspeed: { type: Number, default: .3 }, // .3 ... 2.0
        animemaxsize: { type: Number, default: 50 },
        animeminsize: { type: Number, default: 20 },
        animeingzone: { type: Number, default: 1 }, // 1, 2, 3, 4 - full, left, center, right
        showAnime: { type: Boolean, default: false, notify: true }
    }

    async firstUpdated() {
        super.firstUpdated();
        if (this.showAnime) await this.init();
        this.isReady = true;
    }
    'showAnime-changed'() {
        if (this.isReady) this.init();
    }
    async init() {
        if (this.showAnime) {
            animemax = this.animemax;
            animecolor = this.animecolor;
            animetype = this.animetype;
            animeletter = this.animeletter;
            sinkspeed = this.sinkspeed;
            animemaxsize = this.animemaxsize;
            animeminsize = this.animeminsize;
            animeingzone = this.animeingzone;
            initanime();
        } else {
            clearTimeout(document.animeTimer);
            await new Promise((r) => setTimeout(r, 100));
            const animes = document.querySelectorAll('span.anime');
            animes.forEach(s => {
                s.remove();
            })
            anime = new Array();
            x_mv = new Array();
            crds = new Array();
            lftrght = new Array();
        }
    }
})

let animemax, animecolor, animetype, animeletter, sinkspeed, animemaxsize, animeminsize, animeingzone;
let anime = new Array();
let marginbottom;
let marginright;
let x_mv = new Array();
let crds = new Array();
let lftrght = new Array();
const randommaker = (range) => {
    return Math.floor(range * Math.random());
}
const initanime = () => {
    for (let i = 0; i <= animemax; i++) {
        document.body.insertAdjacentHTML("beforeend", "<span class='anime' id='s" + i + "' style='pointer-events:none;touch-action: none;user-select:none;position:fixed;top:-" + animemaxsize + "'>" + animeletter + "</span>")
    }
    marginbottom = document.documentElement.clientHeight + 50
    marginright = document.body.clientWidth - 15
    let animesizerange = animemaxsize - animeminsize
    for (let i = 0; i <= animemax; i++) {
        crds[i] = 0;
        lftrght[i] = Math.random() * 15;
        x_mv[i] = 0.03 + Math.random() / 10;
        anime[i] = document.getElementById("s" + i)
        anime[i].style.fontFamily = animetype[randommaker(animetype.length)]
        anime[i].size = randommaker(animesizerange) + animeminsize
        anime[i].style.fontSize = anime[i].size + 'px';
        anime[i].style.color = animecolor[randommaker(animecolor.length)]
        anime[i].style.zIndex = 1000
        anime[i].sink = sinkspeed * anime[i].size / 5
        if (animeingzone == 1) { anime[i].posx = randommaker(marginright - anime[i].size) }
        if (animeingzone == 2) { anime[i].posx = randommaker(marginright / 2 - anime[i].size) }
        if (animeingzone == 3) { anime[i].posx = randommaker(marginright / 2 - anime[i].size) + marginright / 4 }
        if (animeingzone == 4) { anime[i].posx = randommaker(marginright / 2 - anime[i].size) + marginright / 2 }
        anime[i].posy = randommaker(2 * marginbottom - marginbottom - 2 * anime[i].size)
        anime[i].style.left = anime[i].posx + 'px';
        anime[i].style.top = anime[i].posy + 'px';
    }
    moveanime();
}
const moveanime = () => {
    for (let i = 0; i <= animemax; i++) {
        crds[i] += x_mv[i];
        anime[i].posy += anime[i].sink
        anime[i].style.left = anime[i].posx + lftrght[i] * Math.sin(crds[i]) + 'px';
        anime[i].style.top = anime[i].posy + 'px';

        if (anime[i].posy >= marginbottom - 2 * anime[i].size || parseInt(anime[i].style.left) > (marginright - 3 * lftrght[i])) {
            if (animeingzone == 1) { anime[i].posx = randommaker(marginright - anime[i].size) }
            if (animeingzone == 2) { anime[i].posx = randommaker(marginright / 2 - anime[i].size) }
            if (animeingzone == 3) { anime[i].posx = randommaker(marginright / 2 - anime[i].size) + marginright / 4 }
            if (animeingzone == 4) { anime[i].posx = randommaker(marginright / 2 - anime[i].size) + marginright / 2 }
            anime[i].posy = 0
        }
    }
    document.animeTimer = setTimeout(moveanime, 50)
}
