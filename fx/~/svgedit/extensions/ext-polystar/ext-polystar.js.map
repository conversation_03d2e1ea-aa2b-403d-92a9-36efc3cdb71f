{"version": 3, "file": "ext-polystar.js", "sources": ["../../../../src/editor/extensions/ext-polystar/ext-polystar.js", "../../../../src/editor/extensions/ext-polystar/locale/en.js", "../../../../src/editor/extensions/ext-polystar/locale/fr.js", "../../../../src/editor/extensions/ext-polystar/locale/tr.js", "../../../../src/editor/extensions/ext-polystar/locale/zh-CN.js"], "sourcesContent": ["/**\n * @file ext-polystar.js\n *\n *\n * @copyright 2010 CloudCanvas, Inc. All rights reserved\n * @copyright 2021 Optimistik SAS, Inc. All rights reserved\n * @license MIT\n *\n */\n\nconst name = 'polystar'\n\nconst loadExtensionTranslation = async function (svgEditor) {\n  let translationModule\n  const lang = svgEditor.configObj.pref('lang')\n  try {\n    translationModule = await import(`./locale/${lang}.js`)\n  } catch (_error) {\n    console.warn(`Missing translation (${lang}) for ${name} - using 'en'`)\n    translationModule = await import('./locale/en.js')\n  }\n  svgEditor.i18next.addResourceBundle(lang, name, translationModule.default)\n}\n\nexport default {\n  name,\n  async init () {\n    const svgEditor = this\n    const { svgCanvas } = svgEditor\n    const { ChangeElementCommand } = svgCanvas.history\n    const addToHistory = (cmd) => { svgCanvas.undoMgr.addCommandToHistory(cmd) }\n    const { $id, $click } = svgCanvas\n    let selElems\n    let started\n    let newFO\n    await loadExtensionTranslation(svgEditor)\n\n    /**\n     * @param {boolean} on true=display\n     * @param {string} tool \"star\" or \"polygone\"\n     * @returns {void}\n     */\n    const showPanel = (on, tool) => {\n      if (on) {\n        $id(`${tool}_panel`).style.removeProperty('display')\n      } else {\n        $id(`${tool}_panel`).style.display = 'none'\n      }\n    }\n\n    /**\n     *\n     * @param {string} attr attribute to change\n     * @param {string|Float} val new value\n     * @returns {void}\n     */\n    const setAttr = (attr, val) => {\n      svgCanvas.changeSelectedAttribute(attr, val)\n      svgCanvas.call('changed', selElems)\n    }\n\n    /**\n     * @param {Float} n angle\n     * @return {Float} cotangeante\n     */\n    const cot = (n) => 1 / Math.tan(n)\n\n    /**\n     * @param {Float} n angle\n     * @returns {Float} sec\n     */\n    const sec = (n) => 1 / Math.cos(n)\n\n    return {\n      name: svgEditor.i18next.t(`${name}:name`),\n      // The callback should be used to load the DOM with the appropriate UI items\n      callback () {\n        // Add the button and its handler(s)\n        // Note: the star extension needs to be loaded before the polygon extension\n        const fbtitle = `${name}:title`\n        const titleStar = `${name}:buttons.0.title`\n        const titlePolygon = `${name}:buttons.1.title`\n        const buttonTemplate = `\n            <se-flyingbutton id=\"tools_polygon\" title=\"${fbtitle}\">\n              <se-button id=\"tool_star\" title=\"${titleStar}\" src=\"star.svg\">\n              </se-button>\n              <se-button id=\"tool_polygon\" title=\"${titlePolygon}\" src=\"polygon.svg\">\n              </se-button>\n            </se-flyingbutton>\n          `\n        svgCanvas.insertChildAtIndex($id('tools_left'), buttonTemplate, 10)\n        // handler\n        $click($id('tool_star'), () => {\n          if (this.leftPanel.updateLeftPanel('tool_star')) {\n            svgCanvas.setMode('star')\n            showPanel(true, 'star')\n            showPanel(false, 'polygon')\n          }\n        })\n        $click($id('tool_polygon'), () => {\n          if (this.leftPanel.updateLeftPanel('tool_polygon')) {\n            svgCanvas.setMode('polygon')\n            showPanel(true, 'polygon')\n            showPanel(false, 'star')\n          }\n        })\n        const label0 = `${name}:contextTools.0.label`\n        const title0 = `${name}:contextTools.0.title`\n        const label1 = `${name}:contextTools.1.label`\n        const title1 = `${name}:contextTools.1.title`\n        const label2 = `${name}:contextTools.2.label`\n        const title2 = `${name}:contextTools.2.title`\n        const label3 = `${name}:contextTools.3.label`\n        const title3 = `${name}:contextTools.3.title`\n        // Add the context panel and its handler(s)\n        const panelTemplate = document.createElement('template')\n        panelTemplate.innerHTML = `\n          <div id=\"star_panel\">\n            <se-spin-input id=\"starNumPoints\" label=\"${label0}\" min=1 step=1 value=5 title=\"${title0}\">\n            </se-spin-input>\n            <se-spin-input id=\"RadiusMultiplier\" label=\"${label1}\" min=1 step=2.5 value=3 title=\"${title1}\">\n            </se-spin-input>\n            <se-spin-input id=\"radialShift\" min=0 step=1 value=0 label=\"${label2}\" title=\"${title2}\">\n            </se-spin-input>\n          </div>\n          <div id=\"polygon_panel\">\n            <se-spin-input size=\"3\" id=\"polySides\" min=1 step=1 value=5 label=\"${label3}\" title=\"${title3}\">\n            </se-spin-input>\n          </div>\n        `\n        // add handlers for the panel\n        $id('tools_top').appendChild(panelTemplate.content.cloneNode(true))\n        // don't display the panels on start\n        showPanel(false, 'star')\n        showPanel(false, 'polygon')\n        $id('starNumPoints').addEventListener('change', (event) => {\n          setAttr('point', event.target.value)\n          const orient = 'point'\n          const point = event.target.value\n          let i = selElems.length\n          while (i--) {\n            const elem = selElems[i]\n            if (elem.hasAttribute('r')) {\n              const oldPoint = elem.getAttribute('point')\n              const oldPoints = elem.getAttribute('points')\n              const radialshift = elem.getAttribute('radialshift')\n              let xpos = 0\n              let ypos = 0\n              if (elem.points) {\n                const list = elem.points\n                const len = list.numberOfItems\n                for (let i = 0; i < len; ++i) {\n                  const pt = list.getItem(i)\n                  xpos += parseFloat(pt.x)\n                  ypos += parseFloat(pt.y)\n                }\n                const cx = xpos / len\n                const cy = ypos / len\n                const circumradius = Number(elem.getAttribute('r'))\n                const inradius = circumradius / elem.getAttribute('starRadiusMultiplier')\n\n                let polyPoints = ''\n                for (let s = 0; point >= s; s++) {\n                  let angle = 2.0 * Math.PI * (s / point)\n                  if (orient === 'point') {\n                    angle -= Math.PI / 2\n                  } else if (orient === 'edge') {\n                    angle = angle + Math.PI / point - Math.PI / 2\n                  }\n\n                  let x = circumradius * Math.cos(angle) + cx\n                  let y = circumradius * Math.sin(angle) + cy\n\n                  polyPoints += x + ',' + y + ' '\n\n                  if (!isNaN(inradius)) {\n                    angle = 2.0 * Math.PI * (s / point) + Math.PI / point\n                    if (orient === 'point') {\n                      angle -= Math.PI / 2\n                    } else if (orient === 'edge') {\n                      angle = angle + Math.PI / point - Math.PI / 2\n                    }\n                    angle += radialshift\n\n                    x = inradius * Math.cos(angle) + cx\n                    y = inradius * Math.sin(angle) + cy\n\n                    polyPoints += x + ',' + y + ' '\n                  }\n                }\n                elem.setAttribute('points', polyPoints)\n                addToHistory(new ChangeElementCommand(elem, { point: oldPoint, points: oldPoints }))\n              }\n            }\n          }\n        })\n        $id('RadiusMultiplier').addEventListener('change', (event) => {\n          setAttr('starRadiusMultiplier', event.target.value)\n        })\n        $id('radialShift').addEventListener('change', (event) => {\n          setAttr('radialshift', event.target.value)\n        })\n        $id('polySides').addEventListener('change', (event) => {\n          setAttr('sides', event.target.value)\n          const sides = event.target.value\n          let i = selElems.length\n          while (i--) {\n            const elem = selElems[i]\n            if (elem.hasAttribute('edge')) {\n              const oldSides = elem.getAttribute('sides')\n              const oldPoints = elem.getAttribute('points')\n              let xpos = 0\n              let ypos = 0\n              if (elem.points) {\n                const list = elem.points\n                const len = list.numberOfItems\n                for (let i = 0; i < len; ++i) {\n                  const pt = list.getItem(i)\n                  xpos += parseFloat(pt.x)\n                  ypos += parseFloat(pt.y)\n                }\n                const cx = xpos / len\n                const cy = ypos / len\n                const edg = elem.getAttribute('edge')\n                const inradius = (edg / 2) * cot(Math.PI / sides)\n                const circumradius = inradius * sec(Math.PI / sides)\n                let points = ''\n                for (let s = 0; sides >= s; s++) {\n                  const angle = (2.0 * Math.PI * s) / sides\n                  const x = circumradius * Math.cos(angle) + cx\n                  const y = circumradius * Math.sin(angle) + cy\n                  points += x + ',' + y + ' '\n                }\n                elem.setAttribute('points', points)\n                addToHistory(new ChangeElementCommand(elem, { sides: oldSides, points: oldPoints }))\n              }\n            }\n          }\n        })\n      },\n      mouseDown (opts) {\n        if (svgCanvas.getMode() === 'star') {\n          const fill = svgCanvas.getColor('fill')\n          const stroke = svgCanvas.getColor('stroke')\n          const strokeWidth = svgCanvas.getStrokeWidth()\n          started = true\n          newFO = svgCanvas.addSVGElementsFromJson({\n            element: 'polygon',\n            attr: {\n              cx: opts.start_x,\n              cy: opts.start_y,\n              id: svgCanvas.getNextId(),\n              shape: 'star',\n              point: $id('starNumPoints').value,\n              r: 0,\n              radialshift: $id('radialShift').value,\n              r2: 0,\n              orient: 'point',\n              fill,\n              stroke,\n              'stroke-width': strokeWidth\n            }\n          })\n          return {\n            started: true\n          }\n        }\n        if (svgCanvas.getMode() === 'polygon') {\n          const fill = svgCanvas.getColor('fill')\n          const stroke = svgCanvas.getColor('stroke')\n          const strokeWidth = svgCanvas.getStrokeWidth()\n          started = true\n          newFO = svgCanvas.addSVGElementsFromJson({\n            element: 'polygon',\n            attr: {\n              cx: opts.start_x,\n              cy: opts.start_y,\n              id: svgCanvas.getNextId(),\n              shape: 'regularPoly',\n              sides: $id('polySides').value,\n              orient: 'x',\n              edge: 0,\n              fill,\n              stroke,\n              'stroke-width': strokeWidth\n            }\n          })\n\n          return {\n            started: true\n          }\n        }\n        return undefined\n      },\n      mouseMove (opts) {\n        if (!started) {\n          return undefined\n        }\n        if (svgCanvas.getMode() === 'star') {\n          const cx = Number(newFO.getAttribute('cx'))\n          const cy = Number(newFO.getAttribute('cy'))\n          const point = Number(newFO.getAttribute('point'))\n          const orient = newFO.getAttribute('orient')\n          const fill = newFO.getAttribute('fill')\n          const stroke = newFO.getAttribute('stroke')\n          const strokeWidth = Number(newFO.getAttribute('stroke-width'))\n          const radialshift = Number(newFO.getAttribute('radialshift'))\n\n          let x = opts.mouse_x\n          let y = opts.mouse_y\n\n          const circumradius =\n            Math.sqrt((x - cx) * (x - cx) + (y - cy) * (y - cy)) / 1.5\n          const RadiusMultiplier = document.getElementById('RadiusMultiplier').value\n          const inradius =\n            circumradius / RadiusMultiplier\n          newFO.setAttribute('r', circumradius)\n          newFO.setAttribute('r2', inradius)\n          newFO.setAttribute('starRadiusMultiplier', RadiusMultiplier)\n\n          let polyPoints = ''\n          for (let s = 0; point >= s; s++) {\n            let angle = 2.0 * Math.PI * (s / point)\n            if (orient === 'point') {\n              angle -= Math.PI / 2\n            } else if (orient === 'edge') {\n              angle = angle + Math.PI / point - Math.PI / 2\n            }\n\n            x = circumradius * Math.cos(angle) + cx\n            y = circumradius * Math.sin(angle) + cy\n\n            polyPoints += x + ',' + y + ' '\n\n            if (!isNaN(inradius)) {\n              angle = 2.0 * Math.PI * (s / point) + Math.PI / point\n              if (orient === 'point') {\n                angle -= Math.PI / 2\n              } else if (orient === 'edge') {\n                angle = angle + Math.PI / point - Math.PI / 2\n              }\n              angle += radialshift\n\n              x = inradius * Math.cos(angle) + cx\n              y = inradius * Math.sin(angle) + cy\n\n              polyPoints += x + ',' + y + ' '\n            }\n          }\n          newFO.setAttribute('points', polyPoints)\n          newFO.setAttribute('fill', fill)\n          newFO.setAttribute('stroke', stroke)\n          newFO.setAttribute('stroke-width', strokeWidth)\n          /* const shape = */ newFO.getAttribute('shape')\n\n          return {\n            started: true\n          }\n        }\n        if (svgCanvas.getMode() === 'polygon') {\n          const cx = Number(newFO.getAttribute('cx'))\n          const cy = Number(newFO.getAttribute('cy'))\n          const sides = Number(newFO.getAttribute('sides'))\n          // const orient = newFO.getAttribute('orient');\n          const fill = newFO.getAttribute('fill')\n          const stroke = newFO.getAttribute('stroke')\n          const strokeWidth = Number(newFO.getAttribute('stroke-width'))\n\n          let x = opts.mouse_x\n          let y = opts.mouse_y\n\n          const edg =\n            Math.sqrt((x - cx) * (x - cx) + (y - cy) * (y - cy)) / 1.5\n          newFO.setAttribute('edge', edg)\n\n          const inradius = (edg / 2) * cot(Math.PI / sides)\n          const circumradius = inradius * sec(Math.PI / sides)\n          let points = ''\n          for (let s = 0; sides >= s; s++) {\n            const angle = (2.0 * Math.PI * s) / sides\n            x = circumradius * Math.cos(angle) + cx\n            y = circumradius * Math.sin(angle) + cy\n\n            points += x + ',' + y + ' '\n          }\n\n          // const poly = newFO.createElementNS(NS.SVG, 'polygon');\n          newFO.setAttribute('points', points)\n          newFO.setAttribute('fill', fill)\n          newFO.setAttribute('stroke', stroke)\n          newFO.setAttribute('stroke-width', strokeWidth)\n          return {\n            started: true\n          }\n        }\n        return undefined\n      },\n      mouseUp () {\n        if (svgCanvas.getMode() === 'star') {\n          const r = newFO.getAttribute('r')\n          return {\n            keep: r !== '0',\n            element: newFO\n          }\n        }\n        if (svgCanvas.getMode() === 'polygon') {\n          const edge = newFO.getAttribute('edge')\n          const keep = edge !== '0'\n          // svgCanvas.addToSelection([newFO], true);\n          return {\n            keep,\n            element: newFO\n          }\n        }\n        return undefined\n      },\n      selectedChanged (opts) {\n        // Use this to update the current selected elements\n        selElems = opts.elems\n        let i = selElems.length\n        // Hide panels if nothing is selected\n        if (!i) {\n          showPanel(false, 'star')\n          showPanel(false, 'polygon')\n          return\n        }\n        while (i--) {\n          const elem = selElems[i]\n          if (elem?.getAttribute('shape') === 'star') {\n            if (opts.selectedElement && !opts.multiselected) {\n              $id('starNumPoints').value = elem.getAttribute('point')\n              $id('radialShift').value = elem.getAttribute('radialshift')\n              showPanel(true, 'star')\n            } else {\n              showPanel(false, 'star')\n            }\n          } else if (elem?.getAttribute('shape') === 'regularPoly') {\n            if (opts.selectedElement && !opts.multiselected) {\n              $id('polySides').value = elem.getAttribute('sides')\n              showPanel(true, 'polygon')\n            } else {\n              showPanel(false, 'polygon')\n            }\n          } else {\n            showPanel(false, 'star')\n            showPanel(false, 'polygon')\n          }\n        }\n      }\n    }\n  }\n}\n", "export default {\n  name: 'star',\n  title: 'Polygone/Star Tool',\n  buttons: [\n    {\n      title: 'Star Tool'\n    },\n    {\n      title: 'Polygon Tool'\n    }\n  ],\n  contextTools: [\n    {\n      title: 'Number of Sides',\n      label: 'points'\n    },\n    {\n      title: 'Pointiness',\n      label: 'Pointiness'\n    },\n    {\n      title: 'Twists the star',\n      label: 'Radial Shift'\n    },\n    {\n      title: 'Number of Sides',\n      label: 'sides'\n    }\n  ]\n}\n", "export default {\n  name: 'etoile',\n  title: 'Outil Polygone/Etoile',\n  buttons: [\n    {\n      title: 'Outil Etoile'\n    },\n    {\n      title: 'Outil Polygone'\n    }\n  ],\n  contextTools: [\n    {\n      title: 'Nombre de côtés',\n      label: 'points'\n    },\n    {\n      title: 'P<PERSON><PERSON>',\n      label: 'P<PERSON><PERSON>'\n    },\n    {\n      title: 'Torsion Etoile',\n      label: 'Décalage Radial'\n    },\n    {\n      title: 'Nombre de côtés',\n      label: 'côtés'\n    }\n  ]\n}\n", "export default {\n  name: 'yıld<PERSON><PERSON>',\n  title: 'Çokgen/Yıldız <PERSON>',\n  buttons: [\n    {\n      title: 'Yıld<PERSON><PERSON>'\n    },\n    {\n      title: '<PERSON><PERSON><PERSON> Aracı'\n    }\n  ],\n  contextTools: [\n    {\n      title: '<PERSON><PERSON>',\n      label: 'noktalar'\n    },\n    {\n      title: 'Sivril<PERSON>',\n      label: 'Sivril<PERSON>'\n    },\n    {\n      title: 'Yıld<PERSON><PERSON><PERSON> Kıvır',\n      label: 'Döngüsel Kaydırma'\n    },\n    {\n      title: '<PERSON><PERSON>',\n      label: 'kenarlar'\n    }\n  ]\n}\n", "export default {\n  name: '星形',\n  title: 'Polygone/Star Tool',\n  buttons: [\n    {\n      title: '星形工具'\n    },\n    {\n      title: '多边形工具'\n    }\n  ],\n  contextTools: [\n    {\n      title: '顶点',\n      label: '顶点'\n    },\n    {\n      title: '钝度',\n      label: '钝度'\n    },\n    {\n      title: '径向',\n      label: '径向'\n    },\n    {\n      title: '边数',\n      label: '边数'\n    }\n  ]\n}\n"], "names": ["name", "loadExtensionTranslation", "async", "svgEditor", "translationModule", "lang", "config<PERSON><PERSON><PERSON>", "pref", "__variableDynamicImportRuntime0__", "concat", "_error", "console", "warn", "Promise", "resolve", "then", "en$1", "i18next", "addResourceBundle", "default", "extPolystar", "this", "svgCanvas", "ChangeElementCommand", "history", "addToHistory", "cmd", "undoMgr", "addCommandToHistory", "$id", "$click", "sel<PERSON><PERSON><PERSON>", "started", "newFO", "showPanel", "on", "tool", "style", "removeProperty", "display", "setAttr", "attr", "val", "changeSelectedAttribute", "call", "cot", "n", "Math", "tan", "sec", "cos", "t", "callback", "fbtitle", "titleStar", "titlePolygon", "buttonTemplate", "insertChildAtIndex", "leftPanel", "updateLeftPanel", "setMode", "label0", "title0", "label1", "title1", "label2", "title2", "label3", "title3", "panelTemplate", "document", "createElement", "innerHTML", "append<PERSON><PERSON><PERSON>", "content", "cloneNode", "addEventListener", "event", "target", "value", "point", "i", "length", "elem", "hasAttribute", "oldPoint", "getAttribute", "oldPoints", "radialshift", "xpos", "ypos", "points", "list", "len", "numberOfItems", "pt", "getItem", "parseFloat", "x", "y", "cx", "cy", "circumradius", "Number", "inradius", "polyPoints", "s", "angle", "PI", "sin", "isNaN", "setAttribute", "sides", "oldSides", "mouseDown", "opts", "getMode", "fill", "getColor", "stroke", "strokeWidth", "getStrokeWidth", "addSVGElementsFrom<PERSON>son", "element", "start_x", "start_y", "id", "getNextId", "shape", "r", "r2", "orient", "edge", "mouseMove", "mouse_x", "mouse_y", "sqrt", "RadiusMultiplier", "getElementById", "edg", "mouseUp", "keep", "<PERSON><PERSON><PERSON><PERSON>", "elems", "selectedElement", "multiselected", "title", "buttons", "contextTools", "label"], "mappings": ";;;;;;;;;AAUA,MAAMA,EAAO,WAEPC,yBAA2BC,eAAgBC,GAC/C,IAAIC,EACJ,MAAMC,EAAOF,EAAUG,UAAUC,KAAK,QACtC,IACEH,0hBAA0BI,CAAM,YAAAC,OAAaJ,EAA7C,QACA,MAAOK,GACPC,QAAQC,KAA6BP,wBAAAA,OAAAA,mBAAaL,EAAlD,kBACAI,QAA0BS,QAAAC,UAAAC,MAAA,WAAA,OAAAC,KAE5Bb,EAAUc,QAAQC,kBAAkBb,EAAML,EAAMI,EAAkBe,UAGpE,IAAeC,EAAA,CACbpB,KAAAA,EACAE,aACE,MAAMC,EAAYkB,MACZC,UAAEA,GAAcnB,GAChBoB,qBAAEA,GAAyBD,EAAUE,QACrCC,aAAgBC,IAAUJ,EAAUK,QAAQC,oBAAoBF,KAChEG,IAAEA,EAAFC,OAAOA,GAAWR,EACxB,IAAIS,EACAC,EACAC,QACEhC,yBAAyBE,GAO/B,MAAM+B,UAAY,CAACC,EAAIC,KACjBD,EACFN,EAAG,GAAApB,OAAI2B,EAAP,WAAqBC,MAAMC,eAAe,WAE1CT,EAAG,GAAApB,OAAI2B,EAAP,WAAqBC,MAAME,QAAU,QAUnCC,QAAU,CAACC,EAAMC,KACrBpB,EAAUqB,wBAAwBF,EAAMC,GACxCpB,EAAUsB,KAAK,UAAWb,IAOtBc,IAAOC,GAAM,EAAIC,KAAKC,IAAIF,GAM1BG,IAAOH,GAAM,EAAIC,KAAKG,IAAIJ,GAEhC,MAAO,CACL9C,KAAMG,EAAUc,QAAQkC,EAAlB,GAAA1C,OAAuBT,EADxB,UAGLoD,WAGE,MAAMC,EAAarD,GAAAA,OAAAA,EAAnB,UACMsD,EAAetD,GAAAA,OAAAA,EAArB,oBACMuD,EAAkBvD,GAAAA,OAAAA,EAAxB,oBACMwD,EAC2CH,4DAAAA,OAAAA,gEACRC,EAFrB,qGAAA7C,OAIwB8C,EAJ5C,gGAQAjC,EAAUmC,mBAAmB5B,EAAI,cAAe2B,EAAgB,IAEhE1B,EAAOD,EAAI,cAAc,KACnBR,KAAKqC,UAAUC,gBAAgB,eACjCrC,EAAUsC,QAAQ,QAClB1B,WAAU,EAAM,QAChBA,WAAU,EAAO,eAGrBJ,EAAOD,EAAI,iBAAiB,KACtBR,KAAKqC,UAAUC,gBAAgB,kBACjCrC,EAAUsC,QAAQ,WAClB1B,WAAU,EAAM,WAChBA,WAAU,EAAO,YAGrB,MAAM2B,EAAY7D,GAAAA,OAAAA,EAAlB,yBACM8D,EAAY9D,GAAAA,OAAAA,EAAlB,yBACM+D,EAAY/D,GAAAA,OAAAA,EAAlB,yBACMgE,EAAYhE,GAAAA,OAAAA,EAAlB,yBACMiE,EAAYjE,GAAAA,OAAAA,EAAlB,yBACMkE,EAAYlE,GAAAA,OAAAA,EAAlB,yBACMmE,EAAYnE,GAAAA,OAAAA,EAAlB,yBACMoE,EAAM,GAAA3D,OAAMT,EAAN,yBAENqE,EAAgBC,SAASC,cAAc,YAC7CF,EAAcG,UAAd,2FAAA/D,OAE+CoD,EAAuCC,kCAAAA,OAAAA,uGAEpCC,EAJlD,oCAAAtD,OAI2FuD,EAEzBC,8GAAAA,OAAAA,sBAAkBC,EANpF,2KAAAzD,OAUyE0D,EAAkBC,aAAAA,OAAAA,kEAK3FvC,EAAI,aAAa4C,YAAYJ,EAAcK,QAAQC,WAAU,IAE7DzC,WAAU,EAAO,QACjBA,WAAU,EAAO,WACjBL,EAAI,iBAAiB+C,iBAAiB,UAAWC,IAC/CrC,QAAQ,QAASqC,EAAMC,OAAOC,OAE9B,MAAMC,EAAQH,EAAMC,OAAOC,MAC3B,IAAIE,EAAIlD,EAASmD,OACjB,KAAOD,KAAK,CACV,MAAME,EAAOpD,EAASkD,GACtB,GAAIE,EAAKC,aAAa,KAAM,CAC1B,MAAMC,EAAWF,EAAKG,aAAa,SAC7BC,EAAYJ,EAAKG,aAAa,UAC9BE,EAAcL,EAAKG,aAAa,eACtC,IAAIG,EAAO,EACPC,EAAO,EACX,GAAIP,EAAKQ,OAAQ,CACf,MAAMC,EAAOT,EAAKQ,OACZE,EAAMD,EAAKE,cACjB,IAAK,IAAIb,EAAI,EAAGA,EAAIY,IAAOZ,EAAG,CAC5B,MAAMc,EAAKH,EAAKI,QAAQf,GACxBQ,GAAQQ,WAAWF,EAAGG,GACtBR,GAAQO,WAAWF,EAAGI,GAExB,MAAMC,EAAKX,EAAOI,EACZQ,EAAKX,EAAOG,EACZS,EAAeC,OAAOpB,EAAKG,aAAa,MACxCkB,EAAWF,EAAenB,EAAKG,aAAa,wBAElD,IAAImB,EAAa,GACjB,IAAK,IAAIC,EAAI,EAAG1B,GAAS0B,EAAGA,IAAK,CAC/B,IAAIC,EAAQ,EAAM5D,KAAK6D,IAAMF,EAAI1B,GAE/B2B,GAAS5D,KAAK6D,GAAK,EAKrB,IAAIV,EAAII,EAAevD,KAAKG,IAAIyD,GAASP,EACrCD,EAAIG,EAAevD,KAAK8D,IAAIF,GAASN,EAEzCI,GAAcP,EAAI,IAAMC,EAAI,IAEvBW,MAAMN,KACTG,EAAQ,EAAM5D,KAAK6D,IAAMF,EAAI1B,GAASjC,KAAK6D,GAAK5B,EAE9C2B,GAAS5D,KAAK6D,GAAK,EAIrBD,GAASnB,EAETU,EAAIM,EAAWzD,KAAKG,IAAIyD,GAASP,EACjCD,EAAIK,EAAWzD,KAAK8D,IAAIF,GAASN,EAEjCI,GAAcP,EAAI,IAAMC,EAAI,KAGhChB,EAAK4B,aAAa,SAAUN,GAC5BhF,aAAa,IAAIF,EAAqB4D,EAAM,CAAEH,MAAOK,EAAUM,OAAQJ,WAK/E1D,EAAI,oBAAoB+C,iBAAiB,UAAWC,IAClDrC,QAAQ,uBAAwBqC,EAAMC,OAAOC,UAE/ClD,EAAI,eAAe+C,iBAAiB,UAAWC,IAC7CrC,QAAQ,cAAeqC,EAAMC,OAAOC,UAEtClD,EAAI,aAAa+C,iBAAiB,UAAWC,IAC3CrC,QAAQ,QAASqC,EAAMC,OAAOC,OAC9B,MAAMiC,EAAQnC,EAAMC,OAAOC,MAC3B,IAAIE,EAAIlD,EAASmD,OACjB,KAAOD,KAAK,CACV,MAAME,EAAOpD,EAASkD,GACtB,GAAIE,EAAKC,aAAa,QAAS,CAC7B,MAAM6B,EAAW9B,EAAKG,aAAa,SAC7BC,EAAYJ,EAAKG,aAAa,UACpC,IAAIG,EAAO,EACPC,EAAO,EACX,GAAIP,EAAKQ,OAAQ,CACf,MAAMC,EAAOT,EAAKQ,OACZE,EAAMD,EAAKE,cACjB,IAAK,IAAIb,EAAI,EAAGA,EAAIY,IAAOZ,EAAG,CAC5B,MAAMc,EAAKH,EAAKI,QAAQf,GACxBQ,GAAQQ,WAAWF,EAAGG,GACtBR,GAAQO,WAAWF,EAAGI,GAExB,MAAMC,EAAKX,EAAOI,EACZQ,EAAKX,EAAOG,EAGZS,EAFMnB,EAAKG,aAAa,QACN,EAAKzC,IAAIE,KAAK6D,GAAKI,GACX/D,IAAIF,KAAK6D,GAAKI,GAC9C,IAAIrB,EAAS,GACb,IAAK,IAAIe,EAAI,EAAGM,GAASN,EAAGA,IAAK,CAC/B,MAAMC,EAAS,EAAM5D,KAAK6D,GAAKF,EAAKM,EAGpCrB,GAFUW,EAAevD,KAAKG,IAAIyD,GAASP,EAE7B,KADJE,EAAevD,KAAK8D,IAAIF,GAASN,GACnB,IAE1BlB,EAAK4B,aAAa,SAAUpB,GAC5BlE,aAAa,IAAIF,EAAqB4D,EAAM,CAAE6B,MAAOC,EAAUtB,OAAQJ,YAMjF2B,UAAWC,GACT,GAA4B,SAAxB7F,EAAU8F,UAAsB,CAClC,MAAMC,EAAO/F,EAAUgG,SAAS,QAC1BC,EAASjG,EAAUgG,SAAS,UAC5BE,EAAclG,EAAUmG,iBAmB9B,OAlBAzF,GAAU,EACVC,EAAQX,EAAUoG,uBAAuB,CACvCC,QAAS,UACTlF,KAAM,CACJ2D,GAAIe,EAAKS,QACTvB,GAAIc,EAAKU,QACTC,GAAIxG,EAAUyG,YACdC,MAAO,OACPhD,MAAOnD,EAAI,iBAAiBkD,MAC5BkD,EAAG,EACHzC,YAAa3D,EAAI,eAAekD,MAChCmD,GAAI,EACJC,OAAQ,QACRd,KAAAA,EACAE,OAAAA,EACA,eAAgBC,KAGb,CACLxF,SAAS,GAGb,GAA4B,YAAxBV,EAAU8F,UAAyB,CACrC,MAAMC,EAAO/F,EAAUgG,SAAS,QAC1BC,EAASjG,EAAUgG,SAAS,UAC5BE,EAAclG,EAAUmG,iBAkB9B,OAjBAzF,GAAU,EACVC,EAAQX,EAAUoG,uBAAuB,CACvCC,QAAS,UACTlF,KAAM,CACJ2D,GAAIe,EAAKS,QACTvB,GAAIc,EAAKU,QACTC,GAAIxG,EAAUyG,YACdC,MAAO,cACPhB,MAAOnF,EAAI,aAAakD,MACxBoD,OAAQ,IACRC,KAAM,EACNf,KAAAA,EACAE,OAAAA,EACA,eAAgBC,KAIb,CACLxF,SAAS,KAKfqG,UAAWlB,GACT,GAAKnF,EAAL,CAGA,GAA4B,SAAxBV,EAAU8F,UAAsB,CAClC,MAAMhB,EAAKG,OAAOtE,EAAMqD,aAAa,OAC/Be,EAAKE,OAAOtE,EAAMqD,aAAa,OAC/BN,EAAQuB,OAAOtE,EAAMqD,aAAa,UAClC6C,EAASlG,EAAMqD,aAAa,UAC5B+B,EAAOpF,EAAMqD,aAAa,QAC1BiC,EAAStF,EAAMqD,aAAa,UAC5BkC,EAAcjB,OAAOtE,EAAMqD,aAAa,iBACxCE,EAAce,OAAOtE,EAAMqD,aAAa,gBAE9C,IAAIY,EAAIiB,EAAKmB,QACTnC,EAAIgB,EAAKoB,QAEb,MAAMjC,EACJvD,KAAKyF,MAAMtC,EAAIE,IAAOF,EAAIE,IAAOD,EAAIE,IAAOF,EAAIE,IAAO,IACnDoC,EAAmBnE,SAASoE,eAAe,oBAAoB3D,MAC/DyB,EACJF,EAAemC,EACjBxG,EAAM8E,aAAa,IAAKT,GACxBrE,EAAM8E,aAAa,KAAMP,GACzBvE,EAAM8E,aAAa,uBAAwB0B,GAE3C,IAAIhC,EAAa,GACjB,IAAK,IAAIC,EAAI,EAAG1B,GAAS0B,EAAGA,IAAK,CAC/B,IAAIC,EAAQ,EAAM5D,KAAK6D,IAAMF,EAAI1B,GAClB,UAAXmD,EACFxB,GAAS5D,KAAK6D,GAAK,EACC,SAAXuB,IACTxB,EAAQA,EAAQ5D,KAAK6D,GAAK5B,EAAQjC,KAAK6D,GAAK,GAG9CV,EAAII,EAAevD,KAAKG,IAAIyD,GAASP,EACrCD,EAAIG,EAAevD,KAAK8D,IAAIF,GAASN,EAErCI,GAAcP,EAAI,IAAMC,EAAI,IAEvBW,MAAMN,KACTG,EAAQ,EAAM5D,KAAK6D,IAAMF,EAAI1B,GAASjC,KAAK6D,GAAK5B,EACjC,UAAXmD,EACFxB,GAAS5D,KAAK6D,GAAK,EACC,SAAXuB,IACTxB,EAAQA,EAAQ5D,KAAK6D,GAAK5B,EAAQjC,KAAK6D,GAAK,GAE9CD,GAASnB,EAETU,EAAIM,EAAWzD,KAAKG,IAAIyD,GAASP,EACjCD,EAAIK,EAAWzD,KAAK8D,IAAIF,GAASN,EAEjCI,GAAcP,EAAI,IAAMC,EAAI,KAShC,OANAlE,EAAM8E,aAAa,SAAUN,GAC7BxE,EAAM8E,aAAa,OAAQM,GAC3BpF,EAAM8E,aAAa,SAAUQ,GAC7BtF,EAAM8E,aAAa,eAAgBS,GACfvF,EAAMqD,aAAa,SAEhC,CACLtD,SAAS,GAGb,GAA4B,YAAxBV,EAAU8F,UAAyB,CACrC,MAAMhB,EAAKG,OAAOtE,EAAMqD,aAAa,OAC/Be,EAAKE,OAAOtE,EAAMqD,aAAa,OAC/B0B,EAAQT,OAAOtE,EAAMqD,aAAa,UAElC+B,EAAOpF,EAAMqD,aAAa,QAC1BiC,EAAStF,EAAMqD,aAAa,UAC5BkC,EAAcjB,OAAOtE,EAAMqD,aAAa,iBAE9C,IAAIY,EAAIiB,EAAKmB,QACTnC,EAAIgB,EAAKoB,QAEb,MAAMI,EACJ5F,KAAKyF,MAAMtC,EAAIE,IAAOF,EAAIE,IAAOD,EAAIE,IAAOF,EAAIE,IAAO,IACzDpE,EAAM8E,aAAa,OAAQ4B,GAE3B,MACMrC,EADYqC,EAAM,EAAK9F,IAAIE,KAAK6D,GAAKI,GACX/D,IAAIF,KAAK6D,GAAKI,GAC9C,IAAIrB,EAAS,GACb,IAAK,IAAIe,EAAI,EAAGM,GAASN,EAAGA,IAAK,CAC/B,MAAMC,EAAS,EAAM5D,KAAK6D,GAAKF,EAAKM,EACpCd,EAAII,EAAevD,KAAKG,IAAIyD,GAASP,EACrCD,EAAIG,EAAevD,KAAK8D,IAAIF,GAASN,EAErCV,GAAUO,EAAI,IAAMC,EAAI,IAQ1B,OAJAlE,EAAM8E,aAAa,SAAUpB,GAC7B1D,EAAM8E,aAAa,OAAQM,GAC3BpF,EAAM8E,aAAa,SAAUQ,GAC7BtF,EAAM8E,aAAa,eAAgBS,GAC5B,CACLxF,SAAS,MAKf4G,UACE,GAA4B,SAAxBtH,EAAU8F,UAAsB,CAElC,MAAO,CACLyB,KAAY,MAFJ5G,EAAMqD,aAAa,KAG3BqC,QAAS1F,GAGb,GAA4B,YAAxBX,EAAU8F,UAAyB,CAIrC,MAAO,CACLyB,KAHoB,MADT5G,EAAMqD,aAAa,QAK9BqC,QAAS1F,KAKf6G,gBAAiB3B,GAEfpF,EAAWoF,EAAK4B,MAChB,IAAI9D,EAAIlD,EAASmD,OAEjB,IAAKD,EAGH,OAFA/C,WAAU,EAAO,aACjBA,WAAU,EAAO,WAGnB,KAAO+C,KAAK,CACV,MAAME,EAAOpD,EAASkD,GACc,UAAhCE,MAAAA,OAAA,EAAAA,EAAMG,aAAa,UACjB6B,EAAK6B,kBAAoB7B,EAAK8B,eAChCpH,EAAI,iBAAiBkD,MAAQI,EAAKG,aAAa,SAC/CzD,EAAI,eAAekD,MAAQI,EAAKG,aAAa,eAC7CpD,WAAU,EAAM,SAEhBA,WAAU,EAAO,QAEsB,iBAAhCiD,MAAAA,OAAAA,EAAAA,EAAMG,aAAa,UACxB6B,EAAK6B,kBAAoB7B,EAAK8B,eAChCpH,EAAI,aAAakD,MAAQI,EAAKG,aAAa,SAC3CpD,WAAU,EAAM,YAEhBA,WAAU,EAAO,YAGnBA,WAAU,EAAO,QACjBA,WAAU,EAAO,yDC7bd,CACblC,KAAM,OACNkJ,MAAO,qBACPC,QAAS,CACP,CACED,MAAO,aAET,CACEA,MAAO,iBAGXE,aAAc,CACZ,CACEF,MAAO,kBACPG,MAAO,UAET,CACEH,MAAO,aACPG,MAAO,cAET,CACEH,MAAO,kBACPG,MAAO,gBAET,CACEH,MAAO,kBACPG,MAAO,qDC1BE,CACbrJ,KAAM,SACNkJ,MAAO,wBACPC,QAAS,CACP,CACED,MAAO,gBAET,CACEA,MAAO,mBAGXE,aAAc,CACZ,CACEF,MAAO,kBACPG,MAAO,UAET,CACEH,MAAO,YACPG,MAAO,aAET,CACEH,MAAO,iBACPG,MAAO,mBAET,CACEH,MAAO,kBACPG,MAAO,qDC1BE,CACbrJ,KAAM,SACNkJ,MAAO,sBACPC,QAAS,CACP,CACED,MAAO,gBAET,CACEA,MAAO,iBAGXE,aAAc,CACZ,CACEF,MAAO,eACPG,MAAO,YAET,CACEH,MAAO,WACPG,MAAO,YAET,CACEH,MAAO,gBACPG,MAAO,qBAET,CACEH,MAAO,eACPG,MAAO,wDC1BE,CACbrJ,KAAM,KACNkJ,MAAO,qBACPC,QAAS,CACP,CACED,MAAO,QAET,CACEA,MAAO,UAGXE,aAAc,CACZ,CACEF,MAAO,KACPG,MAAO,MAET,CACEH,MAAO,KACPG,MAAO,MAET,CACEH,MAAO,KACPG,MAAO,MAET,CACEH,MAAO,KACPG,MAAO"}