import { FxElement, html, css } from '/fx.js';

customElements.define('fx-jtmp', class FxJTMP extends FxElement {
    static get properties() {
        return {
            url: { type: String, notify: true },
            src: { type: String, notify: true },
            srcdoc: { type: Object, notify: true },
            on_resize_delay: { type: String, default: '' },
            resize_fire_name: { type: String, default: '' },
            isReady: { type: Boolean },
            cell: { type: Object }
        }
    }

    constructor() {
        super();
        this._handleWindowResize = this._setIframe.bind(this);
        this._handleChange = this._onChange.bind(this);
    }
    connectedCallback() {
        super.connectedCallback();
        if (this.on_resize_delay)
            window.addEventListener('resize', this._handleWindowResize);
    }
    disconnectedCallback() {
        super.disconnectedCallback();
        if (this.on_resize_delay)
            window.removeEventListener('resize', this._handleWindowResize);
    }
    async firstUpdated() {
        super.firstUpdated();
        this.async(() => {
            this.setIframe(true);
            if (this.resize_fire_name) {
                this.$listen(this.resize_fire_name, () => this._setIframe());
            }
            this.listen('src-changed', () => this.setIframe());
            this.listen('srcdoc-changed', () => this.setIframe());
            this.listen('url-changed', () => this.setIframe());
            this.async(() => {
                this.isReady = true;
            }, 100)
        }, 100)
    }

    _setIframe(delay = +this.on_resize_delay) {
        const id = `setIframe_${this.id || Math.random()}`;
        FX.debounce(id, () => {
            this.setIframe();
        }, delay)
    }
    _onChange(e) {
        if (e.detail?.type === 'no-change') {
            this.fire(e.detail.fire);
            return;
        }
        if (this.saveAttachment) {
            this.saveAttachment(e.detail);
            return;
        }
        if (e && e.detail !== undefined) {
            this.source = this.cell?.source || e.detail;
        }
        if (this.cell) {
            this.cell.source = e.detail;
        }
        this.fire('change', this.source);
        this.$update();
    }
    setIframe(isReady = this.isReady) {
        if (!isReady) return;
        const iframe = this.iframe = this.$qs('iframe');
        if (!iframe) return;
        try {
            iframe.addEventListener('load', () => {
                try {
                    if (iframe.contentDocument)
                        iframe.contentDocument.addEventListener("change", this._handleChange);
                } catch (error) {
                    console.error('Cannot access iframe content:', error);
                }
                this.setTheme();
                top.FX.listen.bind(this, top, 'fx-theme-changed', (e) => {
                    this.setTheme(e.detail.theme);
                })
            })

            const srcdoc = this.srcdoc ? this.srcdoc(this.source || this.src || '') || '' : this.source || this.src || '';
            iframe.src = this.url || URL.createObjectURL(new Blob([srcdoc], { type: 'text/html' }));    
        } catch (error) {
            console.error('Error setting iframe content:', error);
        }
    }
    setTheme(theme = FX._theme) {
        const doc = this.iframe.contentDocument;
        let meta = doc.querySelector('meta[name=color-scheme]');
        if (!meta) {
            meta = doc.createElement('meta');
            meta.name = meta.id = 'color-scheme';
            doc.head.appendChild(meta);
        }
        meta.content = theme;
    }

    static styles = css`
            :host { 
                position: relative; 
                display: flex; 
                width: 100%; 
                height: 100%;
                overflow: hidden; 
                min-height: 20px; 
            }
        `

    render() {
        return html`
            <iframe style="border: none; width: 100%; height: 100%;"></iframe>
        `
    }
})
