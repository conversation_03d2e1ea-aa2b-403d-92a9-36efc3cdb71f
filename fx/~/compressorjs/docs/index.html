<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="JavaScript image compressor.">
  <meta name="author" content="Chen Fengyuan">
  <title>Compressor.js</title>
  <link rel="stylesheet" href="https://unpkg.com/bootstrap@4/dist/css/bootstrap.min.css" crossorigin="anonymous">
  <link rel="stylesheet" href="css/main.css">
</head>
<body>
  <header class="navbar navbar-light navbar-expand-md">
    <div class="container">
      <a class="navbar-brand" href="./">Compressor.js</a>
      <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbar-collapse" aria-controls="navbar-collapse" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse justify-content-end" id="navbar-collapse" role="navigation">
        <ul class="nav navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="https://github.com/fengyuanchen/compressorjs/blob/main/README.md">Docs</a>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdownMenuLink" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              Examples
            </a>
            <div class="dropdown-menu" aria-labelledby="navbarDropdownMenuLink">
              <a class="dropdown-item" href="examples/grayscale.html">Grayscale</a>
              <a class="dropdown-item" href="examples/watermark.html">Watermark</a>
              <a class="dropdown-item" href="examples/work-with-promise.html">Work with promise</a>
            </div>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link" href="https://github.com/fengyuanchen/compressorjs" title="View the GitHub project">GitHub</a>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link" href="https://fengyuanchen.github.io/" title="Explore more projects">Explore</a>
          </li>
          <li class="nav-item dropdown">
            <a class="nav-link" href="https://chenfengyuan.com/" title="About the author">About</a>
          </li>
        </ul>
      </div>
    </div>
  </header>

  <div class="jumbotron bg-primary text-white rounded-0">
    <div class="container">
      <div class="row">
        <div class="col-md">
          <h1>Compressor.js <small class="h6">v1.2.1</small></h1>
          <p class="lead">JavaScript image compressor.</p>
        </div>
        <div class="col-md">
          <div class="carbonads">
            <script id="_carbonads_js" src="https://cdn.carbonads.com/carbon.js?serve=CKYI55Q7&placement=fengyuanchengithubio" async></script>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="container">
    <h3>Playground</h3>
    <hr>
    <div id="app" v-cloak>
      <div class="card bg-light mb-4" @change="change" @dragover="dragover" @drop="drop">
        <div class="card-body">
          <div class="p-5 text-center">Drop image here or <label class="text-primary">browse... <input type="file" class="sr-only" id="file" accept="image/*" ref="input"></label></div>
        </div>
      </div>
      <div class="row">
        <div class="col-lg-4 mb-4">
          <div class="card h-100">
            <h4 class="card-header">Options</h4>
            <div class="card-body">
              <fieldset class="form-group">
                <div class="form-check form-check-inline">
                  <input type="checkbox" name="strict" class="form-check-input" id="inputStrict" v-model="options.strict">
                  <label class="form-check-label" for="inputStrict">strict</label>
                </div>
              </fieldset>
              <fieldset class="form-group">
                <div class="form-check form-check-inline">
                  <input type="checkbox" name="checkOrientation" class="form-check-input" id="inputCheckOrientation" v-model="options.checkOrientation">
                  <label class="form-check-label" for="inputCheckOrientation">checkOrientation</label>
                </div>
              </fieldset>
              <fieldset class="form-group">
                <div class="form-check form-check-inline">
                  <input type="checkbox" name="retainExif" class="form-check-input" id="inputRetainExif" v-model="options.retainExif">
                  <label class="form-check-label" for="inputRetainExif">retainExif</label>
                </div>
              </fieldset>
              <div class="form-group row">
                <label for="inputMaxWidth" class="col-5 col-form-label">maxWidth</label>
                <div class="col-7">
                  <input type="number" name="maxWidth" class="form-control" id="inputMaxWidth" placeholder="Infinity" v-model.number="options.maxWidth">
                </div>
              </div>
              <div class="form-group row">
                <label for="inputMaxHeight" class="col-5 col-form-label">maxHeight</label>
                <div class="col-7">
                  <input type="number" name="maxHeight" class="form-control" id="inputMaxHeight" placeholder="Infinity" v-model.number="options.maxHeight">
                </div>
              </div>
              <div class="form-group row">
                <label for="inputMinWidth" class="col-5 col-form-label">minWidth</label>
                <div class="col-7">
                  <input type="number" name="minWidth" class="form-control" id="inputMinWidth" placeholder="0" v-model.number="options.minWidth">
                </div>
              </div>
              <div class="form-group row">
                <label for="inputMinHeight" class="col-5 col-form-label">minHeight</label>
                <div class="col-7">
                  <input type="number" name="minHeight" class="form-control" id="inputMinHeight" placeholder="0" v-model.number="options.minHeight">
                </div>
              </div>
              <div class="form-group row">
                <label for="inputWidth" class="col-5 col-form-label">width</label>
                <div class="col-7">
                  <input type="number" name="width" class="form-control" id="inputWidth" placeholder="undefined" v-model.number="options.width">
                </div>
              </div>
              <div class="form-group row">
                <label for="inputHeight" class="col-5 col-form-label">height</label>
                <div class="col-7">
                  <input type="number" name="height" class="form-control" id="inputHeight" placeholder="undefined" v-model.number="options.height">
                </div>
              </div>
              <div class="form-group row">
                <label for="inputResize" class="col-5 col-form-label">resize</label>
                <div class="col-7">
                  <select class="form-control" name="resize" id="inputResize" v-model.number="options.resize">
                    <option value="none">none</option>
                    <option value="contain">contain</option>
                    <option value="cover">cover</option>
                  </select>
                </div>
              </div>
              <div class="form-group row">
                <label for="inputQuality" class="col-5 col-form-label">quality</label>
                <div class="col-7">
                  <select class="form-control" name="quality" id="inputQuality" v-model.number="options.quality">
                    <option value="0">0</option>
                    <option value="0.1">0.1</option>
                    <option value="0.2">0.2</option>
                    <option value="0.3">0.3</option>
                    <option value="0.4">0.4</option>
                    <option value="0.5">0.5</option>
                    <option value="0.6">0.6</option>
                    <option value="0.7">0.7</option>
                    <option value="0.8">0.8</option>
                    <option value="0.9">0.9</option>
                    <option value="1">1</option>
                    <option value="">NaN</option>
                  </select>
                </div>
              </div>
              <div class="form-group row">
                <label for="inputMimeType" class="col-5 col-form-label">mimeType</label>
                <div class="col-7">
                  <input type="text" name="mimeType" class="form-control" id="inputMimeType" placeholder="auto" v-model.number="options.mimeType">
                </div>
              </div>
              <div class="form-group row">
                <label for="inputConvertTypes" class="col-5 col-form-label">convertTypes</label>
                <div class="col-7">
                  <input type="text" name="convertTypes" class="form-control" id="inputConvertTypes" placeholder="image/png" v-model="options.convertTypes">
                </div>
              </div>
              <div class="form-group row mb-0">
                <label for="inputConvertSize" class="col-5 col-form-label">convertSize</label>
                <div class="col-7">
                  <input type="number" name="convertSize" class="form-control" id="inputConvertSize" placeholder="5000000" v-model.number="options.convertSize">
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col-lg-8 mb-4">
          <div class="card mb-4">
            <h5 class="card-header d-flex align-items-center justify-content-between">
              <span>Input image <small class="text-secondary">(original image)</small></span>
              <a class="btn btn-sm btn-blocks btn-outline-primary" :download="input.name" :href="inputURL" title="Download the compressed image">Download</a>
            </h5>
            <div class="card-body">
              <div class="row">
                <div class="col-md-4 d-flex mb-3">
                  <div class="w-100 text-center" v-if="inputURL">
                    <img class="mw-100" :src="inputURL" :alt="input.name">
                  </div>
                  <div v-else class="w-100 bg-light"></div>
                </div>
                <div class="col-md-8">
                  <dl class="row">
                    <dt class="col-5">lastModified:</dt>
                    <dd class="col-7">{{ input.lastModified || 'N/A' }}</dd>
                    <dt class="col-5">lastModifiedDate:</dt>
                    <dd class="col-7">{{ input.lastModifiedDate || 'N/A' }}</dd>
                    <dt class="col-5">name:</dt>
                    <dd class="col-7">{{ input.name || 'N/A' }}</dd>
                    <dt class="col-5">type:</dt>
                    <dd class="col-7">{{ input.type || 'N/A' }}</dd>
                    <dt class="col-5">size:</dt>
                    <dd class="col-7">{{ input.size | prettySize }}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
          <div class="card">
            <h5 class="card-header d-flex align-items-center justify-content-between">
              <span>Output image <small class="text-secondary">(compressed image)</small></span>
              <a class="btn btn-sm btn-blocks btn-outline-primary" :download="output.name" :href="outputURL" title="Download the compressed image">Download</a>
            </h5>
            <div class="card-body">
              <div class="row">
                <div class="col-md-4 d-flex mb-3">
                  <div class="w-100 text-center" v-if="outputURL">
                    <img class="mw-100" :src="outputURL" :alt="output.name">
                  </div>
                  <div v-else class="w-100 bg-light"></div>
                </div>
                <div class="col-md-8">
                  <dl class="row">
                    <dt class="col-5">lastModified:</dt>
                    <dd class="col-7">{{ output.lastModified || 'N/A' }}</dd>
                    <dt class="col-5">lastModifiedDate:</dt>
                    <dd class="col-7">{{ output.lastModifiedDate || 'N/A' }}</dd>
                    <dt class="col-5">name:</dt>
                    <dd class="col-7">{{ output.name || 'N/A' }}</dd>
                    <dt class="col-5">type:</dt>
                    <dd class="col-7">{{ output.type || 'N/A' }}</dd>
                    <dt class="col-5">size:</dt>
                    <dd class="col-7">{{ output.size | prettySize }}
                      <span v-if="output.size">({{ ((1 - (output.size / input.size)) * 100).toFixed(2) }}% off)</span>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="card" v-if="inputURL && outputURL">
        <h4 class="card-header">Comparing images</h4>
        <div class="card-body">
          <vue-compare-image v-if="inputURL && outputURL" :left-image="inputURL" :left-image-alt="input.name" left-label="Input image" :right-image="outputURL" :right-image-alt="output.name" right-label="Output image"></vue-compare-image>
        </div>
      </div>
    </div>
  </div>

  <footer class="footer">
    <div class="container">
      <p class="heart"></p>
      <nav class="nav flex-wrap justify-content-center mb-3">
        <a class="nav-link" href="https://github.com/fengyuanchen/compressorjs">GitHub</a>
        <a class="nav-link" href="https://github.com/fengyuanchen/compressorjs/releases">Releases</a>
        <a class="nav-link" href="https://github.com/fengyuanchen/compressorjs/blob/main/LICENSE">License</a>
        <a class="nav-link" href="https://chenfengyuan.com/">About</a>
      </nav>
    </div>
  </footer>

  <script src="https://unpkg.com/jquery@3/dist/jquery.slim.min.js" crossorigin="anonymous"></script>
  <script src="https://unpkg.com/bootstrap@4/dist/js/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
  <script src="https://unpkg.com/vue@2/dist/vue.min.js" crossorigin="anonymous"></script>
  <script src="https://unpkg.com/vue-compare-image@0.2/dist/vueCompareImage.umd.min.js" crossorigin="anonymous"></script>
  <script src="https://fengyuanchen.github.io/shared/google-analytics.js" crossorigin="anonymous"></script>
  <script src="js/compressor.js"></script>
  <script src="js/main.js"></script>
</body>
</html>
