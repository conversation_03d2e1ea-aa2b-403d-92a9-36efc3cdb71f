export default(function(factory){var globalObject="object"==typeof window&&window||"object"==typeof self&&self;"undefined"==typeof exports||exports.nodeType?globalObject&&(globalObject.hljs=factory({}),"function"==typeof define&&define.amd&&define([],(function(){return globalObject.hljs}))):factory(exports)}((function(hljs){var ArrayProto=[],objectKeys=Object.keys,languages={},aliases={},plugins=[],SAFE_MODE=!0,noHighlightRe=/^(no-?highlight|plain|text)$/i,languagePrefixRe=/\blang(?:uage)?-([\w-]+)\b/i,fixMarkupRe=/((^(<[^>]+>|\t|)+|(?:\n)))/gm,LANGUAGE_NOT_FOUND="Could not find the language '{}', did you forget to load/include a language module?",options={classPrefix:"hljs-",tabReplace:null,useBR:!1,languages:void 0},COMMON_KEYWORDS="of and for in not or if then".split(" ");function escape(value){return value.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function tag(node){return node.nodeName.toLowerCase()}function isNotHighlighted(language){return noHighlightRe.test(language)}function inherit(parent){var key,result={},objects=Array.prototype.slice.call(arguments,1);for(key in parent)result[key]=parent[key];return objects.forEach((function(obj){for(key in obj)result[key]=obj[key]})),result}function nodeStream(node){var result=[];return function _nodeStream(node,offset){for(var child=node.firstChild;child;child=child.nextSibling)3===child.nodeType?offset+=child.nodeValue.length:1===child.nodeType&&(result.push({event:"start",offset:offset,node:child}),offset=_nodeStream(child,offset),tag(child).match(/br|hr|img|input/)||result.push({event:"stop",offset:offset,node:child}));return offset}(node,0),result}function expand_or_clone_mode(mode){return mode.variants&&!mode.cached_variants&&(mode.cached_variants=mode.variants.map((function(variant){return inherit(mode,{variants:null},variant)}))),mode.cached_variants?mode.cached_variants:function dependencyOnParent(mode){return!!mode&&(mode.endsWithParent||dependencyOnParent(mode.starts))}(mode)?[inherit(mode,{starts:mode.starts?inherit(mode.starts):null})]:Object.isFrozen(mode)?[inherit(mode)]:[mode]}function scoreForKeyword(keyword,providedScore){return providedScore?Number(providedScore):(word=keyword,COMMON_KEYWORDS.includes(word.toLowerCase())?0:1);var word}function compileLanguage(language){function reStr(re){return re&&re.source||re}function langRe(value,global){return new RegExp(reStr(value),"m"+(language.case_insensitive?"i":"")+(global?"g":""))}function buildModeRegex(mode){var matcherRe,term,matchIndexes={},regexes=[],matcher={},matchAt=1;function addRule(rule,regex){matchIndexes[matchAt]=rule,regexes.push([rule,regex]),matchAt+=function(re){return new RegExp(re.toString()+"|").exec("").length-1}(regex)+1}for(var i=0;i<mode.contains.length;i++){addRule(term=mode.contains[i],term.beginKeywords?"\\.?(?:"+term.begin+")\\.?":term.begin)}mode.terminator_end&&addRule("end",mode.terminator_end),mode.illegal&&addRule("illegal",mode.illegal);var terminators=regexes.map((function(el){return el[1]}));return matcherRe=langRe(function(regexps,separator){for(var backreferenceRe=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./,numCaptures=0,ret="",i=0;i<regexps.length;i++){var offset=numCaptures+=1,re=reStr(regexps[i]);for(i>0&&(ret+=separator),ret+="(";re.length>0;){var match=backreferenceRe.exec(re);if(null==match){ret+=re;break}ret+=re.substring(0,match.index),re=re.substring(match.index+match[0].length),"\\"==match[0][0]&&match[1]?ret+="\\"+String(Number(match[1])+offset):(ret+=match[0],"("==match[0]&&numCaptures++)}ret+=")"}return ret}(terminators,"|"),!0),matcher.lastIndex=0,matcher.exec=function(s){var rule;if(0===regexes.length)return null;matcherRe.lastIndex=matcher.lastIndex;var match=matcherRe.exec(s);if(!match)return null;for(var i=0;i<match.length;i++)if(null!=match[i]&&null!=matchIndexes[""+i]){rule=matchIndexes[""+i];break}return"string"==typeof rule?(match.type=rule,match.extra=[mode.illegal,mode.terminator_end]):(match.type="begin",match.rule=rule),match},matcher}if(language.contains&&language.contains.includes("self")){if(!SAFE_MODE)throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");language.contains=language.contains.filter((function(mode){return"self"!=mode}))}!function compileMode(mode,parent){mode.compiled||(mode.compiled=!0,mode.keywords=mode.keywords||mode.beginKeywords,mode.keywords&&(mode.keywords=function(rawKeywords,case_insensitive){var compiled_keywords={};return"string"==typeof rawKeywords?splitAndCompile("keyword",rawKeywords):objectKeys(rawKeywords).forEach((function(className){splitAndCompile(className,rawKeywords[className])})),compiled_keywords;function splitAndCompile(className,str){case_insensitive&&(str=str.toLowerCase()),str.split(" ").forEach((function(keyword){var pair=keyword.split("|");compiled_keywords[pair[0]]=[className,scoreForKeyword(pair[0],pair[1])]}))}}(mode.keywords,language.case_insensitive)),mode.lexemesRe=langRe(mode.lexemes||/\w+/,!0),parent&&(mode.beginKeywords&&(mode.begin="\\b("+mode.beginKeywords.split(" ").join("|")+")\\b"),mode.begin||(mode.begin=/\B|\b/),mode.beginRe=langRe(mode.begin),mode.endSameAsBegin&&(mode.end=mode.begin),mode.end||mode.endsWithParent||(mode.end=/\B|\b/),mode.end&&(mode.endRe=langRe(mode.end)),mode.terminator_end=reStr(mode.end)||"",mode.endsWithParent&&parent.terminator_end&&(mode.terminator_end+=(mode.end?"|":"")+parent.terminator_end)),mode.illegal&&(mode.illegalRe=langRe(mode.illegal)),null==mode.relevance&&(mode.relevance=1),mode.contains||(mode.contains=[]),mode.contains=Array.prototype.concat.apply([],mode.contains.map((function(c){return expand_or_clone_mode("self"===c?mode:c)}))),mode.contains.forEach((function(c){compileMode(c,mode)})),mode.starts&&compileMode(mode.starts,parent),mode.terminators=buildModeRegex(mode))}(language)}function highlight(languageName,code,ignore_illegals,continuation){var codeToHighlight=code;function keywordMatch(mode,match){var match_str=language.case_insensitive?match[0].toLowerCase():match[0];return mode.keywords.hasOwnProperty(match_str)&&mode.keywords[match_str]}function buildSpan(className,insideSpan,leaveOpen,noPrefix){if(!leaveOpen&&""===insideSpan)return"";if(!className)return insideSpan;var openSpan='<span class="'+(noPrefix?"":options.classPrefix);return(openSpan+=className+'">')+insideSpan+(leaveOpen?"":"</span>")}function processBuffer(){result+=null!=top.subLanguage?function(){var explicit="string"==typeof top.subLanguage;if(explicit&&!languages[top.subLanguage])return escape(mode_buffer);var result=explicit?highlight(top.subLanguage,mode_buffer,!0,continuations[top.subLanguage]):highlightAuto(mode_buffer,top.subLanguage.length?top.subLanguage:void 0);return top.relevance>0&&(relevance+=result.relevance),explicit&&(continuations[top.subLanguage]=result.top),buildSpan(result.language,result.value,!1,!0)}():function(){var keyword_match,last_index,match,result;if(!top.keywords)return escape(mode_buffer);for(result="",last_index=0,top.lexemesRe.lastIndex=0,match=top.lexemesRe.exec(mode_buffer);match;)result+=escape(mode_buffer.substring(last_index,match.index)),(keyword_match=keywordMatch(top,match))?(relevance+=keyword_match[1],result+=buildSpan(keyword_match[0],escape(match[0]))):result+=escape(match[0]),last_index=top.lexemesRe.lastIndex,match=top.lexemesRe.exec(mode_buffer);return result+escape(mode_buffer.substr(last_index))}(),mode_buffer=""}function startNewMode(mode){result+=mode.className?buildSpan(mode.className,"",!0):"",top=Object.create(mode,{parent:{value:top}})}function doBeginMatch(match){var lexeme=match[0],new_mode=match.rule;return new_mode&&new_mode.endSameAsBegin&&(new_mode.endRe=new RegExp(lexeme.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&"),"m")),new_mode.skip?mode_buffer+=lexeme:(new_mode.excludeBegin&&(mode_buffer+=lexeme),processBuffer(),new_mode.returnBegin||new_mode.excludeBegin||(mode_buffer=lexeme)),startNewMode(new_mode),new_mode.returnBegin?0:lexeme.length}function doEndMatch(match){var lexeme=match[0],matchPlusRemainder=codeToHighlight.substr(match.index),end_mode=function endOfMode(mode,lexeme){if(function(re,lexeme){var match=re&&re.exec(lexeme);return match&&0===match.index}(mode.endRe,lexeme)){for(;mode.endsParent&&mode.parent;)mode=mode.parent;return mode}if(mode.endsWithParent)return endOfMode(mode.parent,lexeme)}(top,matchPlusRemainder);if(end_mode){var origin=top;origin.skip?mode_buffer+=lexeme:(origin.returnEnd||origin.excludeEnd||(mode_buffer+=lexeme),processBuffer(),origin.excludeEnd&&(mode_buffer=lexeme));do{top.className&&(result+="</span>"),top.skip||top.subLanguage||(relevance+=top.relevance),top=top.parent}while(top!==end_mode.parent);return end_mode.starts&&(end_mode.endSameAsBegin&&(end_mode.starts.endRe=end_mode.endRe),startNewMode(end_mode.starts)),origin.returnEnd?0:lexeme.length}}var lastMatch={};function processLexeme(text_before_match,match){var lexeme=match&&match[0];if(mode_buffer+=text_before_match,null==lexeme)return processBuffer(),0;if("begin"==lastMatch.type&&"end"==match.type&&lastMatch.index==match.index&&""===lexeme)return mode_buffer+=codeToHighlight.slice(match.index,match.index+1),1;if(lastMatch=match,"begin"===match.type)return doBeginMatch(match);if("illegal"===match.type&&!ignore_illegals)throw new Error('Illegal lexeme "'+lexeme+'" for mode "'+(top.className||"<unnamed>")+'"');if("end"===match.type){var processed=doEndMatch(match);if(null!=processed)return processed}return mode_buffer+=lexeme,lexeme.length}var language=getLanguage(languageName);if(!language)throw console.error(LANGUAGE_NOT_FOUND.replace("{}",languageName)),new Error('Unknown language: "'+languageName+'"');compileLanguage(language);var current,top=continuation||language,continuations={},result="";for(current=top;current!==language;current=current.parent)current.className&&(result=buildSpan(current.className,"",!0)+result);var mode_buffer="",relevance=0;try{for(var match,count,index=0;top.terminators.lastIndex=index,match=top.terminators.exec(codeToHighlight);)count=processLexeme(codeToHighlight.substring(index,match.index),match),index=match.index+count;for(processLexeme(codeToHighlight.substr(index)),current=top;current.parent;current=current.parent)current.className&&(result+="</span>");return{relevance:relevance,value:result,illegal:!1,language:languageName,top:top}}catch(err){if(err.message&&err.message.includes("Illegal"))return{illegal:!0,relevance:0,value:escape(codeToHighlight)};if(SAFE_MODE)return{relevance:0,value:escape(codeToHighlight),language:languageName,top:top,errorRaised:err};throw err}}function highlightAuto(code,languageSubset){languageSubset=languageSubset||options.languages||objectKeys(languages);var result={relevance:0,value:escape(code)},second_best=result;return languageSubset.filter(getLanguage).filter(autoDetection).forEach((function(name){var current=highlight(name,code,!1);current.language=name,current.relevance>second_best.relevance&&(second_best=current),current.relevance>result.relevance&&(second_best=result,result=current)})),second_best.language&&(result.second_best=second_best),result}function fixMarkup(value){return options.tabReplace||options.useBR?value.replace(fixMarkupRe,(function(match,p1){return options.useBR&&"\n"===match?"<br>":options.tabReplace?p1.replace(/\t/g,options.tabReplace):""})):value}function highlightBlock(block){var node,originalStream,result,resultNode,text,language=function(block){var i,match,length,_class,classes=block.className+" ";if(classes+=block.parentNode?block.parentNode.className:"",match=languagePrefixRe.exec(classes)){var language=getLanguage(match[1]);return language||(console.warn(LANGUAGE_NOT_FOUND.replace("{}",match[1])),console.warn("Falling back to no-highlight mode for this block.",block)),language?match[1]:"no-highlight"}for(i=0,length=(classes=classes.split(/\s+/)).length;i<length;i++)if(isNotHighlighted(_class=classes[i])||getLanguage(_class))return _class}(block);isNotHighlighted(language)||(fire("before:highlightBlock",{block:block,language:language}),options.useBR?(node=document.createElement("div")).innerHTML=block.innerHTML.replace(/\n/g,"").replace(/<br[ \/]*>/g,"\n"):node=block,text=node.textContent,result=language?highlight(language,text,!0):highlightAuto(text),(originalStream=nodeStream(node)).length&&((resultNode=document.createElement("div")).innerHTML=result.value,result.value=function(original,highlighted,value){var processed=0,result="",nodeStack=[];function selectStream(){return original.length&&highlighted.length?original[0].offset!==highlighted[0].offset?original[0].offset<highlighted[0].offset?original:highlighted:"start"===highlighted[0].event?original:highlighted:original.length?original:highlighted}function open(node){result+="<"+tag(node)+ArrayProto.map.call(node.attributes,(function(a){return" "+a.nodeName+'="'+escape(a.value).replace(/"/g,"&quot;")+'"'})).join("")+">"}function close(node){result+="</"+tag(node)+">"}function render(event){("start"===event.event?open:close)(event.node)}for(;original.length||highlighted.length;){var stream=selectStream();if(result+=escape(value.substring(processed,stream[0].offset)),processed=stream[0].offset,stream===original){nodeStack.reverse().forEach(close);do{render(stream.splice(0,1)[0]),stream=selectStream()}while(stream===original&&stream.length&&stream[0].offset===processed);nodeStack.reverse().forEach(open)}else"start"===stream[0].event?nodeStack.push(stream[0].node):nodeStack.pop(),render(stream.splice(0,1)[0])}return result+escape(value.substr(processed))}(originalStream,nodeStream(resultNode),text)),result.value=fixMarkup(result.value),fire("after:highlightBlock",{block:block,result:result}),block.innerHTML=result.value,block.className=function(prevClassName,currentLang,resultLang){var language=currentLang?aliases[currentLang]:resultLang,result=[prevClassName.trim()];return prevClassName.match(/\bhljs\b/)||result.push("hljs"),-1===prevClassName.indexOf(language)&&result.push(language),result.join(" ").trim()}(block.className,language,result.language),block.result={language:result.language,re:result.relevance},result.second_best&&(block.second_best={language:result.second_best.language,re:result.second_best.relevance}))}function initHighlighting(){if(!initHighlighting.called){initHighlighting.called=!0;var blocks=document.querySelectorAll("pre code");ArrayProto.forEach.call(blocks,highlightBlock)}}var PLAINTEXT_LANGUAGE={disableAutodetect:!0};function getLanguage(name){return name=(name||"").toLowerCase(),languages[name]||languages[aliases[name]]}function autoDetection(name){var lang=getLanguage(name);return lang&&!lang.disableAutodetect}function fire(event,args){var cb=event;plugins.forEach((function(plugin){plugin[cb]&&plugin[cb](args)}))}return hljs.highlight=highlight,hljs.highlightAuto=highlightAuto,hljs.fixMarkup=fixMarkup,hljs.highlightBlock=highlightBlock,hljs.configure=function(user_options){options=inherit(options,user_options)},hljs.initHighlighting=initHighlighting,hljs.initHighlightingOnLoad=function(){window.addEventListener("DOMContentLoaded",initHighlighting,!1)},hljs.registerLanguage=function(name,language){var lang;try{lang=language(hljs)}catch(error){if(console.error("Language definition for '{}' could not be registered.".replace("{}",name)),!SAFE_MODE)throw error;console.error(error),lang=PLAINTEXT_LANGUAGE}languages[name]=lang,lang.rawDefinition=language.bind(null,hljs),lang.aliases&&lang.aliases.forEach((function(alias){aliases[alias]=name}))},hljs.listLanguages=function(){return objectKeys(languages)},hljs.getLanguage=getLanguage,hljs.requireLanguage=function(name){var lang=getLanguage(name);if(lang)return lang;throw new Error("The '{}' language is required, but not loaded.".replace("{}",name))},hljs.autoDetection=autoDetection,hljs.inherit=inherit,hljs.addPlugin=function(plugin,options){plugins.push(plugin)},hljs.debugMode=function(){SAFE_MODE=!1},hljs.IDENT_RE="[a-zA-Z]\\w*",hljs.UNDERSCORE_IDENT_RE="[a-zA-Z_]\\w*",hljs.NUMBER_RE="\\b\\d+(\\.\\d+)?",hljs.C_NUMBER_RE="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",hljs.BINARY_NUMBER_RE="\\b(0b[01]+)",hljs.RE_STARTERS_RE="!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",hljs.BACKSLASH_ESCAPE={begin:"\\\\[\\s\\S]",relevance:0},hljs.APOS_STRING_MODE={className:"string",begin:"'",end:"'",illegal:"\\n",contains:[hljs.BACKSLASH_ESCAPE]},hljs.QUOTE_STRING_MODE={className:"string",begin:'"',end:'"',illegal:"\\n",contains:[hljs.BACKSLASH_ESCAPE]},hljs.PHRASAL_WORDS_MODE={begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},hljs.COMMENT=function(begin,end,inherits){var mode=hljs.inherit({className:"comment",begin:begin,end:end,contains:[]},inherits||{});return mode.contains.push(hljs.PHRASAL_WORDS_MODE),mode.contains.push({className:"doctag",begin:"(?:TODO|FIXME|NOTE|BUG|XXX):",relevance:0}),mode},hljs.C_LINE_COMMENT_MODE=hljs.COMMENT("//","$"),hljs.C_BLOCK_COMMENT_MODE=hljs.COMMENT("/\\*","\\*/"),hljs.HASH_COMMENT_MODE=hljs.COMMENT("#","$"),hljs.NUMBER_MODE={className:"number",begin:hljs.NUMBER_RE,relevance:0},hljs.C_NUMBER_MODE={className:"number",begin:hljs.C_NUMBER_RE,relevance:0},hljs.BINARY_NUMBER_MODE={className:"number",begin:hljs.BINARY_NUMBER_RE,relevance:0},hljs.CSS_NUMBER_MODE={className:"number",begin:hljs.NUMBER_RE+"(%|em|ex|ch|rem|vw|vh|vmin|vmax|cm|mm|in|pt|pc|px|deg|grad|rad|turn|s|ms|Hz|kHz|dpi|dpcm|dppx)?",relevance:0},hljs.REGEXP_MODE={className:"regexp",begin:/\//,end:/\/[gimuy]*/,illegal:/\n/,contains:[hljs.BACKSLASH_ESCAPE,{begin:/\[/,end:/\]/,relevance:0,contains:[hljs.BACKSLASH_ESCAPE]}]},hljs.TITLE_MODE={className:"title",begin:hljs.IDENT_RE,relevance:0},hljs.UNDERSCORE_TITLE_MODE={className:"title",begin:hljs.UNDERSCORE_IDENT_RE,relevance:0},hljs.METHOD_GUARD={begin:"\\.\\s*"+hljs.UNDERSCORE_IDENT_RE,relevance:0},[hljs.BACKSLASH_ESCAPE,hljs.APOS_STRING_MODE,hljs.QUOTE_STRING_MODE,hljs.PHRASAL_WORDS_MODE,hljs.COMMENT,hljs.C_LINE_COMMENT_MODE,hljs.C_BLOCK_COMMENT_MODE,hljs.HASH_COMMENT_MODE,hljs.NUMBER_MODE,hljs.C_NUMBER_MODE,hljs.BINARY_NUMBER_MODE,hljs.CSS_NUMBER_MODE,hljs.REGEXP_MODE,hljs.TITLE_MODE,hljs.UNDERSCORE_TITLE_MODE,hljs.METHOD_GUARD].forEach((function(obj){!function deepFreeze(o){Object.freeze(o);var objIsFunction="function"==typeof o;return Object.getOwnPropertyNames(o).forEach((function(prop){!o.hasOwnProperty(prop)||null===o[prop]||"object"!=typeof o[prop]&&"function"!=typeof o[prop]||objIsFunction&&("caller"===prop||"callee"===prop||"arguments"===prop)||Object.isFrozen(o[prop])||deepFreeze(o[prop])})),o}(obj)})),hljs})));hljs.registerLanguage("javascript",(function(e){var FRAGMENT_begin="<>",FRAGMENT_end="</>",XML_TAG={begin:/<[A-Za-z0-9\\._:-]+/,end:/\/[A-Za-z0-9\\._:-]+>|\/>/},IDENT_RE="[A-Za-z$_][0-9A-Za-z$_]*",KEYWORDS={keyword:"in of if for while finally var new function do return void else break catch instanceof with throw case default try this switch continue typeof delete let yield const export super debugger as async await static import from as",literal:"true false null undefined NaN Infinity",built_in:"eval isFinite isNaN parseFloat parseInt decodeURI decodeURIComponent encodeURI encodeURIComponent escape unescape Object Function Boolean Error EvalError InternalError RangeError ReferenceError StopIteration SyntaxError TypeError URIError Number Math Date String RegExp Array Float32Array Float64Array Int16Array Int32Array Int8Array Uint16Array Uint32Array Uint8Array Uint8ClampedArray ArrayBuffer DataView JSON Intl arguments require module console window document Symbol Set Map WeakSet WeakMap Proxy Reflect Promise"},NUMBER={className:"number",variants:[{begin:"\\b(0[bB][01]+)n?"},{begin:"\\b(0[oO][0-7]+)n?"},{begin:hljs.C_NUMBER_RE+"n?"}],relevance:0},SUBST={className:"subst",begin:"\\$\\{",end:"\\}",keywords:KEYWORDS,contains:[]},HTML_TEMPLATE={begin:"html`",end:"",starts:{end:"`",returnEnd:!1,contains:[hljs.BACKSLASH_ESCAPE,SUBST],subLanguage:"xml"}},CSS_TEMPLATE={begin:"css`",end:"",starts:{end:"`",returnEnd:!1,contains:[hljs.BACKSLASH_ESCAPE,SUBST],subLanguage:"css"}},TEMPLATE_STRING={className:"string",begin:"`",end:"`",contains:[hljs.BACKSLASH_ESCAPE,SUBST]};SUBST.contains=[hljs.APOS_STRING_MODE,hljs.QUOTE_STRING_MODE,HTML_TEMPLATE,CSS_TEMPLATE,TEMPLATE_STRING,NUMBER,hljs.REGEXP_MODE];var PARAMS_CONTAINS=SUBST.contains.concat([hljs.C_BLOCK_COMMENT_MODE,hljs.C_LINE_COMMENT_MODE]);return{aliases:["js","jsx","mjs","cjs"],keywords:KEYWORDS,contains:[{className:"meta",relevance:10,begin:/^\s*['"]use (strict|asm)['"]/},{className:"meta",begin:/^#!/,end:/$/},hljs.APOS_STRING_MODE,hljs.QUOTE_STRING_MODE,HTML_TEMPLATE,CSS_TEMPLATE,TEMPLATE_STRING,hljs.C_LINE_COMMENT_MODE,hljs.COMMENT("/\\*\\*","\\*/",{relevance:0,contains:[{className:"doctag",begin:"@[A-Za-z]+",contains:[{className:"type",begin:"\\{",end:"\\}",relevance:0},{className:"variable",begin:IDENT_RE+"(?=\\s*(-)|$)",endsParent:!0,relevance:0},{begin:/(?=[^\n])\s/,relevance:0}]}]}),hljs.C_BLOCK_COMMENT_MODE,NUMBER,{begin:/[{,\n]\s*/,relevance:0,contains:[{begin:IDENT_RE+"\\s*:",returnBegin:!0,relevance:0,contains:[{className:"attr",begin:IDENT_RE,relevance:0}]}]},{begin:"("+hljs.RE_STARTERS_RE+"|\\b(case|return|throw)\\b)\\s*",keywords:"return throw case",contains:[hljs.C_LINE_COMMENT_MODE,hljs.C_BLOCK_COMMENT_MODE,hljs.REGEXP_MODE,{className:"function",begin:"(\\(.*?\\)|"+IDENT_RE+")\\s*=>",returnBegin:!0,end:"\\s*=>",contains:[{className:"params",variants:[{begin:IDENT_RE},{begin:/\(\s*\)/},{begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,keywords:KEYWORDS,contains:PARAMS_CONTAINS}]}]},{className:"",begin:/\s/,end:/\s*/,skip:!0},{variants:[{begin:FRAGMENT_begin,end:FRAGMENT_end},{begin:XML_TAG.begin,end:XML_TAG.end}],subLanguage:"xml",contains:[{begin:XML_TAG.begin,end:XML_TAG.end,skip:!0,contains:["self"]}]}],relevance:0},{className:"function",beginKeywords:"function",end:/\{/,excludeEnd:!0,contains:[hljs.inherit(hljs.TITLE_MODE,{begin:IDENT_RE}),{className:"params",begin:/\(/,end:/\)/,excludeBegin:!0,excludeEnd:!0,contains:PARAMS_CONTAINS}],illegal:/\[|%/},{begin:/\$[(.]/},hljs.METHOD_GUARD,{className:"class",beginKeywords:"class",end:/[{;=]/,excludeEnd:!0,illegal:/[:"\[\]]/,contains:[{beginKeywords:"extends"},hljs.UNDERSCORE_TITLE_MODE]},{beginKeywords:"constructor get set",end:/\{/,excludeEnd:!0}],illegal:/#(?!!)/}})),hljs.registerLanguage("xml",(function(e){var XML_ENTITIES={className:"symbol",begin:"&[a-z]+;|&#[0-9]+;|&#x[a-f0-9]+;"},XML_META_KEYWORDS={begin:"\\s",contains:[{className:"meta-keyword",begin:"#?[a-z_][a-z1-9_-]+",illegal:"\\n"}]},XML_META_PAR_KEYWORDS=hljs.inherit(XML_META_KEYWORDS,{begin:"\\(",end:"\\)"}),APOS_META_STRING_MODE=hljs.inherit(hljs.APOS_STRING_MODE,{className:"meta-string"}),QUOTE_META_STRING_MODE=hljs.inherit(hljs.QUOTE_STRING_MODE,{className:"meta-string"}),TAG_INTERNALS={endsWithParent:!0,illegal:/</,relevance:0,contains:[{className:"attr",begin:"[A-Za-z0-9\\._:-]+",relevance:0},{begin:/=\s*/,relevance:0,contains:[{className:"string",endsParent:!0,variants:[{begin:/"/,end:/"/,contains:[XML_ENTITIES]},{begin:/'/,end:/'/,contains:[XML_ENTITIES]},{begin:/[^\s"'=<>`]+/}]}]}]};return{aliases:["html","xhtml","rss","atom","xjb","xsd","xsl","plist","wsf","svg"],case_insensitive:!0,contains:[{className:"meta",begin:"<![a-z]",end:">",relevance:10,contains:[XML_META_KEYWORDS,QUOTE_META_STRING_MODE,APOS_META_STRING_MODE,XML_META_PAR_KEYWORDS,{begin:"\\[",end:"\\]",contains:[{className:"meta",begin:"<![a-z]",end:">",contains:[XML_META_KEYWORDS,XML_META_PAR_KEYWORDS,QUOTE_META_STRING_MODE,APOS_META_STRING_MODE]}]}]},hljs.COMMENT("\x3c!--","--\x3e",{relevance:10}),{begin:"<\\!\\[CDATA\\[",end:"\\]\\]>",relevance:10},XML_ENTITIES,{className:"meta",begin:/<\?xml/,end:/\?>/,relevance:10},{begin:/<\?(php)?/,end:/\?>/,subLanguage:"php",contains:[{begin:"/\\*",end:"\\*/",skip:!0},{begin:'b"',end:'"',skip:!0},{begin:"b'",end:"'",skip:!0},hljs.inherit(hljs.APOS_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0}),hljs.inherit(hljs.QUOTE_STRING_MODE,{illegal:null,className:null,contains:null,skip:!0})]},{className:"tag",begin:"<style(?=\\s|>)",end:">",keywords:{name:"style"},contains:[TAG_INTERNALS],starts:{end:"</style>",returnEnd:!0,subLanguage:["css","xml"]}},{className:"tag",begin:"<script(?=\\s|>)",end:">",keywords:{name:"script"},contains:[TAG_INTERNALS],starts:{end:"<\/script>",returnEnd:!0,subLanguage:["actionscript","javascript","handlebars","xml"]}},{className:"tag",begin:"</?",end:"/?>",contains:[{className:"name",begin:/[^\/><\s]+/,relevance:0},TAG_INTERNALS]}]}})),hljs.registerLanguage("xquery",(function(e){var CONTAINS=[{className:"variable",begin:/[\$][\w-:]+/},{className:"built_in",variants:[{begin:/\barray\:/,end:/(?:append|filter|flatten|fold\-(?:left|right)|for-each(?:\-pair)?|get|head|insert\-before|join|put|remove|reverse|size|sort|subarray|tail)\b/},{begin:/\bmap\:/,end:/(?:contains|entry|find|for\-each|get|keys|merge|put|remove|size)\b/},{begin:/\bmath\:/,end:/(?:a(?:cos|sin|tan[2]?)|cos|exp(?:10)?|log(?:10)?|pi|pow|sin|sqrt|tan)\b/},{begin:/\bop\:/,end:/\(/,excludeEnd:!0},{begin:/\bfn\:/,end:/\(/,excludeEnd:!0},{begin:/[^<\/\$\:'"-]\b(?:abs|accumulator\-(?:after|before)|adjust\-(?:date(?:Time)?|time)\-to\-timezone|analyze\-string|apply|available\-(?:environment\-variables|system\-properties)|avg|base\-uri|boolean|ceiling|codepoints?\-(?:equal|to\-string)|collation\-key|collection|compare|concat|contains(?:\-token)?|copy\-of|count|current(?:\-)?(?:date(?:Time)?|time|group(?:ing\-key)?|output\-uri|merge\-(?:group|key))?data|dateTime|days?\-from\-(?:date(?:Time)?|duration)|deep\-equal|default\-(?:collation|language)|distinct\-values|document(?:\-uri)?|doc(?:\-available)?|element\-(?:available|with\-id)|empty|encode\-for\-uri|ends\-with|environment\-variable|error|escape\-html\-uri|exactly\-one|exists|false|filter|floor|fold\-(?:left|right)|for\-each(?:\-pair)?|format\-(?:date(?:Time)?|time|integer|number)|function\-(?:arity|available|lookup|name)|generate\-id|has\-children|head|hours\-from\-(?:dateTime|duration|time)|id(?:ref)?|implicit\-timezone|in\-scope\-prefixes|index\-of|innermost|insert\-before|iri\-to\-uri|json\-(?:doc|to\-xml)|key|lang|last|load\-xquery\-module|local\-name(?:\-from\-QName)?|(?:lower|upper)\-case|matches|max|minutes\-from\-(?:dateTime|duration|time)|min|months?\-from\-(?:date(?:Time)?|duration)|name(?:space\-uri\-?(?:for\-prefix|from\-QName)?)?|nilled|node\-name|normalize\-(?:space|unicode)|not|number|one\-or\-more|outermost|parse\-(?:ietf\-date|json)|path|position|(?:prefix\-from\-)?QName|random\-number\-generator|regex\-group|remove|replace|resolve\-(?:QName|uri)|reverse|root|round(?:\-half\-to\-even)?|seconds\-from\-(?:dateTime|duration|time)|snapshot|sort|starts\-with|static\-base\-uri|stream\-available|string\-?(?:join|length|to\-codepoints)?|subsequence|substring\-?(?:after|before)?|sum|system\-property|tail|timezone\-from\-(?:date(?:Time)?|time)|tokenize|trace|trans(?:form|late)|true|type\-available|unordered|unparsed\-(?:entity|text)?\-?(?:public\-id|uri|available|lines)?|uri\-collection|xml\-to\-json|years?\-from\-(?:date(?:Time)?|duration)|zero\-or\-one)\b/},{begin:/\blocal\:/,end:/\(/,excludeEnd:!0},{begin:/\bzip\:/,end:/(?:zip\-file|(?:xml|html|text|binary)\-entry| (?:update\-)?entries)\b/},{begin:/\b(?:util|db|functx|app|xdmp|xmldb)\:/,end:/\(/,excludeEnd:!0}]},{className:"string",variants:[{begin:/"/,end:/"/,contains:[{begin:/""/,relevance:0}]},{begin:/'/,end:/'/,contains:[{begin:/''/,relevance:0}]}]},{className:"number",begin:"(\\b0[0-7_]+)|(\\b0x[0-9a-fA-F_]+)|(\\b[1-9][0-9_]*(\\.[0-9_]+)?)|[0_]\\b",relevance:0},{className:"comment",begin:"\\(:",end:":\\)",relevance:10,contains:[{className:"doctag",begin:"@\\w+"}]},{className:"meta",begin:/%[\w-:]+/},{className:"title",begin:/\bxquery version "[13]\.[01]"\s?(?:encoding ".+")?/,end:/;/},{beginKeywords:"element attribute comment document processing-instruction",end:"{",excludeEnd:!0},{begin:/<([\w\._:\-]+)((\s*.*)=('|").*('|"))?>/,end:/(\/[\w\._:\-]+>)/,subLanguage:"xml",contains:[{begin:"{",end:"}",subLanguage:"xquery"},"self"]}];return{aliases:["xpath","xq"],case_insensitive:!1,lexemes:/[a-zA-Z\$][a-zA-Z0-9_:\-]*/,illegal:/(proc)|(abstract)|(extends)|(until)|(#)/,keywords:{keyword:"module schema namespace boundary-space preserve no-preserve strip default collation base-uri ordering context decimal-format decimal-separator copy-namespaces empty-sequence except exponent-separator external grouping-separator inherit no-inherit lax minus-sign per-mille percent schema-attribute schema-element strict unordered zero-digit declare import option function validate variable for at in let where order group by return if then else tumbling sliding window start when only end previous next stable ascending descending allowing empty greatest least some every satisfies switch case typeswitch try catch and or to union intersect instance of treat as castable cast map array delete insert into replace value rename copy modify update",type:"item document-node node attribute document element comment namespace namespace-node processing-instruction text construction xs:anyAtomicType xs:untypedAtomic xs:duration xs:time xs:decimal xs:float xs:double xs:gYearMonth xs:gYear xs:gMonthDay xs:gMonth xs:gDay xs:boolean xs:base64Binary xs:hexBinary xs:anyURI xs:QName xs:NOTATION xs:dateTime xs:dateTimeStamp xs:date xs:string xs:normalizedString xs:token xs:language xs:NMTOKEN xs:Name xs:NCName xs:ID xs:IDREF xs:ENTITY xs:integer xs:nonPositiveInteger xs:negativeInteger xs:long xs:int xs:short xs:byte xs:nonNegativeInteger xs:unisignedLong xs:unsignedInt xs:unsignedShort xs:unsignedByte xs:positiveInteger xs:yearMonthDuration xs:dayTimeDuration",literal:"eq ne lt le gt ge is self:: child:: descendant:: descendant-or-self:: attribute:: following:: following-sibling:: parent:: ancestor:: ancestor-or-self:: preceding:: preceding-sibling:: NaN"},contains:CONTAINS}})),hljs.registerLanguage("css",(function(e){var RULE={begin:/(?:[A-Z\_\.\-]+|--[a-zA-Z0-9_-]+)\s*:/,returnBegin:!0,end:";",endsWithParent:!0,contains:[{className:"attribute",begin:/\S/,end:":",excludeEnd:!0,starts:{endsWithParent:!0,excludeEnd:!0,contains:[{begin:/[\w-]+\(/,returnBegin:!0,contains:[{className:"built_in",begin:/[\w-]+/},{begin:/\(/,end:/\)/,contains:[hljs.APOS_STRING_MODE,hljs.QUOTE_STRING_MODE,hljs.CSS_NUMBER_MODE]}]},hljs.CSS_NUMBER_MODE,hljs.QUOTE_STRING_MODE,hljs.APOS_STRING_MODE,hljs.C_BLOCK_COMMENT_MODE,{className:"number",begin:"#[0-9A-Fa-f]+"},{className:"meta",begin:"!important"}]}}]};return{case_insensitive:!0,illegal:/[=\/|'\$]/,contains:[hljs.C_BLOCK_COMMENT_MODE,{className:"selector-id",begin:/#[A-Za-z0-9_-]+/},{className:"selector-class",begin:/\.[A-Za-z0-9_-]+/},{className:"selector-attr",begin:/\[/,end:/\]/,illegal:"$",contains:[hljs.APOS_STRING_MODE,hljs.QUOTE_STRING_MODE]},{className:"selector-pseudo",begin:/:(:)?[a-zA-Z0-9\_\-\+\(\)"'.]+/},{begin:"@(page|font-face)",lexemes:"@[a-z-]+",keywords:"@page @font-face"},{begin:"@",end:"[{;]",illegal:/:/,returnBegin:!0,contains:[{className:"keyword",begin:/@\-?\w[\w]*(\-\w+)*/},{begin:/\s/,endsWithParent:!0,excludeEnd:!0,relevance:0,keywords:"and or not only",contains:[{begin:/[a-z-]+:/,className:"attribute"},hljs.APOS_STRING_MODE,hljs.QUOTE_STRING_MODE,hljs.CSS_NUMBER_MODE]}]},{className:"selector-tag",begin:"[a-zA-Z-][a-zA-Z0-9_-]*",relevance:0},{begin:"{",end:"}",illegal:/\S/,contains:[hljs.C_BLOCK_COMMENT_MODE,RULE]}]}})),hljs.registerLanguage("http",(function(e){return{aliases:["https"],illegal:"\\S",contains:[{begin:"^HTTP/[0-9\\.]+",end:"$",contains:[{className:"number",begin:"\\b\\d{3}\\b"}]},{begin:"^[A-Z]+ (.*?) HTTP/[0-9\\.]+$",returnBegin:!0,end:"$",contains:[{className:"string",begin:" ",end:" ",excludeBegin:!0,excludeEnd:!0},{begin:"HTTP/[0-9\\.]+"},{className:"keyword",begin:"[A-Z]+"}]},{className:"attribute",begin:"^\\w",end:": ",excludeEnd:!0,illegal:"\\n|\\s|=",starts:{end:"$",relevance:0}},{begin:"\\n\\n",starts:{subLanguage:[],endsWithParent:!0}}]}})),hljs.registerLanguage("ini",(function(e){var NUMBERS={className:"number",relevance:0,variants:[{begin:/([\+\-]+)?[\d]+_[\d_]+/},{begin:hljs.NUMBER_RE}]},COMMENTS=hljs.COMMENT();COMMENTS.variants=[{begin:/;/,end:/$/},{begin:/#/,end:/$/}];var VARIABLES={className:"variable",variants:[{begin:/\$[\w\d"][\w\d_]*/},{begin:/\$\{(.*?)}/}]},LITERALS={className:"literal",begin:/\bon|off|true|false|yes|no\b/},STRINGS={className:"string",contains:[hljs.BACKSLASH_ESCAPE],variants:[{begin:"'''",end:"'''",relevance:10},{begin:'"""',end:'"""',relevance:10},{begin:'"',end:'"'},{begin:"'",end:"'"}]};return{aliases:["toml"],case_insensitive:!0,illegal:/\S/,contains:[COMMENTS,{className:"section",begin:/\[+/,end:/\]+/},{begin:/^[a-z0-9\[\]_\.-]+(?=\s*=\s*)/,className:"attr",starts:{end:/$/,contains:[COMMENTS,{begin:/\[/,end:/\]/,contains:[COMMENTS,LITERALS,VARIABLES,STRINGS,NUMBERS,"self"],relevance:0},LITERALS,VARIABLES,STRINGS,NUMBERS]}}]}})),hljs.registerLanguage("json",(function(e){var LITERALS={literal:"true false null"},ALLOWED_COMMENTS=[hljs.C_LINE_COMMENT_MODE,hljs.C_BLOCK_COMMENT_MODE],TYPES=[hljs.QUOTE_STRING_MODE,hljs.C_NUMBER_MODE],VALUE_CONTAINER={end:",",endsWithParent:!0,excludeEnd:!0,contains:TYPES,keywords:LITERALS},OBJECT={begin:"{",end:"}",contains:[{className:"attr",begin:/"/,end:/"/,contains:[hljs.BACKSLASH_ESCAPE],illegal:"\\n"},hljs.inherit(VALUE_CONTAINER,{begin:/:/})].concat(ALLOWED_COMMENTS),illegal:"\\S"},ARRAY={begin:"\\[",end:"\\]",contains:[hljs.inherit(VALUE_CONTAINER)],illegal:"\\S"};return TYPES.push(OBJECT,ARRAY),ALLOWED_COMMENTS.forEach((function(rule){TYPES.push(rule)})),{contains:TYPES,keywords:LITERALS,illegal:"\\S"}}));