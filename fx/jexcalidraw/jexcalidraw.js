import { FxElement, html, css } from '../../fx.js';

const libPath = import.meta.url.split('/').slice(0, -1).join('/') + '/lib/';

customElements.define('fx-jexcalidraw', class FxJExcalidraw extends FxElement {
    static get styles() {
        return css`
            :host {
                overflow: hidden;
                position: relative;
                width: 100%;
                height: 100%;
            }
            iframe {
                border: none;
                outline: none;
                padding: 0;
                margin: 0;
                width: 100%;
                height: 100%;
                opacity: 0;
            }
        `
    }

    render() {
        return html`
            <iframe style="width: ${this.width || '0'};"></iframe>
        `
    }

    static get properties() {
        return {
            langCode: { type: String, default: 'en-EN', list: ['en-EN', 'ru-RU'] },
            src: { type: String, default: '' },
            value: { type: String, default: '' },
            data: { type: Object, notify: true },
            lib: { type: Object, notify: true },
            cell: { type: Object, default: {}, notify: 'cell_changed' },
        }
    }
    get value() {
        if (this.data)
            return JSON.stringify(this.data) || '';
        return '';
    }
    set value(v) {
        if (v) {
            this._value = v;
            this.data = JSON.parse(v);
        } else {
            this.data = { elements: [] };
        }
    }
    data_changed(v) {
        this.isReady && n && this.excalidrawAPI?.updateScene(n);
    }
    lib_changed(v) {
        this.isReady && n && this.excalidrawAPI?.updateLibrary({ libraryItems: n });
    }
    src_changed(v) {
        this.value = v;
    }
    cell_changed(e) {
        if (this.cell?.source !== undefined)
            this.value = this.cell.source;
    }

    firstUpdated() {
        super.firstUpdated();
        this.iframe = this.$qs('iframe');
        langCode = this.langCode || 'en-EN';
        this.iframe.addEventListener('load', () => {
            this.iframe.contentDocument.addEventListener('excalidrawApi', (e) => {
                this.excalidrawAPI = e.detail.api;
                if (this.data) {
                    this.excalidrawAPI.addFiles(Object.values(this.data.files || []));
                    this.excalidrawAPI.updateScene(this.data);
                    setTimeout(() => {
                        this.excalidrawAPI.scrollToContent();
                        setTimeout(() => {
                            this.iframe.style.opacity = 1;
                            this.$update();
                        }, 100)
                    }, 100)
                }
                if (this.lib)
                    this.excalidrawAPI.updateLibrary({ libraryItems: this.lib });
                this.width = '100%';
                this.$update();
                setTimeout(() => this.isReady = true, 1000);
            }, { once: true })
            this.iframe.contentDocument.addEventListener('excalidrawChange', (e) => {
                if (this.isReady) {
                    FX.debounce('excalidrawChange', () => {
                        const appState = {
                            viewBackgroundColor: e.detail.appState.viewBackgroundColor,
                            theme: e.detail.appState.theme,
                            zenModeEnabled: e.detail.appState.zenModeEnabled,
                            viewModeEnabled: e.detail.appState.viewModeEnabled,
                            gridSize: e.detail.appState.gridSize,
                        }
                        const val = JSON.stringify({ appState, elements: e.detail.elements, files: e.detail.files });
                        if (this._value !== val) {
                            this._value = val;
                            if (this.cell)
                                this.cell.source = val;
                            if (this.item)
                                this.item.value = val;
                            this.fire('change', val);
                        }
                    }, 300)
                }
            })
        })
        this.iframe.srcdoc = srcdoc();
    }
})

let langCode = 'en-EN';
const srcdoc = () => {
    return `
<meta charset="UTF-8" />
<script src="${libPath}react.production.min.js"></script>
<script src="${libPath}react-dom.production.min.js"></script>
<script src="${libPath}excalidraw.production.min.js"></script>
<style>
    html, body { width: 100%; height: 100%; padding: 0; margin: 0; position: relative;}
    /* .dropdown-menu-group { display: none; } */
</style>
<div id="app"></div>
<script type="module">
    const App = () => {
        return React.createElement(
            React.Fragment,
            null,
            React.createElement(
                "div", { style: { height: "100vh", minWidth: "100vw" } },
                React.createElement(ExcalidrawLib.Excalidraw, {
                    langCode: "${langCode || 'en-EN'}",
                    ref: (api) => setTimeout(() => document.dispatchEvent(new CustomEvent("excalidrawApi", { detail: { api } })), 300),
                    onChange: (elements, appState, files) => document.dispatchEvent(new CustomEvent("excalidrawChange", { detail: { elements, appState, files } }))
                })
            )
        )
    }
    const excalidrawWrapper = document.getElementById("app");
    const root = ReactDOM.createRoot(excalidrawWrapper);
    root.render(React.createElement(App));
</script>
`
}
