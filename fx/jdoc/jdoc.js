import { FxElement, html } from '/fx.js';
import { $styles } from './jdoc.x.js';
import '../button/button.js';

customElements.define('fx-jdoc', class FxJDoc extends FxElement {
    static properties = {
        url: { type: String, default: '', notify: true },
        type: { type: String, default: 'auto' }, // auto, doc, excel, ppt
        cell: { type: Object, default: undefined, notify: true },
        base: { type: Object },
        isReady: { type: Boolean, default: false },
        source: { type: String, default: '' }, // Base64 encoded document
        viewerType: { type: String, default: 'office', save: true } // office, google, onlyoffice
    }

    get docType() {
        if (this.type !== 'auto') return this.type;
        
        const url = this.url || this.cell?.url || '';
        const ext = url.split('.').pop().toLowerCase();
        
        if (['doc', 'docx'].includes(ext)) return 'doc';
        if (['xls', 'xlsx'].includes(ext)) return 'excel';
        if (['ppt', 'pptx'].includes(ext)) return 'ppt';
        
        return 'unknown';
    }

    get viewerUrl() {
        // Если есть source (загруженный файл), создаем Blob URL
        if (this.source) {
            if (!this._blobUrl) {
                const binary = atob(this.source.split(',')[1]);
                const array = [];
                for (let i = 0; i < binary.length; i++) {
                    array.push(binary.charCodeAt(i));
                }
                const blob = new Blob([new Uint8Array(array)], {type: 'application/octet-stream'});
                this._blobUrl = URL.createObjectURL(blob);
            }
            return this._blobUrl;
        }

        const url = this.url || this.cell?.url || '';
        
        // Проверяем, локальный ли это URL или из локальной сети
        if (url.startsWith('file:') || this.isLocalNetworkUrl(url)) {
            // Для локальных файлов используем ONLYOFFICE
            return this.getOnlyOfficeViewerUrl(url);
        }
        
        // Для публичных URL используем выбранный просмотрщик
        if (this.viewerType === 'google') {
            return `https://docs.google.com/viewer?url=${encodeURIComponent(url)}&embedded=true`;
        } else if (this.viewerType === 'onlyoffice') {
            return this.getOnlyOfficeViewerUrl(url);
        } else {
            return `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(url)}`;
        }
    }
    
    isLocalNetworkUrl(url) {
        try {
            const parsedUrl = new URL(url);
            const hostname = parsedUrl.hostname;
            return hostname === 'localhost' || 
                   hostname === '127.0.0.1' || 
                   hostname.startsWith('192.168.') || 
                   hostname.startsWith('10.') || 
                   hostname.endsWith('.local');
        } catch (e) {
            return false;
        }
    }
    
    getOnlyOfficeViewerUrl(url) {
        // Используем прокси для загрузки файлов в ONLYOFFICE
        const proxyUrl = 'http://localhost:3001';
        
        // Перенаправляем на прокси, который загрузит файл и откроет его в ONLYOFFICE
        return `${proxyUrl}/proxy?url=${encodeURIComponent(url)}`;
    }

    async firstUpdated() {
        super.firstUpdated();
        
        // Если у нас есть cell с attachments, получаем документ из PouchDB
        if (this.cell?.attachments) {
            await this.loadAttachment();
        }
        
        this.isReady = true;
    }
    
    async loadAttachment() {
        try {
            if (!this.base?.dbLocal) return;
            
            let db = this.base.dbLocal;
            let _id = this.cell?._idFile || 'file:' + this.base.selected?._id + '-' + this.cell?.ulid;
            
            let doc;
            try { 
                doc = await db.get(_id, { attachments: true });
            } catch (error) {
                // Пробуем альтернативный ID
                _id = 'file:' + this.base.selected?._id;
                try { 
                    doc = await db.get(_id, { attachments: true });
                } catch (error) { }
            }
            
            if (doc && doc._attachments?.file) {
                this.source = 'data:' + doc._attachments.file.content_type + ';base64,' + doc._attachments.file.data;
                this.$update();
            }
        } catch (error) {
            console.error('Error loading attachment:', error);
        }
    }
    
    async loadFile(e) {
        const file = e.target?.files[0];
        if (!file) return;
        
        this.url = file.name;
        const reader = new FileReader();
        
        reader.onload = async (e) => {
            this.source = e.target.result;
            
            // Если есть cell и база данных, сохраняем как вложение
            if (this.cell && this.base?.dbLocal) {
                await this.saveAttachment(file);
            }
            
            this.$update();
        };
        
        reader.readAsDataURL(file);
    }
    
    async saveAttachment(file) {
        if (!this.base?.dbLocal || !this.base?.selected?._id) return;
        
        try {
            const db = this.base.dbLocal;
            const _id = this.cell._idFile = 'file:' + this.base.selected._id + '-' + (this.cell.ulid || FX.ulid());
            
            // Создаем документ с вложением
            let attachmentDoc = {
                _id,
                _attachments: {
                    'file': {
                        content_type: file.type || 'application/octet-stream',
                        data: this.source.split(',')[1]
                    }
                }
            };
            
            // Проверяем, существует ли документ
            try {
                const existingDoc = await db.get(_id);
                if (existingDoc._rev) {
                    attachmentDoc._rev = existingDoc._rev;
                }
            } catch (error) { }
            
            // Сохраняем документ
            await db.put(attachmentDoc);
            
            // Обновляем cell
            this.cell.attachments = true;
            this.cell.url = file.name;
            
            this.$update();
        } catch (error) {
            console.error('Error saving attachment:', error);
        }
    }
    
    switchViewer() {
        // Циклически переключаем между просмотрщиками
        if (this.viewerType === 'office') {
            this.viewerType = 'google';
        } else if (this.viewerType === 'google') {
            this.viewerType = 'onlyoffice';
        } else {
            this.viewerType = 'office';
        }
        
        this._blobUrl = null; // Сбрасываем blob URL при смене просмотрщика
        this.$update();
    }
    
    openInNewTab() {
        window.open(this.viewerUrl, '_blank');
    }
    
    async handleProxyResponse(url) {
        try {
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                // Открываем ONLYOFFICE с загруженным файлом
                window.open(data.onlyofficeUrl, '_blank');
                return true;
            } else {
                console.error('Error from proxy:', data.error);
                return false;
            }
        } catch (error) {
            console.error('Error calling proxy:', error);
            return false;
        }
    }
    
    render() {
        if (!this.isReady) {
            return html`<div class="loading">Loading document viewer...</div>`;
        }
        
        if (!this.url && !this.cell?.url && !this.source) {
            return html`
                <div class="drop-area">
                    <div class="upload-prompt">
                        <fx-icon url="fx:upload" size="48"></fx-icon>
                        <p>Drag & drop a document or click to upload</p>
                        <input type="file" @change=${this.loadFile} accept=".doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.txt,.csv">
                    </div>
                </div>
            `;
        }
        
        // Для ONLYOFFICE используем прокси и открываем в новом окне
        if (this.viewerType === 'onlyoffice') {
            const proxyUrl = this.getOnlyOfficeViewerUrl(this.url || this.cell?.url);
            this.handleProxyResponse(proxyUrl);
            
            return html`
                <div class="onlyoffice-redirect">
                    <p>Opening document in ONLYOFFICE...</p>
                    <fx-button @click=${() => window.open(proxyUrl, '_blank')}>
                        Open manually
                    </fx-button>
                </div>
            `;
        }
        
        // Для других просмотрщиков используем стандартный iframe
        return html`
            <div class="viewer-container flex column">
                <div class="toolbar">
                    <fx-button name="carbon:swap-vertical" @click=${this.switchViewer} 
                              title=${`Switch to ${this.viewerType === 'office' ? 'Google' : this.viewerType === 'google' ? 'ONLYOFFICE' : 'Office'} viewer`}></fx-button>
                    <fx-button name="carbon:launch" @click=${this.openInNewTab} title="Open in new tab"></fx-button>
                </div>
                <iframe 
                    class="flex w100" 
                    src=${this.viewerUrl}
                    frameborder="0"
                    allowfullscreen>
                </iframe>
            </div>
        `;
    }

    static styles = [$styles]
});
