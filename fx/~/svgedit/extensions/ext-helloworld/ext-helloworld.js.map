{"version": 3, "file": "ext-helloworld.js", "sources": ["../../../../src/editor/extensions/ext-helloworld/ext-helloworld.js", "../../../../src/editor/extensions/ext-helloworld/locale/en.js", "../../../../src/editor/extensions/ext-helloworld/locale/fr.js", "../../../../src/editor/extensions/ext-helloworld/locale/tr.js", "../../../../src/editor/extensions/ext-helloworld/locale/zh-CN.js"], "sourcesContent": ["/**\n * @file ext-helloworld.js\n *\n * @license MIT\n *\n * @copyright 2010 <PERSON>\n *\n */\n\n/**\n* This is a very basic SVG-Edit extension. It adds a \"Hello World\" button in\n*  the left (\"mode\") panel. Clicking on the button, and then the canvas\n*  will show the user the point on the canvas that was clicked on.\n*/\n\nconst name = 'helloworld'\n\nconst loadExtensionTranslation = async function (svgEditor) {\n  let translationModule\n  const lang = svgEditor.configObj.pref('lang')\n  try {\n    translationModule = await import(`./locale/${lang}.js`)\n  } catch (_error) {\n    console.warn(`Missing translation (${lang}) for ${name} - using 'en'`)\n    translationModule = await import('./locale/en.js')\n  }\n  svgEditor.i18next.addResourceBundle(lang, name, translationModule.default)\n}\n\nexport default {\n  name,\n  async init ({ _importLocale }) {\n    const svgEditor = this\n    await loadExtensionTranslation(svgEditor)\n    const { svgCanvas } = svgEditor\n    const { $id, $click } = svgCanvas\n    return {\n      name: svgEditor.i18next.t(`${name}:name`),\n      callback () {\n        // Add the button and its handler(s)\n        const buttonTemplate = document.createElement('template')\n        const title = `${name}:buttons.0.title`\n        buttonTemplate.innerHTML = `\n        <se-button id=\"hello_world\" title=\"${title}\" src=\"hello_world.svg\"></se-button>\n        `\n        $id('tools_left').append(buttonTemplate.content.cloneNode(true))\n        $click($id('hello_world'), () => {\n          svgCanvas.setMode('hello_world')\n        })\n      },\n      // This is triggered when the main mouse button is pressed down\n      // on the editor canvas (not the tool panels)\n      mouseDown () {\n        // Check the mode on mousedown\n        if (svgCanvas.getMode() === 'hello_world') {\n          // The returned object must include \"started\" with\n          // a value of true in order for mouseUp to be triggered\n          return { started: true }\n        }\n        return undefined\n      },\n\n      // This is triggered from anywhere, but \"started\" must have been set\n      // to true (see above). Note that \"opts\" is an object with event info\n      mouseUp (opts) {\n        // Check the mode on mouseup\n        if (svgCanvas.getMode() === 'hello_world') {\n          const zoom = svgCanvas.getZoom()\n\n          // Get the actual coordinate by dividing by the zoom value\n          const x = opts.mouse_x / zoom\n          const y = opts.mouse_y / zoom\n\n          // We do our own formatting\n          const text = svgEditor.i18next.t(`${name}:text`, { x, y })\n          // Show the text using the custom alert function\n          alert(text)\n        }\n      }\n    }\n  }\n}\n", "export default {\n  name: 'Hello World',\n  text: 'Hello World!\\n\\nYou clicked here: {{x}}, {{y}}',\n  buttons: [\n    {\n      title: \"Say 'Hello World'\"\n    }\n  ]\n}\n", "export default {\n  name: '<PERSON><PERSON><PERSON> le Monde',\n  text: '<PERSON><PERSON><PERSON> le Monde!\\n\\nVous avez cliqué ici: {{x}}, {{y}}',\n  buttons: [\n    {\n      title: \"Dire 'Bonjour le Monde'\"\n    }\n  ]\n}\n", "export default {\n  name: '<PERSON><PERSON><PERSON><PERSON> Dünya',\n  text: 'Merhaba Dünya!\\n\\nBuraya Tıkladınız: {{x}}, {{y}}',\n  buttons: [\n    {\n      title: \"'Merhaba Dünya' De\"\n    }\n  ]\n}\n", "export default {\n  name: 'Hello World',\n  text: 'Hello World!\\n\\n 请点击: {{x}}, {{y}}',\n  buttons: [\n    {\n      title: \"输出 'Hello World'\"\n    }\n  ]\n}\n"], "names": ["name", "loadExtensionTranslation", "async", "svgEditor", "translationModule", "lang", "config<PERSON><PERSON><PERSON>", "pref", "__variableDynamicImportRuntime0__", "concat", "_error", "console", "warn", "Promise", "resolve", "then", "en$1", "i18next", "addResourceBundle", "default", "extHelloworld", "_ref", "this", "svgCanvas", "$id", "$click", "t", "callback", "buttonTemplate", "document", "createElement", "title", "innerHTML", "append", "content", "cloneNode", "setMode", "mouseDown", "getMode", "started", "mouseUp", "opts", "zoom", "getZoom", "x", "mouse_x", "y", "mouse_y", "text", "alert", "buttons"], "mappings": ";;;;;;;;AAeA,MAAMA,EAAO,aAEPC,yBAA2BC,eAAgBC,GAC/C,IAAIC,EACJ,MAAMC,EAAOF,EAAUG,UAAUC,KAAK,QACtC,IACEH,0hBAA0BI,CAAM,YAAAC,OAAaJ,EAA7C,QACA,MAAOK,GACPC,QAAQC,KAA6BP,wBAAAA,OAAAA,mBAAaL,EAAlD,kBACAI,QAA0BS,QAAAC,UAAAC,MAAA,WAAA,OAAAC,KAE5Bb,EAAUc,QAAQC,kBAAkBb,EAAML,EAAMI,EAAkBe,UAGpE,IAAeC,EAAA,CACbpB,KAAAA,EACAE,WAA+BmB,GAC7B,MAAMlB,EAAYmB,WACZrB,yBAAyBE,GAC/B,MAAMoB,UAAEA,GAAcpB,GAChBqB,IAAEA,EAAFC,OAAOA,GAAWF,EACxB,MAAO,CACLvB,KAAMG,EAAUc,QAAQS,EAAlB,GAAAjB,OAAuBT,EADxB,UAEL2B,WAEE,MAAMC,EAAiBC,SAASC,cAAc,YACxCC,EAAW/B,GAAAA,OAAAA,EAAjB,oBACA4B,EAAeI,UAAf,gDAAAvB,OACqCsB,EADrC,kDAGAP,EAAI,cAAcS,OAAOL,EAAeM,QAAQC,WAAU,IAC1DV,EAAOD,EAAI,gBAAgB,KACzBD,EAAUa,QAAQ,mBAKtBC,YAEE,GAA4B,gBAAxBd,EAAUe,UAGZ,MAAO,CAAEC,SAAS,IAOtBC,QAASC,GAEP,GAA4B,gBAAxBlB,EAAUe,UAA6B,CACzC,MAAMI,EAAOnB,EAAUoB,UAGjBC,EAAIH,EAAKI,QAAUH,EACnBI,EAAIL,EAAKM,QAAUL,EAGnBM,EAAO7C,EAAUc,QAAQS,EAAK1B,GAAAA,OAAAA,EAAa,SAAA,CAAE4C,EAAAA,EAAGE,EAAAA,IAEtDG,MAAMD,gDC5ED,CACbhD,KAAM,cACNgD,KAAM,iDACNE,QAAS,CACP,CACEnB,MAAO,iECLE,CACb/B,KAAM,mBACNgD,KAAM,0DACNE,QAAS,CACP,CACEnB,MAAO,uECLE,CACb/B,KAAM,gBACNgD,KAAM,oDACNE,QAAS,CACP,CACEnB,MAAO,kECLE,CACb/B,KAAM,cACNgD,KAAM,qCACNE,QAAS,CACP,CACEnB,MAAO"}