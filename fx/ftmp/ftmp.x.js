import { css } from '/fx.js';

export const $styles = {
    ftmp: css`
        :host {
            position: relative;
            max-width: 100%;
            opacity: 0;
            
        }
        .form {
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            flex: 1;
            color: gray;
            box-sizing: border-box;
        }
        .tab:hover {
            filter: brightness(.8);
        }
    `
}
 

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;
// const usedIcons = {
// }
// FX.setIcons(usedIcons);
