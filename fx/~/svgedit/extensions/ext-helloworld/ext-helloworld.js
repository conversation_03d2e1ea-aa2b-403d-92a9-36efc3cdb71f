/**
 * @file ext-helloworld.js
 *
 * @license MIT
 *
 * @copyright 2010 <PERSON>
 *
 */
const e="helloworld",loadExtensionTranslation=async function(t){let a;const c=t.configObj.pref("lang");try{a=await function __variableDynamicImportRuntime0__(e){switch(e){case"./locale/en.js":return Promise.resolve().then((function(){return n}));case"./locale/fr.js":return Promise.resolve().then((function(){return o}));case"./locale/tr.js":return Promise.resolve().then((function(){return l}));case"./locale/zh-CN.js":return Promise.resolve().then((function(){return r}));default:return new Promise((function(t,n){("function"==typeof queueMicrotask?queueMicrotask:setTimeout)(n.bind(null,new Error("Unknown variable dynamic import: "+e)))}))}}("./locale/".concat(c,".js"))}catch(t){console.warn("Missing translation (".concat(c,") for ").concat(e," - using 'en'")),a=await Promise.resolve().then((function(){return n}))}t.i18next.addResourceBundle(c,e,a.default)};var t={name:e,async init(t){const n=this;await loadExtensionTranslation(n);const{svgCanvas:o}=n,{$id:l,$click:r}=o;return{name:n.i18next.t("".concat(e,":name")),callback(){const t=document.createElement("template"),n="".concat(e,":buttons.0.title");t.innerHTML='\n        <se-button id="hello_world" title="'.concat(n,'" src="hello_world.svg"></se-button>\n        '),l("tools_left").append(t.content.cloneNode(!0)),r(l("hello_world"),(()=>{o.setMode("hello_world")}))},mouseDown(){if("hello_world"===o.getMode())return{started:!0}},mouseUp(t){if("hello_world"===o.getMode()){const l=o.getZoom(),r=t.mouse_x/l,a=t.mouse_y/l,c=n.i18next.t("".concat(e,":text"),{x:r,y:a});alert(c)}}}}},n=Object.freeze({__proto__:null,default:{name:"Hello World",text:"Hello World!\n\nYou clicked here: {{x}}, {{y}}",buttons:[{title:"Say 'Hello World'"}]}}),o=Object.freeze({__proto__:null,default:{name:"Bonjour le Monde",text:"Bonjour le Monde!\n\nVous avez cliqué ici: {{x}}, {{y}}",buttons:[{title:"Dire 'Bonjour le Monde'"}]}}),l=Object.freeze({__proto__:null,default:{name:"Merhaba Dünya",text:"Merhaba Dünya!\n\nBuraya Tıkladınız: {{x}}, {{y}}",buttons:[{title:"'Merhaba Dünya' De"}]}}),r=Object.freeze({__proto__:null,default:{name:"Hello World",text:"Hello World!\n\n 请点击: {{x}}, {{y}}",buttons:[{title:"输出 'Hello World'"}]}});export{t as default};
//# sourceMappingURL=ext-helloworld.js.map
