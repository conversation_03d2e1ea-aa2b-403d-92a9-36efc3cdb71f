import { FxElement, html, css } from '../../fx.js';

customElements.define('fx-rating', class FxRating extends FxElement {

    static get styles() {
        return css`
            .rating {
                display: inline-block;
                font-size: 0;
                cursor: pointer;
                padding: 2px;
            }
            .rating span {
                padding: 0;
                line-height: 1;
                color: lightgrey;
            }
        `;
    }

    render() {
        return html`
            <style>
                .rating > span { color: ${this.color0}; }
                .rating > span.active { color: ${this.color1}; }
            </style>
            <div class="rating ${this.type}" style="font-size: ${this.size || 16}px">
                ${[...Array(this.length || 5).keys()].map((i, idx) => html`
                    <span class=${idx < (this._over ? this._value : this.value) ? 'active' : ''}
                        @click=${() => this._click(idx)}
                        @pointerover=${() => this._pointerover(idx)}
                        @pointerout=${this._pointerout}
                        style="color: ${idx === this.idx ? this.colorIdx : ''}"
                    >${this.symbol}</span>
                `)}
            </div>
        `
    }

    static get properties() {
        return {
            _useInfo: { type: Boolean, default: true },
            readOnly: { type: Boolean },
            value: { type: Number, default: 0 },
            length: { type: Number, default: 5 },
            size: { type: Number, default: 16 },
            min: { type: Number, default: 0 },
            symbol: { type: String, default: '★', list: ['★', '●', '⁕', '⏹', '❤', '⭘'] },
            color0: { type: String, default: 'transparent' },
            color1: { type: String, default: 'gold' },
            idx: { type: Number, default: -1 },
            colorIdx: { type: String, default: 'red' }
        }
    }

    _click(idx) {
        if (this.readOnly) return;
        this.value = this.value === idx + 1 ? idx : idx + 1;
        this.value = this.value < this.min ? this.min : this.value;
        this.fire('change', this.value);
    }
    _pointerover(idx) {
        this._over = true;
        this._value = idx + 1;
        this.$update();
    }
    _pointerout() {
        this._over = false;
        this.$update();
    }

})
