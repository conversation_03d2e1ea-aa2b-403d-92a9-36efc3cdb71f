<merge> ::= <object-merge> | <class-merge> | <function-merge>

<object-merge> ::= "export default" <object>
<object> ::= "{" <properties> "}"
<properties> ::= <property> | <property> "," <properties>
<property> ::= <key> ":" <value> | "$public" ":" <object>
<key> ::= <identifier>
<value> ::= <string> | <number> | <object> | <array>
<array> ::= "[" <value-list> "]"
<value-list> ::= <value> | <value> "," <value-list>

<class-merge> ::= "class" <identifier> "extends" <identifier> "{" <class-body> "}"
<class-body> ::= <method> | <method> <class-body>
<method> ::= <constructor> | <getter> | <setter>
<constructor> ::= "constructor" "(" <params> ")" "{" <statements> "}"
<getter> ::= "get" <identifier> "(" ")" "{" <statements> "}"
<setter> ::= "set" <identifier> "(" <param> ")" "{" <statements> "}"
<params> ::= <param> | <param> "," <params>
<param> ::= <identifier>
<statements> ::= <statement> | <statement> <statements>
<statement> ::= <expression> ";" | "return" <expression> ";"
<expression> ::= <identifier> | <literal> | <binary-expression> | <object>
<binary-expression> ::= <expression> <operator> <expression>
<operator> ::= "+" | "-" | "*" | "/"

<function-merge> ::= "function" <identifier> "(" <params> ")" "{" <statements> "}"
