/**
 * @file ext-eyedropper.js
 *
 * @license MIT
 *
 * @copyright 2010 <PERSON>
 * @copyright 2021 OptimistikSAS
 *
 */
const e="eyedropper",loadExtensionTranslation=async function(t){let a;const s=t.configObj.pref("lang");try{a=await function __variableDynamicImportRuntime0__(e){switch(e){case"./locale/en.js":return Promise.resolve().then((function(){return o}));case"./locale/fr.js":return Promise.resolve().then((function(){return r}));case"./locale/tr.js":return Promise.resolve().then((function(){return n}));case"./locale/zh-CN.js":return Promise.resolve().then((function(){return i}));default:return new Promise((function(t,o){("function"==typeof queueMicrotask?queueMicrotask:setTimeout)(o.bind(null,new Error("Unknown variable dynamic import: "+e)))}))}}("./locale/".concat(s,".js"))}catch(t){console.warn("Missing translation (".concat(s,") for ").concat(e," - using 'en'")),a=await Promise.resolve().then((function(){return o}))}t.i18next.addResourceBundle(s,e,a.default)};var t={name:e,async init(){const t=this,{svgCanvas:o}=t;await loadExtensionTranslation(t);const{ChangeElementCommand:r}=o.history,n={fillPaint:"red",fillOpacity:1,strokePaint:"black",strokeOpacity:1,strokeWidth:5,strokeDashArray:null,opacity:1,strokeLinecap:"butt",strokeLinejoin:"miter"},{$id:i,$click:a}=o,getStyle=e=>{if("eyedropper"===o.getMode())return;const t=i("tool_eyedropper");let r=null;e.multiselected||!e.elems[0]||["svg","g","use"].includes(e.elems[0].nodeName)?t.classList.add("disabled"):(r=e.elems[0],t.classList.remove("disabled"),n.fillPaint=r.getAttribute("fill")||"black",n.fillOpacity=r.getAttribute("fill-opacity")||1,n.strokePaint=r.getAttribute("stroke"),n.strokeOpacity=r.getAttribute("stroke-opacity")||1,n.strokeWidth=r.getAttribute("stroke-width"),n.strokeDashArray=r.getAttribute("stroke-dasharray"),n.strokeLinecap=r.getAttribute("stroke-linecap"),n.strokeLinejoin=r.getAttribute("stroke-linejoin"),n.opacity=r.getAttribute("opacity")||1)};return{name:t.i18next.t("".concat(e,":name")),callback(){const t="".concat(e,":buttons.0.title"),r="".concat(e,":buttons.0.key"),n='\n        <se-button id="tool_eyedropper" title="'.concat(t,'" src="eye_dropper.svg" shortcut=').concat(r,"></se-button>\n        ");o.insertChildAtIndex(i("tools_left"),n,12),a(i("tool_eyedropper"),(()=>{this.leftPanel.updateLeftPanel("tool_eyedropper")&&o.setMode("eyedropper")}))},selectedChanged:getStyle,elementChanged:getStyle,mouseDown(e){if("eyedropper"===o.getMode()){const i=e.event,{target:a}=i;if(!["svg","g","use"].includes(a.nodeName)){const e={},change=function(t,o,r){e[o]=t.getAttribute(o),t.setAttribute(o,r)};n.fillPaint&&change(a,"fill",n.fillPaint),n.fillOpacity&&change(a,"fill-opacity",n.fillOpacity),n.strokePaint&&change(a,"stroke",n.strokePaint),n.strokeOpacity&&change(a,"stroke-opacity",n.strokeOpacity),n.strokeWidth&&change(a,"stroke-width",n.strokeWidth),n.strokeDashArray&&change(a,"stroke-dasharray",n.strokeDashArray),n.opacity&&change(a,"opacity",n.opacity),n.strokeLinecap&&change(a,"stroke-linecap",n.strokeLinecap),n.strokeLinejoin&&change(a,"stroke-linejoin",n.strokeLinejoin),t=new r(a,e),o.undoMgr.addCommandToHistory(t)}}var t}}}},o=Object.freeze({__proto__:null,default:{name:"eyedropper",buttons:[{title:"Eye Dropper Tool",key:"I"}]}}),r=Object.freeze({__proto__:null,default:{name:"pipette",buttons:[{title:"Outil pipette",key:"I"}]}}),n=Object.freeze({__proto__:null,default:{name:"renkseçici",buttons:[{title:"Renk Seçim Aracı",key:"I"}]}}),i=Object.freeze({__proto__:null,default:{name:"滴管",buttons:[{title:"滴管工具",key:"I"}]}});export{t as default};
//# sourceMappingURL=ext-eyedropper.js.map
