import { FxElement, html, css } from '../../fx.js';
import { $styles } from './jexe.x.js';
import '../splitter/splitter.js';
import '../button/button.js';
import '../dropdown/dropdown.js';
import '../jace/jace.js';

customElements.define('fx-jexe', class FxJExe extends FxElement {
    static properties = {
        readOnly: { type: Boolean, global: true },
        idx: { type: Number },
        cell: { type: Object, default: {} },
        mode: { type: String, default: 'html' },
        editedIndex: { type: Number, local: true, notify: true },
        is: { type: String, default: 'exe' },
        notebook: { type: Object }
    }
    get ace() { return this.$qs('fx-jace') }
    get iframe() { return this.$qs('iframe') }
    get srcdoc() {
        return `
<style>
    ${this.cell?.sourceCSS || ''}
</style>
${this.cell?.sourceHTML || ''}
<script type="module">
    ${this.sourceJSON}
    ${this.cell?.sourceJS || ''}
</script>
        `
    }
    get sourceJSON() {
        if (this.cell?.useJson || this.cell?.sourceJSON) return `
let json = top.FX.icaro(${this._sourceJSON || '{}'});
top.FX.icaroListen(json, e => {
    const detail = JSON.stringify(json, null, 4);
    document.dispatchEvent(new CustomEvent('changeJSON', { detail }));
})
        `
        return '';
    }

    constructor() {
        super();
        this.listen('endSplitterMove', (e) => {
            if (e.detail.direction === 'vertical') {
                this.cell.cell_w = e.detail.w;
                this.cell.cell_w = this.cell.cell_w < 3 ? 0 : this.cell.cell_w;
                this.w = this.cell.cell_w;
                if (this.notebook)
                    this.$0.fire('changes-jupyter', { type: 'jupyter_cell', change: 'changeCellValue', cell: this.notebook?.cellOwner || this.cell, notebook: this.notebook });
            }
            this.listenIframe(true);
        })
    }
    firstUpdated() {
        super.firstUpdated();
        setTimeout(() => {
            this._sourceJSON = this.cell?.sourceJSON || '{}';
            this.ace.options = { highlightActiveLine: false, showPrintMargin: false, minLines: 1, fontSize: 16 };
            this.setAceValue();
            this.listenIframe(true);
            setTimeout(() => {
                // ToDo sourceHTML = true ???
                // this.listen('change', (e) => {
                //     if (!this.ace.fires) return;
                //     const v = e.detail;
                //     if (this.mode === 'javascript')
                //         this.cell.sourceJS = v || '';
                //     if (this.mode === 'html')
                //         this.cell.sourceHTML = v || '';
                //     if (this.mode === 'css')
                //         this.cell.sourceCSS = v || '';
                //     if (this.mode === 'json')
                //         this._sourceJSON = this.cell.sourceJSON = v || '{}';
                //     this.listenIframe(true);
                // })
                this.listen('cell-changed', () => {
                    if (this.cell?.source !== undefined) {
                        this.cell.sourceHTML = this.cell.source;
                        this.setAceValue();
                        this.listenIframe(true);
                    }
                })
            }, 500)
            top.FX.listen.bind(this, top, 'fx-theme-changed', (e) => {
                this.setTheme(e.detail.theme);
            })
        })
    }

    setTheme(theme = FX._theme) {
        const doc = this.iframe.contentDocument;
        let meta = doc.querySelector('meta[name=color-scheme]');
        if (!meta) {
            meta = doc.createElement('meta');
            meta.name = meta.id = 'color-scheme';
            doc.head.appendChild(meta);
        }
        meta.content = theme;
    }
    aceChange(e) {
        if (!this.ace.fires) return;
        const v = e.detail;
        if (this.mode === 'javascript')
            this.cell.sourceJS = v || '';
        if (this.mode === 'html')
            this.cell.sourceHTML = v || '';
        if (this.mode === 'css')
            this.cell.sourceCSS = v || '';
        if (this.mode === 'json')
            this._sourceJSON = this.cell.sourceJSON = v || '{}';
        this.listenIframe(true);
    }
    updated(changedProperties) {
        if (changedProperties.has('mode')) this.setAceValue();
    }
    setAceValue(mode = this.mode) {
        this.ace.fires = false;
        if (mode === 'javascript')
            this.ace.value = this.cell.sourceJS || '';
        if (mode === 'html')
            this.ace.value = this.cell.sourceHTML || '';
        if (mode === 'css')
            this.ace.value = this.cell.sourceCSS || '';
        if (mode === 'json')
            this.ace.value = this._sourceJSON || this.cell.sourceJSON || '{}';
        setTimeout(() => this.ace.fires = true, 100);
    }
    listenIframe(update) {
        // FX.throttle('listenIframe', () => {
            this.iframe.style.opacity = 0;
            this.iframe.addEventListener('load', () => {
                this.setTheme();
                this.iframe.style.opacity = 1;
                this.iframe.contentDocument?.addEventListener("changeJSON", (e) => {
                    this._sourceJSON = this.cell.sourceJSON = e.detail;
                    if (this.mode === 'json') {
                        this.setAceValue();
                        // console.log('..... changeJSON from iFrame: ', e.detail)
                        this.$update();
                    }
                    // this.$0.fire('changes-jupyter', { type: 'jupyter_cell', change: 'changeCellValue', cell: this.notebook?.cellOwner || this.cell, notebook: this.notebook, jupyter: this.$0 });
                })
            })
            this.iframe.src = update ? URL.createObjectURL(new Blob([this.srcdoc], { type: 'text/html' })) : this.iframe.src;
        // }, 0, true)
    }
    clearAll() {
        if (!window.confirm(`Do you really want clear all editors content ?`)) return;
        this.cell.sourceHTML = this.cell.sourceJS = this.cell.sourceCSS = this._sourceJSON = '';
        this.cell.sourceJSON = '{}';
        this.listenIframe(true);
        this.ace.value = ''
    }
    flip() {
        if (this.cell.cell_w <= 0) {
            this.cell.cell_w = 50;
        } else {
            this.cell.cell_w = 0;
        }
    }

    static styles = [$styles]

    render() {
        return html`
            <div style="position: relative; display: flex; flex-direction: column; overflow: hidden; width: 100%; height: 100%; min-height: 26px; padding: 2px 2px 0 2px;">
                <div style="display: flex; overflow: hidden; width: 100%; height: 100%">
                    <div style="width: ${this.cell?.cell_w === 0 || this.cell?.cell_w > 0 ? this.cell?.cell_w + '%' : '50%'}; overflow: auto" .hidden=${this.cell?.locked}>
                        <div style="display: flex; flex-direction: column; width: 100%; overflow: auto; height: 100%; position: relative">
                            <div class="panel" style="display: flex; padding: 4px;position: sticky; top: 0; z-index: 9">
                                <span @click=${() => this.mode = 'html'} class="${this.mode === 'html' ? 'selected' : ''}">html</span>
                                <span style="padding: 0 8px;" @click=${() => this.mode = 'javascript'} class="${this.mode === 'javascript' ? 'selected' : ''}">js</span>
                                <span @click=${() => this.mode = 'css'} class="${this.mode === 'css' ? 'selected' : ''}">css</span>
                                ${this.cell?.useJson ? html`
                                    <span @click=${() => this.mode = 'json'} class="ml6 ${this.mode === 'json' ? 'selected' : ''}">json</span>
                                ` : html``}
                                <div style="flex: 1"></div>
                                <fx-icon an="btn" br="square" class="ml8" size=20 name="cb-code" @click=${(e) => { this.cell.useJson = !this.cell.useJson; this.$update() }} title="useJSON"></fx-icon>
                                <fx-icon an="btn" br="square" class="ml8" size=20 name="cb-close" @click=${this.clearAll} title="clear all"></fx-icon>
                                <fx-icon an="btn" br="square" class="ml8" size=20 name="carbon:reset" @click=${(e) => { this.listenIframe(true) }} title="refresh"></fx-icon>
                            </div>
                            <fx-jace class="ace" style="width: 100%" theme=${this.mode === 'html' ? 'cobalt' : this.mode === 'javascript' ? 'solarized_light' : this.mode === 'css' ? 'dawn' : 'chrome'} .mode=${this.mode} @change=${this.aceChange}></fx-jace>
                        </div>
                    </div>
                    <fx-splitter right size=${this.cell?.splitterV >= 0 ? this.cell?.splitterV : 2} color="transparent" style="opacity: .3" .hidden=${this.cell?.locked}></fx-splitter>
                    <div style="flex: 1; overflow: hidden; width: 100%;">
                        <iframe style="border: none; width: 100%; height: 100%"></iframe>
                        <fx-button id="flip" name="cb-book" @click=${this.flip} title="flip" .hidden=${this.cell?.locked}></fx-button>
                    </div>
                </div>
            </div>
        `
    }
})

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

const usedIcons =
{
    "cb-settings": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M27 16.76v-1.53l1.92-1.68A2 2 0 0 0 29.3 11l-2.36-4a2 2 0 0 0-1.73-1a2 2 0 0 0-.64.1l-2.43.82a11.35 11.35 0 0 0-1.31-.75l-.51-2.52a2 2 0 0 0-2-1.61h-4.68a2 2 0 0 0-2 1.61l-.51 2.52a11.48 11.48 0 0 0-1.32.75l-2.38-.86A2 2 0 0 0 6.79 6a2 2 0 0 0-1.73 1L2.7 11a2 2 0 0 0 .41 2.51L5 15.24v1.53l-1.89 1.68A2 2 0 0 0 2.7 21l2.36 4a2 2 0 0 0 1.73 1a2 2 0 0 0 .64-.1l2.43-.82a11.35 11.35 0 0 0 1.31.75l.51 2.52a2 2 0 0 0 2 1.61h4.72a2 2 0 0 0 2-1.61l.51-2.52a11.48 11.48 0 0 0 1.32-.75l2.42.82a2 2 0 0 0 .64.1a2 2 0 0 0 1.73-1l2.28-4a2 2 0 0 0-.41-2.51ZM25.21 24l-3.43-1.16a8.86 8.86 0 0 1-2.71 1.57L18.36 28h-4.72l-.71-3.55a9.36 9.36 0 0 1-2.7-1.57L6.79 24l-2.36-4l2.72-2.4a8.9 8.9 0 0 1 0-3.13L4.43 12l2.36-4l3.43 1.16a8.86 8.86 0 0 1 2.71-1.57L13.64 4h4.72l.71 3.55a9.36 9.36 0 0 1 2.7 1.57L25.21 8l2.36 4l-2.72 2.4a8.9 8.9 0 0 1 0 3.13L27.57 20Z\"/><path fill=\"currentColor\" d=\"M16 22a6 6 0 1 1 6-6a5.94 5.94 0 0 1-6 6Zm0-10a3.91 3.91 0 0 0-4 4a3.91 3.91 0 0 0 4 4a3.91 3.91 0 0 0 4-4a3.91 3.91 0 0 0-4-4Z\"/></svg>"
}
FX.setIcons(usedIcons);
