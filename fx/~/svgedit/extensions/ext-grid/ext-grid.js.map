{"version": 3, "file": "ext-grid.js", "sources": ["../../../../src/editor/extensions/ext-grid/ext-grid.js", "../../../../src/editor/extensions/ext-grid/locale/en.js", "../../../../src/editor/extensions/ext-grid/locale/fr.js", "../../../../src/editor/extensions/ext-grid/locale/tr.js", "../../../../src/editor/extensions/ext-grid/locale/zh-CN.js"], "sourcesContent": ["/**\n * @file ext-grid.js\n *\n * @license Apache-2.0\n *\n * @copyright 2010 Redou Mine, 2010 Alexis <PERSON>\n *\n */\n\nconst name = 'grid'\n\nconst loadExtensionTranslation = async function (svgEditor) {\n  let translationModule\n  const lang = svgEditor.configObj.pref('lang')\n  try {\n    translationModule = await import(`./locale/${lang}.js`)\n  } catch (_error) {\n    console.warn(`Missing translation (${lang}) for ${name} - using 'en'`)\n    translationModule = await import('./locale/en.js')\n  }\n  svgEditor.i18next.addResourceBundle(lang, name, translationModule.default)\n}\n\nexport default {\n  name,\n  async init () {\n    const svgEditor = this\n    await loadExtensionTranslation(svgEditor)\n    const { svgCanvas } = svgEditor\n    const { $id, $click, NS } = svgCanvas\n    const svgdoc = $id('svgcanvas').ownerDocument\n    const { assignAttributes } = svgCanvas\n    const hcanvas = document.createElement('canvas')\n    const canvBG = $id('canvasBackground')\n    const units = svgCanvas.getTypeMap() // Assumes prior `init()` call on `units.js` module\n    const intervals = [0.01, 0.1, 1, 10, 100, 1000]\n    let showGrid = svgEditor.configObj.curConfig.showGrid || false\n\n    hcanvas.style.display = 'none'\n    svgEditor.$svgEditor.appendChild(hcanvas)\n\n    const canvasGrid = svgdoc.createElementNS(NS.SVG, 'svg')\n    assignAttributes(canvasGrid, {\n      id: 'canvasGrid',\n      width: '100%',\n      height: '100%',\n      x: 0,\n      y: 0,\n      overflow: 'visible',\n      display: 'none'\n    })\n    canvBG.appendChild(canvasGrid)\n    const gridDefs = svgdoc.createElementNS(NS.SVG, 'defs')\n    // grid-pattern\n    const gridPattern = svgdoc.createElementNS(NS.SVG, 'pattern')\n    assignAttributes(gridPattern, {\n      id: 'gridpattern',\n      patternUnits: 'userSpaceOnUse',\n      x: 0, // -(value.strokeWidth / 2), // position for strokewidth\n      y: 0, // -(value.strokeWidth / 2), // position for strokewidth\n      width: 100,\n      height: 100\n    })\n\n    const gridimg = svgdoc.createElementNS(NS.SVG, 'image')\n    assignAttributes(gridimg, {\n      x: 0,\n      y: 0,\n      width: 100,\n      height: 100\n    })\n    gridPattern.append(gridimg)\n    gridDefs.append(gridPattern)\n    $id('canvasGrid').appendChild(gridDefs)\n\n    // grid-box\n    const gridBox = svgdoc.createElementNS(NS.SVG, 'rect')\n    assignAttributes(gridBox, {\n      width: '100%',\n      height: '100%',\n      x: 0,\n      y: 0,\n      'stroke-width': 0,\n      stroke: 'none',\n      fill: 'url(#gridpattern)',\n      style: 'pointer-events: none; display:visible;'\n    })\n    $id('canvasGrid').appendChild(gridBox)\n\n    /**\n     *\n     * @param {Float} zoom\n     * @returns {void}\n     */\n    const updateGrid = (zoom) => {\n      // TODO: Try this with <line> elements, then compare performance difference\n      const unit = units[svgEditor.configObj.curConfig.baseUnit] // 1 = 1px\n      const uMulti = unit * zoom\n      // Calculate the main number interval\n      const rawM = 100 / uMulti\n      let multi = 1\n      intervals.some((num) => {\n        multi = num\n        return rawM <= num\n      })\n      const bigInt = multi * uMulti\n\n      // Set the canvas size to the width of the container\n      hcanvas.width = bigInt\n      hcanvas.height = bigInt\n      const ctx = hcanvas.getContext('2d')\n      const curD = 0.5\n      const part = bigInt / 10\n\n      ctx.globalAlpha = 0.2\n      ctx.strokeStyle = svgEditor.configObj.curConfig.gridColor\n      for (let i = 1; i < 10; i++) {\n        const subD = Math.round(part * i) + 0.5\n        // const lineNum = (i % 2)?12:10;\n        const lineNum = 0\n        ctx.moveTo(subD, bigInt)\n        ctx.lineTo(subD, lineNum)\n        ctx.moveTo(bigInt, subD)\n        ctx.lineTo(lineNum, subD)\n      }\n      ctx.stroke()\n      ctx.beginPath()\n      ctx.globalAlpha = 0.5\n      ctx.moveTo(curD, bigInt)\n      ctx.lineTo(curD, 0)\n\n      ctx.moveTo(bigInt, curD)\n      ctx.lineTo(0, curD)\n      ctx.stroke()\n\n      const datauri = hcanvas.toDataURL('image/png')\n      gridimg.setAttribute('width', bigInt)\n      gridimg.setAttribute('height', bigInt)\n      gridimg.parentNode.setAttribute('width', bigInt)\n      gridimg.parentNode.setAttribute('height', bigInt)\n      svgCanvas.setHref(gridimg, datauri)\n    }\n\n    /**\n     *\n     * @returns {void}\n     */\n    const gridUpdate = () => {\n      if (showGrid) {\n        updateGrid(svgCanvas.getZoom())\n      }\n      $id('canvasGrid').style.display = (showGrid) ? 'block' : 'none'\n      $id('view_grid').pressed = showGrid\n    }\n    return {\n      name: svgEditor.i18next.t(`${name}:name`),\n      zoomChanged (zoom) {\n        if (showGrid) { updateGrid(zoom) }\n      },\n      callback () {\n        // Add the button and its handler(s)\n        const buttonTemplate = document.createElement('template')\n        const title = `${name}:buttons.0.title`\n        buttonTemplate.innerHTML = `\n          <se-button id=\"view_grid\" title=\"${title}\" src=\"grid.svg\"></se-button>\n        `\n        $id('editor_panel').append(buttonTemplate.content.cloneNode(true))\n        $click($id('view_grid'), () => {\n          svgEditor.configObj.curConfig.showGrid = showGrid = !showGrid\n          gridUpdate()\n        })\n        if (showGrid) {\n          gridUpdate()\n        }\n      }\n    }\n  }\n}\n", "export default {\n  name: 'View Grid',\n  buttons: [\n    {\n      title: 'Show/Hide Grid'\n    }\n  ]\n}\n", "export default {\n  name: 'Grille',\n  buttons: [\n    {\n      title: 'Aff<PERSON>r/Cacher Grille'\n    }\n  ]\n}\n", "export default {\n  name: 'Izgaray<PERSON> Görü<PERSON>',\n  buttons: [\n    {\n      title: '<PERSON><PERSON><PERSON> Gö<PERSON>/Gizle'\n    }\n  ]\n}\n", "export default {\n  name: '网格视图',\n  buttons: [\n    {\n      title: '显示/隐藏网格'\n    }\n  ]\n}\n"], "names": ["loadExtensionTranslation", "async", "svgEditor", "translationModule", "lang", "config<PERSON><PERSON><PERSON>", "pref", "__variableDynamicImportRuntime0__", "concat", "_error", "console", "warn", "Promise", "resolve", "then", "en$1", "i18next", "addResourceBundle", "default", "extGrid", "name", "this", "svgCanvas", "$id", "$click", "NS", "svgdoc", "ownerDocument", "assignAttributes", "hcanvas", "document", "createElement", "canvBG", "units", "getTypeMap", "intervals", "showGrid", "curConfig", "style", "display", "$svgEditor", "append<PERSON><PERSON><PERSON>", "canvasGrid", "createElementNS", "SVG", "id", "width", "height", "x", "y", "overflow", "gridDefs", "gridPattern", "patternUnits", "gridimg", "append", "gridBox", "stroke", "fill", "updateGrid", "zoom", "uMulti", "baseUnit", "rawM", "multi", "some", "num", "bigInt", "ctx", "getContext", "curD", "part", "globalAlpha", "strokeStyle", "gridColor", "i", "subD", "Math", "round", "lineNum", "moveTo", "lineTo", "beginPath", "datauri", "toDataURL", "setAttribute", "parentNode", "set<PERSON><PERSON>f", "gridUpdate", "getZoom", "pressed", "t", "zoomChanged", "callback", "buttonTemplate", "title", "innerHTML", "content", "cloneNode", "buttons"], "mappings": ";;;;;;;;AASA,MAEMA,yBAA2BC,eAAgBC,GAC/C,IAAIC,EACJ,MAAMC,EAAOF,EAAUG,UAAUC,KAAK,QACtC,IACEH,0hBAA0BI,CAAM,YAAAC,OAAaJ,EAA7C,QACA,MAAOK,GACPC,QAAQC,KAA6BP,wBAAAA,OAAAA,mBAR5B,OAQT,kBACAD,QAA0BS,QAAAC,UAAAC,MAAA,WAAA,OAAAC,KAE5Bb,EAAUc,QAAQC,kBAAkBb,EAXzB,OAWqCD,EAAkBe,UAGpE,IAAeC,EAAA,CACbC,KAfW,OAgBXnB,aACE,MAAMC,EAAYmB,WACZrB,yBAAyBE,GAC/B,MAAMoB,UAAEA,GAAcpB,GAChBqB,IAAEA,EAAFC,OAAOA,EAAPC,GAAeA,GAAOH,EACtBI,EAASH,EAAI,aAAaI,eAC1BC,iBAAEA,GAAqBN,EACvBO,EAAUC,SAASC,cAAc,UACjCC,EAAST,EAAI,oBACbU,EAAQX,EAAUY,aAClBC,EAAY,CAAC,IAAM,GAAK,EAAG,GAAI,IAAK,KAC1C,IAAIC,EAAWlC,EAAUG,UAAUgC,UAAUD,WAAY,EAEzDP,EAAQS,MAAMC,QAAU,OACxBrC,EAAUsC,WAAWC,YAAYZ,GAEjC,MAAMa,EAAahB,EAAOiB,gBAAgBlB,EAAGmB,IAAK,OAClDhB,EAAiBc,EAAY,CAC3BG,GAAI,aACJC,MAAO,OACPC,OAAQ,OACRC,EAAG,EACHC,EAAG,EACHC,SAAU,UACVX,QAAS,SAEXP,EAAOS,YAAYC,GACnB,MAAMS,EAAWzB,EAAOiB,gBAAgBlB,EAAGmB,IAAK,QAE1CQ,EAAc1B,EAAOiB,gBAAgBlB,EAAGmB,IAAK,WACnDhB,EAAiBwB,EAAa,CAC5BP,GAAI,cACJQ,aAAc,iBACdL,EAAG,EACHC,EAAG,EACHH,MAAO,IACPC,OAAQ,MAGV,MAAMO,EAAU5B,EAAOiB,gBAAgBlB,EAAGmB,IAAK,SAC/ChB,EAAiB0B,EAAS,CACxBN,EAAG,EACHC,EAAG,EACHH,MAAO,IACPC,OAAQ,MAEVK,EAAYG,OAAOD,GACnBH,EAASI,OAAOH,GAChB7B,EAAI,cAAckB,YAAYU,GAG9B,MAAMK,EAAU9B,EAAOiB,gBAAgBlB,EAAGmB,IAAK,QAC/ChB,EAAiB4B,EAAS,CACxBV,MAAO,OACPC,OAAQ,OACRC,EAAG,EACHC,EAAG,EACH,eAAgB,EAChBQ,OAAQ,OACRC,KAAM,oBACNpB,MAAO,2CAETf,EAAI,cAAckB,YAAYe,GAO9B,MAAMG,WAAcC,IAElB,MACMC,EADO5B,EAAM/B,EAAUG,UAAUgC,UAAUyB,UAC3BF,EAEhBG,EAAO,IAAMF,EACnB,IAAIG,EAAQ,EACZ7B,EAAU8B,MAAMC,IACdF,EAAQE,EACDH,GAAQG,KAEjB,MAAMC,EAASH,EAAQH,EAGvBhC,EAAQiB,MAAQqB,EAChBtC,EAAQkB,OAASoB,EACjB,MAAMC,EAAMvC,EAAQwC,WAAW,MACzBC,EAAO,GACPC,EAAOJ,EAAS,GAEtBC,EAAII,YAAc,GAClBJ,EAAIK,YAAcvE,EAAUG,UAAUgC,UAAUqC,UAChD,IAAK,IAAIC,EAAI,EAAGA,EAAI,GAAIA,IAAK,CAC3B,MAAMC,EAAOC,KAAKC,MAAMP,EAAOI,GAAK,GAE9BI,EAAU,EAChBX,EAAIY,OAAOJ,EAAMT,GACjBC,EAAIa,OAAOL,EAAMG,GACjBX,EAAIY,OAAOb,EAAQS,GACnBR,EAAIa,OAAOF,EAASH,GAEtBR,EAAIX,SACJW,EAAIc,YACJd,EAAII,YAAc,GAClBJ,EAAIY,OAAOV,EAAMH,GACjBC,EAAIa,OAAOX,EAAM,GAEjBF,EAAIY,OAAOb,EAAQG,GACnBF,EAAIa,OAAO,EAAGX,GACdF,EAAIX,SAEJ,MAAM0B,EAAUtD,EAAQuD,UAAU,aAClC9B,EAAQ+B,aAAa,QAASlB,GAC9Bb,EAAQ+B,aAAa,SAAUlB,GAC/Bb,EAAQgC,WAAWD,aAAa,QAASlB,GACzCb,EAAQgC,WAAWD,aAAa,SAAUlB,GAC1C7C,EAAUiE,QAAQjC,EAAS6B,IAOvBK,WAAa,KACbpD,GACFuB,WAAWrC,EAAUmE,WAEvBlE,EAAI,cAAce,MAAMC,QAAWH,EAAY,QAAU,OACzDb,EAAI,aAAamE,QAAUtD,GAE7B,MAAO,CACLhB,KAAMlB,EAAUc,QAAQ2E,EAAlB,GAAAnF,OAlJC,OAiJF,UAELoF,YAAahC,GACPxB,GAAYuB,WAAWC,IAE7BiC,WAEE,MAAMC,EAAiBhE,SAASC,cAAc,YACxCgE,EAAW3E,GAAAA,OAzJZ,OAyJL,oBACA0E,EAAeE,UAAf,gDAAAxF,OACqCuF,EADrC,2CAGAxE,EAAI,gBAAgBgC,OAAOuC,EAAeG,QAAQC,WAAU,IAC5D1E,EAAOD,EAAI,cAAc,KACvBrB,EAAUG,UAAUgC,UAAUD,SAAWA,GAAYA,EACrDoD,gBAEEpD,GACFoD,yDC5KK,CACbpE,KAAM,YACN+E,QAAS,CACP,CACEJ,MAAO,8DCJE,CACb3E,KAAM,SACN+E,QAAS,CACP,CACEJ,MAAO,sECJE,CACb3E,KAAM,qBACN+E,QAAS,CACP,CACEJ,MAAO,mECJE,CACb3E,KAAM,OACN+E,QAAS,CACP,CACEJ,MAAO"}