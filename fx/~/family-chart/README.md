<!-- PROJECT SHIELDS -->
[![Contributors][contributors-shield]][contributors-url]
[![Forks][forks-shield]][forks-url]
[![Stargazers][stars-shield]][stars-url]
[![Issues][issues-shield]][issues-url]
[![MIT License][license-shield]][license-url]
[![LinkedIn][linkedin-shield]][linkedin-url]



<!-- PROJECT LOGO -->
<br />
<div align="center">
  <a href="https://github.com/donatso/family-chart">
    <img src="examples/logo.svg" alt="Logo" width="80" height="50">
  </a>

<h3 align="center">Family Chart</h3>

  <p align="center">
    Interactive family tree chart built on top of d3.js
    <br />
    <a href="https://github.com/donatso/family-chart"><strong>Explore the docs »</strong></a>
    <br />
    <br />
    <a href="https://donatso.github.io/family-chart/examples/wiki-tree/?wiki_id=Q10633">View Demo</a>
    ·
    <a href="https://github.com/donatso/family-chart/issues">Report Bug</a>
    ·
    <a href="https://github.com/donatso/family-chart/issues">Request Feature</a>
  </p>
</div>



<!-- TABLE OF CONTENTS -->
<details>
  <summary>Table of Contents</summary>
  <ol>
    <li>
      <a href="#about-the-project">About The Project</a>
    </li>
    <li>
      <a href="#getting-started">Getting Started</a>
    </li>
    <li><a href="#usage">Usage</a></li>
    <li><a href="#contributing">Contributing</a></li>
    <li><a href="#license">License</a></li>
    <li><a href="#contact">Contact</a></li>
  </ol>
</details>



<!-- ABOUT THE PROJECT -->
## About The Project

[![Product Name Screen Shot][product-screenshot]](https://donatso.github.io/family-chart/examples/wiki-tree/?wiki_id=Q10633)

family-chart is tool for creating and displaying family trees. 
Although it has many default features, family trees created with this tool are very customizable. 
This is an ongoing project and any suggestion or request will be appreciated :)

## Examples
1. Interactive family tree of all people in wikidata database.
[![Product Name Screen Shot][product-wiki-tree-screenshot]](https://donatso.github.io/family-chart/examples/wiki-tree/?wiki_id=Q1035)

2. Basic tree
[![Product Name Screen Shot][product-basic-tree-screenshot]](https://donatso.github.io/family-chart/examples/basic-tree-1)


<!-- GETTING STARTED -->
## Getting Started

1. Visit [examples/create-tree](https://donatso.github.io/family-chart/examples/create-tree/) 
2. Create your family tree
3. Copy/paste generated code for Vanila, Vue or React.
4. Copy/paste family-chart.css
5. Give me some github stars (:



<!-- USAGE EXAMPLES -->
## Usage

### [Static](https://codesandbox.io/s/family-chart-static-zqzck?file=/FamilyChart.js)

### [React](https://codesandbox.io/s/family-chart-react-eobxc?file=/src/FamilyChart.js)

### [Vue](https://codesandbox.io/s/family-chart-vue-bsrv9?file=/src/components/FamilyChart.vue)



<!-- CONTRIBUTING -->
## Contributing

Contributions are what make the open source community such an amazing place to learn, inspire, and create. Any contributions you make are **greatly appreciated**.

If you have a suggestion that would make this better, please fork the repo and create a pull request. You can also simply open an issue with the tag "enhancement".
Don't forget to give the project a star! Thanks again!

1. Fork the Project
2. Create your Feature Branch (`git checkout -b feature/AmazingFeature`)
3. Commit your Changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the Branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request


<!-- LICENSE -->
## License

Distributed under the MIT License. See `LICENSE.txt` for more information.


<!-- CONTACT -->
## Contact

Project Link: [https://github.com/donatso/family-chart](https://github.com/donatso/family-chart)

<p align="right">(<a href="#top">back to top</a>)</p>



<!-- MARKDOWN LINKS & IMAGES -->
[contributors-shield]: https://img.shields.io/github/contributors/donatso/family-chart.svg?style=for-the-badge
[contributors-url]: https://github.com/donatso/family-chart/graphs/contributors
[forks-shield]: https://img.shields.io/github/forks/donatso/family-chart.svg?style=for-the-badge
[forks-url]: https://github.com/donatso/family-chart/network/members
[stars-shield]: https://img.shields.io/github/stars/donatso/family-chart.svg?style=for-the-badge
[stars-url]: https://github.com/donatso/family-chart/stargazers
[issues-shield]: https://img.shields.io/github/issues/donatso/family-chart.svg?style=for-the-badge
[issues-url]: https://github.com/donatso/family-chart/issues
[license-shield]: https://img.shields.io/github/license/donatso/family-chart.svg?style=for-the-badge
[license-url]: https://github.com/donatso/family-chart/blob/master/LICENSE.txt
[linkedin-shield]: https://img.shields.io/badge/-LinkedIn-black.svg?style=for-the-badge&logo=linkedin&colorB=555
[linkedin-url]: https://linkedin.com/in/donat-sorić-342a92161
[product-screenshot]: https://user-images.githubusercontent.com/26413530/143689335-7cbcd4e8-ff6c-4657-8d9d-ce6f9de3aa1e.png
[product-basic-tree-screenshot]: https://user-images.githubusercontent.com/26413530/143689330-19d07f09-a127-45d9-b664-68207873caa4.png
[product-wiki-tree-screenshot]: https://user-images.githubusercontent.com/26413530/143689337-e810d7ad-fefe-4edd-a7e1-0499bfc40abf.png

