import { FxElement, html, css } from '../../fx.js';

import '../jtmp/jtmp.js';

const distPath = import.meta.url.split('/').slice(0, -1).join('/') + '/dist/';

customElements.define('fx-jspreadsheet', class FxJSpreadsheet extends FxElement {
    render() {
        return html`
            <fx-jtmp id="editor" src=${this.src} .srcdoc=${this.srcdoc} editMode></fx-jtmp>
        `
    }

    static properties = {
        cell: { type: Object },
        src: {
            type: String, default: `[
                {
                    "name": "sheet1"
                },
                {
                    "name": "sheet2"
                },
                {
                    "name": "sheet3"
                }
            ]`
        }
    }
    firstUpdated() {
        super.firstUpdated();
        if (this.cell?.source)
            this.src = this.cell.source;
        this.listen('change', e => {
            if (this.cell) {
                // console.log(e.detail)
                this.cell.source = e.detail;
                this.$update();
            }
        })
    }

    srcdoc(src) {
        return `
<style>
    ::-webkit-scrollbar { width: 4px; height: 4px; }
    ::-webkit-scrollbar:hover { width: 14px; height: 14px; }
    ::-webkit-scrollbar-track { background: #f0f0f0; }
    ::-webkit-scrollbar-thumb { background-color: #d0d0d0; }
</style>
<link rel="stylesheet" href="${distPath}xspreadsheet.css">
<script src="${distPath}xspreadsheet.js"></script>

<div id="xspreadsheet"></div>

<script>
    const s = x_spreadsheet('#xspreadsheet')
		.loadData(${src})
        .change(data => {
            // console.log(data)
            document.dispatchEvent(new CustomEvent('change', { detail: JSON.stringify(s.getData()) }));
        })
    s.validate()
</script>
        `
    }
})
