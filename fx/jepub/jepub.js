import { FxElement, html, css } from '../../fx.js';
import { $styles } from './jepub.x.js';
import '/fx/~/epub/epub.js';
import '../button/button.js';
import '../checkbox/checkbox.js';
import * as JSZip from '/fx/~/epub/jszip.min.js';

customElements.define('fx-jepub', class FxJEpub extends FxElement {
    static properties = {
        src: { type: String, default: '' },
        labelBook: { type: String, default: '' },
        url: { type: String, default: '', notify: true },
        source: { type: String, default: '', notify: true },
        prevLabel: { type: String, default: '' },
        nextLabel: { type: String, default: '' },
        curLabel: { type: String, default: '' },
        listLabel: { type: Array },
        listHref: { type: Array }
    }

    get _checkedURL() { return FX.checkUrl(this.url || '') }

    firstUpdated() {
        super.firstUpdated();
        this.async(async () => {
            this._lastURL = this.url;
            await this.getBlob();
            this.getBook();
            this.isReady = true;
        }, 100)
    }

    'url-changed'(e) {
        if (this.source || this._lastURL === this.url || !this.url || !this.isReady) return;
        this._lastURL = this.url;
        this.getBook();
    }
    'source-changed'(e) {
        if (!this.isReady) return;
        this.getBlob();
        this.$update();
    }

    set src(val) {
        if (val) {
            let oldVal = this._src;
            if (val !== oldVal) {
                this._src = val;
                this.requestUpdate('src', oldVal);
                this._fetchSrc(val);
            }
        }
    }
    async _fetchSrc(s) {
        const src = await fetch(s);
        const arrayBuffer = src && src.ok ? await src.arrayBuffer() : s;
        this._book = arrayBuffer;
        this.getBook();
    }

    getBook() {
        if (!this._checkedURL && !this._book) return;
        const params = URLSearchParams && new URLSearchParams(document.location.search.substring(1));
        // const url = params && params.get('url') && decodeURIComponent(params.get('url'));
        const currentSectionIndex = (params && params.get('loc')) ? params.get('loc') : undefined;
        this.book?.destroy();
        let book = this._book;
        this._book = undefined;
        if (book) {
            this.book = ePub();
            this.book.open(book, "binary");
        } else {
            if (this._checkedURL)
                this.book = ePub(this._checkedURL);
        }
        this.rendition = this.book.renderTo(this.$qs('#viewer'), {
            flow: 'scrolled-doc',
            width: '100%',
            fullsize: true
        })
        this.rendition.display(currentSectionIndex);
        this.rendition.on('relocated', (location) => {

        })
        this.rendition.on('rendered', (section) => {
            const nextSection = section.next();
            const prevSection = section.prev();
            if (nextSection) {
                const nextNav = this.book.navigation.get(nextSection.href);
                this.nextLabel = nextNav ? nextNav.label : 'next';
            } else this.nextLabel = '';
            if (prevSection) {
                const prevNav = this.book.navigation.get(prevSection.href);
                this.prevLabel = prevNav ? prevNav.label : 'previous';
            } else this.prevLabel = '';

            const current = this.book.navigation?.get(section.href);
            if (current) this.curLabel = current.label;
            this.$update();
        })
        this.book.loaded.navigation.then((toc) => {
            this.listLabel = [];
            this.listHref = [];
            toc.forEach((chapter) => {
                this.listLabel.push(chapter.label);
                this.listHref.push(chapter.href);
            })
        })
    }
    async _openDD(e) {
        let toc = this.$qs('#toc');
        let res = await FX.show('dropdown', 'cell', { type: '123', value: this.curLabel, props: { list: this.listLabel } }, { parent: toc, align: 'left', intersect: true, minWidth: '500px' });
        this.curLabel = res?.detail?.value || '';
        for (let i = 0; i < this.listLabel.length; i++) {
            if (this.listLabel[i].includes(this.curLabel)) {
                this.rendition.display(this.listHref[i]);
                break;
            }
        }
        this.$update();
    }
    aClick(e) {
        let id = e.target?.id;
        this.rendition[id]();
        e.preventDefault();
    }

    async getBlob() {
        if (!this.source) return;
        this.blob = this.b64toBlob(this.source?.split('base64,')[1]);
        const reader = new FileReader();
        reader.onload = async (e) => {
            this._book = e.target.result;
            this.getBook();
            setTimeout(() => this.fire('change', this.source), 50);
            this.$update();
        }
        reader.readAsArrayBuffer(this.blob);
    }
    b64toBlob(b64Data, contentType = 'application/epub', sliceSize = 512) {
        const byteCharacters = atob(b64Data);
        const byteArrays = [];
        for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
            const slice = byteCharacters.slice(offset, offset + sliceSize);
            const byteNumbers = new Array(slice.length);
            for (let i = 0; i < slice.length; i++) {
                byteNumbers[i] = slice.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            byteArrays.push(byteArray);
        }
        const blob = new Blob(byteArrays, { type: contentType });
        return blob;
    }
    openEPUB() {
        let url = this.blobURL || this._checkedURL || '';
        window.open(url, '_blank').focus();
    }

    static styles = [$styles]

    render() {
        return html`
            <div class="vertical" style="width: 100%; height: 100%; min-height: 24px; margin: 1px 0;">
                <div class="vertical flex justify" style="width: 100%; height: 100%;">
                    <div class="horizontal align brb" style="min-height: 0px; max-height: 24px; display: ${this.book ? '' : 'none'}; overflow: hidden">
                        <a id="prev" class="navlink p4" @click=${this.aClick}>${this.prevLabel}</a>
                        <label class="flex" style="text-overflow: ellipsis; width: 100%; color: gray; font-size: 18px; text-align: center; cursor: pointer; font-weight: 600; overflow: hidden; white-space: nowrap">${this.curLabel}</label>
                        <fx-icon an="btn2"  size ="14" id="toc" name="cb-chevron-down" width="auto" @click=${this._openDD} title="select chapter" style="margin-right: 4px;"></fx-icon an="btn2" >
                        <a id="next" class="navlink p4" @click=${this.aClick} style="text-align: right;" >${this.nextLabel}</a>
                    </div>
                    <div class="vertical flex" style="width: 100%; height: 100%; position: relative; overflow-x: hidden; overflow-y: auto; min-height: 0px;">
                        <div id="viewer" style="min-height: 0px;"></div>
                    </div>
                </div>
            </div>
        `
    }
})

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

const usedIcons =
{
    "cb-add": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M17 15V8h-2v7H8v2h7v7h2v-7h7v-2z\"/></svg>",
    "cb-launch": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 28H6a2.003 2.003 0 0 1-2-2V6a2.003 2.003 0 0 1 2-2h10v2H6v20h20V16h2v10a2.003 2.003 0 0 1-2 2Z\"/><path fill=\"currentColor\" d=\"M20 2v2h6.586L18 12.586L19.414 14L28 5.414V12h2V2H20z\"/></svg>",
    "cb-chevron-down": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M16 22L6 12l1.4-1.4l8.6 8.6l8.6-8.6L26 12z\"/></svg>"
}

FX.setIcons(usedIcons);
