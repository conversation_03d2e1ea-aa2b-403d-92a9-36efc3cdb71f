import { css } from '/fx.js';

export const $styles = css`
    * { box-sizing: border-box; }
    :host { 
        position: relative; 
        display: flex; 
        width: 100%; 
        height: 100%;
        overflow: hidden; 
        /* opacity: 0; */
    }
    span {
        cursor: pointer;
        font-size: 12px;
        padding: 2px;
        margin: auto 0;
    }
    .selected {
        color: red;
        background: white;
    }
    #flip {
        position: absolute; 
        top: 4px;
        right: 2px; 
        opacity: 0!important; 
        z-index: 1;
    }
    #flip:hover {
        opacity: .7!important;
    }
`

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;
// const usedIcons = {
// }
// FX.setIcons(usedIcons);
