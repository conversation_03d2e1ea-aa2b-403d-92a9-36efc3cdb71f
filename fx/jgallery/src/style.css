::-webkit-scrollbar { width: 0px; height: 0px; }
html, body {
    height: 100%;
    max-height: 100%;
}

body, ul {
    margin: 0;
}

ul {
    display: grid;
    height: 100%;
    overflow: auto;
    list-style: none;
    will-change: opacity;
}

ul img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
    cursor: pointer;
}

#thumbnails {
    --columns: 2;
    --gutter: 10px;
    --size: calc((100vw - (var(--columns) + 1) * var(--gutter)) / var(--columns));
    grid: auto-flow var(--size) / repeat(var(--columns), 1fr);
    gap: var(--gutter);
    padding: var(--gutter);
    box-sizing: border-box;
}

@media (min-width: 400px) {
    #thumbnails {
        --columns: 3;
        --gutter: 11px;
    }
}

@media (min-width: 800px) {
    #thumbnails {
        --columns: 4;
        --gutter: 12px;
    }
}

@media (min-width: 1200px) {
    #thumbnails {
        --columns: 5;
        --gutter: 13px;
    }
}
@media (min-width: 1600px) {
    #thumbnails {
        --columns: 6;
        --gutter: 14px;
    }
}
@media (min-width: 2000px) {
    #thumbnails {
        --columns: 7;
        --gutter: 15px;
    }
}
@media (min-width: 2400px) {
    #thumbnails {
        --columns: 8;
        --gutter: 16px;
    }
}
@media (min-width: 2800px) {
    #thumbnails {
        --columns: 9;
        --gutter: 17px;
    }
}
@media (min-width: 3200px) {
    #thumbnails {
        --columns: 10;
        --gutter: 16px;
    }
}

#scroller {
    grid: 100% / auto-flow 100%;
    scroll-snap-type: x mandatory;
    padding: 0;
}

#scroller li {
    scroll-snap-align: center;
    scroll-snap-stop: always;
}

.hidden {
    visibility: hidden;
}

@media (prefers-color-scheme: dark) {
    body {
        background: #000;
    }
}
