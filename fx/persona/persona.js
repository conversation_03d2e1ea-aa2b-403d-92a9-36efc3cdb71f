import { FxElement, html, css } from '/fx.js';

import '../avatar/avatar.js'

customElements.define('fx-persona', class FxPersona extends FxElement {
    static properties = {
        base: { type: Object }
    }
    get item() { return this.base?.fxSelected }

    firstUpdated() {
        super.firstUpdated();
        this.async(() => {
            this.$update();
        }, 300)
    }

    render() {
        return html`
            <div class="horizontal wrap w100 overflow justify pt2 pb2">
                <div class="vertical overflow gray m4 shadow" style="background: #fcfbee">
                    <fx-persona-info .persona=${this.item} .selected=${this.item} .base=${this.base}></fx-persona-info>
                </div>
                ${this.item?.father || this.item?.mother ? html`
                    ${this.item?.father ? html`
                        <div class="vertical overflow gray m4 shadow" style="background: #effcee">
                            <fx-persona-info label="Отец" type="man" .persona=${this.item.father} .selected=${this.item} .base=${this.base} h="300px"></fx-persona-info>
                        </div>
                    ` : html``}
                    ${this.item?.mother ? html`
                        <div class="vertical overflow gray m4 shadow" style="background: #effcee">
                            <fx-persona-info label="Мать" type="woman" .persona=${this.item.mother} .selected=${this.item} .base=${this.base} h="300px"></fx-persona-info>
                            </div>
                    ` : html``}
                ` : html``}
                ${Object.keys(this.item?.spouses || {}).length ? html`
                    ${Object.values(this.item.spouses || {}).map((i, idx) => {
            this.item.spouses ||= {};
            this.item.spouses[i._id] ||= {};
            return html`
                            <div class="vertical overflow gray m4 box shadow" style="background: #eff8fb">
                                <fx-persona-info .itemSpouses=${this.item.spouses?.[i._id]} .persona=${i} .selected=${this.item} .base=${this.base} label=${this.item.is === 'man' ? 'Супруга' : 'Супруг'} type=${i.is}></fx-persona-info>
                            </div>
                        `
        })}
                ` : html``}
                ${(this.item?.children || []).map((i, idx) => html`
                    <div class="vertical overflow gray m4 box shadow" style="background: #fffaff">
                        <fx-persona-info .persona=${i} type=${i.is} .selected=${this.item} .base=${this.base} label="Дети" h="300px"></fx-persona-info>
                    </div>
                `)}
            </div>
        `
    }

})

customElements.define('fx-persona-info', class FxPersonaInfo extends FxElement {
    static properties = {
        persona: { type: Object },
        selected: { type: Object },
        item: { type: Object },
        itemSpouses: { type: Object },
        propsAvatar: { type: Object },
        label: { type: String },
        h: { type: String },
        type: { type: String },
        base: { type: Object }
    }
    get item() { return this.base?.fxFlat[this.persona._id || this.persona] }
    get years() {
        const d1 = (new Date(this.item?.dateStart)).getTime();
        const d2 = (new Date(this.item?.dateEnd || new Date())).getTime();
        const diff = Math.abs(d2 - d1);
        return diff ? (diff / 1000 / 60 / 60 / 24 / 365.25).toFixed(2) : '';
    }
    get years2() {
        if (!this.item?.dateEnd) return '';
        const d1 = (new Date(this.item?.dateEnd)).getTime();
        const d2 = new Date().getTime();
        const diff = Math.abs(d2 - d1);
        return diff ? (diff / 1000 / 60 / 60 / 24 / 365.25).toFixed(2) : '';
    }
    get years3() {
        if (!this.item?.dateEnd) return '';
        const d1 = (new Date(this.item?.dateStart)).getTime();
        const d2 = new Date().getTime();
        const diff = Math.abs(d2 - d1);
        return diff ? (diff / 1000 / 60 / 60 / 24 / 365.25).toFixed(2) : '';
    }

    addAvatar(e) {
        const file = e.target.files[0];
        e.target.value = null;
        if (!file) return;
        const reader = new FileReader();
        reader.addEventListener("load", async () => {
            this.propsAvatar ||= { width: 400, height: 600 };
            this.propsAvatar.src = reader.result;
            this.propsAvatar.showAvatar = true;
            this.$update();
            const img = await new Promise((resolve, reject) => {
                this.listen('_changeAvatar', (e) => {
                    resolve(this.propsAvatar.img || e.detail);
                })
            })
            if (img) {
                this.item ||= {};
                this.item.photo = img;
                this.$update();
            }
        }, false)
        reader.readAsDataURL(file);
    }
    async changeAvatar() {
        this.fire('_changeAvatar');
    }
    deletePhoto(e) {
        this.item.photo = undefined;
        this.$update();
    }
    addPhoto(e) {
        this.$id('loadPhoto').click();
        this.$update();
    }
    async addParents(e) {
        if (this._addParents === true) {
            this._addParents = false;
            if (this.base?.tree) {
                let res = await this.base.tree.allItems.map(async i => {
                    if (i.checked) {
                        if (i.is === 'man') {
                            this.item.father = i._id;
                        } else if (i.is === 'woman') {
                            this.item.mother = i._id;
                        }
                        await this.base.getParents();
                    }
                })
                await Promise.all(res);
                this.base.tree.clearChecked();
                this.base.tree.singleCheckMode = false;
            }
            setTimeout(() => this.$update(), 20);
        } else {
            if (this.base?.tree) {
                this._addParents = true;
                this.base.tree.singleCheckMode = true;
                this.$update();
            }
        }
    }
    deleteParents(e) {
        this.item.father = this.persona.father = this.item.mother = this.persona.mother = undefined;
        this.$update();
    }
    async addSpouses(e) {
        if (this._addSpouses === true) {
            this._addSpouses = false;
            if (this.base?.tree) {
                let res = await this.base.tree.allItems.map(async i => {
                    if (i.checked) {
                        this.item.spouses ||= {};
                        if (this.item.is === 'woman' && i.is === 'man') {
                            this.item.spouses[i._id] = { _id: i._id };
                            let w = await this.base.getBsItem(i._id, true);
                            w.doc.spouses ||= {};
                            w.doc.spouses[this.item._id] = { _id: this.item._id };
                        } else if (this.item.is === 'man' && i.is === 'woman') {
                            this.item.spouses[i._id] = { _id: i._id };
                            let m = await this.base.getBsItem(i._id, true);
                            m.doc.spouses ||= {};
                            m.doc.spouses[this.item._id] = { _id: this.item._id };
                        }
                        await this.base.getSpouses();
                    }
                })
                await Promise.all(res);
                this.base.tree.clearChecked();
                this.base.tree.singleCheckMode = false;
            }
            setTimeout(() => this.$update(), 20);
        } else {
            if (this.base?.tree) {
                this._addSpouses = true;
                this.base.tree.singleCheckMode = true;
                this.$update();
            }
        }
    }
    deleteSpouses(e) {
        this.item.spouses = this.persona.spouses = undefined;
        this.$update();
    }
    async onchangeStartEnd(e) {
        const id = e.target.id;
        if (id.includes('Wedding')) {
            this.itemSpouses[id] = e.target.value;
            const bs = await this.base.getBsItem(this.itemSpouses._id, true);
            await this.base.getSpouses(bs);
            bs.spouses[this.base.bsSelected._id][id] = e.target.value;
            bs.doc.spouses[this.base.bsSelected._id][id] = e.target.value;
        } else {
            this.item[id] = e.target.value;
        }
        this.$update();
    }
    linkClick(url) {
        if (url) {
            window.open(url, '_blank').focus();
        }
    }

    static styles = css`
        input {
            border: none;
            border-bottom: 1px solid lightgray; 
            outline: none;
            min-width: 0px; 
            color: gray; 
            font-size: large;
            font: inherit;
            background-color: transparent!important;
            cursor: pointer;
            height: 24px;
        }
        .inpt::-webkit-input-placeholder {
            color: lightgray;
        }
        .inpt {
            background-color: transparent!important;
        }
        .inpt2 {
            /* min-height: 35px; */
            max-height: 34px;
        }
        .inpt3 {
            height: 64px;
        }
        label {
            color: darkgray;
            font-size: 12px;
        }
        textarea {
            outline: none;
            -moz-appearance: none;
            resize: none;
            border: none;
            border-bottom: 1px solid lightgray;
            color: gray;
            font-size: 14px;
            font: Arial, Helvetica, sans-serif;
        }
        textarea::-webkit-scrollbar {
            display: none;
        }
        input {
            color: #333!important;
        }
    `
    render() {
        const i = this.item || {};
        return html`
            <div class="horizontal wrap overflow w100 h100 box" style="align-items: flex-start; justify-content: center"> 
                <div class="vertical center mt4 p4">
                    <div class="p4 m0" style="width: 200px; height:300px;">
                        <img class="lightgray" style="width: 200px; height:300px; opacity: ${i?.photo ? 1 : .1}" src=${(i?.photo || '').replace('li-fx/persona', 'fx/persona') || (this.type ? '/fx/persona/' + this.type + '.jpg' : '') || (i?.is ? '/fx/persona/' + i?.is + '.jpg' : '')} alt="" onerror="this.style.opacity='0'">
                    </div>
                    <div class="horizontal p8" style="width: 180px;">
                        <fx-icon url="cb-subtract" size="22" @click=${(e) => this.deletePhoto(e)} an="btn" br="circle" fill="red"></fx-icon>
                        <label class="flex tac p2 c4 fm">${this.label}</label>
                        <fx-icon url="cb-add" size="22" @click=${e => this.addPhoto(e)} an="btn" br="circle" fill="red"></fx-icon>
                        <input id="loadPhoto" type="file" style="display: none" @change=${this.addAvatar}/>
                    </div>
                </div>
                <div class="vertical mt4 p4 flex" style="min-width: 240px; height: ${this.h || '340px'}; width: 280px">
                    <div class="horizontal w100 brb">
                        <label class="inpt flr center p4 c3 w100 overflow-h" style="text-align: center; flex: 1; max-height: 42px; min-height: 42px">${i?.label}</label>
                    </div>
                    <div class="horizontal center">
                        <fx-icon url="dateStart" class="m4" fill="gray"></fx-icon>  
                        <input id="dateStart" type="datetime-local" .value=${i?.dateStart || ''} style="flex: 1" @input=${this.onchangeStartEnd}>
                        <fx-icon class="pointer mt-1 mr4" name="cb-calendar-heat-map" size="18" scale="1.0" an="btn" @click=${e => { i.dateStartInCalendar = !i.dateStartInCalendar; this.$update() }} back=${i.dateStartInCalendar ? 'lightyellow' : ''} fill=${i.dateStartInCalendar ? 'green' : 'lightgray'} title="показывать событие в календаре"></fx-icon>
                    </div>
                    <textarea id="placeStart" class="fs inpt inpt2" placeholder="место" style="flex: 1" @change=${this.onchangeStartEnd} .value=${i?.placeStart || ''}></textarea>
                    <div class="horizontal center">
                        <fx-icon url="dateEnd" strw="1"  class="m4" fill="gray"></fx-icon>
                        <input id="dateEnd" type="datetime-local" .value=${i?.dateEnd || ''} style="flex: 1" @input=${this.onchangeStartEnd}>
                        <fx-icon class="pointer mt-1 mr4" name="cb-calendar-heat-map" size="18" scale="1.0" an="btn" @click=${e => { i.dateEndInCalendar = !i.dateEndInCalendar; this.$update() }} back=${i.dateEndInCalendar ? 'lightyellow' : ''} fill=${i.dateEndInCalendar ? 'green' : 'lightgray'} title="показывать событие в календаре"></fx-icon>
                    </div>
                    <textarea id="placeEnd" class="fs inpt inpt2" placeholder="место" style="flex: 1" @change=${this.onchangeStartEnd} .value=${i?.placeEnd || ''}></textarea>
                    <div class=horizontal>
                        <textarea id="note" class="inpt inpt3 flex" placeholder="примечание" style="flex: 1" @change=${this.onchangeStartEnd} .value=${i?.note || ''}></textarea>
                        <div class="vertical ml8 mt8">
                            <label class="fm" style="color: green; text-align: right">${this.years}</label>
                            <label class="fm" style="color: red; text-align: right">${this.years2}</label>
                            <label class="fm" style="color: blue; text-align: right">${this.years3}</label>
                        </div>
                    </div>
                    <div class="horizontal center">
                        <input class="fxs" style="cursor: default" .value=${i.link1 || ''} @change=${e => { i.link1 = e.target.value }}>
                        <fx-icon class="pointer" name="fxemoji:meridianglobe:24" @click=${e => this.linkClick(i.link1)} style="opacity: ${i.link1 ? 1 : .1}"></fx-icon>
                        <input class="fxs" style="cursor: default" .value=${i.link2 || ''} @change=${e => { i.link2 = e.target.value }}>
                        <fx-icon class="pointer" name="fxemoji:meridianglobe:24" @click=${e => this.linkClick(i.link2)} style="opacity: ${i.link2 ? 1 : .1}"></fx-icon>
                        <input class="fxs" style="cursor: default" .value=${i.link3 || ''} @change=${e => { i.link3 = e.target.value }}>
                        <fx-icon class="pointer" name="fxemoji:meridianglobe:24" @click=${e => this.linkClick(i.link3)} style="opacity: ${i.link3 ? 1 : .1}"></fx-icon>
                    </div>
                    ${this.label ? html`` : html`
                        <div class="horizontal center mt16 pt6 pl4 pr4">
                            <fx-icon url="cb-subtract" size="28" @click=${this.deleteParents} an="btn" br="circle" back="#effcee" fill="red"></fx-icon>
                            <label style="width: 70px; text-align: center; color: #404040">Родители</label>
                            <fx-icon url=${this._addParents ? 'lucide:copy-plus' : 'cb-add'} size="28" @click=${this.addParents} an="btn" br="circle" back="#effcee" fill=${this._addParents ? "var(--fx-color-selected)" : "red"}></fx-icon>
                            <div class="flex"></div>
                            <fx-icon url="cb-subtract" size="28" @click=${this.deleteSpouses} an="btn" br="circle" back="#eff8fb" fill="red"></fx-icon>
                            <label style="width: 70px; text-align: center; color: #404040">Супруги</label>
                            <fx-icon url=${this._addSpouses ? 'lucide:copy-plus' : 'cb-add'} size="28" @click=${this.addSpouses} an="btn" br="circle" back="#eff8fb" fill=${this._addSpouses ? "var(--fx-color-selected)" : "red"}></fx-icon>
                        </div>
                    `}
                    ${this.label?.includes('Супруг') ? html`
                        <div class="vertical">
                            <div class="horizontal center">
                                <fx-icon url="startWedding"></fx-icon>
                                <input id="startWedding" type="datetime-local" .value=${this.itemSpouses?.startWedding || ''} style="flex: 1" @input=${this.onchangeStartEnd}>
                                <fx-icon class="pointer mt-1 mr4" name="cb-calendar-heat-map" size="18" scale="1.0" an="btn" @click=${e => { this.itemSpouses.startInCalendar = !this.itemSpouses?.startInCalendar; this.$update() }} back=${this.itemSpouses?.startInCalendar ? 'lightyellow' : ''} fill=${this.itemSpouses?.startInCalendar ? 'green' : 'lightgray'} title="показывать событие в календаре"></fx-icon>
                            </div>
                            <div class="horizontal center">
                                <fx-icon url="endWedding"></fx-icon>
                                <input id="endWedding" type="datetime-local" .value=${this.itemSpouses?.endWedding || ''} style="flex: 1" @input=${this.onchangeStartEnd}>
                                <fx-icon class="pointer mt-1 mr4" name="cb-calendar-heat-map" size="18" scale="1.0" an="btn" @click=${e => { this.itemSpouses.endInCalendar = !this.itemSpouses?.endInCalendar; this.$update() }} back=${this.itemSpouses?.endInCalendar ? 'lightyellow' : ''} fill=${this.itemSpouses?.endInCalendar ? 'green' : 'lightgray'} title="показывать событие в календаре"></fx-icon>
                            </div>
                        </div>
                    ` : html``}
                </div>
            </div>
            ${this.propsAvatar?.showAvatar ? html`<fx-avatar .props=${this.propsAvatar} @changeAvatar=${this.changeAvatar}></fx-avatar>` : html``}
        `
    }
})

let startWedding = `
<?xml version="1.0" encoding="iso-8859-1"?>
<!-- Uploaded to: SVG Repo, www.svgrepo.com, Generator: SVG Repo Mixer Tools -->
<svg height="24" width="24" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 511.999 511.999" xml:space="preserve">
<path style="fill:#FF9F99;" d="M183.784,300.834c-22.656-48.58-1.545-106.581,47.07-129.246c12.996-6.059,26.812-9.131,41.054-9.131
	c37.675,0,72.303,22.05,88.218,56.183c0.068,0.145,0.137,0.282,0.196,0.427c0.145-0.068,0.29-0.137,0.435-0.205
	c12.988-6.059,26.803-9.131,41.054-9.131c37.683,0,72.303,22.059,88.218,56.192c10.982,23.543,12.134,49.963,3.251,74.377
	c-8.883,24.414-26.752,43.913-50.304,54.895l-177.195,82.628L183.784,300.834z"/>
<path style="fill:#FF877F;" d="M55.016,262.545C5.027,220.603-1.518,145.774,40.449,95.752
	c22.554-26.871,55.629-42.291,90.735-42.291c27.767,0,54.784,9.839,76.066,27.699c0.981,0.819,1.946,1.673,2.91,2.551
	c0.794-1.033,1.604-2.039,2.432-3.021c22.545-26.871,55.629-42.291,90.752-42.291c27.75,0,54.758,9.839,76.049,27.708
	c39.578,33.212,52.727,87.808,32.708,135.868l-3.234,7.765l-8.405,0.119c-10.53,0.145-20.651,1.869-30.097,5.12l-9.771,3.362
	l-5.342-8.841c-17.562-29.022-49.493-47.044-83.345-47.044c-14.251,0-28.066,3.072-41.054,9.131
	c-23.509,10.965-41.361,30.464-50.253,54.912c-8.9,24.448-7.765,50.859,3.2,74.368l29.167,62.967l-19.866,15.164L55.016,262.545z"/>
<path style="fill:#573A32;" d="M387.619,56.294c-23.586-19.789-53.521-30.694-84.275-30.694c-35.994,0-70.05,14.609-94.737,40.371
	c-22.4-16.375-49.562-25.31-77.406-25.31c-38.912,0-75.571,17.084-100.565,46.865C-15.862,142.95-8.609,225.86,46.807,272.366
	l174.08,146.807c1.527,1.289,3.243,2.219,5.009,3.021l22.869,49.365c2.859,6.17,8.055,10.948,14.438,13.286
	c2.85,1.033,5.828,1.553,8.798,1.553c3.703,0,7.398-0.802,10.82-2.398l165.564-77.201c26.65-12.425,46.865-34.483,56.917-62.114
	c10.052-27.631,8.755-57.523-3.678-84.173c-14.498-31.104-42.812-53.077-75.511-60.561
	C443.666,149.947,430.551,92.322,387.619,56.294z M63.267,252.757c-44.681-37.487-50.5-104.098-13.013-148.77
	c20.89-24.892,50.816-37.726,80.947-37.726c23.953,0,48.043,8.107,67.823,24.704c4.838,4.062,9.011,8.602,12.937,13.278
	c3.046-5.291,6.374-10.487,10.436-15.326c20.881-24.883,50.807-37.717,80.947-37.717c23.953,0,48.043,8.107,67.823,24.704
	c36.207,30.387,46.379,79.71,29.116,121.148c-11.682,0.162-23.108,2.039-34.082,5.82c-19.755-32.666-55.612-53.222-94.293-53.222
	c-16.128,0-31.761,3.473-46.464,10.334c-55.014,25.651-78.899,91.273-53.248,146.287l29.158,62.933L63.267,252.757z
	 M478.431,271.325c19.721,42.283,1.425,92.553-40.866,112.265l-165.564,77.21l-76.604-165.35
	c-19.721-42.283-1.425-92.553,40.866-112.265c11.554-5.385,23.689-7.936,35.652-7.936c31.812,0,62.285,18.065,76.621,48.794
	c2.133,4.582,3.627,9.284,4.89,14.003c4.002-2.807,8.166-5.444,12.749-7.586c11.554-5.385,23.689-7.936,35.652-7.936
	C433.623,222.532,464.095,240.588,478.431,271.325z"/>
</svg>
`

let endWedding = `
<svg viewBox="0 0 36 36" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" class="iconify iconify--twemoji" preserveAspectRatio="xMidYMid meet" fill="#000000">

<g id="SVGRepo_bgCarrier" stroke-width="0"/>

<g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"/>

<g id="SVGRepo_iconCarrier"> <g fill="#DD2E44"> <path d="M13.589 26.521a1.5 1.5 0 0 1 .035-1.599l4.395-6.646l-5.995-5.139a1.5 1.5 0 0 1-.31-1.911l4.304-7.172a9.778 9.778 0 0 0-6.035-2.09c-5.45 0-9.868 4.417-9.868 9.868c0 .772.098 1.52.266 2.241C1.751 22.587 11.216 31.568 18 34.034c.025-.009.052-.022.077-.032l-4.488-7.481z"> </path> <path d="M26.018 1.966c-2.765 0-5.248 1.151-7.037 2.983l-4.042 6.737l6.039 5.176a1.5 1.5 0 0 1 .274 1.966l-4.604 6.962l4.161 6.935c6.338-3.529 13.621-11.263 14.809-18.649c.17-.721.268-1.469.268-2.241c-.001-5.452-4.419-9.869-9.868-9.869z"> </path> </g> </g>

</svg>
`

let dateEnd = `
<svg fill="#000000" height="24px" width="24px" version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 512 512" xml:space="preserve">
<g>
	<g>
		<path d="M422.957,178.087h-116.87v-33.391h50.087c9.22,0,16.696-7.475,16.696-16.696V61.217c0-9.22-7.475-16.696-16.696-16.696
			h-50.087V16.696c0-9.22-7.475-16.696-16.696-16.696h-66.783c-9.22,0-16.696,7.475-16.696,16.696v27.826h-50.087
			c-9.22,0-16.696,7.475-16.696,16.696V128c0,9.22,7.475,16.696,16.696,16.696h50.087v33.391H89.044
			c-9.22,0-16.696,7.475-16.696,16.696v66.783c0,9.22,7.475,16.696,16.696,16.696h116.87v41.508l-47.342-7.889
			c-4.841-0.805-9.791,0.558-13.537,3.73c-3.744,3.172-5.904,7.831-5.904,12.739v66.783c0,8.162,5.9,15.126,13.951,16.469
			l52.832,8.805v74.9c0,9.22,7.475,16.696,16.696,16.696h66.783c9.22,0,16.696-7.475,16.696-16.696v-58.203l47.342,7.889
			c4.844,0.807,9.793-0.558,13.537-3.73c3.744-3.172,5.904-7.831,5.904-12.739v-66.783c0-8.162-5.9-15.126-13.951-16.469
			l-52.832-8.805v-58.204h116.87c9.22,0,16.696-7.475,16.696-16.696v-66.783C439.652,185.562,432.177,178.087,422.957,178.087z
			 M406.261,244.87h-116.87c-9.22,0-16.696,7.475-16.696,16.696v89.043c0,8.162,5.9,15.126,13.951,16.469l52.832,8.805v32.93
			l-47.342-7.89c-10.181-1.692-19.44,6.165-19.44,16.469v61.217h-33.391v-72.348c0-8.162-5.9-15.126-13.951-16.469l-52.832-8.805
			v-32.931l47.342,7.89c10.168,1.694,19.44-6.155,19.44-16.469v-77.913c0-9.22-7.475-16.696-16.696-16.696h-116.87v-33.391h116.87
			c9.22,0,16.696-7.475,16.696-16.696V128c0-9.22-7.475-16.696-16.696-16.696h-50.087V77.913h50.087
			c9.22,0,16.696-7.475,16.696-16.696V33.391h33.391v27.826c0,9.22,7.475,16.696,16.696,16.696h50.087v33.391h-50.087
			c-9.22,0-16.696,7.475-16.696,16.696v66.783c0,9.22,7.475,16.696,16.696,16.696h116.87V244.87z"/>
	</g>
</g>
</svg>
`

let dateStart = `
<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" 
	 viewBox="0 0 503.466 503.466" xml:space="preserve">
<path style="fill:#FF7474;" d="M337.273,4.266c61.44,0,110.933,49.493,110.933,110.933c0,78.507-96.427,187.733-162.133,221.013
	c0,0-20.48,7.68-34.133,9.387c-59.733,0-196.267-136.533-196.267-230.4c0-61.44,49.493-110.933,110.933-110.933
	c34.133,0,64.853,15.36,85.333,40.107C272.42,19.626,303.14,4.266,337.273,4.266"/>
<path style="fill:#51565F;" d="M251.94,503.466c-1.707,0-3.413-0.853-4.267-2.56c-0.853-1.707-17.92-45.227,0-80.213
	c13.653-27.307,4.267-61.44,0.853-70.827C185.38,345.6,51.407,211.627,51.407,115.2C51.407,52.053,103.46,0,166.607,0
	c32.427,0,63.147,13.653,85.333,37.547C274.127,13.653,304.847,0,337.273,0c63.147,0,115.2,52.053,115.2,115.2
	c0,82.773-98.133,191.147-164.693,224.427c-1.707,0.853-4.267,0-5.973-1.707c-0.853-1.707,0-4.267,1.707-5.973
	C346.66,300.373,443.94,193.706,443.94,115.2c0-58.88-47.787-106.667-106.667-106.667c-31.573,0-61.44,13.653-81.92,38.4
	c-1.707,1.707-5.12,1.707-6.827,0c-20.48-24.747-50.347-38.4-81.92-38.4C107.726,8.533,59.94,56.32,59.94,115.2
	c0,92.16,134.827,226.133,192,226.133c1.707,0,3.413,0.853,4.267,2.56c0.853,1.707,17.92,45.227,0,80.213
	c-16.213,31.573,0,72.533,0,73.387c0.853,2.56,0,4.267-2.56,5.12C252.793,503.466,252.793,503.466,251.94,503.466z M329.593,436.053
	c-1.707,0-3.413-0.853-4.267-3.413c0,0-1.707-6.827,0-14.507c1.707-6.827,5.973-16.213,20.48-20.48
	c8.533-2.56,9.387-7.68,9.387-17.92c0.853-10.24,1.707-22.187,16.213-26.453c7.68-2.56,12.8-6.827,14.507-13.653
	c0.853-5.12,0-10.24,0-10.24c-0.853-2.56,0.853-4.267,3.413-5.12c2.56-0.853,4.267,0.853,5.12,3.413c0,0,1.707,6.827,0,14.507
	c-1.707,6.827-5.973,16.213-20.48,20.48c-8.533,2.56-9.387,7.68-9.387,17.92c-0.853,10.24-1.707,22.187-16.213,26.453
	c-7.68,2.56-12.8,6.827-14.507,13.653c-0.853,5.12,0,10.24,0,10.24c0.853,2.56-0.853,4.267-3.413,5.12
	C330.447,436.053,329.593,436.053,329.593,436.053z M64.207,366.933h-0.853c-9.387-2.56-25.6-12.8-20.48-34.987
	c2.56-8.533-2.56-11.947-11.093-17.92c-8.533-5.12-18.773-11.947-14.507-26.453c5.12-18.773-13.653-23.893-13.653-24.747
	c-2.56-0.853-3.413-2.56-3.413-5.12c0-2.56,2.56-3.413,5.12-3.413c9.387,2.56,25.6,12.8,20.48,34.987
	c-2.56,8.533,2.56,11.947,11.093,17.92c8.533,5.12,18.773,11.947,14.507,26.453c-5.12,18.773,13.653,23.893,13.653,24.747
	c2.56,0.853,3.413,2.56,3.413,5.12C67.62,366.08,65.913,366.933,64.207,366.933z M491.727,332.8c-0.853,0-2.56,0-3.413-0.853
	c0,0-5.12-5.12-7.68-11.947c-2.56-6.827-2.56-17.067,7.68-28.16c5.973-5.973,3.413-11.947-0.853-20.48
	c-4.267-8.533-10.24-19.627,0.853-30.72c13.653-13.653,1.707-26.453,0-28.16c-1.707-1.707-1.707-4.267,0-5.973s4.267-1.707,5.973,0
	c6.827,6.827,16.213,23.893,0,40.107c-5.973,5.973-3.413,11.947,0.853,20.48c4.267,8.533,10.24,19.627-0.853,30.72
	c-13.653,13.653-1.707,26.453,0,28.16c1.707,1.707,1.707,4.267,0,5.973C494.287,332.8,492.58,332.8,491.727,332.8z M414.073,119.466
	c-2.56,0-4.267-1.707-4.267-4.267c0-11.093-2.56-22.187-7.68-32.427c-6.827-14.507-18.773-25.6-32.427-32.427
	c-1.707-0.853-2.56-3.413-1.707-5.973c0.853-1.707,3.413-2.56,5.973-1.707c15.36,7.68,29.013,20.48,36.693,36.693
	c5.973,11.093,8.533,23.04,8.533,35.84C418.34,117.76,416.633,119.466,414.073,119.466z"/>
</svg>
`

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

const usedIcons = {
    "startWedding": startWedding,
    "endWedding": endWedding,
    "dateEnd": dateEnd,
    "dateStart": dateStart,
    "fxemoji:meridianglobe": {
        "svg": "<path fill=\"#0096D1\" d=\"M425.393 86.607C380.146 41.361 319.988 16.442 256 16.442S131.854 41.361 86.607 86.607S16.442 192.012 16.442 256S41.36 380.146 86.607 425.393S192.012 495.558 256 495.558s124.146-24.918 169.393-70.165S495.558 319.988 495.558 256S470.64 131.854 425.393 86.607M386.027 242.5c-1.141-38.785-7.187-75.873-17.566-108.605c16.922-4.791 32.653-10.738 47.349-17.882c30.041 34.253 49.265 78.207 52.307 126.487zM242.5 466.638c-20.989-5.949-40.869-25.655-57.048-56.984a228 228 0 0 1-5.844-12.219c11.593-2.202 23.68-3.935 36.277-5.158a428 428 0 0 1 26.615-1.739zm27-76.15c21.326.656 42.336 2.977 62.887 6.956a228 228 0 0 1-5.839 12.209c-16.179 31.329-36.059 51.036-57.048 56.984zm-27-26.963c-9.7.314-19.444.927-29.225 1.877c-15.111 1.467-29.588 3.622-43.422 6.429c-9.922-30.536-15.727-65.521-16.87-102.331H242.5zM152.984 242.5c1.143-36.816 6.95-71.805 16.874-102.345c23.712 4.87 47.989 7.663 72.642 8.375v93.97zM242.5 121.523c-21.327-.657-42.338-2.984-62.891-6.959a229 229 0 0 1 5.843-12.218c16.179-31.33 36.058-51.037 57.048-56.985zm27-76.161c20.989 5.948 40.869 25.655 57.048 56.985a228 228 0 0 1 5.871 12.282c-10.417 1.958-21.302 3.531-32.689 4.73a430 430 0 0 1-30.229 2.096zm81.038 44.597c-6.618-12.816-13.906-24.061-21.732-33.669c24.658 9.017 47.19 22.48 66.629 39.411c-11.359 4.975-23.438 9.21-36.287 12.755c-2.686-6.4-5.554-12.579-8.61-18.497m-189.076 0c-3.041 5.888-5.896 12.035-8.57 18.401c-13.017-3.574-25.073-7.775-36.326-12.659c19.438-16.93 41.97-30.393 66.628-39.41c-7.826 9.607-15.114 20.852-21.732 33.668m-17.892 43.84c-10.398 32.755-16.455 69.878-17.597 108.701h-82.09c3.041-48.266 22.254-92.208 52.281-126.457c14.553 7.039 30.243 12.923 47.406 17.756M125.973 269.5c1.142 38.814 7.196 75.928 17.589 108.678c-16.978 4.812-32.778 10.77-47.359 17.823c-30.049-34.255-49.278-78.215-52.32-126.501zm26.909 134.116a258 258 0 0 0 8.58 18.425c6.618 12.816 13.906 24.061 21.731 33.669c-24.647-9.014-47.171-22.469-66.604-39.389c11.31-4.92 23.428-9.151 36.293-12.705m206.215.051c12.792 3.547 24.916 7.797 36.26 12.702c-19.421 16.898-41.926 30.336-66.55 39.341c7.825-9.608 15.113-20.853 21.732-33.669c3.036-5.88 5.887-12.018 8.558-18.374m-16.954-31.825c-23.709-4.874-47.99-7.655-72.643-8.367V269.5h89.516c-1.144 36.815-6.95 71.803-16.873 102.342M269.5 242.5v-94.023a457 457 0 0 0 33.056-2.267c13.854-1.458 27.024-3.464 39.606-5.993c9.912 30.525 15.712 65.492 16.855 102.283zm146.193 153.618l.068-.141c-14.598-7.008-30.463-12.952-47.339-17.75c10.403-32.762 16.463-69.894 17.605-108.728h82.089c-3.045 48.343-22.315 92.347-52.423 126.619\"/>",
        "vb": "0 0 512 512",
        "strw": "0"
    },
    "lucide:copy-plus": {
        "svg": "<g fill=\"none\" stroke=\"currentColor\" stroke-linecap=\"round\" stroke-linejoin=\"round\" stroke-width=\"2\"><path d=\"M15 12v6m-3-3h6\"/><rect width=\"14\" height=\"14\" x=\"8\" y=\"8\" rx=\"2\" ry=\"2\"/><path d=\"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\"/></g>",
        "vb": "0 0 24 24",
        "strw": "0"
    },
    "cb-subtract": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M8 15h16v2H8z\"/></svg>",
    "cb-add": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M17 15V8h-2v7H8v2h7v7h2v-7h7v-2z\"/></svg>",
    "cb-calendar-heat-map": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M26 4h-4V2h-2v2h-8V2h-2v2H6a2.002 2.002 0 0 0-2 2v20a2.002 2.002 0 0 0 2 2h20a2.002 2.002 0 0 0 2-2V6a2.002 2.002 0 0 0-2-2ZM6 6h4v2h2V6h8v2h2V6h4v4H6Zm0 6h5v6H6Zm13 14h-6v-6h6Zm0-8h-6v-6h6Zm2 8v-6h5l.001 6Z\"/></svg>"
}
FX.setIcons(usedIcons);
