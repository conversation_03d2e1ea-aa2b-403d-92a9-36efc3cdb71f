<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <title>create-tree</title>
    <link rel="shortcut icon" type="image/svg" href="/examples/logo.svg">
    <link rel="stylesheet" href="/fx/~/family-chart/plugins/materialize.min.css">
    <script src="/fx/~/family-chart/plugins/materialize.min.js"></script>
    <script src="/fx/~/family-chart/plugins/d3.v6.js"></script>
    <link rel="stylesheet" href="/fx/~/family-chart/styles/main.css">

    <style>
        html,
        body {
            display: flex;
            flex: 1;
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        #FamilyChart {
            height: 100%;
            width: 100%;
        }
    </style>

</head>

<body>
    <div id="FamilyChart" style="position: relative"></div>

    <script src="/fx/~/family-chart/create-tree/create-tree.js" type="module"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const modal = document.body.appendChild(document.createElement("div"))
            modal.setAttribute("id", "form_modal")
            modal.setAttribute("class", "modal")
            M.Modal.init(modal, {});
        })
    </script>
</body>

</html>