import { FxElement, html, css } from '/fx.js';

import '../image-compressor/image-compressor.js';
import cropper from '/fx/cropper/dist/cropper.js';
import '../button/button.js';
import { $styles } from './cropper-h.js';

customElements.define('fx-cropper', class FxCropper extends FxElement {
    static styles = [$styles.main];

    render() {
        return html`
            <div class="vertical flex w100 h100 relative">
                <div class="horizontal flex w100 align no-wrap relative m4">
                    <fx-icon an="btn2" name=${this.btnLoad} size=${this.btnSize} id="load-file" class="ml4" for="file" @click=${() => { this.$id('file').click() }} title="открыть фото"></fx-icon>
                    <input id="file" type="file" id="file" @change=${this.isLoad} style="display: none"/>
                    <fx-icon an="btn2" name=${this.btnCancel} size=${this.btnSize} class="ml4" @click=${this.isCancel} title="отмена"></fx-icon>
                    <fx-icon an="btn2" name=${this.btnOk} size=${this.btnSize} class="ml4 ${this.src ? '' : 'hidden'}" @click=${this.isOk} title="Ok"></fx-icon>
                    <div class="flex"></div>
                    <fx-icon an="btn2" name=${this.btnSet} size=${this.btnSize} class="mr10" @click=${this.isOk} title="settings"></fx-icon>
                </div>
                <div class="image-container">
                    <img id="image" class="${this.isReady ? 'hidden' : ''}"/>
                </div>
            </div>
		`
    }

    static properties = {
        src: { type: String, default: '' },
        props: { type: Object, default: { showAvatar: false } },
        width: { type: Number, default: 1920 },
        height: { type: Number, default: 1080 },
        btnLoad: { type: String, default: 'fc-add_image' },
        btnCancel: { type: String, default: 'fc-remove_image' },
        btnOk: { type: String, default: 'fc-ok' },
        btnSet: { type: String, default: 'flat-color-icons:settings' },
        btnSize: { type: Number, default: 32 }
    }

    get _width() { return this.props.width || this.width || 200 }
    get _height() { return this.props.height || this.height || 300 }

    async firstUpdated() {
        super.firstUpdated();
        await new Promise((r) => setTimeout(r, 100));
        if (this.props?.loadImage) {
            this.$id('load-file').click();
        } else if (this.src || this.props?.src) {
            this.setCropper(this.src || this.props?.src);
        }
    }

    isLoad(e) {
        let fileInput = e.target;
        let reader = new FileReader();
        reader.readAsDataURL(fileInput.files[0]);
        reader.onload = () => {
            let src = reader.result;
            this.setCropper(src);
            fileInput.value = '';
        }
        this.fileName = fileInput.files[0].name;
    }
    setCropper(src) {
        let image = this.$qs('#image');
        this.src = image.src = src;
        if (this.cropper) {
            this.cropper.destroy();
        }
        this.cropper = new cropper(image);
        // this.cropper.setAspectRatio(+this.aspectRatio || (this._width / this._height));
        this.cropper.setAspectRatio(NaN);
        this.isReady = true;
        this.$update();
    }
    isOk(e) {
        const base64 = this.cropper.getCroppedCanvas({
            width: this._width,
            height: this._height
        }).toDataURL();
        let byteCharacters = atob(base64.split('base64,')[1]);
        let byteArrays = [];
        for (let i = 0; i < byteCharacters.length; i++) {
            byteArrays.push(byteCharacters.charCodeAt(i));
        }
        let blob = new Blob([new Uint8Array(byteArrays)], { type: 'image/png' });

        let blobUrl = URL.createObjectURL(blob);
        window.open(blobUrl, '_blank');

        const file = new File([blob], "fileNameGoesHere", { type: "image/png" });
        var options = {
            file,
            quality: 0.6,
            mimeType: 'image/jpeg',
            maxWidth: this._width,
            maxHeight: this._height,
            width: this._width,
            height: this._height,
            minWidth: this._width,
            minHeight: this._height,
            convertSize: Infinity,
            loose: true,
            redressOrientation: true,
            // Callback before compression
            beforeCompress: (result) => {
                console.log('Image size before compression:', result.size);
                console.log('mime type:', result.type);
            },
            // Compression success callback
            success: (result) => {
                let reader = new FileReader();
                reader.readAsDataURL(result);
                reader.onload = () => {
                    this.props.img = reader.result;
                    this.$0.fire('changedAvatar', this.props.img);
                    this.fire('changeAvatar', this.props.img);
                    this.props.showAvatar = false;
                    this.$update();
                }

                console.log('result:', result)
                console.log('Image size after compression:', result.size);
                console.log('mime type:', result.type);
                console.log('Actual compression ratio:', ((file.size - result.size) / file.size * 100).toFixed(2) + '%');
            },
            // An error occurred
            error: (msg) => {
                console.error(msg);
            }
        }
        new ImageCompressor(options);
    }
    isCancel() {
        this.props.img = '';
        this.fire('changedAvatar', '');
        this.fire('changeAvatar', '');
        this.props.showAvatar = false;
        this.$update();
    }
})

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

const usedIcons =
{
    "fc-remove_image": "<svg version=\"1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" enable-background=\"new 0 0 48 48\">\r\n    <path fill=\"#8CBCD6\" d=\"M40,41H8c-2.2,0-4-1.8-4-4V11c0-2.2,1.8-4,4-4h32c2.2,0,4,1.8,4,4v26C44,39.2,42.2,41,40,41z\"/>\r\n    <circle fill=\"#B3DDF5\" cx=\"35\" cy=\"16\" r=\"3\"/>\r\n    <polygon fill=\"#9AC9E3\" points=\"20,16 9,32 31,32\"/>\r\n    <polygon fill=\"#B3DDF5\" points=\"31,22 23,32 39,32\"/>\r\n    <circle fill=\"#F44336\" cx=\"38\" cy=\"38\" r=\"10\"/>\r\n    <g fill=\"#fff\">\r\n        <rect x=\"36.5\" y=\"32\" transform=\"matrix(-.707 .707 -.707 -.707 91.74 38)\" width=\"3\" height=\"12\"/>\r\n        <rect x=\"36.5\" y=\"32\" transform=\"matrix(-.707 -.707 .707 -.707 38 91.74)\" width=\"3\" height=\"12\"/>\r\n    </g>\r\n</svg>\r\n",
    "fc-ok": "<svg version=\"1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" enable-background=\"new 0 0 48 48\">\r\n    <circle fill=\"#4CAF50\" cx=\"24\" cy=\"24\" r=\"21\"/>\r\n    <polygon fill=\"#CCFF90\" points=\"34.6,14.6 21,28.2 15.4,22.6 12.6,25.4 21,33.8 37.4,17.4\"/>\r\n</svg>\r\n",
    "fc-add_image": "<svg version=\"1\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 48 48\" enable-background=\"new 0 0 48 48\">\r\n    <path fill=\"#8CBCD6\" d=\"M40,41H8c-2.2,0-4-1.8-4-4V11c0-2.2,1.8-4,4-4h32c2.2,0,4,1.8,4,4v26C44,39.2,42.2,41,40,41z\"/>\r\n    <circle fill=\"#B3DDF5\" cx=\"35\" cy=\"16\" r=\"3\"/>\r\n    <polygon fill=\"#9AC9E3\" points=\"20,16 9,32 31,32\"/>\r\n    <polygon fill=\"#B3DDF5\" points=\"31,22 23,32 39,32\"/>\r\n    <circle fill=\"#43A047\" cx=\"38\" cy=\"38\" r=\"10\"/>\r\n    <g fill=\"#fff\">\r\n        <rect x=\"36\" y=\"32\" width=\"4\" height=\"12\"/>\r\n        <rect x=\"32\" y=\"36\" width=\"12\" height=\"4\"/>\r\n    </g>\r\n</svg>\r\n"
}

FX.setIcons(usedIcons);
