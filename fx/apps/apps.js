import { FxElement, html, css } from '/fx.js';
import { $apps } from './apps.x.js';
import '../button/button.js';

customElements.define('fx-apps', class FxApps extends FxElement {
    static properties = {
            mediaPath: { type: String, default: '', local: true },
            src: { type: String, notify: true }
    }
    get iframe() { return this.$qs('iframe') }

    async firstUpdated() {
        super.firstUpdated();
        // console.log($apps);
        let src = window.location.href.split('#')?.[1];
        src ||= window.location.href.split('?')?.[1];
        src ||= this.src || 'anime';
        if (src) {
            try {
                if ($apps[src]) {
                    src = $apps[src];
                    this.iframe.src = src;
                    this.$update();
                }
            //     if (src.startsWith('~')) {
            //         src = src.replace('~', '');
            //         if (this.mediaPath) {
            //             src = this.mediaPath + src;
            //         } else {
            //             let port = location.port,
            //                 origin = location.origin;
            //             if (port) {
            //                 origin = origin.replace(port, (+port + 1) + '');
            //                 src = origin + '/~apps~/' + src + '.js';
            //             }
            //         }
            //         src = await import(src);
            //     } else {
            //         src = await import(this.$url.replace('apps.js', 'src/' + src + '.js'));
            //     }
            //     src = location.origin + src?.default?.src || '';
            //     this.$qs('iframe').src = src;
            //     this.$update();
            } catch (error) {
            //     this.$qs('iframe').srcdoc = '<div style="color: red; font-size: 24px; margin-bottom: 4px">... error ...</div>.' + error;
            //     this.$update();
            }
        }
    }

    render() {
        return html`
            <div class="vertical flex relative w100 h100">
                <iframe style="border: none; width: 100%; height: 100%"></iframe>
            </div>
        `
    }
})
