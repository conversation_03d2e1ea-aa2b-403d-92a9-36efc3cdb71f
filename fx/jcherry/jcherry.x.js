import { css } from '/fx.js';

export const $styles = {
    main: css`
        iframe {
            width: 100%;
            height: 0px;
            opacity: 0;
            border: none;
        }
        #html-wrapper {
            width: 100%;
            height: 0px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        #html-view {
            width: 95%;
            /* max-width: 1200px; */
            flex: 1;
            line-height: 22px;
        }
        img {
            max-width: 90%;
        }
        .anchor {
            color: transparent !important;
        }
        blockquote {
            border-left: 4px solid #d6dbdf;
            padding: 8px;
            background: #fcfcfc;
        }
        table { border-collapse: collapse; width: 100%; margin-bottom: 16px;}
        th { background: lightgray; border: 1px solid darkgray; padding: 2px; }
        td { border: 1px solid lightgray; padding: 2px; }
        .code-line {
            display: flex;
            flex-wrap: wrap;
        }
        [alt=tr5] {
            transform:translate(0, 5px);
        }
        [alt=tr6] {
            transform:translate(0, 6px);
        }
        [alt=tr7] {
            transform:translate(0, 7px);
        }
        [alt=tr8] {
            transform:translate(0, 8px);
        }
        [alt=tr9] {
            transform:translate(0, 9px);
        }
        [alt=tr10] {
            transform:translate(0, 10px);
        }
        [alt=tr11] {
            transform:translate(0, 11px);
        }
        [alt=tr12] {
            transform:translate(0, 12px);
        }
    `
}

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

// const usedIcons =
// {
// }

// FX.setIcons(usedIcons);