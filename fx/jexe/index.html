<script type="module" src="./jexe.js"></script>

<fx-jexe id="editor"></fx-jexe>

<script type="module">
    editor.cell = {
        useJson: true,
        cell_h: 'calc(100vh - 2px)',
        sourceHTML: `
<h1>Counter demo</h1>
<span>counts: </span> 
<span id="counter">0</span>
        `,
        sourceJS: `
let upto=0;
const counter = document.getElementById("counter");
setInterval(() => {
	 counter.innerHTML=++upto;
}, 100)
        `,
        sourceCSS: `
h1 { color: red; }
#counter {
	font-size: 24px;
	color: blue;
}
        `,
        sourceJSON: `{}`
    }
</script>