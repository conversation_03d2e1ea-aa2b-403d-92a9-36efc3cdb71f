import { FxElement, html, unsafeSVG } from '/fx.js';

import { $styles } from './jgallery-h.js';
import '../icon/icon.js';

export class FxJGallery extends FxElement {
    static properties = {
        mediaPath: { type: String, default: '' },
        url: { type: String, default: '' },
        edSrc: { type: String, default: '' },
        images: { type: String, default: '' },
        type: { type: String, default: 'gallery' },
        showSets: { type: Boolean }
    }

    get editor() { return this.$qs('.editor') }
    get input() { return this.$qs('#input') }
    get iframe() { return this.$qs('iframe') }

    async firstUpdated() {
        super.firstUpdated();
        await new Promise((r) => setTimeout(r, 0));
        if (this.cell) {
            this.edSrc = this.cell.edSrc || this.edSrc || '';
            this.url = this.cell.url || this.url || '';
            this.url = this.url.replace('li-fx', '_fx');
        }
        if (this.images) {
            this.setIframe();
        } else {
            this._checkURL(this.url);
        }
    }

    async loadFile(e) {
        const file = e.target?.files[0];
        this.url = file.name;
        this.editor.value = this.url;
        const reader = new FileReader();
        reader.onload = async (e) => {
            let url = e.target.result;
            let res = await fetch(url);
            res = await res.text();
            this.editor.value = res;
            this.$update();
        }
        reader.readAsDataURL(file);
    }
    async setSets(e) {
        e.stopPropagation();
        e.preventDefault();
        if (this.showSets) {
            this.url = this.input.value;
            this.edSrc = this.editor.value;
            if (this.cell) {
                this.cell.url = this.url;
                this.cell.edSrc = this.edSrc;
            }
            this._checkURL();
            this.showSets = false;
        } else {
            this.showSets = true;
            await new Promise((r) => setTimeout(r, 0));
            this.input.value = this.cell?.url || this.url || '';
            this.editor.value = this.cell?.edSrc || this.edSrc || '';
            if (!this.input.value && !this.editor.value)
                this.editor.value = defEdSrc;
        }
    }

    async _checkURL(url = this.url) {
        this.iframe.srcdoc = this._src = '';
        this.images = undefined;
        if ((this.edSrc.includes('</script>') || this.edSrc.includes('</iframe>')) && this.iframe) {
            setTimeout(() => {
                this._srcDoc = this.edSrc;
                this.iframe.srcdoc = this.edSrc;
            }, 100)
            return;
        }
        if (!url && !this.edSrc)
            return;
        if (this.edSrc && this.edSrc.includes('.')) {
            if (this.edSrc.includes('prefix:')) {
                let source = new Function(`return {${this.edSrc}}`);
                source = source();
                this.getImages(url, source);
            } else {
                let images = this.edSrc.split('\n');
                images = images.map(i => this.url + i);
                this.images = JSON.stringify(images || []);
            }
        } else {
            if (url.startsWith('/~') || url.startsWith('~')) {
                if (url.startsWith('~')) url = url.replace('~', '/');
                if (this.mediaPath) {
                    url = this.mediaPath + url;
                } else {
                    let port = location.port,
                        origin = location.origin;
                    if (port) {
                        origin = origin.replace(port, (+port + 1) + '');
                        url = origin + url;
                    }
                }
            }
            if (url.startsWith('http') || url.startsWith('../') || url.startsWith('./') || url.startsWith('/')) {
                let _source;
                try {
                    let { source } = await import(url);
                    _source = source;
                } catch (error) {
                    console.log(error);
                }
                if (_source?.images?.length) {
                    this.images = JSON.stringify(_source.images);
                } else {
                    if (_source) {
                        let _url = url.replace(url.split('/').at(-1), '');
                        this.getImages(_url, _source);
                    }
                    if (!this.images?.length) {
                        this.iframe.removeAttribute('srcdoc');
                        this.iframe.src = this._src = url;
                        this.$update();
                        return;
                    }
                }
            } else {
                return;
            }
        }
        if (!this.images?.length)
            return;
        this.setIframe();
        this.$update();
    }
    getImages(url, source) {
        const images = [];
        for (let i = source.start; i <= source.end; i++) {
            const _i = source.length > 0 ? (i + '').padStart(source.length, "0") : i;
            const name = url + (source.prefix || '') + _i + (source.ext || 'jpg');
            images.push(name);
        }
        this.images = JSON.stringify(images || []);
    }
    openFile() {
        if(this._src) {
            window.open(this._src, '_blank');
            return;
        }
        const content = this._srcDoc || this.srcdoc();
        const tab = window.open('about:blank', '_blank');
        tab.document.write(content);
        tab.document.close();
    }
    setIframe() {
        requestAnimationFrame(() => {
            if (this.iframe) {
                this.iframe.srcdoc = this.srcdoc();
            }
        }, 100)
    }

    static styles = [$styles.main]

    render() {
        return html`
            <iframe style="border: none; width: 100%; height: 100%; min-height: 0px; position: relative;"></iframe>
            <fx-icon an="btn2" url="cb-document" class="absolute" fill="gray" style="top: 4px; right: 4px; z-index: 1;" @click=${this.openFile}></fx-icon>
            <fx-icon an="btn2" url=${this.showSets ? 'cb-close' : 'cb-settings'} class="absolute" fill="gray" style="top: 4px; right: 32px; z-index: 1;" @click=${this.setSets}></fx-icon>
            ${this.showSets ? html`
                <div class="vertical flex absolute w100 h100" style="top: 0; background: white;">
                    <div class="horizontal w100" style="height: 32px">
                        <label>url :</label>
                        <input id="input">
                        <fx-icon for="load" an="btn2" url="cb-add" class="absolute" fill="gray" style="top: 4px; right: 60px; z-index: 1;" @click=${() => this.$qs('#load').click()} title="load .js"></fx-icon>
                        <input id="load" type="file" style="display: none" @change=${(e) => this.loadFile(e)}/>
                    </div>
                    <div class="horizontal w100 flex">
                        <label>source</label>
                        <textarea class="editor flex"></textarea>
                    </div>
                </div>
            ` : html``}
        `
    }

    srcdoc(images = this.images) {
        return `
<title>fx-gallery</title>
<style>
    ::-webkit-scrollbar {
        width: 4px;
        height: 4px;
    }
    ::-webkit-scrollbar-track {
        background: #f0f0f0;
    }
    ::-webkit-scrollbar-thumb {
        background-color: #a0a0a0;
    }
    body, html {
        margin: 0;
        width: 100%;
        height: 100%;
    }
</style>
<template id="tmp">
  <li>
    <img alt="" loading="lazy">
  </li>
</template>
<link rel="stylesheet" href="/fx/jgallery/src/style.css">
<script type="module">
    setTimeout(() => {
        tmp.images = '${images}';
        import('/fx/jgallery/src/gallery.js');
    }, 100)
</script>
        `
    }
}
customElements.define('fx-jgallery', FxJGallery)

const defEdSrc =
    `url: /my-gallery/cuba/   (../../cuba/)
source:
prefix: 'cuba-',
start: 1,
end: 15, 
length: 5,
ext: '.jpg'



url: /my-gallery/cuba/   (../../cuba/)
source:
cuba-00001.jpg
...
cuba-00015.jpg



url: /~photo~/cuba/images.js
url: ~/~photo~/cuba/images.js
url: https//site.com/~photo~/cuba/images.js
url: ../../cuba/images.js



source:
https://www.publicalbum.org/blog/embedding-google-photos-albums
<script ...>...</script>
<iframe ...></iframe>
`
