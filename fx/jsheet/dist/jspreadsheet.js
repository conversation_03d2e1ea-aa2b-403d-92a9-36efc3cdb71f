'use strict';

if (! jSuites && typeof(require) === 'function') {
    var jSuites = require('jsuites');
}

;(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
    typeof define === 'function' && define.amd ? define(factory) :
    global.formula = factory();
}(this, (function () {

var Formula;!function(){var n={765:function(n,t){var r;r=function(n){n.version="1.0.2";var t=Math;function r(n,t){for(var r=0,e=0;r<n.length;++r)e=t*e+n[r];return e}function e(n,t,r,e,o){if(0===t)return r;if(1===t)return e;for(var i=2/n,u=e,a=1;a<t;++a)u=e*a*i+o*r,r=e,e=u;return u}function o(n,t,r,o,i){return function(r,u){if(o){if(0===r)return 1==o?-1/0:1/0;if(r<0)return NaN}return 0===u?n(r):1===u?t(r):u<0?NaN:e(r,u|=0,n(r),t(r),i)}}var i,u,a,f,l,c,s,h,g,p,m,d,v,E=function(){var n=.636619772,o=[57568490574,-13362590354,651619640.7,-11214424.18,77392.33017,-184.9052456].reverse(),i=[57568490411,1029532985,9494680.718,59272.64853,267.8532712,1].reverse(),u=[1,-.001098628627,2734510407e-14,-2073370639e-15,2.093887211e-7].reverse(),a=[-.01562499995,.0001430488765,-6911147651e-15,7.621095161e-7,-9.34935152e-8].reverse();function f(e){var f=0,l=0,c=0,s=e*e;if(e<8)f=(l=r(o,s))/(c=r(i,s));else{var h=e-.785398164;l=r(u,s=64/s),c=r(a,s),f=t.sqrt(n/e)*(t.cos(h)*l-t.sin(h)*c*8/e)}return f}var l=[72362614232,-7895059235,242396853.1,-2972611.439,15704.4826,-30.16036606].reverse(),c=[144725228442,2300535178,18583304.74,99447.43394,376.9991397,1].reverse(),s=[1,.00183105,-3516396496e-14,2457520174e-15,-2.40337019e-7].reverse(),h=[.04687499995,-.0002002690873,8449199096e-15,-8.8228987e-7,1.05787412e-7].reverse();function g(e){var o=0,i=0,u=0,a=e*e,f=t.abs(e)-2.356194491;return Math.abs(e)<8?o=(i=e*r(l,a))/(u=r(c,a)):(i=r(s,a=64/a),u=r(h,a),o=t.sqrt(n/t.abs(e))*(t.cos(f)*i-t.sin(f)*u*8/t.abs(e)),e<0&&(o=-o)),o}return function n(r,o){if(o=Math.round(o),!isFinite(r))return isNaN(r)?r:0;if(o<0)return(o%2?-1:1)*n(r,-o);if(r<0)return(o%2?-1:1)*n(-r,o);if(0===o)return f(r);if(1===o)return g(r);if(0===r)return 0;var i=0;if(r>o)i=e(r,o,f(r),g(r),-1);else{for(var u=!1,a=0,l=0,c=1,s=0,h=2/r,p=2*t.floor((o+t.floor(t.sqrt(40*o)))/2);p>0;p--)s=p*h*c-a,a=c,c=s,t.abs(c)>1e10&&(c*=1e-10,a*=1e-10,i*=1e-10,l*=1e-10),u&&(l+=c),u=!u,p==o&&(i=a);i/=l=2*l-c}return i}}(),M=(i=.636619772,u=[-2957821389,7062834065,-512359803.6,10879881.29,-86327.92757,228.4622733].reverse(),a=[40076544269,745249964.8,7189466.438,47447.2647,226.1030244,1].reverse(),f=[1,-.001098628627,2734510407e-14,-2073370639e-15,2.093887211e-7].reverse(),l=[-.01562499995,.0001430488765,-6911147651e-15,7.621095161e-7,-9.34945152e-8].reverse(),c=[-4900604943e3,127527439e4,-51534381390,734926455.1,-4237922.726,8511.937935].reverse(),s=[249958057e5,424441966400,3733650367,22459040.02,102042.605,354.9632885,1].reverse(),h=[1,.00183105,-3516396496e-14,2457520174e-15,-2.40337019e-7].reverse(),g=[.04687499995,-.0002002690873,8449199096e-15,-8.8228987e-7,1.05787412e-7].reverse(),o((function(n){var e=0,o=0,c=0,s=n*n,h=n-.785398164;return n<8?e=(o=r(u,s))/(c=r(a,s))+i*E(n,0)*t.log(n):(o=r(f,s=64/s),c=r(l,s),e=t.sqrt(i/n)*(t.sin(h)*o+t.cos(h)*c*8/n)),e}),(function(n){var e=0,o=0,u=0,a=n*n,f=n-2.356194491;return n<8?e=(o=n*r(c,a))/(u=r(s,a))+i*(E(n,1)*t.log(n)-1/n):(o=r(h,a=64/a),u=r(g,a),e=t.sqrt(i/n)*(t.sin(f)*o+t.cos(f)*u*8/n)),e}),0,1,-1)),N=(p=[1,3.5156229,3.0899424,1.2067492,.2659732,.0360768,.0045813].reverse(),m=[.39894228,.01328592,.00225319,-.00157565,.00916281,-.02057706,.02635537,-.01647633,.00392377].reverse(),d=[.5,.87890594,.51498869,.15084934,.02658733,.00301532,32411e-8].reverse(),v=[.39894228,-.03988024,-.00362018,.00163801,-.01031555,.02282967,-.02895312,.01787654,-.00420059].reverse(),function n(e,o){if(0===(o=Math.round(o)))return function(n){return n<=3.75?r(p,n*n/14.0625):t.exp(t.abs(n))/t.sqrt(t.abs(n))*r(m,3.75/t.abs(n))}(e);if(1===o)return function(n){return n<3.75?n*r(d,n*n/14.0625):(n<0?-1:1)*t.exp(t.abs(n))/t.sqrt(t.abs(n))*r(v,3.75/t.abs(n))}(e);if(o<0)return NaN;if(0===t.abs(e))return 0;if(e==1/0)return 1/0;var i,u=0,a=2/t.abs(e),f=0,l=1,c=0;for(i=2*t.round((o+t.round(t.sqrt(40*o)))/2);i>0;i--)c=i*a*l+f,f=l,l=c,t.abs(l)>1e10&&(l*=1e-10,f*=1e-10,u*=1e-10),i==o&&(u=f);return u*=n(e,0)/l,e<0&&o%2?-u:u}),w=function(){var n=[-.57721566,.4227842,.23069756,.0348859,.00262698,1075e-7,74e-7].reverse(),e=[1.25331414,-.07832358,.02189568,-.01062446,.00587872,-.0025154,53208e-8].reverse(),i=[1,.15443144,-.67278579,-.18156897,-.01919402,-.00110404,-4686e-8].reverse(),u=[1.25331414,.23498619,-.0365562,.01504268,-.00780353,.00325614,-68245e-8].reverse();return o((function(o){return o<=2?-t.log(o/2)*N(o,0)+r(n,o*o/4):t.exp(-o)/t.sqrt(o)*r(e,2/o)}),(function(n){return n<=2?t.log(n/2)*N(n,1)+1/n*r(i,n*n/4):t.exp(-n)/t.sqrt(n)*r(u,2/n)}),0,2,1)}();n.besselj=E,n.bessely=M,n.besseli=N,n.besselk=w},"undefined"==typeof DO_NOT_EXPORT_BESSEL?r(t):r({})},162:function(n){var t;n.exports=(t=function(n,t){var r=Array.prototype.concat,e=Array.prototype.slice,o=Object.prototype.toString;function i(t,r){var e=t>r?t:r;return n.pow(10,17-~~(n.log(e>0?e:-e)*n.LOG10E))}var u=Array.isArray||function(n){return"[object Array]"===o.call(n)};function a(n){return"[object Function]"===o.call(n)}function f(n){return"number"==typeof n&&n-n==0}function l(){return new l._init(arguments)}function c(){return 0}function s(){return 1}function h(n,t){return n===t?1:0}l.fn=l.prototype,l._init=function(n){if(u(n[0]))if(u(n[0][0])){a(n[1])&&(n[0]=l.map(n[0],n[1]));for(var t=0;t<n[0].length;t++)this[t]=n[0][t];this.length=n[0].length}else this[0]=a(n[1])?l.map(n[0],n[1]):n[0],this.length=1;else if(f(n[0]))this[0]=l.seq.apply(null,n),this.length=1;else{if(n[0]instanceof l)return l(n[0].toArray());this[0]=[],this.length=1}return this},l._init.prototype=l.prototype,l._init.constructor=l,l.utils={calcRdx:i,isArray:u,isFunction:a,isNumber:f,toVector:function(n){return r.apply([],n)}},l._random_fn=n.random,l.setRandom=function(n){if("function"!=typeof n)throw new TypeError("fn is not a function");l._random_fn=n},l.extend=function(n){var t,r;if(1===arguments.length){for(r in n)l[r]=n[r];return this}for(t=1;t<arguments.length;t++)for(r in arguments[t])n[r]=arguments[t][r];return n},l.rows=function(n){return n.length||1},l.cols=function(n){return n[0].length||1},l.dimensions=function(n){return{rows:l.rows(n),cols:l.cols(n)}},l.row=function(n,t){return u(t)?t.map((function(t){return l.row(n,t)})):n[t]},l.rowa=function(n,t){return l.row(n,t)},l.col=function(n,t){if(u(t)){var r=l.arange(n.length).map((function(){return new Array(t.length)}));return t.forEach((function(t,e){l.arange(n.length).forEach((function(o){r[o][e]=n[o][t]}))})),r}for(var e=new Array(n.length),o=0;o<n.length;o++)e[o]=[n[o][t]];return e},l.cola=function(n,t){return l.col(n,t).map((function(n){return n[0]}))},l.diag=function(n){for(var t=l.rows(n),r=new Array(t),e=0;e<t;e++)r[e]=[n[e][e]];return r},l.antidiag=function(n){for(var t=l.rows(n)-1,r=new Array(t),e=0;t>=0;t--,e++)r[e]=[n[e][t]];return r},l.transpose=function(n){var t,r,e,o,i,a=[];for(u(n[0])||(n=[n]),r=n.length,e=n[0].length,i=0;i<e;i++){for(t=new Array(r),o=0;o<r;o++)t[o]=n[o][i];a.push(t)}return 1===a.length?a[0]:a},l.map=function(n,t,r){var e,o,i,a,f;for(u(n[0])||(n=[n]),o=n.length,i=n[0].length,a=r?n:new Array(o),e=0;e<o;e++)for(a[e]||(a[e]=new Array(i)),f=0;f<i;f++)a[e][f]=t(n[e][f],e,f);return 1===a.length?a[0]:a},l.cumreduce=function(n,t,r){var e,o,i,a,f;for(u(n[0])||(n=[n]),o=n.length,i=n[0].length,a=r?n:new Array(o),e=0;e<o;e++)for(a[e]||(a[e]=new Array(i)),i>0&&(a[e][0]=n[e][0]),f=1;f<i;f++)a[e][f]=t(a[e][f-1],n[e][f]);return 1===a.length?a[0]:a},l.alter=function(n,t){return l.map(n,t,!0)},l.create=function(n,t,r){var e,o,i=new Array(n);for(a(t)&&(r=t,t=n),e=0;e<n;e++)for(i[e]=new Array(t),o=0;o<t;o++)i[e][o]=r(e,o);return i},l.zeros=function(n,t){return f(t)||(t=n),l.create(n,t,c)},l.ones=function(n,t){return f(t)||(t=n),l.create(n,t,s)},l.rand=function(n,t){return f(t)||(t=n),l.create(n,t,l._random_fn)},l.identity=function(n,t){return f(t)||(t=n),l.create(n,t,h)},l.symmetric=function(n){var t,r,e=n.length;if(n.length!==n[0].length)return!1;for(t=0;t<e;t++)for(r=0;r<e;r++)if(n[r][t]!==n[t][r])return!1;return!0},l.clear=function(n){return l.alter(n,c)},l.seq=function(n,t,r,e){a(e)||(e=!1);var o,u=[],f=i(n,t),l=(t*f-n*f)/((r-1)*f),c=n;for(o=0;c<=t&&o<r;c=(n*f+l*f*++o)/f)u.push(e?e(c,o):c);return u},l.arange=function(n,r,e){var o,i=[];if(e=e||1,r===t&&(r=n,n=0),n===r||0===e)return[];if(n<r&&e<0)return[];if(n>r&&e>0)return[];if(e>0)for(o=n;o<r;o+=e)i.push(o);else for(o=n;o>r;o+=e)i.push(o);return i},l.slice=function(){function n(n,r,e,o){var i,u=[],a=n.length;if(r===t&&e===t&&o===t)return l.copy(n);if(o=o||1,(r=(r=r||0)>=0?r:a+r)===(e=(e=e||n.length)>=0?e:a+e)||0===o)return[];if(r<e&&o<0)return[];if(r>e&&o>0)return[];if(o>0)for(i=r;i<e;i+=o)u.push(n[i]);else for(i=r;i>e;i+=o)u.push(n[i]);return u}return function(t,r){var e,o;return f((r=r||{}).row)?f(r.col)?t[r.row][r.col]:n(l.rowa(t,r.row),(e=r.col||{}).start,e.end,e.step):f(r.col)?n(l.cola(t,r.col),(o=r.row||{}).start,o.end,o.step):(o=r.row||{},e=r.col||{},n(t,o.start,o.end,o.step).map((function(t){return n(t,e.start,e.end,e.step)})))}}(),l.sliceAssign=function(r,e,o){var i,u;if(f(e.row)){if(f(e.col))return r[e.row][e.col]=o;e.col=e.col||{},e.col.start=e.col.start||0,e.col.end=e.col.end||r[0].length,e.col.step=e.col.step||1,i=l.arange(e.col.start,n.min(r.length,e.col.end),e.col.step);var a=e.row;return i.forEach((function(n,t){r[a][n]=o[t]})),r}if(f(e.col)){e.row=e.row||{},e.row.start=e.row.start||0,e.row.end=e.row.end||r.length,e.row.step=e.row.step||1,u=l.arange(e.row.start,n.min(r[0].length,e.row.end),e.row.step);var c=e.col;return u.forEach((function(n,t){r[n][c]=o[t]})),r}return o[0].length===t&&(o=[o]),e.row.start=e.row.start||0,e.row.end=e.row.end||r.length,e.row.step=e.row.step||1,e.col.start=e.col.start||0,e.col.end=e.col.end||r[0].length,e.col.step=e.col.step||1,u=l.arange(e.row.start,n.min(r.length,e.row.end),e.row.step),i=l.arange(e.col.start,n.min(r[0].length,e.col.end),e.col.step),u.forEach((function(n,t){i.forEach((function(e,i){r[n][e]=o[t][i]}))})),r},l.diagonal=function(n){var t=l.zeros(n.length,n.length);return n.forEach((function(n,r){t[r][r]=n})),t},l.copy=function(n){return n.map((function(n){return f(n)?n:n.map((function(n){return n}))}))};var g=l.prototype;return g.length=0,g.push=Array.prototype.push,g.sort=Array.prototype.sort,g.splice=Array.prototype.splice,g.slice=Array.prototype.slice,g.toArray=function(){return this.length>1?e.call(this):e.call(this)[0]},g.map=function(n,t){return l(l.map(this,n,t))},g.cumreduce=function(n,t){return l(l.cumreduce(this,n,t))},g.alter=function(n){return l.alter(this,n),this},function(n){for(var t=0;t<n.length;t++)!function(n){g[n]=function(t){var r,e=this;return t?(setTimeout((function(){t.call(e,g[n].call(e))})),this):(r=l[n](this),u(r)?l(r):r)}}(n[t])}("transpose clear symmetric rows cols dimensions diag antidiag".split(" ")),function(n){for(var t=0;t<n.length;t++)!function(n){g[n]=function(t,r){var e=this;return r?(setTimeout((function(){r.call(e,g[n].call(e,t))})),this):l(l[n](this,t))}}(n[t])}("row col".split(" ")),function(n){for(var t=0;t<n.length;t++)!function(n){g[n]=function(){return l(l[n].apply(null,arguments))}}(n[t])}("create zeros ones rand identity".split(" ")),l}(Math),function(n,t){var r=n.utils.isFunction;function e(n,t){return n-t}function o(n,r,e){return t.max(r,t.min(n,e))}n.sum=function(n){for(var t=0,r=n.length;--r>=0;)t+=n[r];return t},n.sumsqrd=function(n){for(var t=0,r=n.length;--r>=0;)t+=n[r]*n[r];return t},n.sumsqerr=function(t){for(var r,e=n.mean(t),o=0,i=t.length;--i>=0;)o+=(r=t[i]-e)*r;return o},n.sumrow=function(n){for(var t=0,r=n.length;--r>=0;)t+=n[r];return t},n.product=function(n){for(var t=1,r=n.length;--r>=0;)t*=n[r];return t},n.min=function(n){for(var t=n[0],r=0;++r<n.length;)n[r]<t&&(t=n[r]);return t},n.max=function(n){for(var t=n[0],r=0;++r<n.length;)n[r]>t&&(t=n[r]);return t},n.unique=function(n){for(var t={},r=[],e=0;e<n.length;e++)t[n[e]]||(t[n[e]]=!0,r.push(n[e]));return r},n.mean=function(t){return n.sum(t)/t.length},n.meansqerr=function(t){return n.sumsqerr(t)/t.length},n.geomean=function(r){var e=r.map(t.log),o=n.mean(e);return t.exp(o)},n.median=function(n){var t=n.length,r=n.slice().sort(e);return 1&t?r[t/2|0]:(r[t/2-1]+r[t/2])/2},n.cumsum=function(t){return n.cumreduce(t,(function(n,t){return n+t}))},n.cumprod=function(t){return n.cumreduce(t,(function(n,t){return n*t}))},n.diff=function(n){var t,r=[],e=n.length;for(t=1;t<e;t++)r.push(n[t]-n[t-1]);return r},n.rank=function(n){var t,r=[],o={};for(t=0;t<n.length;t++)o[f=n[t]]?o[f]++:(o[f]=1,r.push(f));var i=r.sort(e),u={},a=1;for(t=0;t<i.length;t++){var f,l=o[f=i[t]],c=(a+(a+l-1))/2;u[f]=c,a+=l}return n.map((function(n){return u[n]}))},n.mode=function(n){var t,r=n.length,o=n.slice().sort(e),i=1,u=0,a=0,f=[];for(t=0;t<r;t++)o[t]===o[t+1]?i++:(i>u?(f=[o[t]],u=i,a=0):i===u&&(f.push(o[t]),a++),i=1);return 0===a?f[0]:f},n.range=function(t){return n.max(t)-n.min(t)},n.variance=function(t,r){return n.sumsqerr(t)/(t.length-(r?1:0))},n.pooledvariance=function(t){return t.reduce((function(t,r){return t+n.sumsqerr(r)}),0)/(t.reduce((function(n,t){return n+t.length}),0)-t.length)},n.deviation=function(t){for(var r=n.mean(t),e=t.length,o=new Array(e),i=0;i<e;i++)o[i]=t[i]-r;return o},n.stdev=function(r,e){return t.sqrt(n.variance(r,e))},n.pooledstdev=function(r){return t.sqrt(n.pooledvariance(r))},n.meandev=function(r){for(var e=n.mean(r),o=[],i=r.length-1;i>=0;i--)o.push(t.abs(r[i]-e));return n.mean(o)},n.meddev=function(r){for(var e=n.median(r),o=[],i=r.length-1;i>=0;i--)o.push(t.abs(r[i]-e));return n.median(o)},n.coeffvar=function(t){return n.stdev(t)/n.mean(t)},n.quartiles=function(n){var r=n.length,o=n.slice().sort(e);return[o[t.round(r/4)-1],o[t.round(r/2)-1],o[t.round(3*r/4)-1]]},n.quantiles=function(n,r,i,u){var a,f,l,c,s,h=n.slice().sort(e),g=[r.length],p=n.length;for(void 0===i&&(i=3/8),void 0===u&&(u=3/8),a=0;a<r.length;a++)l=p*(f=r[a])+(i+f*(1-i-u)),c=t.floor(o(l,1,p-1)),s=o(l-c,0,1),g[a]=(1-s)*h[c-1]+s*h[c];return g},n.percentile=function(n,t,r){var o=n.slice().sort(e),i=t*(o.length+(r?1:-1))+(r?0:1),u=parseInt(i),a=i-u;return u+1<o.length?o[u-1]+a*(o[u]-o[u-1]):o[u-1]},n.percentileOfScore=function(n,t,r){var e,o,i=0,u=n.length,a=!1;for("strict"===r&&(a=!0),o=0;o<u;o++)e=n[o],(a&&e<t||!a&&e<=t)&&i++;return i/u},n.histogram=function(r,e){e=e||4;var o,i=n.min(r),u=(n.max(r)-i)/e,a=r.length,f=[];for(o=0;o<e;o++)f[o]=0;for(o=0;o<a;o++)f[t.min(t.floor((r[o]-i)/u),e-1)]+=1;return f},n.covariance=function(t,r){var e,o=n.mean(t),i=n.mean(r),u=t.length,a=new Array(u);for(e=0;e<u;e++)a[e]=(t[e]-o)*(r[e]-i);return n.sum(a)/(u-1)},n.corrcoeff=function(t,r){return n.covariance(t,r)/n.stdev(t,1)/n.stdev(r,1)},n.spearmancoeff=function(t,r){return t=n.rank(t),r=n.rank(r),n.corrcoeff(t,r)},n.stanMoment=function(r,e){for(var o=n.mean(r),i=n.stdev(r),u=r.length,a=0,f=0;f<u;f++)a+=t.pow((r[f]-o)/i,e);return a/r.length},n.skewness=function(t){return n.stanMoment(t,3)},n.kurtosis=function(t){return n.stanMoment(t,4)-3};var i=n.prototype;!function(t){for(var e=0;e<t.length;e++)!function(t){i[t]=function(e,o){var u=[],a=0,f=this;if(r(e)&&(o=e,e=!1),o)return setTimeout((function(){o.call(f,i[t].call(f,e))})),this;if(this.length>1){for(f=!0===e?this:this.transpose();a<f.length;a++)u[a]=n[t](f[a]);return u}return n[t](this[0],e)}}(t[e])}("cumsum cumprod".split(" ")),function(t){for(var e=0;e<t.length;e++)!function(t){i[t]=function(e,o){var u=[],a=0,f=this;if(r(e)&&(o=e,e=!1),o)return setTimeout((function(){o.call(f,i[t].call(f,e))})),this;if(this.length>1){for("sumrow"!==t&&(f=!0===e?this:this.transpose());a<f.length;a++)u[a]=n[t](f[a]);return!0===e?n[t](n.utils.toVector(u)):u}return n[t](this[0],e)}}(t[e])}("sum sumsqrd sumsqerr sumrow product min max unique mean meansqerr geomean median diff rank mode range variance deviation stdev meandev meddev coeffvar quartiles histogram skewness kurtosis".split(" ")),function(t){for(var e=0;e<t.length;e++)!function(t){i[t]=function(){var e,o=[],u=0,a=this,f=Array.prototype.slice.call(arguments);if(r(f[f.length-1])){e=f[f.length-1];var l=f.slice(0,f.length-1);return setTimeout((function(){e.call(a,i[t].apply(a,l))})),this}e=void 0;var c=function(r){return n[t].apply(a,[r].concat(f))};if(this.length>1){for(a=a.transpose();u<a.length;u++)o[u]=c(a[u]);return o}return c(this[0])}}(t[e])}("quantiles percentileOfScore".split(" "))}(t,Math),function(n,t){n.gammaln=function(n){var r,e,o,i=0,u=[76.18009172947146,-86.50532032941678,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18],a=1.000000000190015;for(o=(e=r=n)+5.5,o-=(r+.5)*t.log(o);i<6;i++)a+=u[i]/++e;return t.log(2.5066282746310007*a/r)-o},n.loggam=function(n){var r,e,o,i,u,a,f,l=[.08333333333333333,-.002777777777777778,.0007936507936507937,-.0005952380952380952,.0008417508417508418,-.001917526917526918,.00641025641025641,-.02955065359477124,.1796443723688307,-1.3924322169059];if(r=n,f=0,1==n||2==n)return 0;for(n<=7&&(r=n+(f=t.floor(7-n))),e=1/(r*r),o=2*t.PI,u=l[9],a=8;a>=0;a--)u*=e,u+=l[a];if(i=u/r+.5*t.log(o)+(r-.5)*t.log(r)-r,n<=7)for(a=1;a<=f;a++)i-=t.log(r-1),r-=1;return i},n.gammafn=function(n){var r,e,o,i,u=[-1.716185138865495,24.76565080557592,-379.80425647094563,629.3311553128184,866.9662027904133,-31451.272968848367,-36144.413418691176,66456.14382024054],a=[-30.8402300119739,315.35062697960416,-1015.1563674902192,-3107.771671572311,22538.11842098015,4755.846277527881,-134659.9598649693,-115132.2596755535],f=!1,l=0,c=0,s=0,h=n;if(n>171.6243769536076)return 1/0;if(h<=0){if(!(i=h%1+36e-17))return 1/0;f=(1&h?-1:1)*t.PI/t.sin(t.PI*i),h=1-h}for(o=h,e=h<1?h++:(h-=l=(0|h)-1)-1,r=0;r<8;++r)s=(s+u[r])*e,c=c*e+a[r];if(i=s/c+1,o<h)i/=o;else if(o>h)for(r=0;r<l;++r)i*=h,h++;return f&&(i=f/i),i},n.gammap=function(t,r){return n.lowRegGamma(t,r)*n.gammafn(t)},n.lowRegGamma=function(r,e){var o,i=n.gammaln(r),u=r,a=1/r,f=a,l=e+1-r,c=1/1e-30,s=1/l,h=s,g=1,p=-~(8.5*t.log(r>=1?r:1/r)+.4*r+17);if(e<0||r<=0)return NaN;if(e<r+1){for(;g<=p;g++)a+=f*=e/++u;return a*t.exp(-e+r*t.log(e)-i)}for(;g<=p;g++)h*=(s=1/(s=(o=-g*(g-r))*s+(l+=2)))*(c=l+o/c);return 1-h*t.exp(-e+r*t.log(e)-i)},n.factorialln=function(t){return t<0?NaN:n.gammaln(t+1)},n.factorial=function(t){return t<0?NaN:n.gammafn(t+1)},n.combination=function(r,e){return r>170||e>170?t.exp(n.combinationln(r,e)):n.factorial(r)/n.factorial(e)/n.factorial(r-e)},n.combinationln=function(t,r){return n.factorialln(t)-n.factorialln(r)-n.factorialln(t-r)},n.permutation=function(t,r){return n.factorial(t)/n.factorial(t-r)},n.betafn=function(r,e){if(!(r<=0||e<=0))return r+e>170?t.exp(n.betaln(r,e)):n.gammafn(r)*n.gammafn(e)/n.gammafn(r+e)},n.betaln=function(t,r){return n.gammaln(t)+n.gammaln(r)-n.gammaln(t+r)},n.betacf=function(n,r,e){var o,i,u,a,f=1e-30,l=1,c=r+e,s=r+1,h=r-1,g=1,p=1-c*n/s;for(t.abs(p)<f&&(p=f),a=p=1/p;l<=100&&(p=1+(i=l*(e-l)*n/((h+(o=2*l))*(r+o)))*p,t.abs(p)<f&&(p=f),g=1+i/g,t.abs(g)<f&&(g=f),a*=(p=1/p)*g,p=1+(i=-(r+l)*(c+l)*n/((r+o)*(s+o)))*p,t.abs(p)<f&&(p=f),g=1+i/g,t.abs(g)<f&&(g=f),a*=u=(p=1/p)*g,!(t.abs(u-1)<3e-7));l++);return a},n.gammapinv=function(r,e){var o,i,u,a,f,l,c=0,s=e-1,h=n.gammaln(e);if(r>=1)return t.max(100,e+100*t.sqrt(e));if(r<=0)return 0;for(e>1?(f=t.log(s),l=t.exp(s*(f-1)-h),a=r<.5?r:1-r,o=(2.30753+.27061*(i=t.sqrt(-2*t.log(a))))/(1+i*(.99229+.04481*i))-i,r<.5&&(o=-o),o=t.max(.001,e*t.pow(1-1/(9*e)-o/(3*t.sqrt(e)),3))):o=r<(i=1-e*(.253+.12*e))?t.pow(r/i,1/e):1-t.log(1-(r-i)/(1-i));c<12;c++){if(o<=0)return 0;if((o-=i=(u=(n.lowRegGamma(e,o)-r)/(i=e>1?l*t.exp(-(o-s)+s*(t.log(o)-f)):t.exp(-o+s*t.log(o)-h)))/(1-.5*t.min(1,u*((e-1)/o-1))))<=0&&(o=.5*(o+i)),t.abs(i)<1e-8*o)break}return o},n.erf=function(n){var r,e,o,i,u=[-1.3026537197817094,.6419697923564902,.019476473204185836,-.00956151478680863,-.000946595344482036,.000366839497852761,42523324806907e-18,-20278578112534e-18,-1624290004647e-18,130365583558e-17,1.5626441722e-8,-8.5238095915e-8,6.529054439e-9,5.059343495e-9,-9.91364156e-10,-2.27365122e-10,96467911e-18,2394038e-18,-6886027e-18,894487e-18,313092e-18,-112708e-18,381e-18,7106e-18,-1523e-18,-94e-18,121e-18,-28e-18],a=u.length-1,f=!1,l=0,c=0;for(n<0&&(n=-n,f=!0),e=4*(r=2/(2+n))-2;a>0;a--)o=l,l=e*l-c+u[a],c=o;return i=r*t.exp(-n*n+.5*(u[0]+e*l)-c),f?i-1:1-i},n.erfc=function(t){return 1-n.erf(t)},n.erfcinv=function(r){var e,o,i,u,a=0;if(r>=2)return-100;if(r<=0)return 100;for(u=r<1?r:2-r,e=-.70711*((2.30753+.27061*(i=t.sqrt(-2*t.log(u/2))))/(1+i*(.99229+.04481*i))-i);a<2;a++)e+=(o=n.erfc(e)-u)/(1.1283791670955126*t.exp(-e*e)-e*o);return r<1?e:-e},n.ibetainv=function(r,e,o){var i,u,a,f,l,c,s,h,g,p,m=e-1,d=o-1,v=0;if(r<=0)return 0;if(r>=1)return 1;for(e>=1&&o>=1?(a=r<.5?r:1-r,c=(2.30753+.27061*(f=t.sqrt(-2*t.log(a))))/(1+f*(.99229+.04481*f))-f,r<.5&&(c=-c),s=(c*c-3)/6,h=2/(1/(2*e-1)+1/(2*o-1)),g=c*t.sqrt(s+h)/h-(1/(2*o-1)-1/(2*e-1))*(s+5/6-2/(3*h)),c=e/(e+o*t.exp(2*g))):(i=t.log(e/(e+o)),u=t.log(o/(e+o)),c=r<(f=t.exp(e*i)/e)/(g=f+(l=t.exp(o*u)/o))?t.pow(e*g*r,1/e):1-t.pow(o*g*(1-r),1/o)),p=-n.gammaln(e)-n.gammaln(o)+n.gammaln(e+o);v<10;v++){if(0===c||1===c)return c;if((c-=f=(l=(n.ibeta(c,e,o)-r)/(f=t.exp(m*t.log(c)+d*t.log(1-c)+p)))/(1-.5*t.min(1,l*(m/c-d/(1-c)))))<=0&&(c=.5*(c+f)),c>=1&&(c=.5*(c+f+1)),t.abs(f)<1e-8*c&&v>0)break}return c},n.ibeta=function(r,e,o){var i=0===r||1===r?0:t.exp(n.gammaln(e+o)-n.gammaln(e)-n.gammaln(o)+e*t.log(r)+o*t.log(1-r));return!(r<0||r>1)&&(r<(e+1)/(e+o+2)?i*n.betacf(r,e,o)/e:1-i*n.betacf(1-r,o,e)/o)},n.randn=function(r,e){var o,i,u,a,f;if(e||(e=r),r)return n.create(r,e,(function(){return n.randn()}));do{o=n._random_fn(),i=1.7156*(n._random_fn()-.5),f=(u=o-.449871)*u+(a=t.abs(i)+.386595)*(.196*a-.25472*u)}while(f>.27597&&(f>.27846||i*i>-4*t.log(o)*o*o));return i/o},n.randg=function(r,e,o){var i,u,a,f,l,c,s=r;if(o||(o=e),r||(r=1),e)return(c=n.zeros(e,o)).alter((function(){return n.randg(r)})),c;r<1&&(r+=1),i=r-1/3,u=1/t.sqrt(9*i);do{do{f=1+u*(l=n.randn())}while(f<=0);f*=f*f,a=n._random_fn()}while(a>1-.331*t.pow(l,4)&&t.log(a)>.5*l*l+i*(1-f+t.log(f)));if(r==s)return i*f;do{a=n._random_fn()}while(0===a);return t.pow(a,1/s)*i*f},function(t){for(var r=0;r<t.length;r++)!function(t){n.fn[t]=function(){return n(n.map(this,(function(r){return n[t](r)})))}}(t[r])}("gammaln gammafn factorial factorialln".split(" ")),function(t){for(var r=0;r<t.length;r++)!function(t){n.fn[t]=function(){return n(n[t].apply(null,arguments))}}(t[r])}("randn".split(" "))}(t,Math),function(n,t){function r(n,r,e,o){for(var i,u=0,a=1,f=1,l=1,c=0,s=0;t.abs((f-s)/f)>o;)s=f,a=l+(i=-(r+c)*(r+e+c)*n/(r+2*c)/(r+2*c+1))*a,f=(u=f+i*u)+(i=(c+=1)*(e-c)*n/(r+2*c-1)/(r+2*c))*f,u/=l=a+i*l,a/=l,f/=l,l=1;return f/r}function e(r,e,o){var i=[.9815606342467192,.9041172563704749,.7699026741943047,.5873179542866175,.3678314989981802,.1252334085114689],u=[.04717533638651183,.10693932599531843,.16007832854334622,.20316742672306592,.2334925365383548,.24914704581340277],a=.5*r;if(a>=8)return 1;var f,l=2*n.normal.cdf(a,0,1,1,0)-1;l=l>=t.exp(-50/o)?t.pow(l,o):0;for(var c=a,s=(8-a)/(f=r>3?2:3),h=c+s,g=0,p=o-1,m=1;m<=f;m++){for(var d=0,v=.5*(h+c),E=.5*(h-c),M=1;M<=12;M++){var N,w=v+E*(6<M?i[(N=12-M+1)-1]:-i[(N=M)-1]),y=w*w;if(y>60)break;var I=2*n.normal.cdf(w,0,1,1,0)*.5-2*n.normal.cdf(w,r,1,1,0)*.5;I>=t.exp(-30/p)&&(d+=I=u[N-1]*t.exp(-.5*y)*t.pow(I,p))}g+=d*=2*E*o/t.sqrt(2*t.PI),c=h,h+=s}return(l+=g)<=t.exp(-30/e)?0:(l=t.pow(l,e))>=1?1:l}!function(t){for(var r=0;r<t.length;r++)!function(t){n[t]=function n(t,r,e){return this instanceof n?(this._a=t,this._b=r,this._c=e,this):new n(t,r,e)},n.fn[t]=function(r,e,o){var i=n[t](r,e,o);return i.data=this,i},n[t].prototype.sample=function(r){var e=this._a,o=this._b,i=this._c;return r?n.alter(r,(function(){return n[t].sample(e,o,i)})):n[t].sample(e,o,i)},function(r){for(var e=0;e<r.length;e++)!function(r){n[t].prototype[r]=function(e){var o=this._a,i=this._b,u=this._c;return e||0===e||(e=this.data),"number"!=typeof e?n.fn.map.call(e,(function(e){return n[t][r](e,o,i,u)})):n[t][r](e,o,i,u)}}(r[e])}("pdf cdf inv".split(" ")),function(r){for(var e=0;e<r.length;e++)!function(r){n[t].prototype[r]=function(){return n[t][r](this._a,this._b,this._c)}}(r[e])}("mean median mode variance".split(" "))}(t[r])}("beta centralF cauchy chisquare exponential gamma invgamma kumaraswamy laplace lognormal noncentralt normal pareto studentt weibull uniform binomial negbin hypgeom poisson triangular tukey arcsine".split(" ")),n.extend(n.beta,{pdf:function(r,e,o){return r>1||r<0?0:1==e&&1==o?1:e<512&&o<512?t.pow(r,e-1)*t.pow(1-r,o-1)/n.betafn(e,o):t.exp((e-1)*t.log(r)+(o-1)*t.log(1-r)-n.betaln(e,o))},cdf:function(t,r,e){return t>1||t<0?1*(t>1):n.ibeta(t,r,e)},inv:function(t,r,e){return n.ibetainv(t,r,e)},mean:function(n,t){return n/(n+t)},median:function(t,r){return n.ibetainv(.5,t,r)},mode:function(n,t){return(n-1)/(n+t-2)},sample:function(t,r){var e=n.randg(t);return e/(e+n.randg(r))},variance:function(n,r){return n*r/(t.pow(n+r,2)*(n+r+1))}}),n.extend(n.centralF,{pdf:function(r,e,o){var i;return r<0?0:e<=2?0===r&&e<2?1/0:0===r&&2===e?1:1/n.betafn(e/2,o/2)*t.pow(e/o,e/2)*t.pow(r,e/2-1)*t.pow(1+e/o*r,-(e+o)/2):(i=e*r/(o+r*e),e*(o/(o+r*e))/2*n.binomial.pdf((e-2)/2,(e+o-2)/2,i))},cdf:function(t,r,e){return t<0?0:n.ibeta(r*t/(r*t+e),r/2,e/2)},inv:function(t,r,e){return e/(r*(1/n.ibetainv(t,r/2,e/2)-1))},mean:function(n,t){return t>2?t/(t-2):void 0},mode:function(n,t){return n>2?t*(n-2)/(n*(t+2)):void 0},sample:function(t,r){return 2*n.randg(t/2)/t/(2*n.randg(r/2)/r)},variance:function(n,t){if(!(t<=4))return 2*t*t*(n+t-2)/(n*(t-2)*(t-2)*(t-4))}}),n.extend(n.cauchy,{pdf:function(n,r,e){return e<0?0:e/(t.pow(n-r,2)+t.pow(e,2))/t.PI},cdf:function(n,r,e){return t.atan((n-r)/e)/t.PI+.5},inv:function(n,r,e){return r+e*t.tan(t.PI*(n-.5))},median:function(n){return n},mode:function(n){return n},sample:function(r,e){return n.randn()*t.sqrt(1/(2*n.randg(.5)))*e+r}}),n.extend(n.chisquare,{pdf:function(r,e){return r<0?0:0===r&&2===e?.5:t.exp((e/2-1)*t.log(r)-r/2-e/2*t.log(2)-n.gammaln(e/2))},cdf:function(t,r){return t<0?0:n.lowRegGamma(r/2,t/2)},inv:function(t,r){return 2*n.gammapinv(t,.5*r)},mean:function(n){return n},median:function(n){return n*t.pow(1-2/(9*n),3)},mode:function(n){return n-2>0?n-2:0},sample:function(t){return 2*n.randg(t/2)},variance:function(n){return 2*n}}),n.extend(n.exponential,{pdf:function(n,r){return n<0?0:r*t.exp(-r*n)},cdf:function(n,r){return n<0?0:1-t.exp(-r*n)},inv:function(n,r){return-t.log(1-n)/r},mean:function(n){return 1/n},median:function(n){return 1/n*t.log(2)},mode:function(){return 0},sample:function(r){return-1/r*t.log(n._random_fn())},variance:function(n){return t.pow(n,-2)}}),n.extend(n.gamma,{pdf:function(r,e,o){return r<0?0:0===r&&1===e?1/o:t.exp((e-1)*t.log(r)-r/o-n.gammaln(e)-e*t.log(o))},cdf:function(t,r,e){return t<0?0:n.lowRegGamma(r,t/e)},inv:function(t,r,e){return n.gammapinv(t,r)*e},mean:function(n,t){return n*t},mode:function(n,t){if(n>1)return(n-1)*t},sample:function(t,r){return n.randg(t)*r},variance:function(n,t){return n*t*t}}),n.extend(n.invgamma,{pdf:function(r,e,o){return r<=0?0:t.exp(-(e+1)*t.log(r)-o/r-n.gammaln(e)+e*t.log(o))},cdf:function(t,r,e){return t<=0?0:1-n.lowRegGamma(r,e/t)},inv:function(t,r,e){return e/n.gammapinv(1-t,r)},mean:function(n,t){return n>1?t/(n-1):void 0},mode:function(n,t){return t/(n+1)},sample:function(t,r){return r/n.randg(t)},variance:function(n,t){if(!(n<=2))return t*t/((n-1)*(n-1)*(n-2))}}),n.extend(n.kumaraswamy,{pdf:function(n,r,e){return 0===n&&1===r?e:1===n&&1===e?r:t.exp(t.log(r)+t.log(e)+(r-1)*t.log(n)+(e-1)*t.log(1-t.pow(n,r)))},cdf:function(n,r,e){return n<0?0:n>1?1:1-t.pow(1-t.pow(n,r),e)},inv:function(n,r,e){return t.pow(1-t.pow(1-n,1/e),1/r)},mean:function(t,r){return r*n.gammafn(1+1/t)*n.gammafn(r)/n.gammafn(1+1/t+r)},median:function(n,r){return t.pow(1-t.pow(2,-1/r),1/n)},mode:function(n,r){if(n>=1&&r>=1&&1!==n&&1!==r)return t.pow((n-1)/(n*r-1),1/n)},variance:function(){throw new Error("variance not yet implemented")}}),n.extend(n.lognormal,{pdf:function(n,r,e){return n<=0?0:t.exp(-t.log(n)-.5*t.log(2*t.PI)-t.log(e)-t.pow(t.log(n)-r,2)/(2*e*e))},cdf:function(r,e,o){return r<0?0:.5+.5*n.erf((t.log(r)-e)/t.sqrt(2*o*o))},inv:function(r,e,o){return t.exp(-1.4142135623730951*o*n.erfcinv(2*r)+e)},mean:function(n,r){return t.exp(n+r*r/2)},median:function(n){return t.exp(n)},mode:function(n,r){return t.exp(n-r*r)},sample:function(r,e){return t.exp(n.randn()*e+r)},variance:function(n,r){return(t.exp(r*r)-1)*t.exp(2*n+r*r)}}),n.extend(n.noncentralt,{pdf:function(r,e,o){return t.abs(o)<1e-14?n.studentt.pdf(r,e):t.abs(r)<1e-14?t.exp(n.gammaln((e+1)/2)-o*o/2-.5*t.log(t.PI*e)-n.gammaln(e/2)):e/r*(n.noncentralt.cdf(r*t.sqrt(1+2/e),e+2,o)-n.noncentralt.cdf(r,e,o))},cdf:function(r,e,o){var i=1e-14;if(t.abs(o)<i)return n.studentt.cdf(r,e);var u=!1;r<0&&(u=!0,o=-o);for(var a=n.normal.cdf(-o,0,1),f=i+1,l=f,c=r*r/(r*r+e),s=0,h=t.exp(-o*o/2),g=t.exp(-o*o/2-.5*t.log(2)-n.gammaln(1.5))*o;s<200||l>i||f>i;)l=f,s>0&&(h*=o*o/(2*s),g*=o*o/(2*(s+.5))),a+=.5*(f=h*n.beta.cdf(c,s+.5,e/2)+g*n.beta.cdf(c,s+1,e/2)),s++;return u?1-a:a}}),n.extend(n.normal,{pdf:function(n,r,e){return t.exp(-.5*t.log(2*t.PI)-t.log(e)-t.pow(n-r,2)/(2*e*e))},cdf:function(r,e,o){return.5*(1+n.erf((r-e)/t.sqrt(2*o*o)))},inv:function(t,r,e){return-1.4142135623730951*e*n.erfcinv(2*t)+r},mean:function(n){return n},median:function(n){return n},mode:function(n){return n},sample:function(t,r){return n.randn()*r+t},variance:function(n,t){return t*t}}),n.extend(n.pareto,{pdf:function(n,r,e){return n<r?0:e*t.pow(r,e)/t.pow(n,e+1)},cdf:function(n,r,e){return n<r?0:1-t.pow(r/n,e)},inv:function(n,r,e){return r/t.pow(1-n,1/e)},mean:function(n,r){if(!(r<=1))return r*t.pow(n,r)/(r-1)},median:function(n,r){return n*(r*t.SQRT2)},mode:function(n){return n},variance:function(n,r){if(!(r<=2))return n*n*r/(t.pow(r-1,2)*(r-2))}}),n.extend(n.studentt,{pdf:function(r,e){return e=e>1e100?1e100:e,1/(t.sqrt(e)*n.betafn(.5,e/2))*t.pow(1+r*r/e,-(e+1)/2)},cdf:function(r,e){var o=e/2;return n.ibeta((r+t.sqrt(r*r+e))/(2*t.sqrt(r*r+e)),o,o)},inv:function(r,e){var o=n.ibetainv(2*t.min(r,1-r),.5*e,.5);return o=t.sqrt(e*(1-o)/o),r>.5?o:-o},mean:function(n){return n>1?0:void 0},median:function(){return 0},mode:function(){return 0},sample:function(r){return n.randn()*t.sqrt(r/(2*n.randg(r/2)))},variance:function(n){return n>2?n/(n-2):n>1?1/0:void 0}}),n.extend(n.weibull,{pdf:function(n,r,e){return n<0||r<0||e<0?0:e/r*t.pow(n/r,e-1)*t.exp(-t.pow(n/r,e))},cdf:function(n,r,e){return n<0?0:1-t.exp(-t.pow(n/r,e))},inv:function(n,r,e){return r*t.pow(-t.log(1-n),1/e)},mean:function(t,r){return t*n.gammafn(1+1/r)},median:function(n,r){return n*t.pow(t.log(2),1/r)},mode:function(n,r){return r<=1?0:n*t.pow((r-1)/r,1/r)},sample:function(r,e){return r*t.pow(-t.log(n._random_fn()),1/e)},variance:function(r,e){return r*r*n.gammafn(1+2/e)-t.pow(n.weibull.mean(r,e),2)}}),n.extend(n.uniform,{pdf:function(n,t,r){return n<t||n>r?0:1/(r-t)},cdf:function(n,t,r){return n<t?0:n<r?(n-t)/(r-t):1},inv:function(n,t,r){return t+n*(r-t)},mean:function(n,t){return.5*(n+t)},median:function(t,r){return n.mean(t,r)},mode:function(){throw new Error("mode is not yet implemented")},sample:function(t,r){return t/2+r/2+(r/2-t/2)*(2*n._random_fn()-1)},variance:function(n,r){return t.pow(r-n,2)/12}}),n.extend(n.binomial,{pdf:function(r,e,o){return 0===o||1===o?e*o===r?1:0:n.combination(e,r)*t.pow(o,r)*t.pow(1-o,e-r)},cdf:function(e,o,i){var u,a=1e-10;if(e<0)return 0;if(e>=o)return 1;if(i<0||i>1||o<=0)return NaN;var f=i,l=(e=t.floor(e))+1,c=o-e,s=l+c,h=t.exp(n.gammaln(s)-n.gammaln(c)-n.gammaln(l)+l*t.log(f)+c*t.log(1-f));return u=f<(l+1)/(s+2)?h*r(f,l,c,a):1-h*r(1-f,c,l,a),t.round(1/a*(1-u))/(1/a)}}),n.extend(n.negbin,{pdf:function(r,e,o){return r===r>>>0&&(r<0?0:n.combination(r+e-1,e-1)*t.pow(1-o,r)*t.pow(o,e))},cdf:function(t,r,e){var o=0,i=0;if(t<0)return 0;for(;i<=t;i++)o+=n.negbin.pdf(i,r,e);return o}}),n.extend(n.hypgeom,{pdf:function(r,e,o,i){if(r!=r|0)return!1;if(r<0||r<o-(e-i))return 0;if(r>i||r>o)return 0;if(2*o>e)return 2*i>e?n.hypgeom.pdf(e-o-i+r,e,e-o,e-i):n.hypgeom.pdf(i-r,e,e-o,i);if(2*i>e)return n.hypgeom.pdf(o-r,e,o,e-i);if(o<i)return n.hypgeom.pdf(r,e,i,o);for(var u=1,a=0,f=0;f<r;f++){for(;u>1&&a<i;)u*=1-o/(e-a),a++;u*=(i-f)*(o-f)/((f+1)*(e-o-i+f+1))}for(;a<i;a++)u*=1-o/(e-a);return t.min(1,t.max(0,u))},cdf:function(r,e,o,i){if(r<0||r<o-(e-i))return 0;if(r>=i||r>=o)return 1;if(2*o>e)return 2*i>e?n.hypgeom.cdf(e-o-i+r,e,e-o,e-i):1-n.hypgeom.cdf(i-r-1,e,e-o,i);if(2*i>e)return 1-n.hypgeom.cdf(o-r-1,e,o,e-i);if(o<i)return n.hypgeom.cdf(r,e,i,o);for(var u=1,a=1,f=0,l=0;l<r;l++){for(;u>1&&f<i;){var c=1-o/(e-f);a*=c,u*=c,f++}u+=a*=(i-l)*(o-l)/((l+1)*(e-o-i+l+1))}for(;f<i;f++)u*=1-o/(e-f);return t.min(1,t.max(0,u))}}),n.extend(n.poisson,{pdf:function(r,e){return e<0||r%1!=0||r<0?0:t.pow(e,r)*t.exp(-e)/n.factorial(r)},cdf:function(t,r){var e=[],o=0;if(t<0)return 0;for(;o<=t;o++)e.push(n.poisson.pdf(o,r));return n.sum(e)},mean:function(n){return n},variance:function(n){return n},sampleSmall:function(r){var e=1,o=0,i=t.exp(-r);do{o++,e*=n._random_fn()}while(e>i);return o-1},sampleLarge:function(r){var e,o,i,u,a,f,l,c,s,h,g=r;for(u=t.sqrt(g),a=t.log(g),f=.02483*(l=.931+2.53*u)-.059,c=1.1239+1.1328/(l-3.4),s=.9277-3.6224/(l-2);;){if(o=t.random()-.5,i=t.random(),h=.5-t.abs(o),e=t.floor((2*f/h+l)*o+g+.43),h>=.07&&i<=s)return e;if(!(e<0||h<.013&&i>h)&&t.log(i)+t.log(c)-t.log(f/(h*h)+l)<=e*a-g-n.loggam(e+1))return e}},sample:function(n){return n<10?this.sampleSmall(n):this.sampleLarge(n)}}),n.extend(n.triangular,{pdf:function(n,t,r,e){return r<=t||e<t||e>r?NaN:n<t||n>r?0:n<e?2*(n-t)/((r-t)*(e-t)):n===e?2/(r-t):2*(r-n)/((r-t)*(r-e))},cdf:function(n,r,e,o){return e<=r||o<r||o>e?NaN:n<=r?0:n>=e?1:n<=o?t.pow(n-r,2)/((e-r)*(o-r)):1-t.pow(e-n,2)/((e-r)*(e-o))},inv:function(n,r,e,o){return e<=r||o<r||o>e?NaN:n<=(o-r)/(e-r)?r+(e-r)*t.sqrt(n*((o-r)/(e-r))):r+(e-r)*(1-t.sqrt((1-n)*(1-(o-r)/(e-r))))},mean:function(n,t,r){return(n+t+r)/3},median:function(n,r,e){return e<=(n+r)/2?r-t.sqrt((r-n)*(r-e))/t.sqrt(2):e>(n+r)/2?n+t.sqrt((r-n)*(e-n))/t.sqrt(2):void 0},mode:function(n,t,r){return r},sample:function(r,e,o){var i=n._random_fn();return i<(o-r)/(e-r)?r+t.sqrt(i*(e-r)*(o-r)):e-t.sqrt((1-i)*(e-r)*(e-o))},variance:function(n,t,r){return(n*n+t*t+r*r-n*t-n*r-t*r)/18}}),n.extend(n.arcsine,{pdf:function(n,r,e){return e<=r?NaN:n<=r||n>=e?0:2/t.PI*t.pow(t.pow(e-r,2)-t.pow(2*n-r-e,2),-.5)},cdf:function(n,r,e){return n<r?0:n<e?2/t.PI*t.asin(t.sqrt((n-r)/(e-r))):1},inv:function(n,r,e){return r+(.5-.5*t.cos(t.PI*n))*(e-r)},mean:function(n,t){return t<=n?NaN:(n+t)/2},median:function(n,t){return t<=n?NaN:(n+t)/2},mode:function(){throw new Error("mode is not yet implemented")},sample:function(r,e){return(r+e)/2+(e-r)/2*t.sin(2*t.PI*n.uniform.sample(0,1))},variance:function(n,r){return r<=n?NaN:t.pow(r-n,2)/8}}),n.extend(n.laplace,{pdf:function(n,r,e){return e<=0?0:t.exp(-t.abs(n-r)/e)/(2*e)},cdf:function(n,r,e){return e<=0?0:n<r?.5*t.exp((n-r)/e):1-.5*t.exp(-(n-r)/e)},mean:function(n){return n},median:function(n){return n},mode:function(n){return n},variance:function(n,t){return 2*t*t},sample:function(r,e){var o,i=n._random_fn()-.5;return r-e*((o=i)/t.abs(o))*t.log(1-2*t.abs(i))}}),n.extend(n.tukey,{cdf:function(r,o,i){var u=o,a=[.9894009349916499,.9445750230732326,.8656312023878318,.755404408355003,.6178762444026438,.45801677765722737,.2816035507792589,.09501250983763744],f=[.027152459411754096,.062253523938647894,.09515851168249279,.12462897125553388,.14959598881657674,.16915651939500254,.18260341504492358,.1894506104550685];if(r<=0)return 0;if(i<2||u<2)return NaN;if(!Number.isFinite(r))return 1;if(i>25e3)return e(r,1,u);var l,c=.5*i,s=c*t.log(i)-i*t.log(2)-n.gammaln(c),h=c-1,g=.25*i;l=i<=100?1:i<=800?.5:i<=5e3?.25:.125,s+=t.log(l);for(var p=0,m=1;m<=50;m++){for(var d=0,v=(2*m-1)*l,E=1;E<=16;E++){var M,N;8<E?(M=E-8-1,N=s+h*t.log(v+a[M]*l)-(a[M]*l+v)*g):(M=E-1,N=s+h*t.log(v-a[M]*l)+(a[M]*l-v)*g),N>=-30&&(d+=e(8<E?r*t.sqrt(.5*(a[M]*l+v)):r*t.sqrt(.5*(-a[M]*l+v)),1,u)*f[M]*t.exp(N))}if(m*l>=1&&d<=1e-14)break;p+=d}if(d>1e-14)throw new Error("tukey.cdf failed to converge");return p>1&&(p=1),p},inv:function(r,e,o){if(o<2||e<2)return NaN;if(r<0||r>1)return NaN;if(0===r)return 0;if(1===r)return 1/0;var i,u=function(n,r,e){var o=.5-.5*n,i=t.sqrt(t.log(1/(o*o))),u=i+((((-453642210148e-16*i-.204231210125)*i-.342242088547)*i-1)*i+.322232421088)/((((.0038560700634*i+.10353775285)*i+.531103462366)*i+.588581570495)*i+.099348462606);e<120&&(u+=(u*u*u+u)/e/4);var a=.8832-.2368*u;return e<120&&(a+=-1.214/e+1.208*u/e),u*(a*t.log(r-1)+1.4142)}(r,e,o),a=n.tukey.cdf(u,e,o)-r;i=a>0?t.max(0,u-1):u+1;for(var f,l=n.tukey.cdf(i,e,o)-r,c=1;c<50;c++)if(f=i-l*(i-u)/(l-a),a=l,u=i,f<0&&(f=0,l=-r),l=n.tukey.cdf(f,e,o)-r,i=f,t.abs(i-u)<1e-4)return f;throw new Error("tukey.inv failed to converge")}})}(t,Math),function(n,t){var r,e,o=Array.prototype.push,i=n.utils.isArray;function u(t){return i(t)||t instanceof n}n.extend({add:function(t,r){return u(r)?(u(r[0])||(r=[r]),n.map(t,(function(n,t,e){return n+r[t][e]}))):n.map(t,(function(n){return n+r}))},subtract:function(t,r){return u(r)?(u(r[0])||(r=[r]),n.map(t,(function(n,t,e){return n-r[t][e]||0}))):n.map(t,(function(n){return n-r}))},divide:function(t,r){return u(r)?(u(r[0])||(r=[r]),n.multiply(t,n.inv(r))):n.map(t,(function(n){return n/r}))},multiply:function(t,r){var e,o,i,a,f,l,c,s;if(void 0===t.length&&void 0===r.length)return t*r;if(f=t.length,l=t[0].length,c=n.zeros(f,i=u(r)?r[0].length:l),s=0,u(r)){for(;s<i;s++)for(e=0;e<f;e++){for(a=0,o=0;o<l;o++)a+=t[e][o]*r[o][s];c[e][s]=a}return 1===f&&1===s?c[0][0]:c}return n.map(t,(function(n){return n*r}))},outer:function(t,r){return n.multiply(t.map((function(n){return[n]})),[r])},dot:function(t,r){u(t[0])||(t=[t]),u(r[0])||(r=[r]);for(var e,o,i=1===t[0].length&&1!==t.length?n.transpose(t):t,a=1===r[0].length&&1!==r.length?n.transpose(r):r,f=[],l=0,c=i.length,s=i[0].length;l<c;l++){for(f[l]=[],e=0,o=0;o<s;o++)e+=i[l][o]*a[l][o];f[l]=e}return 1===f.length?f[0]:f},pow:function(r,e){return n.map(r,(function(n){return t.pow(n,e)}))},exp:function(r){return n.map(r,(function(n){return t.exp(n)}))},log:function(r){return n.map(r,(function(n){return t.log(n)}))},abs:function(r){return n.map(r,(function(n){return t.abs(n)}))},norm:function(n,r){var e=0,o=0;for(isNaN(r)&&(r=2),u(n[0])&&(n=n[0]);o<n.length;o++)e+=t.pow(t.abs(n[o]),r);return t.pow(e,1/r)},angle:function(r,e){return t.acos(n.dot(r,e)/(n.norm(r)*n.norm(e)))},aug:function(n,t){var r,e=[];for(r=0;r<n.length;r++)e.push(n[r].slice());for(r=0;r<e.length;r++)o.apply(e[r],t[r]);return e},inv:function(t){for(var r,e=t.length,o=t[0].length,i=n.identity(e,o),u=n.gauss_jordan(t,i),a=[],f=0;f<e;f++)for(a[f]=[],r=o;r<u[0].length;r++)a[f][r-o]=u[f][r];return a},det:function n(t){if(2===t.length)return t[0][0]*t[1][1]-t[0][1]*t[1][0];for(var r=0,e=0;e<t.length;e++){for(var o=[],i=1;i<t.length;i++){o[i-1]=[];for(var u=0;u<t.length;u++)u<e?o[i-1][u]=t[i][u]:u>e&&(o[i-1][u-1]=t[i][u])}var a=e%2?-1:1;r+=n(o)*t[0][e]*a}return r},gauss_elimination:function(r,e){var o,i,u,a,f=0,l=0,c=r.length,s=r[0].length,h=1,g=0,p=[];for(o=(r=n.aug(r,e))[0].length,f=0;f<c;f++){for(i=r[f][f],l=f,a=f+1;a<s;a++)i<t.abs(r[a][f])&&(i=r[a][f],l=a);if(l!=f)for(a=0;a<o;a++)u=r[f][a],r[f][a]=r[l][a],r[l][a]=u;for(l=f+1;l<c;l++)for(h=r[l][f]/r[f][f],a=f;a<o;a++)r[l][a]=r[l][a]-h*r[f][a]}for(f=c-1;f>=0;f--){for(g=0,l=f+1;l<=c-1;l++)g+=p[l]*r[f][l];p[f]=(r[f][o-1]-g)/r[f][f]}return p},gauss_jordan:function(r,e){var o,i,u,a=n.aug(r,e),f=a.length,l=a[0].length,c=0;for(i=0;i<f;i++){var s=i;for(u=i+1;u<f;u++)t.abs(a[u][i])>t.abs(a[s][i])&&(s=u);var h=a[i];for(a[i]=a[s],a[s]=h,u=i+1;u<f;u++)for(c=a[u][i]/a[i][i],o=i;o<l;o++)a[u][o]-=a[i][o]*c}for(i=f-1;i>=0;i--){for(c=a[i][i],u=0;u<i;u++)for(o=l-1;o>i-1;o--)a[u][o]-=a[i][o]*a[u][i]/c;for(a[i][i]/=c,o=f;o<l;o++)a[i][o]/=c}return a},triaUpSolve:function(t,r){var e,o=t[0].length,i=n.zeros(1,o)[0],u=!1;return null!=r[0].length&&(r=r.map((function(n){return n[0]})),u=!0),n.arange(o-1,-1,-1).forEach((function(u){e=n.arange(u+1,o).map((function(n){return i[n]*t[u][n]})),i[u]=(r[u]-n.sum(e))/t[u][u]})),u?i.map((function(n){return[n]})):i},triaLowSolve:function(t,r){var e,o=t[0].length,i=n.zeros(1,o)[0],u=!1;return null!=r[0].length&&(r=r.map((function(n){return n[0]})),u=!0),n.arange(o).forEach((function(o){e=n.arange(o).map((function(n){return t[o][n]*i[n]})),i[o]=(r[o]-n.sum(e))/t[o][o]})),u?i.map((function(n){return[n]})):i},lu:function(t){var r,e=t.length,o=n.identity(e),i=n.zeros(t.length,t[0].length);return n.arange(e).forEach((function(n){i[0][n]=t[0][n]})),n.arange(1,e).forEach((function(u){n.arange(u).forEach((function(e){r=n.arange(e).map((function(n){return o[u][n]*i[n][e]})),o[u][e]=(t[u][e]-n.sum(r))/i[e][e]})),n.arange(u,e).forEach((function(e){r=n.arange(u).map((function(n){return o[u][n]*i[n][e]})),i[u][e]=t[r.length][e]-n.sum(r)}))})),[o,i]},cholesky:function(r){var e,o=r.length,i=n.zeros(r.length,r[0].length);return n.arange(o).forEach((function(u){e=n.arange(u).map((function(n){return t.pow(i[u][n],2)})),i[u][u]=t.sqrt(r[u][u]-n.sum(e)),n.arange(u+1,o).forEach((function(t){e=n.arange(u).map((function(n){return i[u][n]*i[t][n]})),i[t][u]=(r[u][t]-n.sum(e))/i[u][u]}))})),i},gauss_jacobi:function(r,e,o,i){for(var u,a,f,l,c=0,s=0,h=r.length,g=[],p=[],m=[];c<h;c++)for(g[c]=[],p[c]=[],m[c]=[],s=0;s<h;s++)c>s?(g[c][s]=r[c][s],p[c][s]=m[c][s]=0):c<s?(p[c][s]=r[c][s],g[c][s]=m[c][s]=0):(m[c][s]=r[c][s],g[c][s]=p[c][s]=0);for(f=n.multiply(n.multiply(n.inv(m),n.add(g,p)),-1),a=n.multiply(n.inv(m),e),u=o,l=n.add(n.multiply(f,o),a),c=2;t.abs(n.norm(n.subtract(l,u)))>i;)u=l,l=n.add(n.multiply(f,u),a),c++;return l},gauss_seidel:function(r,e,o,i){for(var u,a,f,l,c,s=0,h=r.length,g=[],p=[],m=[];s<h;s++)for(g[s]=[],p[s]=[],m[s]=[],u=0;u<h;u++)s>u?(g[s][u]=r[s][u],p[s][u]=m[s][u]=0):s<u?(p[s][u]=r[s][u],g[s][u]=m[s][u]=0):(m[s][u]=r[s][u],g[s][u]=p[s][u]=0);for(l=n.multiply(n.multiply(n.inv(n.add(m,g)),p),-1),f=n.multiply(n.inv(n.add(m,g)),e),a=o,c=n.add(n.multiply(l,o),f),s=2;t.abs(n.norm(n.subtract(c,a)))>i;)a=c,c=n.add(n.multiply(l,a),f),s+=1;return c},SOR:function(r,e,o,i,u){for(var a,f,l,c,s,h=0,g=r.length,p=[],m=[],d=[];h<g;h++)for(p[h]=[],m[h]=[],d[h]=[],a=0;a<g;a++)h>a?(p[h][a]=r[h][a],m[h][a]=d[h][a]=0):h<a?(m[h][a]=r[h][a],p[h][a]=d[h][a]=0):(d[h][a]=r[h][a],p[h][a]=m[h][a]=0);for(c=n.multiply(n.inv(n.add(d,n.multiply(p,u))),n.subtract(n.multiply(d,1-u),n.multiply(m,u))),l=n.multiply(n.multiply(n.inv(n.add(d,n.multiply(p,u))),e),u),f=o,s=n.add(n.multiply(c,o),l),h=2;t.abs(n.norm(n.subtract(s,f)))>i;)f=s,s=n.add(n.multiply(c,f),l),h++;return s},householder:function(r){for(var e,o,i,u,a=r.length,f=r[0].length,l=0,c=[],s=[];l<a-1;l++){for(e=0,u=l+1;u<f;u++)e+=r[u][l]*r[u][l];for(e=(r[l+1][l]>0?-1:1)*t.sqrt(e),o=t.sqrt((e*e-r[l+1][l]*e)/2),(c=n.zeros(a,1))[l+1][0]=(r[l+1][l]-e)/(2*o),i=l+2;i<a;i++)c[i][0]=r[i][l]/(2*o);s=n.subtract(n.identity(a,f),n.multiply(n.multiply(c,n.transpose(c)),2)),r=n.multiply(s,n.multiply(r,s))}return r},QR:(r=n.sum,e=n.arange,function(o){var i,u,a,f=o.length,l=o[0].length,c=n.zeros(l,l);for(o=n.copy(o),u=0;u<l;u++){for(c[u][u]=t.sqrt(r(e(f).map((function(n){return o[n][u]*o[n][u]})))),i=0;i<f;i++)o[i][u]=o[i][u]/c[u][u];for(a=u+1;a<l;a++)for(c[u][a]=r(e(f).map((function(n){return o[n][u]*o[n][a]}))),i=0;i<f;i++)o[i][a]=o[i][a]-o[i][u]*c[u][a]}return[o,c]}),lstsq:function(t,r){var e=!1;void 0===r[0].length&&(r=r.map((function(n){return[n]})),e=!0);var o=n.QR(t),i=o[0],u=o[1],a=t[0].length,f=n.slice(i,{col:{end:a}}),l=function(t){var r=(t=n.copy(t)).length,e=n.identity(r);return n.arange(r-1,-1,-1).forEach((function(r){n.sliceAssign(e,{row:r},n.divide(n.slice(e,{row:r}),t[r][r])),n.sliceAssign(t,{row:r},n.divide(n.slice(t,{row:r}),t[r][r])),n.arange(r).forEach((function(o){var i=n.multiply(t[o][r],-1),u=n.slice(t,{row:o}),a=n.multiply(n.slice(t,{row:r}),i);n.sliceAssign(t,{row:o},n.add(u,a));var f=n.slice(e,{row:o}),l=n.multiply(n.slice(e,{row:r}),i);n.sliceAssign(e,{row:o},n.add(f,l))}))})),e}(n.slice(u,{row:{end:a}})),c=n.transpose(f);void 0===c[0].length&&(c=[c]);var s=n.multiply(n.multiply(l,c),r);return void 0===s.length&&(s=[[s]]),e?s.map((function(n){return n[0]})):s},jacobi:function(r){for(var e,o,i,u,a,f,l,c=1,s=r.length,h=n.identity(s,s),g=[];1===c;){for(a=r[0][1],i=0,u=1,e=0;e<s;e++)for(o=0;o<s;o++)e!=o&&a<t.abs(r[e][o])&&(a=t.abs(r[e][o]),i=e,u=o);for(f=r[i][i]===r[u][u]?r[i][u]>0?t.PI/4:-t.PI/4:t.atan(2*r[i][u]/(r[i][i]-r[u][u]))/2,(l=n.identity(s,s))[i][i]=t.cos(f),l[i][u]=-t.sin(f),l[u][i]=t.sin(f),l[u][u]=t.cos(f),h=n.multiply(h,l),r=n.multiply(n.multiply(n.inv(l),r),l),c=0,e=1;e<s;e++)for(o=1;o<s;o++)e!=o&&t.abs(r[e][o])>.001&&(c=1)}for(e=0;e<s;e++)g.push(r[e][e]);return[h,g]},rungekutta:function(n,t,r,e,o,i){var u,a,f;if(2===i)for(;e<=r;)o+=((u=t*n(e,o))+(a=t*n(e+t,o+u)))/2,e+=t;if(4===i)for(;e<=r;)o+=((u=t*n(e,o))+2*(a=t*n(e+t/2,o+u/2))+2*(f=t*n(e+t/2,o+a/2))+t*n(e+t,o+f))/6,e+=t;return o},romberg:function(n,r,e,o){for(var i,u,a,f,l,c=0,s=(e-r)/2,h=[],g=[],p=[];c<o/2;){for(l=n(r),a=r,f=0;a<=e;a+=s,f++)h[f]=a;for(i=h.length,a=1;a<i-1;a++)l+=(a%2!=0?4:2)*n(h[a]);l=s/3*(l+n(e)),p[c]=l,s/=2,c++}for(u=p.length,i=1;1!==u;){for(a=0;a<u-1;a++)g[a]=(t.pow(4,i)*p[a+1]-p[a])/(t.pow(4,i)-1);u=g.length,p=g,g=[],i++}return p},richardson:function(n,r,e,o){function i(n,t){for(var r,e=0,o=n.length;e<o;e++)n[e]===t&&(r=e);return r}for(var u,a,f,l,c,s=t.abs(e-n[i(n,e)+1]),h=0,g=[],p=[];o>=s;)u=i(n,e+o),a=i(n,e),g[h]=(r[u]-2*r[a]+r[2*a-u])/(o*o),o/=2,h++;for(l=g.length,f=1;1!=l;){for(c=0;c<l-1;c++)p[c]=(t.pow(4,f)*g[c+1]-g[c])/(t.pow(4,f)-1);l=p.length,g=p,p=[],f++}return g},simpson:function(n,t,r,e){for(var o,i=(r-t)/e,u=n(t),a=[],f=t,l=0,c=1;f<=r;f+=i,l++)a[l]=f;for(o=a.length;c<o-1;c++)u+=(c%2!=0?4:2)*n(a[c]);return i/3*(u+n(r))},hermite:function(n,t,r,e){for(var o,i=n.length,u=0,a=0,f=[],l=[],c=[],s=[];a<i;a++){for(f[a]=1,o=0;o<i;o++)a!=o&&(f[a]*=(e-n[o])/(n[a]-n[o]));for(l[a]=0,o=0;o<i;o++)a!=o&&(l[a]+=1/(n[a]-n[o]));c[a]=(1-2*(e-n[a])*l[a])*(f[a]*f[a]),s[a]=(e-n[a])*(f[a]*f[a]),u+=c[a]*t[a]+s[a]*r[a]}return u},lagrange:function(n,t,r){for(var e,o,i=0,u=0,a=n.length;u<a;u++){for(o=t[u],e=0;e<a;e++)u!=e&&(o*=(r-n[e])/(n[u]-n[e]));i+=o}return i},cubic_spline:function(t,r,e){for(var o,i,u=t.length,a=0,f=[],l=[],c=[],s=[],h=[],g=[];a<u-1;a++)s[a]=t[a+1]-t[a];for(c[0]=0,a=1;a<u-1;a++)c[a]=3/s[a]*(r[a+1]-r[a])-3/s[a-1]*(r[a]-r[a-1]);for(a=1;a<u-1;a++)f[a]=[],l[a]=[],f[a][a-1]=s[a-1],f[a][a]=2*(s[a-1]+s[a]),f[a][a+1]=s[a],l[a][0]=c[a];for(i=n.multiply(n.inv(f),l),o=0;o<u-1;o++)h[o]=(r[o+1]-r[o])/s[o]-s[o]*(i[o+1][0]+2*i[o][0])/3,g[o]=(i[o+1][0]-i[o][0])/(3*s[o]);for(o=0;o<u&&!(t[o]>e);o++);return r[o-=1]+(e-t[o])*h[o]+n.sq(e-t[o])*i[o]+(e-t[o])*n.sq(e-t[o])*g[o]},gauss_quadrature:function(){throw new Error("gauss_quadrature not yet implemented")},PCA:function(t){var r,e,o,i,u,a=t.length,f=t[0].length,l=0,c=[],s=[],h=[],g=[],p=[],m=[],d=[];for(l=0;l<a;l++)c[l]=n.sum(t[l])/f;for(l=0;l<f;l++)for(p[l]=[],r=0;r<a;r++)p[l][r]=t[r][l]-c[r];for(p=n.transpose(p),l=0;l<a;l++)for(m[l]=[],r=0;r<a;r++)m[l][r]=n.dot([p[l]],[p[r]])/(f-1);for(u=(o=n.jacobi(m))[0],s=o[1],d=n.transpose(u),l=0;l<s.length;l++)for(r=l;r<s.length;r++)s[l]<s[r]&&(e=s[l],s[l]=s[r],s[r]=e,h=d[l],d[l]=d[r],d[r]=h);for(i=n.transpose(p),l=0;l<a;l++)for(g[l]=[],r=0;r<i.length;r++)g[l][r]=n.dot([d[l]],[i[r]]);return[t,s,d,g]}}),function(t){for(var r=0;r<t.length;r++)!function(t){n.fn[t]=function(r,e){var o=this;return e?(setTimeout((function(){e.call(o,n.fn[t].call(o,r))}),15),this):"number"==typeof n[t](this,r)?n[t](this,r):n(n[t](this,r))}}(t[r])}("add divide multiply subtract dot pow exp log abs norm angle".split(" "))}(t,Math),function(n,t){var r=[].slice,e=n.utils.isNumber,o=n.utils.isArray;function i(n,r,e,o){if(n>1||e>1||n<=0||e<=0)throw new Error("Proportions should be greater than 0 and less than 1");var i=(n*r+e*o)/(r+o);return(n-e)/t.sqrt(i*(1-i)*(1/r+1/o))}n.extend({zscore:function(){var t=r.call(arguments);return e(t[1])?(t[0]-t[1])/t[2]:(t[0]-n.mean(t[1]))/n.stdev(t[1],t[2])},ztest:function(){var e,i=r.call(arguments);return o(i[1])?(e=n.zscore(i[0],i[1],i[3]),1===i[2]?n.normal.cdf(-t.abs(e),0,1):2*n.normal.cdf(-t.abs(e),0,1)):i.length>2?(e=n.zscore(i[0],i[1],i[2]),1===i[3]?n.normal.cdf(-t.abs(e),0,1):2*n.normal.cdf(-t.abs(e),0,1)):(e=i[0],1===i[1]?n.normal.cdf(-t.abs(e),0,1):2*n.normal.cdf(-t.abs(e),0,1))}}),n.extend(n.fn,{zscore:function(n,t){return(n-this.mean())/this.stdev(t)},ztest:function(r,e,o){var i=t.abs(this.zscore(r,o));return 1===e?n.normal.cdf(-i,0,1):2*n.normal.cdf(-i,0,1)}}),n.extend({tscore:function(){var e=r.call(arguments);return 4===e.length?(e[0]-e[1])/(e[2]/t.sqrt(e[3])):(e[0]-n.mean(e[1]))/(n.stdev(e[1],!0)/t.sqrt(e[1].length))},ttest:function(){var o,i=r.call(arguments);return 5===i.length?(o=t.abs(n.tscore(i[0],i[1],i[2],i[3])),1===i[4]?n.studentt.cdf(-o,i[3]-1):2*n.studentt.cdf(-o,i[3]-1)):e(i[1])?(o=t.abs(i[0]),1==i[2]?n.studentt.cdf(-o,i[1]-1):2*n.studentt.cdf(-o,i[1]-1)):(o=t.abs(n.tscore(i[0],i[1])),1==i[2]?n.studentt.cdf(-o,i[1].length-1):2*n.studentt.cdf(-o,i[1].length-1))}}),n.extend(n.fn,{tscore:function(n){return(n-this.mean())/(this.stdev(!0)/t.sqrt(this.cols()))},ttest:function(r,e){return 1===e?1-n.studentt.cdf(t.abs(this.tscore(r)),this.cols()-1):2*n.studentt.cdf(-t.abs(this.tscore(r)),this.cols()-1)}}),n.extend({anovafscore:function(){var e,o,i,u,a,f,l,c,s=r.call(arguments);if(1===s.length){for(a=new Array(s[0].length),l=0;l<s[0].length;l++)a[l]=s[0][l];s=a}for(o=new Array,l=0;l<s.length;l++)o=o.concat(s[l]);for(i=n.mean(o),e=0,l=0;l<s.length;l++)e+=s[l].length*t.pow(n.mean(s[l])-i,2);for(e/=s.length-1,f=0,l=0;l<s.length;l++)for(u=n.mean(s[l]),c=0;c<s[l].length;c++)f+=t.pow(s[l][c]-u,2);return e/(f/(o.length-s.length))},anovaftest:function(){var t,o,i,u,a=r.call(arguments);if(e(a[0]))return 1-n.centralF.cdf(a[0],a[1],a[2]);var f=n.anovafscore(a);for(t=a.length-1,i=0,u=0;u<a.length;u++)i+=a[u].length;return o=i-t-1,1-n.centralF.cdf(f,t,o)},ftest:function(t,r,e){return 1-n.centralF.cdf(t,r,e)}}),n.extend(n.fn,{anovafscore:function(){return n.anovafscore(this.toArray())},anovaftes:function(){var t,r=0;for(t=0;t<this.length;t++)r+=this[t].length;return n.ftest(this.anovafscore(),this.length-1,r-this.length)}}),n.extend({qscore:function(){var o,i,u,a,f,l=r.call(arguments);return e(l[0])?(o=l[0],i=l[1],u=l[2],a=l[3],f=l[4]):(o=n.mean(l[0]),i=n.mean(l[1]),u=l[0].length,a=l[1].length,f=l[2]),t.abs(o-i)/(f*t.sqrt((1/u+1/a)/2))},qtest:function(){var t,e=r.call(arguments);3===e.length?(t=e[0],e=e.slice(1)):7===e.length?(t=n.qscore(e[0],e[1],e[2],e[3],e[4]),e=e.slice(5)):(t=n.qscore(e[0],e[1],e[2]),e=e.slice(3));var o=e[0],i=e[1];return 1-n.tukey.cdf(t,i,o-i)},tukeyhsd:function(t){for(var r=n.pooledstdev(t),e=t.map((function(t){return n.mean(t)})),o=t.reduce((function(n,t){return n+t.length}),0),i=[],u=0;u<t.length;++u)for(var a=u+1;a<t.length;++a){var f=n.qtest(e[u],e[a],t[u].length,t[a].length,r,o,t.length);i.push([[u,a],f])}return i}}),n.extend({normalci:function(){var e,o=r.call(arguments),i=new Array(2);return e=4===o.length?t.abs(n.normal.inv(o[1]/2,0,1)*o[2]/t.sqrt(o[3])):t.abs(n.normal.inv(o[1]/2,0,1)*n.stdev(o[2])/t.sqrt(o[2].length)),i[0]=o[0]-e,i[1]=o[0]+e,i},tci:function(){var e,o=r.call(arguments),i=new Array(2);return e=4===o.length?t.abs(n.studentt.inv(o[1]/2,o[3]-1)*o[2]/t.sqrt(o[3])):t.abs(n.studentt.inv(o[1]/2,o[2].length-1)*n.stdev(o[2],!0)/t.sqrt(o[2].length)),i[0]=o[0]-e,i[1]=o[0]+e,i},significant:function(n,t){return n<t}}),n.extend(n.fn,{normalci:function(t,r){return n.normalci(t,r,this.toArray())},tci:function(t,r){return n.tci(t,r,this.toArray())}}),n.extend(n.fn,{oneSidedDifferenceOfProportions:function(t,r,e,o){var u=i(t,r,e,o);return n.ztest(u,1)},twoSidedDifferenceOfProportions:function(t,r,e,o){var u=i(t,r,e,o);return n.ztest(u,2)}})}(t,Math),t.models=function(){function n(n,r){var e=n.length,o=r[0].length-1,i=e-o-1,u=t.lstsq(r,n),a=t.multiply(r,u.map((function(n){return[n]}))).map((function(n){return n[0]})),f=t.subtract(n,a),l=t.mean(n),c=t.sum(a.map((function(n){return Math.pow(n-l,2)}))),s=t.sum(n.map((function(n,t){return Math.pow(n-a[t],2)}))),h=c+s;return{exog:r,endog:n,nobs:e,df_model:o,df_resid:i,coef:u,predict:a,resid:f,ybar:l,SST:h,SSE:c,SSR:s,R2:c/h}}function r(r){var e,o,i=(e=r.exog,o=e[0].length,t.arange(o).map((function(r){var i=t.arange(o).filter((function(n){return n!==r}));return n(t.col(e,r).map((function(n){return n[0]})),t.col(e,i))}))),u=Math.sqrt(r.SSR/r.df_resid),a=i.map((function(n){var t=n.SST,r=n.R2;return u/Math.sqrt(t*(1-r))})),f=r.coef.map((function(n,t){return(n-0)/a[t]})),l=f.map((function(n){var e=t.studentt.cdf(n,r.df_resid);return 2*(e>.5?1-e:e)})),c=t.studentt.inv(.975,r.df_resid),s=r.coef.map((function(n,t){var r=c*a[t];return[n-r,n+r]}));return{se:a,t:f,p:l,sigmaHat:u,interval95:s}}return{ols:function(e,o){var i=n(e,o),u=r(i),a=function(n){var r,e,o,i=n.R2/n.df_model/((1-n.R2)/n.df_resid);return{F_statistic:i,pvalue:1-(r=i,e=n.df_model,o=n.df_resid,t.beta.cdf(r/(o/e+r),e/2,o/2))}}(i),f=1-(1-i.R2)*((i.nobs-1)/i.df_resid);return i.t=u,i.f=a,i.adjust_R2=f,i}}}(),t.extend({buildxmatrix:function(){for(var n=new Array(arguments.length),r=0;r<arguments.length;r++){n[r]=[1].concat(arguments[r])}return t(n)},builddxmatrix:function(){for(var n=new Array(arguments[0].length),r=0;r<arguments[0].length;r++){n[r]=[1].concat(arguments[0][r])}return t(n)},buildjxmatrix:function(n){for(var r=new Array(n.length),e=0;e<n.length;e++)r[e]=n[e];return t.builddxmatrix(r)},buildymatrix:function(n){return t(n).transpose()},buildjymatrix:function(n){return n.transpose()},matrixmult:function(n,r){var e,o,i,u,a;if(n.cols()==r.rows()){if(r.rows()>1){for(u=[],e=0;e<n.rows();e++)for(u[e]=[],o=0;o<r.cols();o++){for(a=0,i=0;i<n.cols();i++)a+=n.toArray()[e][i]*r.toArray()[i][o];u[e][o]=a}return t(u)}for(u=[],e=0;e<n.rows();e++)for(u[e]=[],o=0;o<r.cols();o++){for(a=0,i=0;i<n.cols();i++)a+=n.toArray()[e][i]*r.toArray()[o];u[e][o]=a}return t(u)}},regress:function(n,r){var e=t.xtranspxinv(n),o=n.transpose(),i=t.matrixmult(t(e),o);return t.matrixmult(i,r)},regresst:function(n,r,e){var o=t.regress(n,r),i={anova:{}},u=t.jMatYBar(n,o);i.yBar=u;var a=r.mean();i.anova.residuals=t.residuals(r,u),i.anova.ssr=t.ssr(u,a),i.anova.msr=i.anova.ssr/(n[0].length-1),i.anova.sse=t.sse(r,u),i.anova.mse=i.anova.sse/(r.length-(n[0].length-1)-1),i.anova.sst=t.sst(r,a),i.anova.mst=i.anova.sst/(r.length-1),i.anova.r2=1-i.anova.sse/i.anova.sst,i.anova.r2<0&&(i.anova.r2=0),i.anova.fratio=i.anova.msr/i.anova.mse,i.anova.pvalue=t.anovaftest(i.anova.fratio,n[0].length-1,r.length-(n[0].length-1)-1),i.anova.rmse=Math.sqrt(i.anova.mse),i.anova.r2adj=1-i.anova.mse/i.anova.mst,i.anova.r2adj<0&&(i.anova.r2adj=0),i.stats=new Array(n[0].length);for(var f,l,c,s=t.xtranspxinv(n),h=0;h<o.length;h++)f=Math.sqrt(i.anova.mse*Math.abs(s[h][h])),l=Math.abs(o[h]/f),c=t.ttest(l,r.length-n[0].length-1,e),i.stats[h]=[o[h],f,l,c];return i.regress=o,i},xtranspx:function(n){return t.matrixmult(n.transpose(),n)},xtranspxinv:function(n){var r=t.matrixmult(n.transpose(),n);return t.inv(r)},jMatYBar:function(n,r){var e=t.matrixmult(n,r);return new t(e)},residuals:function(n,r){return t.matrixsubtract(n,r)},ssr:function(n,t){for(var r=0,e=0;e<n.length;e++)r+=Math.pow(n[e]-t,2);return r},sse:function(n,t){for(var r=0,e=0;e<n.length;e++)r+=Math.pow(n[e]-t[e],2);return r},sst:function(n,t){for(var r=0,e=0;e<n.length;e++)r+=Math.pow(n[e]-t,2);return r},matrixsubtract:function(n,r){for(var e=new Array(n.length),o=0;o<n.length;o++){e[o]=new Array(n[o].length);for(var i=0;i<n[o].length;i++)e[o][i]=n[o][i]-r[o][i]}return t(e)}}),t.jStat=t,t)},960:function(n,t,r){const e=r(592);n.exports=function(n){function t(n,t){const r=t.split(".");let e=n;for(const n of r){if(null==e)return;e=e[n]}return e}for(let r=0;r<Object.keys(e).length;r++){let o,i=Object.keys(e)[r],u=[];if("object"==typeof e[i]){u=Object.keys(e[i]),o=Object.values(e[i]);for(let n=0;n<o.length;n++)if("object"==typeof o[n]){let t=u[n];e[i][t]&&(u=[...u,...Object.keys(e[i][t]).map((n=>t+"."+n))],u.splice(u.indexOf(t),1))}}if(u.length<1)n[i]=e[i];else for(let r=0;r<u.length;r++)"function"==typeof t(e[i],u[r])&&(n[i]=t(e[i],u[r]))}let r=function(n){return"number"==typeof n&&(n=parseInt(n)),n},o=null,i=null,u=null;n.TABLE=function(){return u},n.COLUMN=n.COL=function(){return u.tracking&&u.tracking.push(l.getColumnNameFromCoords(r(o),r(i))),r(o)+1},n.ROW=function(){return u.tracking&&u.tracking.push(l.getColumnNameFromCoords(r(o),r(i))),r(i)+1},n.CELL=function(){return l.getColumnNameFromCoords(o,i)},n.VALUE=function(n,t,e){return u.getValueFromCoords(r(n)-1,r(t)-1,e)},n.THISROWCELL=function(n){return u.getValueFromCoords(r(n)-1,r(i))};const a=function(n,t){for(let r=0;r<n.length;r++){let e=l.getTokensFromRange(n[r]);t=t.replace(n[r],"["+e.join(",")+"]")}return t},f=function(n){return"string"==typeof n&&(n=n.trim()),!isNaN(n)&&null!==n&&""!==n},l=function(n,t,r,e,l){u=l,o=r,i=e;let c="",s={};if(t)if(t.size){let n,r=null;t.forEach((function(t,r){n=r.replace(/!/g,"."),-1!==n.indexOf(".")&&(n=n.split("."),s[n[0]]=!0)})),n=Object.keys(s);for(let t=0;t<n.length;t++)c+="var "+n[t]+" = {};";t.forEach((function(e,o){n=o.replace(/!/g,"."),null===e||f(e)||(r=e.match(/(('.*?'!)|(\w*!))?(\$?[A-Z]+\$?[0-9]*):(\$?[A-Z]+\$?[0-9]*)?/g),r&&r.length&&(e=updateRanges(r,e))),n.indexOf(".")>0?c+=n+" = "+t.get(o)+";\n":c+="var "+n+" = "+e+";\n"}))}else{let n=Object.keys(t);if(n.length){let r,e={};for(let t=0;t<n.length;t++)if(r=n[t].replace(/\!/g,"."),r.indexOf(".")>0){let n=n.split(".");e[n[0]]={}}r=Object.keys(e);for(let n=0;n<r.length;n++)c+="var "+r[n]+" = {};";for(let e=0;e<n.length;e++){if(r=n[e].replace(/!/g,"."),null!==t[n[e]]&&!f(t[n[e]])){let r=t[n[e]].match(/(('.*?'!)|(\w*!))?(\$?[A-Z]+\$?[0-9]*):(\$?[A-Z]+\$?[0-9]*)?/g);r&&r.length&&(t[n[e]]=a(r,t[n[e]]))}r.indexOf(".")>0?c+=r+" = "+t[n[e]]+";\n":c+="var "+r+" = "+t[n[e]]+";\n"}}}let h=(n=function(n,t){let r="",e=0,o=["=","!",">","<"];for(let i=0;i<n.length;i++)'"'===n[i]&&(e=0===e?1:0),1===e?r+=n[i]:(r+=n[i].toUpperCase(),!0===t&&i>0&&"="===n[i]&&-1===o.indexOf(n[i-1])&&-1===o.indexOf(n[i+1])&&(r+="="));return r=r.replace(/\^/g,"**"),r=r.replace(/<>/g,"!="),r=r.replace(/&/g,"+"),r=r.replace(/\$/g,""),r}(n=(n=n.replace(/\$/g,"")).replace(/!/g,"."),!0)).match(/(('.*?'!)|(\w*!))?(\$?[A-Z]+\$?[0-9]*):(\$?[A-Z]+\$?[0-9]*)?/g);h&&h.length&&(n=a(h,n));let g=new Function(c+"; return "+n)();return null===g&&(g=0),g};return l.getColumnNameFromCoords=function(n,t){return r=parseInt(n),e="",r>701?(e+=String.fromCharCode(64+parseInt(r/676)),e+=String.fromCharCode(64+parseInt(r%676/26))):r>25&&(e+=String.fromCharCode(64+parseInt(r/26))),e+String.fromCharCode(65+r%26)+(parseInt(t)+1);var r,e},l.getCoordsFromColumnName=function(n){var t=/^[a-zA-Z]+/.exec(n);if(t){for(var r=0,e=0;e<t[0].length;e++)r+=parseInt(t[0].charCodeAt(e)-64)*Math.pow(26,t[0].length-1-e);--r<0&&(r=0);var o=parseInt(/[0-9]+$/.exec(n))||null;return o>0&&o--,[r,o]}},l.getRangeFromTokens=function(n){n=n.filter((function(n){return"#REF!"!=n}));for(var t="",r="",e=0;e<n.length;e++)n[e].indexOf(".")>=0?t=".":n[e].indexOf("!")>=0&&(t="!"),t&&(r=n[e].split(t),n[e]=r[1],r=r[0]+t);return n.sort((function(n,t){var r=Helpers.getCoordsFromColumnName(n),e=Helpers.getCoordsFromColumnName(t);return r[1]>e[1]?1:r[1]<e[1]?-1:r[0]>e[0]?1:r[0]<e[0]?-1:0})),n.length?r+(n[0]+":")+n[n.length-1]:"#REF!"},l.getTokensFromRange=function(n){if(n.indexOf(".")>0){var t=n.split(".");n=t[1],t=t[0]+"."}else n.indexOf("!")>0?(t=n.split("!"),n=t[1],t=t[0]+"!"):t="";n=n.split(":");var r=l.getCoordsFromColumnName(n[0]),e=l.getCoordsFromColumnName(n[1]);if(r[0]<=e[0])var o=r[0],i=e[0];else o=e[0],i=r[0];if(null===r[1]&&null==e[1])for(var u=null,a=null,f=Object.keys(vars),c=0;c<f.length;c++){var s=l.getCoordsFromColumnName(f[c]);s[0]===r[0]&&(null===u||s[1]<u)&&(u=s[1]),s[0]===e[0]&&(null===a||s[1]>a)&&(a=s[1])}else r[1]<=e[1]?(u=r[1],a=e[1]):(u=e[1],a=r[1]);for(var h=[],g=u;g<=a;g++){var p=[];for(c=o;c<=i;c++)p.push(t+l.getColumnNameFromCoords(c,g));h.push(p)}return h},l.setFormula=function(t){let r=Object.keys(t);for(let e=0;e<r.length;e++)"function"==typeof t[r[e]]&&(n[r[e]]=t[r[e]])},l.basic=!0,l}("undefined"==typeof window?r.g:window)},592:function(n,t,r){"use strict";var e=r(162),o=r(765);const i=new Error("#NULL!"),u=new Error("#DIV/0!"),a=new Error("#VALUE!"),f=new Error("#REF!"),l=new Error("#NAME?"),c=new Error("#NUM!"),s=new Error("#N/A"),h=new Error("#ERROR!"),g=new Error("#GETTING_DATA");var p=Object.freeze({__proto__:null,data:g,div0:u,error:h,na:s,name:l,nil:i,num:c,ref:f,value:a});function m(n){const t=[];return d(n,(n=>{t.push(n)})),t}function d(n,t){let r=-1;const e=n.length;for(;++r<e&&!1!==t(n[r],r,n););return n}function v(n){let t,r=n.length;for(;r--;)if(t=n[r],"number"!=typeof t)if(!0!==t)if(!1!==t){if("string"==typeof t){const e=R(t);n[r]=e instanceof Error?0:e}}else n[r]=0;else n[r]=1;return n}function E(n,t){if(!n)return a;n.every((n=>Array.isArray(n)))&&0!==n.length||(n=[[...n]]),n.map(((t,r)=>{t.map(((t,e)=>{t||(n[r][e]=0)}))}));const r=n.reduce(((t,r,e)=>r.length>n[t].length?e:t),0),e=n[r].length;return n.map((n=>[...n,...Array(e-n.length).fill(t||0)]))}function M(){let n;if(1===arguments.length){const r=arguments[0];n=null!=(t=r)&&"number"==typeof t.length&&"string"!=typeof t?m.apply(null,arguments):[r]}else n=Array.from(arguments);for(var t;!w(n);)n=N(n);return n}function N(n){return n&&n.reduce?n.reduce(((n,t)=>{const r=Array.isArray(n),e=Array.isArray(t);return r&&e?n.concat(t):r?(n.push(t),n):e?[n].concat(t):[n,t]})):[n]}function w(n){if(!n)return!1;for(let t=0;t<n.length;++t)if(Array.isArray(n[t]))return!1;return!0}function y(n,t){return t=t||1,n&&"function"==typeof n.slice?n.slice(t):n}function I(n){return n?n[0].map(((t,r)=>n.map((n=>n[r])))):a}function b(n,t){let r=null;return d(n,((n,e)=>{if(n[0]===t)return r=e,!1})),null==r?a:r}function T(){for(let n=0;n<arguments.length;n++)if(arguments[n]instanceof Error)return arguments[n]}function S(){let n=arguments.length;for(;n--;)if(arguments[n]instanceof Error)return!0;return!1}function A(n){const t=1e14;return Math.round(n*t)/t}function C(){return M.apply(null,arguments).filter((n=>"number"==typeof n))}function D(n){if("boolean"==typeof n)return n;if(n instanceof Error)return n;if("number"==typeof n)return 0!==n;if("string"==typeof n){const t=n.toUpperCase();if("TRUE"===t)return!0;if("FALSE"===t)return!1}return n instanceof Date&&!isNaN(n)||a}function O(n){if(!isNaN(n)){if(n instanceof Date)return new Date(n);const t=parseFloat(n);return t<0||t>=2958466?c:function(n){n<60&&(n+=1);const t=Math.floor(n-25569),r=new Date(86400*t*1e3),e=n-Math.floor(n)+1e-7;let o=Math.floor(86400*e);const i=o%60;o-=i;const u=Math.floor(o/3600),a=Math.floor(o/60)%60;let f=r.getUTCDate(),l=r.getUTCMonth();return n>=60&&n<61&&(f=29,l=1),new Date(r.getUTCFullYear(),l,f,u,a,i)}(t)}return"string"!=typeof n||(n=/(\d{4})-(\d\d?)-(\d\d?)$/.test(n)?new Date(n+"T00:00:00.000"):new Date(n),isNaN(n))?a:n}function x(n){let t,r=n.length;for(;r--;){if(t=O(n[r]),t===a)return t;n[r]=t}return n}function R(n){return n instanceof Error?n:null==n?0:("boolean"==typeof n&&(n=+n),isNaN(n)||""===n?a:parseFloat(n))}function P(n){let t,r;if(!n||0===(t=n.length))return a;for(;t--;){if(n[t]instanceof Error)return n[t];if(r=R(n[t]),r instanceof Error)return r;n[t]=r}return n}function L(n){return n instanceof Error?n:null==n?"":n.toString()}function F(){let n=arguments.length;for(;n--;)if("string"==typeof arguments[n])return!0;return!1}function q(n){return null!=n}const U="=",_=[">",">=","<","<=","=","<>"],V="operator",k="literal",j=[V,k],Y=V,G=k;function H(n,t){if(-1===j.indexOf(t))throw new Error("Unsupported token type: "+t);return{value:n,type:t}}function X(n){return function(n){let t="";const r=[];for(let e=0;e<n.length;e++){const o=n[e];0===e&&_.indexOf(o)>=0?r.push(H(o,Y)):t+=o}return t.length>0&&r.push(H(function(n){return"string"!=typeof n||/^\d+(\.\d+)?$/.test(n)&&(n=-1===n.indexOf(".")?parseInt(n,10):parseFloat(n)),n}(t),G)),r.length>0&&r[0].type!==Y&&r.unshift(H(U,Y)),r}(function(n){const t=n.length,r=[];let e=0,o="",i="";for(;e<t;){const t=n.charAt(e);switch(t){case">":case"<":case"=":i+=t,o.length>0&&(r.push(o),o="");break;default:i.length>0&&(r.push(i),i=""),o+=t}e++}return o.length>0&&r.push(o),i.length>0&&r.push(i),r}(n))}const B=function(n){const t=[];let r;for(let e=0;e<n.length;e++){const o=n[e];switch(o.type){case Y:r=o.value;break;case G:t.push(o.value)}}return function(n,t){let r=!1;switch(t){case">":r=n[0]>n[1];break;case">=":r=n[0]>=n[1];break;case"<":r=n[0]<n[1];break;case"<=":r=n[0]<=n[1];break;case"=":r=n[0]==n[1];break;case"<>":r=n[0]!=n[1]}return r}(t,r)},$={};function z(n){return[a,f,u,c,l,i].indexOf(n)>=0||"number"==typeof n&&(isNaN(n)||!isFinite(n))}function W(n){return z(n)||n===s}function K(n){return!0===n||!1===n}function Q(n){return"number"==typeof n&&!isNaN(n)&&isFinite(n)}function Z(n){return"string"==typeof n}function J(){const n=[];for(let t=0;t<arguments.length;++t){let r=!1;const e=arguments[t];for(let t=0;t<n.length&&(r=n[t]===e,!r);++t);r||n.push(e)}return n}function nn(n,t,r,e){if(!t||!r)return s;e=!(0===e||!1===e);let o=s;const i="number"==typeof n;let u=!1;for(let a=0;a<t.length;a++){const l=t[a];if(l[0]===n){o=r<l.length+1?l[r-1]:f;break}!u&&(i&&e&&l[0]<=n||e&&"string"==typeof l[0]&&l[0].localeCompare(n)<0)&&(o=r<l.length+1?l[r-1]:f),i&&l[0]>n&&(u=!0)}return o}function tn(){const n=M(arguments).filter(q);if(0===n.length)return u;const t=T.apply(void 0,n);if(t)return t;const r=C(n),e=r.length;let o,i=0,a=0;for(let n=0;n<e;n++)i+=r[n],a+=1;return o=i/a,isNaN(o)&&(o=c),o}function rn(){const n=M(arguments).filter(q);if(0===n.length)return u;const t=T.apply(void 0,n);if(t)return t;const r=n,e=r.length;let o,i=0,a=0;for(let n=0;n<e;n++){const t=r[n];"number"==typeof t&&(i+=t),!0===t&&i++,null!==t&&a++}return o=i/a,isNaN(o)&&(o=c),o}$.TYPE=n=>{switch(n){case i:return 1;case u:return 2;case a:return 3;case f:return 4;case l:return 5;case c:return 6;case s:return 7;case g:return 8}return s};const en={DIST:function(n,t,r,o,i,u){return arguments.length<4?a:(i=void 0===i?0:i,u=void 0===u?1:u,S(n=R(n),t=R(t),r=R(r),i=R(i),u=R(u))?a:(n=(n-i)/(u-i),o?e.beta.cdf(n,t,r):e.beta.pdf(n,t,r)))},INV:(n,t,r,o,i)=>(o=void 0===o?0:o,i=void 0===i?1:i,S(n=R(n),t=R(t),r=R(r),o=R(o),i=R(i))?a:e.beta.inv(n,t,r)*(i-o)+o)},on={DIST:(n,t,r,o)=>S(n=R(n),t=R(t),r=R(r),o=R(o))?a:o?e.binomial.cdf(n,t,r):e.binomial.pdf(n,t,r)};on.DIST.RANGE=(n,t,r,e)=>{if(e=void 0===e?r:e,S(n=R(n),t=R(t),r=R(r),e=R(e)))return a;let o=0;for(let i=r;i<=e;i++)o+=Gn(n,i)*Math.pow(t,i)*Math.pow(1-t,n-i);return o},on.INV=(n,t,r)=>{if(S(n=R(n),t=R(t),r=R(r)))return a;let o=0;for(;o<=n;){if(e.binomial.cdf(o,n,t)>=r)return o;o++}};const un={DIST:(n,t,r)=>S(n=R(n),t=R(t))?a:r?e.chisquare.cdf(n,t):e.chisquare.pdf(n,t)};un.DIST.RT=(n,t)=>!n|!t?s:n<1||t>Math.pow(10,10)?c:"number"!=typeof n||"number"!=typeof t?a:1-e.chisquare.cdf(n,t),un.INV=(n,t)=>S(n=R(n),t=R(t))?a:e.chisquare.inv(n,t),un.INV.RT=(n,t)=>!n|!t?s:n<0||n>1||t<1||t>Math.pow(10,10)?c:"number"!=typeof n||"number"!=typeof t?a:e.chisquare.inv(1-n,t),un.TEST=function(n,t){if(2!==arguments.length)return s;if(!(n instanceof Array&&t instanceof Array))return a;if(n.length!==t.length)return a;if(n[0]&&t[0]&&n[0].length!==t[0].length)return a;const r=n.length;let e,o,i;for(o=0;o<r;o++)n[o]instanceof Array||(e=n[o],n[o]=[],n[o].push(e)),t[o]instanceof Array||(e=t[o],t[o]=[],t[o].push(e));const u=n[0].length,f=1===u?r-1:(r-1)*(u-1);let l=0;const c=Math.PI;for(o=0;o<r;o++)for(i=0;i<u;i++)l+=Math.pow(n[o][i]-t[o][i],2)/t[o][i];return Math.round(1e6*function(n,t){let r=Math.exp(-.5*n);t%2==1&&(r*=Math.sqrt(2*n/c));let e=t;for(;e>=2;)r=r*n/e,e-=2;let o=r,i=t;for(;o>1e-10*r;)i+=2,o=o*n/i,r+=o;return 1-r}(l,f))/1e6};const an={};function fn(){return C(M(arguments)).length}function ln(){const n=M(arguments);return n.length-cn(n)}function cn(){const n=M(arguments);let t,r=0;for(let e=0;e<n.length;e++)t=n[e],null!=t&&""!==t||r++;return r}an.NORM=(n,t,r)=>S(n=R(n),t=R(t),r=R(r))?a:e.normalci(1,n,t,r)[1]-1,an.T=(n,t,r)=>S(n=R(n),t=R(t),r=R(r))?a:e.tci(1,n,t,r)[1]-1;const sn={P:(n,t)=>{if(S(n=P(M(n)),t=P(M(t))))return a;const r=e.mean(n),o=e.mean(t);let i=0;const u=n.length;for(let e=0;e<u;e++)i+=(n[e]-r)*(t[e]-o);return i/u},S:(n,t)=>S(n=P(M(n)),t=P(M(t)))?a:e.covariance(n,t)},hn={DIST:(n,t,r)=>S(n=R(n),t=R(t))?a:r?e.exponential.cdf(n,t):e.exponential.pdf(n,t)},gn={};function pn(n,t,r){if(S(n=R(n),t=P(M(t)),r=P(M(r))))return a;const o=e.mean(r),i=e.mean(t),u=r.length;let f=0,l=0;for(let n=0;n<u;n++)f+=(r[n]-o)*(t[n]-i),l+=Math.pow(r[n]-o,2);const c=f/l;return i-c*o+c*n}function mn(n){return(n=R(n))instanceof Error?n:0===n||parseInt(n,10)===n&&n<0?c:e.gammafn(n)}function dn(n){return(n=R(n))instanceof Error?n:e.gammaln(n)}gn.DIST=(n,t,r,o)=>S(n=R(n),t=R(t),r=R(r))?a:o?e.centralF.cdf(n,t,r):e.centralF.pdf(n,t,r),gn.DIST.RT=function(n,t,r){return 3!==arguments.length?s:n<0||t<1||r<1?c:"number"!=typeof n||"number"!=typeof t||"number"!=typeof r?a:1-e.centralF.cdf(n,t,r)},gn.INV=(n,t,r)=>S(n=R(n),t=R(t),r=R(r))?a:n<=0||n>1?c:e.centralF.inv(n,t,r),gn.INV.RT=function(n,t,r){return 3!==arguments.length?s:n<0||n>1||t<1||t>Math.pow(10,10)||r<1||r>Math.pow(10,10)?c:"number"!=typeof n||"number"!=typeof t||"number"!=typeof r?a:e.centralF.inv(1-n,t,r)},gn.TEST=(n,t)=>{if(!n||!t)return s;if(!(n instanceof Array&&t instanceof Array))return s;if(n.length<2||t.length<2)return u;const r=(n,t)=>{let r=0;for(let e=0;e<n.length;e++)r+=Math.pow(n[e]-t,2);return r},e=Qn(n)/n.length,o=Qn(t)/t.length;return r(n,e)/(n.length-1)/(r(t,o)/(t.length-1))},mn.DIST=function(n,t,r,o){return 4!==arguments.length?s:n<0||t<=0||r<=0||"number"!=typeof n||"number"!=typeof t||"number"!=typeof r?a:o?e.gamma.cdf(n,t,r,!0):e.gamma.pdf(n,t,r,!1)},mn.INV=function(n,t,r){return 3!==arguments.length?s:n<0||n>1||t<=0||r<=0?c:"number"!=typeof n||"number"!=typeof t||"number"!=typeof r?a:e.gamma.inv(n,t,r)},dn.PRECISE=function(n){return 1!==arguments.length?s:n<=0?c:"number"!=typeof n?a:e.gammaln(n)};const vn={};function En(n,t){return S(n=P(M(n)),t=R(t))?n:t<0||n.length<t?a:n.sort(((n,t)=>t-n))[t-1]}function Mn(n,t){if(S(n=P(M(n)),t=P(M(t))))return a;const r=e.mean(n),o=e.mean(t),i=t.length;let u=0,f=0;for(let e=0;e<i;e++)u+=(t[e]-o)*(n[e]-r),f+=Math.pow(t[e]-o,2);const l=u/f;return[l,r-l*o]}vn.DIST=(n,t,r,e,o)=>{if(S(n=R(n),t=R(t),r=R(r),e=R(e)))return a;function i(n,t,r,e){return Gn(r,n)*Gn(e-r,t-n)/Gn(e,t)}return o?function(n,t,r,e){let o=0;for(let u=0;u<=n;u++)o+=i(u,t,r,e);return o}(n,t,r,e):i(n,t,r,e)};const Nn={};function wn(){const n=M(arguments),t=T.apply(void 0,n);if(t)return t;const r=C(n);return 0===r.length?0:Math.max.apply(Math,r)}function yn(){const n=M(arguments),t=T.apply(void 0,n);if(t)return t;const r=v(n);let o=e.median(r);return isNaN(o)&&(o=c),o}function In(){const n=M(arguments),t=T.apply(void 0,n);if(t)return t;const r=C(n);return 0===r.length?0:Math.min.apply(Math,r)}Nn.DIST=(n,t,r,o)=>S(n=R(n),t=R(t),r=R(r))?a:o?e.lognormal.cdf(n,t,r):e.lognormal.pdf(n,t,r),Nn.INV=(n,t,r)=>S(n=R(n),t=R(t),r=R(r))?a:e.lognormal.inv(n,t,r);const bn={MULT:function(){const n=P(M(arguments));if(n instanceof Error)return n;const t=n.length,r={};let e,o=[],i=0;for(let u=0;u<t;u++)e=n[u],r[e]=r[e]?r[e]+1:1,r[e]>i&&(i=r[e],o=[]),r[e]===i&&(o[o.length]=e);return o},SNGL:function(){const n=P(M(arguments));return n instanceof Error?n:bn.MULT(n).sort(((n,t)=>n-t))[0]}},Tn={DIST:(n,t,r,o)=>S(n=R(n),t=R(t),r=R(r))?a:o?e.negbin.cdf(n,t,r):e.negbin.pdf(n,t,r)},Sn={};function An(n,t){if(S(t=P(M(t)),n=P(M(n))))return a;const r=e.mean(n),o=e.mean(t),i=n.length;let u=0,f=0,l=0;for(let e=0;e<i;e++)u+=(n[e]-r)*(t[e]-o),f+=Math.pow(n[e]-r,2),l+=Math.pow(t[e]-o,2);return u/Math.sqrt(f*l)}Sn.DIST=(n,t,r,o)=>S(n=R(n),t=R(t),r=R(r))?a:r<=0?c:o?e.normal.cdf(n,t,r):e.normal.pdf(n,t,r),Sn.INV=(n,t,r)=>S(n=R(n),t=R(t),r=R(r))?a:e.normal.inv(n,t,r),Sn.S={},Sn.S.DIST=(n,t)=>(n=R(n))instanceof Error?a:t?e.normal.cdf(n,0,1):e.normal.pdf(n,0,1),Sn.S.INV=n=>(n=R(n))instanceof Error?a:e.normal.inv(n,0,1);const Cn={EXC:(n,t)=>{if(S(n=P(M(n)),t=R(t)))return a;const r=(n=n.sort(((n,t)=>n-t))).length;if(t<1/(r+1)||t>1-1/(r+1))return c;const e=t*(r+1)-1,o=Math.floor(e);return A(e===o?n[e]:n[o]+(e-o)*(n[o+1]-n[o]))},INC:(n,t)=>{if(S(n=P(M(n)),t=R(t)))return a;const r=t*((n=n.sort(((n,t)=>n-t))).length-1),e=Math.floor(r);return A(r===e?n[r]:n[e]+(r-e)*(n[e+1]-n[e]))}},Dn={EXC:(n,t,r)=>{if(r=void 0===r?3:r,S(n=P(M(n)),t=R(t),r=R(r)))return a;n=n.sort(((n,t)=>n-t));const e=J.apply(null,n),o=n.length,i=e.length,u=Math.pow(10,r);let f=0,l=!1,c=0;for(;!l&&c<i;)t===e[c]?(f=(n.indexOf(e[c])+1)/(o+1),l=!0):t>=e[c]&&(t<e[c+1]||c===i-1)&&(f=(n.indexOf(e[c])+1+(t-e[c])/(e[c+1]-e[c]))/(o+1),l=!0),c++;return Math.floor(f*u)/u},INC:(n,t,r)=>{if(r=void 0===r?3:r,S(n=P(M(n)),t=R(t),r=R(r)))return a;n=n.sort(((n,t)=>n-t));const e=J.apply(null,n),o=n.length,i=e.length,u=Math.pow(10,r);let f=0,l=!1,c=0;for(;!l&&c<i;)t===e[c]?(f=n.indexOf(e[c])/(o-1),l=!0):t>=e[c]&&(t<e[c+1]||c===i-1)&&(f=(n.indexOf(e[c])+(t-e[c])/(e[c+1]-e[c]))/(o-1),l=!0),c++;return Math.floor(f*u)/u}},On={DIST:(n,t,r)=>S(n=R(n),t=R(t))?a:r?e.poisson.cdf(n,t):e.poisson.pdf(n,t)},xn={EXC:(n,t)=>{if(S(n=P(M(n)),t=R(t)))return a;switch(t){case 1:return Cn.EXC(n,.25);case 2:return Cn.EXC(n,.5);case 3:return Cn.EXC(n,.75);default:return c}},INC:(n,t)=>{if(S(n=P(M(n)),t=R(t)))return a;switch(t){case 1:return Cn.INC(n,.25);case 2:return Cn.INC(n,.5);case 3:return Cn.INC(n,.75);default:return c}}},Rn={};function Pn(){const n=P(M(arguments));if(n instanceof Error)return n;const t=e.mean(n),r=n.length;let o=0;for(let e=0;e<r;e++)o+=Math.pow(n[e]-t,3);return r*o/((r-1)*(r-2)*Math.pow(e.stdev(n,!0),3))}function Ln(n,t){return S(n=P(M(n)),t=R(t))?n:n.sort(((n,t)=>n-t))[t-1]}Rn.AVG=(n,t,r)=>{if(S(n=R(n),t=P(M(t))))return a;const e=(r=r||!1)?(n,t)=>n-t:(n,t)=>t-n,o=(t=(t=M(t)).sort(e)).length;let i=0;for(let r=0;r<o;r++)t[r]===n&&i++;return i>1?(2*t.indexOf(n)+i+1)/2:t.indexOf(n)+1},Rn.EQ=(n,t,r)=>{if(S(n=R(n),t=P(M(t))))return a;const e=(r=r||!1)?(n,t)=>n-t:(n,t)=>t-n;return(t=t.sort(e)).indexOf(n)+1},Pn.P=function(){const n=P(M(arguments));if(n instanceof Error)return n;const t=e.mean(n),r=n.length;let o=0,i=0;for(let e=0;e<r;e++)i+=Math.pow(n[e]-t,3),o+=Math.pow(n[e]-t,2);return i/=r,o/=r,i/Math.pow(o,1.5)};const Fn={P:function(){const n=Un.P.apply(this,arguments);let t=Math.sqrt(n);return isNaN(t)&&(t=c),t},S:function(){const n=Un.S.apply(this,arguments);return Math.sqrt(n)}},qn={DIST:(n,t,r)=>1!==r&&2!==r?c:1===r?qn.DIST.RT(n,t):qn.DIST["2T"](n,t)};qn.DIST["2T"]=function(n,t){return 2!==arguments.length?s:n<0||t<1?c:"number"!=typeof n||"number"!=typeof t?a:2*(1-e.studentt.cdf(n,t))},qn.DIST.RT=function(n,t){return 2!==arguments.length?s:n<0||t<1?c:"number"!=typeof n||"number"!=typeof t?a:1-e.studentt.cdf(n,t)},qn.INV=(n,t)=>S(n=R(n),t=R(t))?a:e.studentt.inv(n,t),qn.INV["2T"]=(n,t)=>(n=R(n),t=R(t),n<=0||n>1||t<1?c:S(n,t)?a:Math.abs(e.studentt.inv(n/2,t))),qn.TEST=(n,t)=>{if(S(n=P(M(n)),t=P(M(t))))return a;const r=e.mean(n),o=e.mean(t);let i,u=0,f=0;for(i=0;i<n.length;i++)u+=Math.pow(n[i]-r,2);for(i=0;i<t.length;i++)f+=Math.pow(t[i]-o,2);u/=n.length-1,f/=t.length-1;const l=Math.abs(r-o)/Math.sqrt(u/n.length+f/t.length);return qn.DIST["2T"](l,n.length+t.length-2)};const Un={};function _n(){const n=M(arguments),t=n.length;let r=0,e=0;const o=rn(n);for(let i=0;i<t;i++){const t=n[i];r+="number"==typeof t?Math.pow(t-o,2):!0===t?Math.pow(1-o,2):Math.pow(0-o,2),null!==t&&e++}return r/(e-1)}function Vn(){const n=M(arguments),t=n.length;let r=0,e=0;const o=rn(n);let i;for(let i=0;i<t;i++){const t=n[i];r+="number"==typeof t?Math.pow(t-o,2):!0===t?Math.pow(1-o,2):Math.pow(0-o,2),null!==t&&e++}return i=r/e,isNaN(i)&&(i=c),i}Un.P=function(){const n=C(M(arguments)),t=n.length;let r=0;const e=tn(n);let o;for(let o=0;o<t;o++)r+=Math.pow(n[o]-e,2);return o=r/t,isNaN(o)&&(o=c),o},Un.S=function(){const n=C(M(arguments)),t=n.length;let r=0;const e=tn(n);for(let o=0;o<t;o++)r+=Math.pow(n[o]-e,2);return r/(t-1)};const kn={DIST:(n,t,r,e)=>S(n=R(n),t=R(t),r=R(r))?a:e?1-Math.exp(-Math.pow(n/r,t)):Math.pow(n,t-1)*Math.exp(-Math.pow(n/r,t))*t/Math.pow(r,t)},jn={};function Yn(n,t,r){const e=T(n=R(n),t=R(t),r=R(r));if(e)return e;if(0===t)return 0;t=Math.abs(t);const o=-Math.floor(Math.log(t)/Math.log(10));return n>=0?Kn(Math.ceil(n/t)*t,o):0===r?-Kn(Math.floor(Math.abs(n)/t)*t,o):-Kn(Math.ceil(Math.abs(n)/t)*t,o)}function Gn(n,t){return T(n=R(n),t=R(t))||(n<t?c:Xn(n)/(Xn(t)*Xn(n-t)))}jn.TEST=(n,t,r)=>{if(S(n=P(M(n)),t=R(t)))return a;r=r||Fn.S(n);const e=n.length;return 1-Sn.S.DIST((tn(n)-t)/(r/Math.sqrt(e)),!0)},Yn.MATH=Yn,Yn.PRECISE=Yn;const Hn=[];function Xn(n){if((n=R(n))instanceof Error)return n;const t=Math.floor(n);return 0===t||1===t?1:(Hn[t]>0||(Hn[t]=Xn(t-1)*t),Hn[t])}function Bn(n,t){const r=T(n=R(n),t=R(t));if(r)return r;if(0===t)return 0;if(!(n>=0&&t>0||n<=0&&t<0))return c;t=Math.abs(t);const e=-Math.floor(Math.log(t)/Math.log(10));return n>=0?Kn(Math.floor(n/t)*t,e):-Kn(Math.ceil(Math.abs(n)/t),e)}Bn.MATH=(n,t,r)=>{if(t instanceof Error)return t;t=void 0===t?0:t;const e=T(n=R(n),t=R(t),r=R(r));if(e)return e;if(0===t)return 0;t=t?Math.abs(t):1;const o=-Math.floor(Math.log(t)/Math.log(10));return n>=0?Kn(Math.floor(n/t)*t,o):0===r||void 0===r?-Kn(Math.ceil(Math.abs(n)/t)*t,o):-Kn(Math.floor(Math.abs(n)/t)*t,o)},Bn.PRECISE=Bn.MATH;const $n={CEILING:Yn};function zn(n,t){const r=T(n=R(n),t=R(t));if(r)return r;if(0===n&&0===t)return c;const e=Math.pow(n,t);return isNaN(e)?c:e}function Wn(){const n=M(arguments).filter((n=>null!=n));if(0===n.length)return 0;const t=P(n);if(t instanceof Error)return t;let r=1;for(let n=0;n<t.length;n++)r*=t[n];return r}function Kn(n,t){return T(n=R(n),t=R(t))||Number(Math.round(Number(n+"e"+t))+"e"+-1*t)}function Qn(){let n=0;return d(m(arguments),(t=>{if(n instanceof Error)return!1;if(t instanceof Error)n=t;else if("number"==typeof t)n+=t;else if("string"==typeof t){const r=parseFloat(t);!isNaN(r)&&(n+=r)}else if(Array.isArray(t)){const r=Qn.apply(null,t);r instanceof Error?n=r:n+=r}})),n}var Zn=Object.freeze({__proto__:null,ADD:function(n,t){if(2!==arguments.length)return s;return T(n=R(n),t=R(t))||n+t},DIVIDE:function(n,t){if(2!==arguments.length)return s;return T(n=R(n),t=R(t))||(0===t?u:n/t)},EQ:function(n,t){return 2!==arguments.length?s:n instanceof Error?n:t instanceof Error?t:(null===n&&(n=void 0),null===t&&(t=void 0),n===t)},GT:function(n,t){if(2!==arguments.length)return s;if(n instanceof Error)return n;if(t instanceof Error)return t;F(n,t)?(n=L(n),t=L(t)):(n=R(n),t=R(t));return T(n,t)||n>t},GTE:function(n,t){if(2!==arguments.length)return s;F(n,t)?(n=L(n),t=L(t)):(n=R(n),t=R(t));return T(n,t)||n>=t},LT:function(n,t){if(2!==arguments.length)return s;F(n,t)?(n=L(n),t=L(t)):(n=R(n),t=R(t));return T(n,t)||n<t},LTE:function(n,t){if(2!==arguments.length)return s;F(n,t)?(n=L(n),t=L(t)):(n=R(n),t=R(t));return T(n,t)||n<=t},MINUS:function(n,t){if(2!==arguments.length)return s;return T(n=R(n),t=R(t))||n-t},MULTIPLY:function(n,t){if(2!==arguments.length)return s;return T(n=R(n),t=R(t))||n*t},NE:function(n,t){return 2!==arguments.length?s:n instanceof Error?n:t instanceof Error?t:(null===n&&(n=void 0),null===t&&(t=void 0),n!==t)},POW:function(n,t){return 2!==arguments.length?s:zn(n,t)}});const Jn=new Date(Date.UTC(1900,0,1)),nt=[void 0,0,1,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,1,2,3,4,5,6,0],tt=[[],[1,2,3,4,5,6,7],[7,1,2,3,4,5,6],[6,0,1,2,3,4,5],[],[],[],[],[],[],[],[7,1,2,3,4,5,6],[6,7,1,2,3,4,5],[5,6,7,1,2,3,4],[4,5,6,7,1,2,3],[3,4,5,6,7,1,2],[2,3,4,5,6,7,1],[1,2,3,4,5,6,7]],rt=[[],[6,0],[0,1],[1,2],[2,3],[3,4],[4,5],[5,6],void 0,void 0,void 0,[0,0],[1,1],[2,2],[3,3],[4,4],[5,5],[6,6]];function et(n,t,r){r=r.toUpperCase(),n=O(n),t=O(t);const e=n.getFullYear(),o=n.getMonth(),i=n.getDate(),u=t.getFullYear(),a=t.getMonth(),f=t.getDate();let l;switch(r){case"Y":l=Math.floor(ht(n,t));break;case"D":l=it(t,n);break;case"M":l=a-o+12*(u-e),f<i&&l--;break;case"MD":i<=f?l=f-i:(0===a?(n.setFullYear(u-1),n.setMonth(12)):(n.setFullYear(u),n.setMonth(a-1)),l=it(t,n));break;case"YM":l=a-o+12*(u-e),f<i&&l--,l%=12;break;case"YD":a>o||a===o&&f<i?n.setFullYear(u):n.setFullYear(u-1),l=it(t,n)}return l}function ot(n){const t=new Date(n);return t.setHours(0,0,0,0),t}function it(n,t){return n=O(n),t=O(t),n instanceof Error?n:t instanceof Error?t:gt(ot(n))-gt(ot(t))}function ut(n,t,r){if(r=D(r||"false"),n=O(n),t=O(t),n instanceof Error)return n;if(t instanceof Error)return t;if(r instanceof Error)return r;const e=n.getMonth();let o,i,u=t.getMonth();if(r)o=31===n.getDate()?30:n.getDate(),i=31===t.getDate()?30:t.getDate();else{const r=new Date(n.getFullYear(),e+1,0).getDate(),a=new Date(t.getFullYear(),u+1,0).getDate();o=n.getDate()===r?30:n.getDate(),t.getDate()===a?o<30?(u++,i=1):i=30:i=t.getDate()}return 360*(t.getFullYear()-n.getFullYear())+30*(u-e)+(i-o)}function at(n){if((n=O(n))instanceof Error)return n;(n=ot(n)).setDate(n.getDate()+4-(n.getDay()||7));const t=new Date(n.getFullYear(),0,1);return Math.ceil(((n-t)/864e5+1)/7)}function ft(n,t,r){return ft.INTL(n,t,1,r)}function lt(n,t,r){return lt.INTL(n,t,1,r)}function ct(n){return 1===new Date(n,1,29).getMonth()}function st(n,t){return Math.ceil((t-n)/1e3/60/60/24)}function ht(n,t,r){if((n=O(n))instanceof Error)return n;if((t=O(t))instanceof Error)return t;r=r||0;let e=n.getDate();const o=n.getMonth()+1,i=n.getFullYear();let u=t.getDate();const a=t.getMonth()+1,f=t.getFullYear();switch(r){case 0:return 31===e&&31===u?(e=30,u=30):31===e?e=30:30===e&&31===u&&(u=30),(u+30*a+360*f-(e+30*o+360*i))/360;case 1:{const r=(n,t)=>{const r=n.getFullYear(),e=new Date(r,2,1);if(ct(r)&&n<e&&t>=e)return!0;const o=t.getFullYear(),i=new Date(o,2,1);return ct(o)&&t>=i&&n<i};let l=365;if(i===f||i+1===f&&(o>a||o===a&&e>=u))return(i===f&&ct(i)||r(n,t)||1===a&&29===u)&&(l=366),st(n,t)/l;const c=f-i+1,s=(new Date(f+1,0,1)-new Date(i,0,1))/1e3/60/60/24/c;return st(n,t)/s}case 2:return st(n,t)/360;case 3:return st(n,t)/365;case 4:return(u+30*a+360*f-(e+30*o+360*i))/360}}function gt(n){const t=n>-22038912e5?2:1;return Math.ceil((n-Jn)/864e5)+t}function pt(n){return 0===(n=R(n))?a:n instanceof Error?n:String.fromCharCode(n)}function mt(n){if(S(n))return n;let t=(n=n||"").charCodeAt(0);return isNaN(t)&&(t=a),t}function dt(){const n=M(arguments),t=T.apply(void 0,n);if(t)return t;let r=0;for(;(r=n.indexOf(!0))>-1;)n[r]="TRUE";let e=0;for(;(e=n.indexOf(!1))>-1;)n[e]="FALSE";return n.join("")}ft.INTL=(n,t,r,e)=>{if((n=O(n))instanceof Error)return n;if((t=O(t))instanceof Error)return t;let o=!1;const i=[],u=[1,2,3,4,5,6,0],f=new RegExp("^[0|1]{7}$");if(void 0===r)r=rt[1];else if("string"==typeof r&&f.test(r)){o=!0,r=r.split("");for(let n=0;n<r.length;n++)"1"===r[n]&&i.push(u[n])}else r=rt[r];if(!(r instanceof Array))return a;void 0===e?e=[]:e instanceof Array||(e=[e]);for(let n=0;n<e.length;n++){const t=O(e[n]);if(t instanceof Error)return t;e[n]=t}const l=Math.round((t-n)/864e5)+1;let c=l;const s=n;for(let n=0;n<l;n++){const n=(new Date).getTimezoneOffset()>0?s.getUTCDay():s.getDay();let t=o?i.includes(n):n===r[0]||n===r[1];for(let n=0;n<e.length;n++){const r=e[n];if(r.getDate()===s.getDate()&&r.getMonth()===s.getMonth()&&r.getFullYear()===s.getFullYear()){t=!0;break}}t&&c--,s.setDate(s.getDate()+1)}return c},lt.INTL=(n,t,r,e)=>{if((n=O(n))instanceof Error)return n;if((t=R(t))instanceof Error)return t;if(t<0)return c;if(!((r=void 0===r?rt[1]:rt[r])instanceof Array))return a;void 0===e?e=[]:e instanceof Array||(e=[e]);for(let n=0;n<e.length;n++){const t=O(e[n]);if(t instanceof Error)return t;e[n]=t}let o=0;for(;o<t;){n.setDate(n.getDate()+1);const t=n.getDay();if(t!==r[0]&&t!==r[1]){for(let t=0;t<e.length;t++){const r=e[t];if(r.getDate()===n.getDate()&&r.getMonth()===n.getMonth()&&r.getFullYear()===n.getFullYear()){o--;break}}o++}}return n};const vt=dt;function Et(n,t=2,r=!1){if(n=R(n),isNaN(n))return a;if(t=R(t),isNaN(t))return a;if(t<0){const r=Math.pow(10,-t);n=Math.round(n/r)*r}else n=n.toFixed(t);if(r)n=n.toString().replace(/,/g,"");else{const t=n.toString().split(".");t[0]=t[0].replace(/\B(?=(\d{3})+$)/g,","),n=t.join(".")}return n}function Mt(n,t){return T(n,t)||(n=L(n),(t=R(t))instanceof Error?t:new Array(t+1).join(n))}const Nt=pt,wt=mt;function yt(n){return/^[01]{1,10}$/.test(n)}function It(n,t,r){if(S(n=R(n),t=R(t)))return n;if("i"!==(r=void 0===r?"i":r)&&"j"!==r)return a;if(0===n&&0===t)return 0;if(0===n)return 1===t?r:t.toString()+r;if(0===t)return n.toString();{const e=t>0?"+":"";return n.toString()+e+(1===t?r:t.toString()+r)}}function bt(n,t){return t=void 0===t?0:t,S(n=R(n),t=R(t))?a:e.erf(n)}function Tt(n){return isNaN(n)?a:e.erfc(n)}function St(n){const t=Rt(n),r=At(n);return S(t,r)?a:Math.sqrt(Math.pow(t,2)+Math.pow(r,2))}function At(n){if(void 0===n||!0===n||!1===n)return a;if(0===n||"0"===n)return 0;if(["i","j"].indexOf(n)>=0)return 1;let t=(n=(n+="").replace("+i","+1i").replace("-i","-1i").replace("+j","+1j").replace("-j","-1j")).indexOf("+"),r=n.indexOf("-");0===t&&(t=n.indexOf("+",1)),0===r&&(r=n.indexOf("-",1));const e=n.substring(n.length-1,n.length),o="i"===e||"j"===e;return t>=0||r>=0?o?t>=0?isNaN(n.substring(0,t))||isNaN(n.substring(t+1,n.length-1))?c:Number(n.substring(t+1,n.length-1)):isNaN(n.substring(0,r))||isNaN(n.substring(r+1,n.length-1))?c:-Number(n.substring(r+1,n.length-1)):c:o?isNaN(n.substring(0,n.length-1))?c:n.substring(0,n.length-1):isNaN(n)?c:0}function Ct(n){const t=Rt(n),r=At(n);return S(t,r)?a:0===t&&0===r?u:0===t&&r>0?Math.PI/2:0===t&&r<0?-Math.PI/2:0===r&&t>0?0:0===r&&t<0?-Math.PI:t>0?Math.atan(r/t):t<0&&r>=0?Math.atan(r/t)+Math.PI:Math.atan(r/t)-Math.PI}function Dt(n){const t=Rt(n),r=At(n);if(S(t,r))return a;let e=n.substring(n.length-1);return e="i"===e||"j"===e?e:"i",It(Math.cos(t)*(Math.exp(r)+Math.exp(-r))/2,-Math.sin(t)*(Math.exp(r)-Math.exp(-r))/2,e)}function Ot(n){const t=Rt(n),r=At(n);if(S(t,r))return a;let e=n.substring(n.length-1);return e="i"===e||"j"===e?e:"i",It(Math.cos(r)*(Math.exp(t)+Math.exp(-t))/2,Math.sin(r)*(Math.exp(t)-Math.exp(-t))/2,e)}function xt(n,t){const r=Rt(n),e=At(n),o=Rt(t),i=At(t);if(S(r,e,o,i))return a;const u=n.substring(n.length-1),f=t.substring(t.length-1);let l="i";if(("j"===u||"j"===f)&&(l="j"),0===o&&0===i)return c;const s=o*o+i*i;return It((r*o+e*i)/s,(e*o-r*i)/s,l)}function Rt(n){if(void 0===n||!0===n||!1===n)return a;if(0===n||"0"===n)return 0;if(["i","+i","1i","+1i","-i","-1i","j","+j","1j","+1j","-j","-1j"].indexOf(n)>=0)return 0;let t=(n+="").indexOf("+"),r=n.indexOf("-");0===t&&(t=n.indexOf("+",1)),0===r&&(r=n.indexOf("-",1));const e=n.substring(n.length-1,n.length),o="i"===e||"j"===e;return t>=0||r>=0?o?t>=0?isNaN(n.substring(0,t))||isNaN(n.substring(t+1,n.length-1))?c:Number(n.substring(0,t)):isNaN(n.substring(0,r))||isNaN(n.substring(r+1,n.length-1))?c:Number(n.substring(0,r)):c:o?isNaN(n.substring(0,n.length-1))?c:0:isNaN(n)?c:n}function Pt(n){const t=Rt(n),r=At(n);if(S(t,r))return a;let e=n.substring(n.length-1);return e="i"===e||"j"===e?e:"i",It(Math.sin(t)*(Math.exp(r)+Math.exp(-r))/2,Math.cos(t)*(Math.exp(r)-Math.exp(-r))/2,e)}function Lt(n){const t=Rt(n),r=At(n);if(S(t,r))return a;let e=n.substring(n.length-1);return e="i"===e||"j"===e?e:"i",It(Math.cos(r)*(Math.exp(t)-Math.exp(-t))/2,Math.sin(r)*(Math.exp(t)+Math.exp(-t))/2,e)}bt.PRECISE=()=>{throw new Error("ERF.PRECISE is not implemented")},Tt.PRECISE=()=>{throw new Error("ERFC.PRECISE is not implemented")};const Ft=en.DIST,qt=en.INV,Ut=on.DIST,_t=Yn.MATH,Vt=Yn.PRECISE,kt=un.DIST,jt=un.DIST.RT,Yt=un.INV,Gt=un.INV.RT,Ht=un.TEST,Xt=sn.P,Bt=sn.P,$t=sn.S,zt=on.INV,Wt=Tt.PRECISE,Kt=bt.PRECISE,Qt=hn.DIST,Zt=gn.DIST,Jt=gn.DIST.RT,nr=gn.INV,tr=gn.INV.RT,rr=Bn.MATH,er=Bn.PRECISE,or=gn.TEST,ir=mn.DIST,ur=mn.INV,ar=dn.PRECISE,fr=vn.DIST,lr=Nn.INV,cr=Nn.DIST,sr=Nn.INV,hr=bn.MULT,gr=bn.SNGL,pr=Tn.DIST,mr=ft.INTL,dr=Sn.DIST,vr=Sn.INV,Er=Sn.S.DIST,Mr=Sn.S.INV,Nr=Cn.EXC,wr=Cn.INC,yr=Dn.EXC,Ir=Dn.INC,br=On.DIST,Tr=xn.EXC,Sr=xn.INC,Ar=Rn.AVG,Cr=Rn.EQ,Dr=Pn.P,Or=Fn.P,xr=Fn.S,Rr=qn.DIST,Pr=qn.DIST.RT,Lr=qn.INV,Fr=qn.TEST,qr=Un.P,Ur=Un.S,_r=kn.DIST,Vr=lt.INTL,kr=jn.TEST;function jr(n){const t=[];return d(n,(n=>{n&&t.push(n)})),t}function Yr(n,t){const r={};for(let t=1;t<n[0].length;++t)r[t]=!0;let e=t[0].length;for(let n=1;n<t.length;++n)t[n].length>e&&(e=t[n].length);for(let o=1;o<n.length;++o)for(let i=1;i<n[o].length;++i){let u=!1,a=!1;for(let r=0;r<t.length;++r){const f=t[r];if(f.length<e)continue;const l=f[0];if(n[o][0]===l){a=!0;for(let t=1;t<f.length;++t)if(!u)if(void 0===f[t]||"*"===f[t])u=!0;else{const r=X(f[t]+""),e=[H(n[o][i],G)].concat(r);u=B(e)}}}a&&(r[i]=r[i]&&u)}const o=[];for(let t=0;t<n[0].length;++t)r[t]&&o.push(t-1);return o}function Gr(n){return n&&n.getTime&&!isNaN(n.getTime())}function Hr(n){return n instanceof Date?n:new Date(n)}function Xr(n,t,r,e,o){if(e=e||0,o=o||0,S(n=R(n),t=R(t),r=R(r),e=R(e),o=R(o)))return a;let i;if(0===n)i=e+r*t;else{const u=Math.pow(1+n,t);i=1===o?e*u+r*(1+n)*(u-1)/n:e*u+r*(u-1)/n}return-i}function Br(n,t,r,e,o,i){if(o=o||0,i=i||0,S(n=R(n),t=R(t),r=R(r),e=R(e),o=R(o),i=R(i)))return a;const u=zr(n,r,e,o,i);return(1===t?1===i?0:-e:1===i?Xr(n,t-2,u,e,1)-u:Xr(n,t-1,u,e,0))*n}function $r(){const n=P(M(arguments));if(n instanceof Error)return n;const t=n[0];let r=0;for(let e=1;e<n.length;e++)r+=n[e]/Math.pow(1+t,e);return r}function zr(n,t,r,e,o){if(e=e||0,o=o||0,S(n=R(n),t=R(t),r=R(r),e=R(e),o=R(o)))return a;let i;if(0===n)i=(r+e)/t;else{const u=Math.pow(1+n,t);i=1===o?(e*n/(u-1)+r*n/(1-1/u))/(1+n):e*n/(u-1)+r*n/(1-1/u)}return-i}const Wr={errors:p,symbols:Zn};t.ABS=function(n){return(n=R(n))instanceof Error?n:Math.abs(n)},t.ACCRINT=function(n,t,r,e,o,i,u){return n=Hr(n),t=Hr(t),r=Hr(r),Gr(n)&&Gr(t)&&Gr(r)?e<=0||o<=0||-1===[1,2,4].indexOf(i)||-1===[0,1,2,3,4].indexOf(u)||r<=n?c:(o=o||0)*e*ht(n,r,u=u||0):a},t.ACCRINTM=function(){throw new Error("ACCRINTM is not implemented")},t.ACOS=function(n){if((n=R(n))instanceof Error)return n;let t=Math.acos(n);return isNaN(t)&&(t=c),t},t.ACOSH=function(n){if((n=R(n))instanceof Error)return n;let t=Math.log(n+Math.sqrt(n*n-1));return isNaN(t)&&(t=c),t},t.ACOT=function(n){return(n=R(n))instanceof Error?n:Math.atan(1/n)},t.ACOTH=function(n){if((n=R(n))instanceof Error)return n;let t=.5*Math.log((n+1)/(n-1));return isNaN(t)&&(t=c),t},t.AGGREGATE=function(n,t,r,e){if(S(n=R(n),R(n)))return a;switch(n){case 1:return tn(r);case 2:return fn(r);case 3:return ln(r);case 4:return wn(r);case 5:return In(r);case 6:return Wn(r);case 7:return Fn.S(r);case 8:return Fn.P(r);case 9:return Qn(r);case 10:return Un.S(r);case 11:return Un.P(r);case 12:return yn(r);case 13:return bn.SNGL(r);case 14:return En(r,e);case 15:return Ln(r,e);case 16:return Cn.INC(r,e);case 17:return xn.INC(r,e);case 18:return Cn.EXC(r,e);case 19:return xn.EXC(r,e)}},t.AMORDEGRC=function(){throw new Error("AMORDEGRC is not implemented")},t.AMORLINC=function(){throw new Error("AMORLINC is not implemented")},t.AND=function(){const n=M(arguments);let t=a;for(let r=0;r<n.length;r++){if(n[r]instanceof Error)return n[r];void 0!==n[r]&&null!==n[r]&&"string"!=typeof n[r]&&(t===a&&(t=!0),n[r]||(t=!1))}return t},t.ARABIC=function(n){if(null==n)return 0;if(n instanceof Error)return n;if(!/^M*(?:D?C{0,3}|C[MD])(?:L?X{0,3}|X[CL])(?:V?I{0,3}|I[XV])$/.test(n))return a;let t=0;return n.replace(/[MDLV]|C[MD]?|X[CL]?|I[XV]?/g,(n=>{t+={M:1e3,CM:900,D:500,CD:400,C:100,XC:90,L:50,XL:40,X:10,IX:9,V:5,IV:4,I:1}[n]})),t},t.ASC=function(){throw new Error("ASC is not implemented")},t.ASIN=function(n){if((n=R(n))instanceof Error)return n;let t=Math.asin(n);return isNaN(t)&&(t=c),t},t.ASINH=function(n){return(n=R(n))instanceof Error?n:Math.log(n+Math.sqrt(n*n+1))},t.ATAN=function(n){return(n=R(n))instanceof Error?n:Math.atan(n)},t.ATAN2=function(n,t){return T(n=R(n),t=R(t))||Math.atan2(n,t)},t.ATANH=function(n){if((n=R(n))instanceof Error)return n;let t=Math.log((1+n)/(1-n))/2;return isNaN(t)&&(t=c),t},t.AVEDEV=function(){const n=M(arguments).filter(q);if(0===n.length)return c;const t=P(n);return t instanceof Error?t:e.sum(e(t).subtract(e.mean(t)).abs()[0])/t.length},t.AVERAGE=tn,t.AVERAGEA=rn,t.AVERAGEIF=function(n,t,r){if(arguments.length<=1)return s;if(r=P(M(r=r||n).filter(q)),n=M(n),r instanceof Error)return r;let e=0,o=0;const i=void 0===t||"*"===t,u=i?null:X(t+"");for(let t=0;t<n.length;t++){const a=n[t];if(i)o+=r[t],e++;else{const n=[H(a,G)].concat(u);B(n)&&(o+=r[t],e++)}}return o/e},t.AVERAGEIFS=function(){const n=m(arguments),t=(n.length-1)/2,r=M(n[0]);let e=0,o=0;for(let i=0;i<r.length;i++){let u=!1;for(let r=0;r<t;r++){const t=n[2*r+1][i],e=n[2*r+2];let o=!1;if(void 0===e||"*"===e)o=!0;else{const n=X(e+""),r=[H(t,G)].concat(n);o=B(r)}if(!o){u=!1;break}u=!0}u&&(o+=r[i],e++)}const i=o/e;return isNaN(i)?0:i},t.BAHTTEXT=function(){throw new Error("BAHTTEXT is not implemented")},t.BASE=function(n,t,r){const e=T(n=R(n),t=R(t),r=R(r));if(e)return e;if(0===t)return c;const o=n.toString(t);return new Array(Math.max(r+1-o.length,0)).join("0")+o},t.BESSELI=function(n,t){return S(n=R(n),t=R(t))?a:o.besseli(n,t)},t.BESSELJ=function(n,t){return S(n=R(n),t=R(t))?a:o.besselj(n,t)},t.BESSELK=function(n,t){return S(n=R(n),t=R(t))?a:o.besselk(n,t)},t.BESSELY=function(n,t){return S(n=R(n),t=R(t))?a:o.bessely(n,t)},t.BETA=en,t.BETADIST=Ft,t.BETAINV=qt,t.BIN2DEC=function(n){if(!yt(n))return c;const t=parseInt(n,2),r=n.toString();return 10===r.length&&"1"===r.substring(0,1)?parseInt(r.substring(1),2)-512:t},t.BIN2HEX=function(n,t){if(!yt(n))return c;const r=n.toString();if(10===r.length&&"1"===r.substring(0,1))return(0xfffffffe00+parseInt(r.substring(1),2)).toString(16);const e=parseInt(n,2).toString(16);return void 0===t?e:isNaN(t)?a:t<0?c:(t=Math.floor(t))>=e.length?Mt("0",t-e.length)+e:c},t.BIN2OCT=function(n,t){if(!yt(n))return c;const r=n.toString();if(10===r.length&&"1"===r.substring(0,1))return(1073741312+parseInt(r.substring(1),2)).toString(8);const e=parseInt(n,2).toString(8);return void 0===t?e:isNaN(t)?a:t<0?c:(t=Math.floor(t))>=e.length?Mt("0",t-e.length)+e:c},t.BINOM=on,t.BINOMDIST=Ut,t.BITAND=function(n,t){return S(n=R(n),t=R(t))?a:n<0||t<0||Math.floor(n)!==n||Math.floor(t)!==t||n>0xffffffffffff||t>0xffffffffffff?c:n&t},t.BITLSHIFT=function(n,t){return S(n=R(n),t=R(t))?a:n<0||Math.floor(n)!==n||n>0xffffffffffff||Math.abs(t)>53?c:t>=0?n<<t:n>>-t},t.BITOR=function(n,t){return S(n=R(n),t=R(t))?a:n<0||t<0||Math.floor(n)!==n||Math.floor(t)!==t||n>0xffffffffffff||t>0xffffffffffff?c:n|t},t.BITRSHIFT=function(n,t){return S(n=R(n),t=R(t))?a:n<0||Math.floor(n)!==n||n>0xffffffffffff||Math.abs(t)>53?c:t>=0?n>>t:n<<-t},t.BITXOR=function(n,t){return S(n=R(n),t=R(t))?a:n<0||t<0||Math.floor(n)!==n||Math.floor(t)!==t||n>0xffffffffffff||t>0xffffffffffff?c:n^t},t.CEILING=Yn,t.CEILINGMATH=_t,t.CEILINGPRECISE=Vt,t.CELL=function(){throw new Error("CELL is not implemented")},t.CHAR=pt,t.CHIDIST=kt,t.CHIDISTRT=jt,t.CHIINV=Yt,t.CHIINVRT=Gt,t.CHISQ=un,t.CHITEST=Ht,t.CHOOSE=function(){if(arguments.length<2)return s;const n=arguments[0];return n<1||n>254||arguments.length<n+1?a:arguments[n]},t.CLEAN=function(n){return S(n)?n:(n=n||"").replace(/[\0-\x1F]/g,"")},t.CODE=mt,t.COLUMN=function(n,t){return 2!==arguments.length?s:t<0?c:n instanceof Array&&"number"==typeof t?0!==n.length?e.col(n,t):void 0:a},t.COLUMNS=function(n){return 1!==arguments.length?s:n instanceof Array?0===n.length?0:e.cols(n):a},t.COMBIN=Gn,t.COMBINA=function(n,t){return T(n=R(n),t=R(t))||(n<t?c:0===n&&0===t?1:Gn(n+t-1,n-1))},t.COMPLEX=It,t.CONCAT=vt,t.CONCATENATE=dt,t.CONFIDENCE=an,t.CONVERT=function(n,t,r){if((n=R(n))instanceof Error)return n;const e=[["a.u. of action","?",null,"action",!1,!1,105457168181818e-48],["a.u. of charge","e",null,"electric_charge",!1,!1,160217653141414e-33],["a.u. of energy","Eh",null,"energy",!1,!1,435974417757576e-32],["a.u. of length","a?",null,"length",!1,!1,529177210818182e-25],["a.u. of mass","m?",null,"mass",!1,!1,910938261616162e-45],["a.u. of time","?/Eh",null,"time",!1,!1,241888432650516e-31],["admiralty knot","admkn",null,"speed",!1,!0,.514773333],["ampere","A",null,"electric_current",!0,!1,1],["ampere per meter","A/m",null,"magnetic_field_intensity",!0,!1,1],["ГҐngstrГ¶m","Г…",["ang"],"length",!1,!0,1e-10],["are","ar",null,"area",!1,!0,100],["astronomical unit","ua",null,"length",!1,!1,149597870691667e-25],["bar","bar",null,"pressure",!1,!1,1e5],["barn","b",null,"area",!1,!1,1e-28],["becquerel","Bq",null,"radioactivity",!0,!1,1],["bit","bit",["b"],"information",!1,!0,1],["btu","BTU",["btu"],"energy",!1,!0,1055.05585262],["byte","byte",null,"information",!1,!0,8],["candela","cd",null,"luminous_intensity",!0,!1,1],["candela per square metre","cd/m?",null,"luminance",!0,!1,1],["coulomb","C",null,"electric_charge",!0,!1,1],["cubic ГҐngstrГ¶m","ang3",["ang^3"],"volume",!1,!0,1e-30],["cubic foot","ft3",["ft^3"],"volume",!1,!0,.028316846592],["cubic inch","in3",["in^3"],"volume",!1,!0,16387064e-12],["cubic light-year","ly3",["ly^3"],"volume",!1,!0,846786664623715e-61],["cubic metre","m?",null,"volume",!0,!0,1],["cubic mile","mi3",["mi^3"],"volume",!1,!0,4168181825.44058],["cubic nautical mile","Nmi3",["Nmi^3"],"volume",!1,!0,6352182208],["cubic Pica","Pica3",["Picapt3","Pica^3","Picapt^3"],"volume",!1,!0,7.58660370370369e-8],["cubic yard","yd3",["yd^3"],"volume",!1,!0,.764554857984],["cup","cup",null,"volume",!1,!0,.0002365882365],["dalton","Da",["u"],"mass",!1,!1,166053886282828e-41],["day","d",["day"],"time",!1,!0,86400],["degree","В°",null,"angle",!1,!1,.0174532925199433],["degrees Rankine","Rank",null,"temperature",!1,!0,.555555555555556],["dyne","dyn",["dy"],"force",!1,!0,1e-5],["electronvolt","eV",["ev"],"energy",!1,!0,1.60217656514141],["ell","ell",null,"length",!1,!0,1.143],["erg","erg",["e"],"energy",!1,!0,1e-7],["farad","F",null,"electric_capacitance",!0,!1,1],["fluid ounce","oz",null,"volume",!1,!0,295735295625e-16],["foot","ft",null,"length",!1,!0,.3048],["foot-pound","flb",null,"energy",!1,!0,1.3558179483314],["gal","Gal",null,"acceleration",!1,!1,.01],["gallon","gal",null,"volume",!1,!0,.003785411784],["gauss","G",["ga"],"magnetic_flux_density",!1,!0,1],["grain","grain",null,"mass",!1,!0,647989e-10],["gram","g",null,"mass",!1,!0,.001],["gray","Gy",null,"absorbed_dose",!0,!1,1],["gross registered ton","GRT",["regton"],"volume",!1,!0,2.8316846592],["hectare","ha",null,"area",!1,!0,1e4],["henry","H",null,"inductance",!0,!1,1],["hertz","Hz",null,"frequency",!0,!1,1],["horsepower","HP",["h"],"power",!1,!0,745.69987158227],["horsepower-hour","HPh",["hh","hph"],"energy",!1,!0,2684519.538],["hour","h",["hr"],"time",!1,!0,3600],["imperial gallon (U.K.)","uk_gal",null,"volume",!1,!0,.00454609],["imperial hundredweight","lcwt",["uk_cwt","hweight"],"mass",!1,!0,50.802345],["imperial quart (U.K)","uk_qt",null,"volume",!1,!0,.0011365225],["imperial ton","brton",["uk_ton","LTON"],"mass",!1,!0,1016.046909],["inch","in",null,"length",!1,!0,.0254],["international acre","uk_acre",null,"area",!1,!0,4046.8564224],["IT calorie","cal",null,"energy",!1,!0,4.1868],["joule","J",null,"energy",!0,!0,1],["katal","kat",null,"catalytic_activity",!0,!1,1],["kelvin","K",["kel"],"temperature",!0,!0,1],["kilogram","kg",null,"mass",!0,!0,1],["knot","kn",null,"speed",!1,!0,.514444444444444],["light-year","ly",null,"length",!1,!0,9460730472580800],["litre","L",["l","lt"],"volume",!1,!0,.001],["lumen","lm",null,"luminous_flux",!0,!1,1],["lux","lx",null,"illuminance",!0,!1,1],["maxwell","Mx",null,"magnetic_flux",!1,!1,1e-18],["measurement ton","MTON",null,"volume",!1,!0,1.13267386368],["meter per hour","m/h",["m/hr"],"speed",!1,!0,.00027777777777778],["meter per second","m/s",["m/sec"],"speed",!0,!0,1],["meter per second squared","m?s??",null,"acceleration",!0,!1,1],["parsec","pc",["parsec"],"length",!1,!0,0x6da012f958ee1c],["meter squared per second","m?/s",null,"kinematic_viscosity",!0,!1,1],["metre","m",null,"length",!0,!0,1],["miles per hour","mph",null,"speed",!1,!0,.44704],["millimetre of mercury","mmHg",null,"pressure",!1,!1,133.322],["minute","?",null,"angle",!1,!1,.000290888208665722],["minute","min",["mn"],"time",!1,!0,60],["modern teaspoon","tspm",null,"volume",!1,!0,5e-6],["mole","mol",null,"amount_of_substance",!0,!1,1],["morgen","Morgen",null,"area",!1,!0,2500],["n.u. of action","?",null,"action",!1,!1,105457168181818e-48],["n.u. of mass","m?",null,"mass",!1,!1,910938261616162e-45],["n.u. of speed","c?",null,"speed",!1,!1,299792458],["n.u. of time","?/(me?c??)",null,"time",!1,!1,128808866778687e-35],["nautical mile","M",["Nmi"],"length",!1,!0,1852],["newton","N",null,"force",!0,!0,1],["Е“rsted","Oe ",null,"magnetic_field_intensity",!1,!1,79.5774715459477],["ohm","О©",null,"electric_resistance",!0,!1,1],["ounce mass","ozm",null,"mass",!1,!0,.028349523125],["pascal","Pa",null,"pressure",!0,!1,1],["pascal second","Pa?s",null,"dynamic_viscosity",!0,!1,1],["pferdestГ¤rke","PS",null,"power",!1,!0,735.49875],["phot","ph",null,"illuminance",!1,!1,1e-4],["pica (1/6 inch)","pica",null,"length",!1,!0,.00035277777777778],["pica (1/72 inch)","Pica",["Picapt"],"length",!1,!0,.00423333333333333],["poise","P",null,"dynamic_viscosity",!1,!1,.1],["pond","pond",null,"force",!1,!0,.00980665],["pound force","lbf",null,"force",!1,!0,4.4482216152605],["pound mass","lbm",null,"mass",!1,!0,.45359237],["quart","qt",null,"volume",!1,!0,.000946352946],["radian","rad",null,"angle",!0,!1,1],["second","?",null,"angle",!1,!1,484813681109536e-20],["second","s",["sec"],"time",!0,!0,1],["short hundredweight","cwt",["shweight"],"mass",!1,!0,45.359237],["siemens","S",null,"electrical_conductance",!0,!1,1],["sievert","Sv",null,"equivalent_dose",!0,!1,1],["slug","sg",null,"mass",!1,!0,14.59390294],["square ГҐngstrГ¶m","ang2",["ang^2"],"area",!1,!0,1e-20],["square foot","ft2",["ft^2"],"area",!1,!0,.09290304],["square inch","in2",["in^2"],"area",!1,!0,64516e-8],["square light-year","ly2",["ly^2"],"area",!1,!0,895054210748189e17],["square meter","m?",null,"area",!0,!0,1],["square mile","mi2",["mi^2"],"area",!1,!0,2589988.110336],["square nautical mile","Nmi2",["Nmi^2"],"area",!1,!0,3429904],["square Pica","Pica2",["Picapt2","Pica^2","Picapt^2"],"area",!1,!0,1792111111111e-17],["square yard","yd2",["yd^2"],"area",!1,!0,.83612736],["statute mile","mi",null,"length",!1,!0,1609.344],["steradian","sr",null,"solid_angle",!0,!1,1],["stilb","sb",null,"luminance",!1,!1,1e-4],["stokes","St",null,"kinematic_viscosity",!1,!1,1e-4],["stone","stone",null,"mass",!1,!0,6.35029318],["tablespoon","tbs",null,"volume",!1,!0,147868e-10],["teaspoon","tsp",null,"volume",!1,!0,492892e-11],["tesla","T",null,"magnetic_flux_density",!0,!0,1],["thermodynamic calorie","c",null,"energy",!1,!0,4.184],["ton","ton",null,"mass",!1,!0,907.18474],["tonne","t",null,"mass",!1,!1,1e3],["U.K. pint","uk_pt",null,"volume",!1,!0,.00056826125],["U.S. bushel","bushel",null,"volume",!1,!0,.03523907],["U.S. oil barrel","barrel",null,"volume",!1,!0,.158987295],["U.S. pint","pt",["us_pt"],"volume",!1,!0,.000473176473],["U.S. survey mile","survey_mi",null,"length",!1,!0,1609.347219],["U.S. survey/statute acre","us_acre",null,"area",!1,!0,4046.87261],["volt","V",null,"voltage",!0,!1,1],["watt","W",null,"power",!0,!0,1],["watt-hour","Wh",["wh"],"energy",!1,!0,3600],["weber","Wb",null,"magnetic_flux",!0,!1,1],["yard","yd",null,"length",!1,!0,.9144],["year","yr",null,"time",!1,!0,31557600]],o={Yi:["yobi",80,12089258196146292e8,"Yi","yotta"],Zi:["zebi",70,11805916207174113e5,"Zi","zetta"],Ei:["exbi",60,0x1000000000000000,"Ei","exa"],Pi:["pebi",50,0x4000000000000,"Pi","peta"],Ti:["tebi",40,1099511627776,"Ti","tera"],Gi:["gibi",30,1073741824,"Gi","giga"],Mi:["mebi",20,1048576,"Mi","mega"],ki:["kibi",10,1024,"ki","kilo"]},i={Y:["yotta",1e24,"Y"],Z:["zetta",1e21,"Z"],E:["exa",1e18,"E"],P:["peta",1e15,"P"],T:["tera",1e12,"T"],G:["giga",1e9,"G"],M:["mega",1e6,"M"],k:["kilo",1e3,"k"],h:["hecto",100,"h"],e:["dekao",10,"e"],d:["deci",.1,"d"],c:["centi",.01,"c"],m:["milli",.001,"m"],u:["micro",1e-6,"u"],n:["nano",1e-9,"n"],p:["pico",1e-12,"p"],f:["femto",1e-15,"f"],a:["atto",1e-18,"a"],z:["zepto",1e-21,"z"],y:["yocto",1e-24,"y"]};let u,a=null,f=null,l=t,c=r,h=1,g=1;for(let n=0;n<e.length;n++)u=null===e[n][2]?[]:e[n][2],(e[n][1]===l||u.indexOf(l)>=0)&&(a=e[n]),(e[n][1]===c||u.indexOf(c)>=0)&&(f=e[n]);if(null===a){const n=o[t.substring(0,2)];let r=i[t.substring(0,1)];"da"===t.substring(0,2)&&(r=["dekao",10,"da"]),n?(h=n[2],l=t.substring(2)):r&&(h=r[1],l=t.substring(r[2].length));for(let n=0;n<e.length;n++)u=null===e[n][2]?[]:e[n][2],(e[n][1]===l||u.indexOf(l)>=0)&&(a=e[n])}if(null===f){const n=o[r.substring(0,2)];let t=i[r.substring(0,1)];"da"===r.substring(0,2)&&(t=["dekao",10,"da"]),n?(g=n[2],c=r.substring(2)):t&&(g=t[1],c=r.substring(t[2].length));for(let n=0;n<e.length;n++)u=null===e[n][2]?[]:e[n][2],(e[n][1]===c||u.indexOf(c)>=0)&&(f=e[n])}return null===a||null===f||a[3]!==f[3]?s:n*a[6]*h/(f[6]*g)},t.CORREL=function(n,t){return S(n=P(M(n)),t=P(M(t)))?a:e.corrcoeff(n,t)},t.COS=function(n){return(n=R(n))instanceof Error?n:Math.cos(n)},t.COSH=function(n){return(n=R(n))instanceof Error?n:(Math.exp(n)+Math.exp(-n))/2},t.COT=function(n){return(n=R(n))instanceof Error?n:0===n?u:1/Math.tan(n)},t.COTH=function(n){if((n=R(n))instanceof Error)return n;if(0===n)return u;const t=Math.exp(2*n);return(t+1)/(t-1)},t.COUNT=fn,t.COUNTA=ln,t.COUNTBLANK=cn,t.COUNTIF=function(n,t){if(n=M(n),void 0===t||"*"===t)return n.length;let r=0;const e=X(t+"");for(let t=0;t<n.length;t++){const o=[H(n[t],G)].concat(e);B(o)&&r++}return r},t.COUNTIFS=function(){const n=m(arguments),t=new Array(M(n[0]).length);for(let n=0;n<t.length;n++)t[n]=!0;for(let r=0;r<n.length;r+=2){const e=M(n[r]),o=n[r+1];if(void 0!==o&&"*"!==o){const n=X(o+"");for(let r=0;r<e.length;r++){const o=[H(e[r],G)].concat(n);t[r]=t[r]&&B(o)}}}let r=0;for(let n=0;n<t.length;n++)t[n]&&r++;return r},t.COUPDAYBS=function(){throw new Error("COUPDAYBS is not implemented")},t.COUPDAYS=function(){throw new Error("COUPDAYS is not implemented")},t.COUPDAYSNC=function(){throw new Error("COUPDAYSNC is not implemented")},t.COUPNCD=function(){throw new Error("COUPNCD is not implemented")},t.COUPNUM=function(){throw new Error("COUPNUM is not implemented")},t.COUPPCD=function(){throw new Error("COUPPCD is not implemented")},t.COVAR=Xt,t.COVARIANCE=sn,t.COVARIANCEP=Bt,t.COVARIANCES=$t,t.CRITBINOM=zt,t.CSC=function(n){return(n=R(n))instanceof Error?n:0===n?u:1/Math.sin(n)},t.CSCH=function(n){return(n=R(n))instanceof Error?n:0===n?u:2/(Math.exp(n)-Math.exp(-n))},t.CUMIPMT=function(n,t,r,e,o,i){if(S(n=R(n),t=R(t),r=R(r)))return a;if(n<=0||t<=0||r<=0)return c;if(e<1||o<1||e>o)return c;if(0!==i&&1!==i)return c;const u=zr(n,t,r,0,i);let f=0;1===e&&(0===i&&(f=-r),e++);for(let t=e;t<=o;t++)f+=1===i?Xr(n,t-2,u,r,1)-u:Xr(n,t-1,u,r,0);return f*=n,f},t.CUMPRINC=function(n,t,r,e,o,i){if(S(n=R(n),t=R(t),r=R(r)))return a;if(n<=0||t<=0||r<=0)return c;if(e<1||o<1||e>o)return c;if(0!==i&&1!==i)return c;const u=zr(n,t,r,0,i);let f=0;1===e&&(f=0===i?u+r*n:u,e++);for(let t=e;t<=o;t++)f+=i>0?u-(Xr(n,t-2,u,r,1)-u)*n:u-Xr(n,t-1,u,r,0)*n;return f},t.DATE=function(n,t,r){let e;return S(n=R(n),t=R(t),r=R(r))?e=a:(e=new Date(n,t-1,r),e.getFullYear()<0&&(e=c)),e},t.DATEDIF=et,t.DATEVALUE=function(n){if("string"!=typeof n)return a;const t=Date.parse(n);return isNaN(t)?a:new Date(n)},t.DAVERAGE=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);let i=0;return d(e,(n=>{i+=o[n]})),0===e.length?u:i/e.length},t.DAY=function(n){const t=O(n);return t instanceof Error?t:t.getDate()},t.DAYS=it,t.DAYS360=ut,t.DB=function(n,t,r,e,o){if(o=void 0===o?12:o,S(n=R(n),t=R(t),r=R(r),e=R(e),o=R(o)))return a;if(n<0||t<0||r<0||e<0)return c;if(-1===[1,2,3,4,5,6,7,8,9,10,11,12].indexOf(o))return c;if(e>r)return c;if(t>=n)return 0;const i=(1-Math.pow(t/n,1/r)).toFixed(3),u=n*i*o/12;let f=u,l=0;const s=e===r?r-1:e;for(let t=2;t<=s;t++)l=(n-f)*i,f+=l;return 1===e?u:e===r?(n-f)*i:l},t.DBCS=function(){throw new Error("DBCS is not implemented")},t.DCOUNT=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);const i=[];return d(e,(n=>{i.push(o[n])})),fn(i)},t.DCOUNTA=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);const i=[];return d(e,(n=>{i.push(o[n])})),ln(i)},t.DDB=function(n,t,r,e,o){if(o=void 0===o?2:o,S(n=R(n),t=R(t),r=R(r),e=R(e),o=R(o)))return a;if(n<0||t<0||r<0||e<0||o<=0)return c;if(e>r)return c;if(t>=n)return 0;let i=0,u=0;for(let a=1;a<=e;a++)u=Math.min(o/r*(n-i),n-t-i),i+=u;return u},t.DEC2BIN=function(n,t){if((n=R(n))instanceof Error)return n;if(!/^-?[0-9]{1,3}$/.test(n)||n<-512||n>511)return c;if(n<0)return"1"+Mt("0",9-(512+n).toString(2).length)+(512+n).toString(2);const r=parseInt(n,10).toString(2);return void 0===t?r:isNaN(t)?a:t<0?c:(t=Math.floor(t))>=r.length?Mt("0",t-r.length)+r:c},t.DEC2HEX=function(n,t){if((n=R(n))instanceof Error)return n;if(!/^-?[0-9]{1,12}$/.test(n)||n<-549755813888||n>549755813887)return c;if(n<0)return(1099511627776+n).toString(16);const r=parseInt(n,10).toString(16);return void 0===t?r:isNaN(t)?a:t<0?c:(t=Math.floor(t))>=r.length?Mt("0",t-r.length)+r:c},t.DEC2OCT=function(n,t){if((n=R(n))instanceof Error)return n;if(!/^-?[0-9]{1,9}$/.test(n)||n<-536870912||n>536870911)return c;if(n<0)return(1073741824+n).toString(8);const r=parseInt(n,10).toString(8);return void 0===t?r:isNaN(t)?a:t<0?c:(t=Math.floor(t))>=r.length?Mt("0",t-r.length)+r:c},t.DECIMAL=function(n,t){if(arguments.length<1)return a;return T(n=R(n),t=R(t))||(0===t?c:parseInt(n,t))},t.DEGREES=function(n){return(n=R(n))instanceof Error?n:180*n/Math.PI},t.DELTA=function(n,t){return t=void 0===t?0:t,S(n=R(n),t=R(t))?a:n===t?1:0},t.DEVSQ=function(){const n=P(M(arguments));if(n instanceof Error)return n;const t=e.mean(n);let r=0;for(let e=0;e<n.length;e++)r+=Math.pow(n[e]-t,2);return r},t.DGET=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];return o=y("string"==typeof t?n[b(n,t)]:n[t]),0===e.length?a:e.length>1?c:o[e[0]]},t.DISC=function(n,t,r,e,o){if(S(n=O(n),t=O(t),r=R(r),e=R(e),o=(o=R(o))||0))return a;if(r<=0||e<=0)return c;if(n>=t)return a;let i,u;switch(o){case 0:i=360,u=ut(n,t,!1);break;case 1:case 3:i=365,u=et(n,t,"D");break;case 2:i=360,u=et(n,t,"D");break;case 4:i=360,u=ut(n,t,!0);break;default:return c}return(e-r)/e*i/u},t.DMAX=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);let i=o[e[0]];return d(e,(n=>{i<o[n]&&(i=o[n])})),i},t.DMIN=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);let i=o[e[0]];return d(e,(n=>{i>o[n]&&(i=o[n])})),i},t.DOLLAR=function(n,t=2){if(n=R(n),isNaN(n))return a;const r={style:"currency",currency:"USD",minimumFractionDigits:t>=0?t:0,maximumFractionDigits:t>=0?t:0},e=(n=Kn(n,t)).toLocaleString("en-US",r);return n<0?"$("+e.slice(2)+")":e},t.DOLLARDE=function(n,t){if(S(n=R(n),t=R(t)))return a;if(t<0)return c;if(t>=0&&t<1)return u;t=parseInt(t,10);let r=parseInt(n,10);r+=n%1*Math.pow(10,Math.ceil(Math.log(t)/Math.LN10))/t;const e=Math.pow(10,Math.ceil(Math.log(t)/Math.LN2)+1);return r=Math.round(r*e)/e,r},t.DOLLARFR=function(n,t){if(S(n=R(n),t=R(t)))return a;if(t<0)return c;if(t>=0&&t<1)return u;t=parseInt(t,10);let r=parseInt(n,10);return r+=n%1*Math.pow(10,-Math.ceil(Math.log(t)/Math.LN10))*t,r},t.DPRODUCT=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);let i=[];d(e,(n=>{i.push(o[n])})),i=jr(i);let u=1;return d(i,(n=>{u*=n})),u},t.DSTDEV=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);let i=[];return d(e,(n=>{i.push(o[n])})),i=jr(i),Fn.S(i)},t.DSTDEVP=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);let i=[];return d(e,(n=>{i.push(o[n])})),i=jr(i),Fn.P(i)},t.DSUM=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);const i=[];return d(e,(n=>{i.push(o[n])})),Qn(i)},t.DURATION=function(){throw new Error("DURATION is not implemented")},t.DVAR=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);const i=[];return d(e,(n=>{i.push(o[n])})),Un.S(i)},t.DVARP=function(n,t,r){if(isNaN(t)&&"string"!=typeof t)return a;const e=Yr(n,r);let o=[];if("string"==typeof t){const r=b(n,t);o=y(n[r])}else o=y(n[t]);const i=[];return d(e,(n=>{i.push(o[n])})),Un.P(i)},t.EDATE=function(n,t){return(n=O(n))instanceof Error?n:isNaN(t)?a:(t=parseInt(t,10),n.setMonth(n.getMonth()+t),n)},t.EFFECT=function(n,t){return S(n=R(n),t=R(t))?a:n<=0||t<1?c:(t=parseInt(t,10),Math.pow(1+n/t,t)-1)},t.EOMONTH=function(n,t){return(n=O(n))instanceof Error?n:isNaN(t)?a:(t=parseInt(t,10),new Date(n.getFullYear(),n.getMonth()+t+1,0))},t.ERF=bt,t.ERFC=Tt,t.ERFCPRECISE=Wt,t.ERFPRECISE=Kt,t.ERROR=$,t.EVEN=function(n){return(n=R(n))instanceof Error?n:Yn(n,-2,-1)},t.EXACT=function(n,t){if(2!==arguments.length)return s;return T(n,t)||(n=L(n))===L(t)},t.EXP=function(n){return arguments.length<1?s:arguments.length>1?h:(n=R(n))instanceof Error?n:n=Math.exp(n)},t.EXPON=hn,t.EXPONDIST=Qt,t.F=gn,t.FACT=Xn,t.FACTDOUBLE=function n(t){if((t=R(t))instanceof Error)return t;const r=Math.floor(t);return r<=0?1:r*n(r-2)},t.FALSE=function(){return!1},t.FDIST=Zt,t.FDISTRT=Jt,t.FIND=function(n,t,r){if(arguments.length<2)return s;n=L(n),r=void 0===r?0:r;const e=(t=L(t)).indexOf(n,r-1);return-1===e?a:e+1},t.FINV=nr,t.FINVRT=tr,t.FISHER=function(n){return(n=R(n))instanceof Error?n:Math.log((1+n)/(1-n))/2},t.FISHERINV=function(n){if((n=R(n))instanceof Error)return n;const t=Math.exp(2*n);return(t-1)/(t+1)},t.FIXED=Et,t.FLOOR=Bn,t.FLOORMATH=rr,t.FLOORPRECISE=er,t.FORECAST=pn,t.FREQUENCY=function(n,t){if(S(n=P(M(n)),t=P(M(t))))return a;const r=n.length,e=t.length,o=[];for(let i=0;i<=e;i++){o[i]=0;for(let u=0;u<r;u++)0===i?n[u]<=t[0]&&(o[0]+=1):i<e?n[u]>t[i-1]&&n[u]<=t[i]&&(o[i]+=1):i===e&&n[u]>t[e-1]&&(o[e]+=1)}return o},t.FTEST=or,t.FV=Xr,t.FVSCHEDULE=function(n,t){if(S(n=R(n),t=P(M(t))))return a;const r=t.length;let e=n;for(let n=0;n<r;n++)e*=1+t[n];return e},t.GAMMA=mn,t.GAMMADIST=ir,t.GAMMAINV=ur,t.GAMMALN=dn,t.GAMMALNPRECISE=ar,t.GAUSS=function(n){return(n=R(n))instanceof Error?n:e.normal.cdf(n,0,1)-.5},t.GCD=function(){const n=P(M(arguments));if(n instanceof Error)return n;const t=n.length,r=n[0];let e=r<0?-r:r;for(let r=1;r<t;r++){const t=n[r];let o=t<0?-t:t;for(;e&&o;)e>o?e%=o:o%=e;e+=o}return e},t.GEOMEAN=function(){const n=P(M(arguments));return n instanceof Error?n:e.geomean(n)},t.GESTEP=function(n,t){return S(t=t||0,n=R(n))?n:n>=t?1:0},t.GROWTH=function(n,t,r,e){if((n=P(n))instanceof Error)return n;let o;if(void 0===t)for(t=[],o=1;o<=n.length;o++)t.push(o);if(void 0===r)for(r=[],o=1;o<=n.length;o++)r.push(o);if(S(t=P(t),r=P(r)))return a;void 0===e&&(e=!0);const i=n.length;let u,f,l=0,c=0,s=0,h=0;for(o=0;o<i;o++){const r=t[o],e=Math.log(n[o]);l+=r,c+=e,s+=r*e,h+=r*r}l/=i,c/=i,s/=i,h/=i,e?(u=(s-l*c)/(h-l*l),f=c-u*l):(u=s/h,f=0);const g=[];for(o=0;o<r.length;o++)g.push(Math.exp(f+u*r[o]));return g},t.HARMEAN=function(){const n=P(M(arguments));if(n instanceof Error)return n;const t=n.length;let r=0;for(let e=0;e<t;e++)r+=1/n[e];return t/r},t.HEX2BIN=function(n,t){if(!/^[0-9A-Fa-f]{1,10}$/.test(n))return c;const r=!(10!==n.length||"f"!==n.substring(0,1).toLowerCase()),e=r?parseInt(n,16)-1099511627776:parseInt(n,16);if(e<-512||e>511)return c;if(r)return"1"+Mt("0",9-(512+e).toString(2).length)+(512+e).toString(2);const o=e.toString(2);return void 0===t?o:isNaN(t)?a:t<0?c:(t=Math.floor(t))>=o.length?Mt("0",t-o.length)+o:c},t.HEX2DEC=function(n){if(!/^[0-9A-Fa-f]{1,10}$/.test(n))return c;const t=parseInt(n,16);return t>=549755813888?t-1099511627776:t},t.HEX2OCT=function(n,t){if(!/^[0-9A-Fa-f]{1,10}$/.test(n))return c;const r=parseInt(n,16);if(r>536870911&&r<0xffe0000000)return c;if(r>=0xffe0000000)return(r-0xffc0000000).toString(8);const e=r.toString(8);return void 0===t?e:isNaN(t)?a:t<0?c:(t=Math.floor(t))>=e.length?Mt("0",t-e.length)+e:c},t.HLOOKUP=function(n,t,r,e){return nn(n,I(t),r,e)},t.HOUR=function(n){return(n=O(n))instanceof Error?n:n.getHours()},t.HYPGEOM=vn,t.HYPGEOMDIST=fr,t.IF=function(n,t,r){return n instanceof Error?n:(null==(t=!(arguments.length>=2)||t)&&(t=0),null==(r=3===arguments.length&&r)&&(r=0),n?t:r)},t.IFERROR=function(n,t){return W(n)?t:n},t.IFNA=function(n,t){return n===s?t:n},t.IFS=function(){for(let n=0;n<arguments.length/2;n++)if(arguments[2*n])return arguments[2*n+1];return s},t.IMABS=St,t.IMAGINARY=At,t.IMARGUMENT=Ct,t.IMCONJUGATE=function(n){const t=Rt(n),r=At(n);if(S(t,r))return a;let e=n.substring(n.length-1);return e="i"===e||"j"===e?e:"i",0!==r?It(t,-r,e):n},t.IMCOS=Dt,t.IMCOSH=Ot,t.IMCOT=function(n){return S(Rt(n),At(n))?a:xt(Dt(n),Pt(n))},t.IMCSC=function(n){return!0===n||!1===n?a:S(Rt(n),At(n))?c:xt("1",Pt(n))},t.IMCSCH=function(n){return!0===n||!1===n?a:S(Rt(n),At(n))?c:xt("1",Lt(n))},t.IMDIV=xt,t.IMEXP=function(n){const t=Rt(n),r=At(n);if(S(t,r))return a;let e=n.substring(n.length-1);e="i"===e||"j"===e?e:"i";const o=Math.exp(t);return It(o*Math.cos(r),o*Math.sin(r),e)},t.IMLN=function(n){const t=Rt(n),r=At(n);if(S(t,r))return a;let e=n.substring(n.length-1);return e="i"===e||"j"===e?e:"i",It(Math.log(Math.sqrt(t*t+r*r)),Math.atan(r/t),e)},t.IMLOG10=function(n){const t=Rt(n),r=At(n);if(S(t,r))return a;let e=n.substring(n.length-1);return e="i"===e||"j"===e?e:"i",It(Math.log(Math.sqrt(t*t+r*r))/Math.log(10),Math.atan(r/t)/Math.log(10),e)},t.IMLOG2=function(n){const t=Rt(n),r=At(n);if(S(t,r))return a;let e=n.substring(n.length-1);return e="i"===e||"j"===e?e:"i",It(Math.log(Math.sqrt(t*t+r*r))/Math.log(2),Math.atan(r/t)/Math.log(2),e)},t.IMPOWER=function(n,t){if(S(t=R(t),Rt(n),At(n)))return a;let r=n.substring(n.length-1);r="i"===r||"j"===r?r:"i";const e=Math.pow(St(n),t),o=Ct(n);return It(e*Math.cos(t*o),e*Math.sin(t*o),r)},t.IMPRODUCT=function(){let n=arguments[0];if(!arguments.length)return a;for(let t=1;t<arguments.length;t++){const r=Rt(n),e=At(n),o=Rt(arguments[t]),i=At(arguments[t]);if(S(r,e,o,i))return a;n=It(r*o-e*i,r*i+e*o)}return n},t.IMREAL=Rt,t.IMSEC=function(n){return!0===n||!1===n||S(Rt(n),At(n))?a:xt("1",Dt(n))},t.IMSECH=function(n){return S(Rt(n),At(n))?a:xt("1",Ot(n))},t.IMSIN=Pt,t.IMSINH=Lt,t.IMSQRT=function(n){if(S(Rt(n),At(n)))return a;let t=n.substring(n.length-1);t="i"===t||"j"===t?t:"i";const r=Math.sqrt(St(n)),e=Ct(n);return It(r*Math.cos(e/2),r*Math.sin(e/2),t)},t.IMSUB=function(n,t){const r=Rt(n),e=At(n),o=Rt(t),i=At(t);if(S(r,e,o,i))return a;const u=n.substring(n.length-1),f=t.substring(t.length-1);let l="i";return("j"===u||"j"===f)&&(l="j"),It(r-o,e-i,l)},t.IMSUM=function(){if(!arguments.length)return a;const n=M(arguments);let t=n[0];for(let r=1;r<n.length;r++){const e=Rt(t),o=At(t),i=Rt(n[r]),u=At(n[r]);if(S(e,o,i,u))return a;t=It(e+i,o+u)}return t},t.IMTAN=function(n){return!0===n||!1===n||S(Rt(n),At(n))?a:xt(Pt(n),Dt(n))},t.INDEX=function(n,t,r){const e=T(n,t,r);if(e)return e;if(!Array.isArray(n))return a;const o=n.length>0&&!Array.isArray(n[0]);return o&&!r?(r=t,t=1):(r=r||1,t=t||1),r<0||t<0?a:o&&1===t&&r<=n.length?n[r-1]:t<=n.length&&r<=n[t-1].length?n[t-1][r-1]:f},t.INFO=function(){throw new Error("INFO is not implemented")},t.INT=function(n){return(n=R(n))instanceof Error?n:Math.floor(n)},t.INTERCEPT=function(n,t){return S(n=P(n),t=P(t))?a:n.length!==t.length?s:pn(0,n,t)},t.INTRATE=function(){throw new Error("INTRATE is not implemented")},t.IPMT=Br,t.IRR=function(n,t){if(t=t||0,S(n=P(M(n)),t=R(t)))return a;const r=(n,t,r)=>{const e=r+1;let o=n[0];for(let r=1;r<n.length;r++)o+=n[r]/Math.pow(e,(t[r]-t[0])/365);return o},e=(n,t,r)=>{const e=r+1;let o=0;for(let r=1;r<n.length;r++){const i=(t[r]-t[0])/365;o-=i*n[r]/Math.pow(e,i+1)}return o},o=[];let i=!1,u=!1;for(let t=0;t<n.length;t++)o[t]=0===t?0:o[t-1]+365,n[t]>0&&(i=!0),n[t]<0&&(u=!0);if(!i||!u)return c;let f,l,s,h=t=void 0===t?.1:t,g=!0;do{s=r(n,o,h),f=h-s/e(n,o,h),l=Math.abs(f-h),h=f,g=l>1e-10&&Math.abs(s)>1e-10}while(g);return h},t.ISBLANK=function(n){return null===n},t.ISERR=z,t.ISERROR=W,t.ISEVEN=function(n){return!(1&Math.floor(Math.abs(n)))},t.ISFORMULA=function(){throw new Error("ISFORMULA is not implemented")},t.ISLOGICAL=K,t.ISNA=function(n){return n===s},t.ISNONTEXT=function(n){return"string"!=typeof n},t.ISNUMBER=Q,t.ISO=$n,t.ISODD=function(n){return!!(1&Math.floor(Math.abs(n)))},t.ISOWEEKNUM=at,t.ISPMT=function(n,t,r,e){return S(n=R(n),t=R(t),r=R(r),e=R(e))?a:e*n*(t/r-1)},t.ISREF=function(){throw new Error("ISREF is not implemented")},t.ISTEXT=Z,t.KURT=function(){const n=P(M(arguments));if(n instanceof Error)return n;const t=e.mean(n),r=n.length;let o=0;for(let e=0;e<r;e++)o+=Math.pow(n[e]-t,4);return o/=Math.pow(e.stdev(n,!0),4),r*(r+1)/((r-1)*(r-2)*(r-3))*o-3*(r-1)*(r-1)/((r-2)*(r-3))},t.LARGE=En,t.LCM=function(){const n=P(M(arguments));if(n instanceof Error)return n;for(var t,r,e,o,i=1;void 0!==(e=n.pop());){if(0===e)return 0;for(;e>1;){if(e%2){for(t=3,r=Math.floor(Math.sqrt(e));t<=r&&e%t;t+=2);o=t<=r?t:e}else o=2;for(e/=o,i*=o,t=n.length;t;n[--t]%o==0&&1==(n[t]/=o)&&n.splice(t,1));}}return i},t.LEFT=function(n,t){return T(n,t)||(n=L(n),(t=R(t=void 0===t?1:t))instanceof Error||"string"!=typeof n?a:n.substring(0,t))},t.LEN=function(n){return 0===arguments.length?h:n instanceof Error?n:Array.isArray(n)?a:L(n).length},t.LINEST=Mn,t.LN=function(n){return(n=R(n))instanceof Error?n:0===n?c:Math.log(n)},t.LOG=function(n,t){return T(n=R(n),t=R(t))||(0===n||0===t?c:Math.log(n)/Math.log(t))},t.LOG10=function(n){return(n=R(n))instanceof Error?n:0===n?c:Math.log(n)/Math.log(10)},t.LOGEST=function(n,t){if(S(n=P(M(n)),t=P(M(t))))return a;if(n.length!==t.length)return a;for(let t=0;t<n.length;t++)n[t]=Math.log(n[t]);const r=Mn(n,t);return r[0]=Math.round(1e6*Math.exp(r[0]))/1e6,r[1]=Math.round(1e6*Math.exp(r[1]))/1e6,r},t.LOGINV=lr,t.LOGNORM=Nn,t.LOGNORMDIST=cr,t.LOGNORMINV=sr,t.LOOKUP=function(n,t,r){t=M(t),r=r?M(r):t;const e="number"==typeof n;let o=s;for(let i=0;i<t.length;i++){if(t[i]===n)return r[i];if(e&&t[i]<=n||"string"==typeof t[i]&&t[i].localeCompare(n)<0)o=r[i];else if(e&&t[i]>n)return o}return o},t.LOWER=function(n){return 1!==arguments.length?a:S(n=L(n))?n:n.toLowerCase()},t.MATCH=function(n,t,r){if(!n||!t)return s;if(2===arguments.length&&(r=1),!((t=M(t))instanceof Array))return s;if(-1!==r&&0!==r&&1!==r)return s;let e,o;for(let i=0;i<t.length;i++)if(1===r){if(t[i]===n)return i+1;t[i]<n&&(o?t[i]>o&&(e=i+1,o=t[i]):(e=i+1,o=t[i]))}else if(0===r){if("string"==typeof n&&"string"==typeof t[i]){const r=n.toLowerCase().replace(/\?/g,".").replace(/\*/g,".*").replace(/~/g,"\\");if(new RegExp("^"+r+"$").test(t[i].toLowerCase()))return i+1}else if(t[i]===n)return i+1}else if(-1===r){if(t[i]===n)return i+1;t[i]>n&&(o?t[i]<o&&(e=i+1,o=t[i]):(e=i+1,o=t[i]))}return e||s},t.MAX=wn,t.MAXA=function(){const n=M(arguments),t=T.apply(void 0,n);if(t)return t;let r=v(n);return r=r.map((n=>null==n?0:n)),0===r.length?0:Math.max.apply(Math,r)},t.MDURATION=function(){throw new Error("MDURATION is not implemented")},t.MEDIAN=yn,t.MID=function(n,t,r){if(null==t)return a;if(S(t=R(t),r=R(r))||"string"!=typeof n)return r;const e=t-1,o=e+r;return n.substring(e,o)},t.MIN=In,t.MINA=function(){const n=M(arguments),t=T.apply(void 0,n);if(t)return t;let r=v(n);return r=r.map((n=>null==n?0:n)),0===r.length?0:Math.min.apply(Math,r)},t.MINUTE=function(n){return(n=O(n))instanceof Error?n:n.getMinutes()},t.MIRR=function(n,t,r){if(S(n=P(M(n)),t=R(t),r=R(r)))return a;const e=n.length,o=[],i=[];for(let t=0;t<e;t++)n[t]<0?o.push(n[t]):i.push(n[t]);const u=-$r(r,i)*Math.pow(1+r,e-1),f=$r(t,o)*(1+t);return Math.pow(u/f,1/(e-1))-1},t.MMULT=function(n,t){return!Array.isArray(n)||!Array.isArray(t)||n.some((n=>!n.length))||t.some((n=>!n.length))||N(n).some((n=>"number"!=typeof n))||N(t).some((n=>"number"!=typeof n))||n[0].length!==t.length?a:Array(n.length).fill(0).map((()=>Array(t[0].length).fill(0))).map(((r,e)=>r.map(((r,o)=>n[e].reduce(((n,r,e)=>n+r*t[e][o]),0)))))},t.MOD=function(n,t){const r=T(n=R(n),t=R(t));if(r)return r;if(0===t)return u;let e=Math.abs(n%t);return e=n<0?t-e:e,t>0?e:-e},t.MODE=bn,t.MODEMULT=hr,t.MODESNGL=gr,t.MONTH=function(n){return(n=O(n))instanceof Error?n:n.getMonth()+1},t.MROUND=function(n,t){return T(n=R(n),t=R(t))||(n*t==0?0:n*t<0?c:Math.round(n/t)*t)},t.MULTINOMIAL=function(){const n=P(M(arguments));if(n instanceof Error)return n;let t=0,r=1;for(let e=0;e<n.length;e++)t+=n[e],r*=Xn(n[e]);return Xn(t)/r},t.MUNIT=function(n){return arguments.length>1?s:!(n=parseInt(n))||n<=0?a:Array(n).fill(0).map((()=>Array(n).fill(0))).map(((n,t)=>(n[t]=1,n)))},t.N=function(n){return Q(n)?n:n instanceof Date?n.getTime():!0===n?1:!1===n?0:W(n)?n:0},t.NA=function(){return s},t.NEGBINOM=Tn,t.NEGBINOMDIST=pr,t.NETWORKDAYS=ft,t.NETWORKDAYSINTL=mr,t.NOMINAL=function(n,t){return S(n=R(n),t=R(t))?a:n<=0||t<1?c:(t=parseInt(t,10),(Math.pow(n+1,1/t)-1)*t)},t.NORM=Sn,t.NORMDIST=dr,t.NORMINV=vr,t.NORMSDIST=Er,t.NORMSINV=Mr,t.NOT=function(n){return"string"==typeof n?a:n instanceof Error?n:!n},t.NOW=function(){return new Date},t.NPER=function(n,t,r,e,o){if(o=void 0===o?0:o,e=void 0===e?0:e,S(n=R(n),t=R(t),r=R(r),e=R(e),o=R(o)))return a;if(0===n)return-(r+e)/t;{const i=t*(1+n*o)-e*n,u=r*n+t*(1+n*o);return Math.log(i/u)/Math.log(1+n)}},t.NPV=$r,t.NUMBERVALUE=function(n,t,r){return"number"==typeof(n=q(n)?n:"")?n:"string"!=typeof n?s:(t=void 0===t?".":t,r=void 0===r?",":r,Number(n.replace(t,".").replace(r,"")))},t.OCT2BIN=function(n,t){if(!/^[0-7]{1,10}$/.test(n))return c;const r=!(10!==n.length||"7"!==n.substring(0,1)),e=r?parseInt(n,8)-1073741824:parseInt(n,8);if(e<-512||e>511)return c;if(r)return"1"+Mt("0",9-(512+e).toString(2).length)+(512+e).toString(2);const o=e.toString(2);return void 0===t?o:isNaN(t)?a:t<0?c:(t=Math.floor(t))>=o.length?Mt("0",t-o.length)+o:c},t.OCT2DEC=function(n){if(!/^[0-7]{1,10}$/.test(n))return c;const t=parseInt(n,8);return t>=536870912?t-1073741824:t},t.OCT2HEX=function(n,t){if(!/^[0-7]{1,10}$/.test(n))return c;const r=parseInt(n,8);if(r>=536870912)return"ff"+(r+3221225472).toString(16);const e=r.toString(16);return void 0===t?e:isNaN(t)?a:t<0?c:(t=Math.floor(t))>=e.length?Mt("0",t-e.length)+e:c},t.ODD=function(n){if((n=R(n))instanceof Error)return n;let t=Math.ceil(Math.abs(n));return t=1&t?t:t+1,n>=0?t:-t},t.ODDFPRICE=function(){throw new Error("ODDFPRICE is not implemented")},t.ODDFYIELD=function(){throw new Error("ODDFYIELD is not implemented")},t.ODDLPRICE=function(){throw new Error("ODDLPRICE is not implemented")},t.ODDLYIELD=function(){throw new Error("ODDLYIELD is not implemented")},t.OR=function(){const n=M(arguments);let t=a;for(let r=0;r<n.length;r++){if(n[r]instanceof Error)return n[r];void 0!==n[r]&&null!==n[r]&&"string"!=typeof n[r]&&(t===a&&(t=!1),n[r]&&(t=!0))}return t},t.PDURATION=function(n,t,r){return S(n=R(n),t=R(t),r=R(r))?a:n<=0?c:(Math.log(r)-Math.log(t))/Math.log(1+n)},t.PEARSON=An,t.PERCENTILE=Cn,t.PERCENTILEEXC=Nr,t.PERCENTILEINC=wr,t.PERCENTRANK=Dn,t.PERCENTRANKEXC=yr,t.PERCENTRANKINC=Ir,t.PERMUT=function(n,t){return S(n=R(n),t=R(t))?a:Xn(n)/Xn(n-t)},t.PERMUTATIONA=function(n,t){return S(n=R(n),t=R(t))?a:Math.pow(n,t)},t.PHI=function(n){return(n=R(n))instanceof Error?a:Math.exp(-.5*n*n)/2.5066282746310002},t.PI=function(){return Math.PI},t.PMT=zr,t.POISSON=On,t.POISSONDIST=br,t.POWER=zn,t.PPMT=function(n,t,r,e,o,i){return o=o||0,i=i||0,S(n=R(n),r=R(r),e=R(e),o=R(o),i=R(i))?a:zr(n,r,e,o,i)-Br(n,t,r,e,o,i)},t.PRICE=function(){throw new Error("PRICE is not implemented")},t.PRICEDISC=function(n,t,r,e,o){if(S(n=O(n),t=O(t),r=R(r),e=R(e),o=(o=R(o))||0))return a;if(r<=0||e<=0)return c;if(n>=t)return a;let i,u;switch(o){case 0:i=360,u=ut(n,t,!1);break;case 1:case 3:i=365,u=et(n,t,"D");break;case 2:i=360,u=et(n,t,"D");break;case 4:i=360,u=ut(n,t,!0);break;default:return c}return e-r*e*u/i},t.PRICEMAT=function(){throw new Error("PRICEMAT is not implemented")},t.PROB=function(n,t,r,e){if(void 0===r)return 0;if(e=void 0===e?r:e,S(n=P(M(n)),t=P(M(t)),r=R(r),e=R(e)))return a;if(r===e)return n.indexOf(r)>=0?t[n.indexOf(r)]:0;const o=n.sort(((n,t)=>n-t)),i=o.length;let u=0;for(let a=0;a<i;a++)o[a]>=r&&o[a]<=e&&(u+=t[n.indexOf(o[a])]);return u},t.PRODUCT=Wn,t.PRONETIC=function(){throw new Error("PRONETIC is not implemented")},t.PROPER=function(n){return S(n)?n:isNaN(n)&&"number"==typeof n?a:(n=L(n)).replace(/\w\S*/g,(n=>n.charAt(0).toUpperCase()+n.substr(1).toLowerCase()))},t.PV=function(n,t,r,e,o){return e=e||0,o=o||0,S(n=R(n),t=R(t),r=R(r),e=R(e),o=R(o))?a:0===n?-r*t-e:((1-Math.pow(1+n,t))/n*r*(1+n*o)-e)/Math.pow(1+n,t)},t.QUARTILE=xn,t.QUARTILEEXC=Tr,t.QUARTILEINC=Sr,t.QUOTIENT=function(n,t){return T(n=R(n),t=R(t))||parseInt(n/t,10)},t.RADIANS=function(n){return(n=R(n))instanceof Error?n:n*Math.PI/180},t.RAND=function(){return Math.random()},t.RANDBETWEEN=function(n,t){return T(n=R(n),t=R(t))||n+Math.ceil((t-n+1)*Math.random())-1},t.RANK=Rn,t.RANKAVG=Ar,t.RANKEQ=Cr,t.RATE=function(n,t,r,e,o,i){if(i=void 0===i?.01:i,e=void 0===e?0:e,o=void 0===o?0:o,S(n=R(n),t=R(t),r=R(r),e=R(e),o=R(o),i=R(i)))return a;const u=1e-10;let f=i;o=o?1:0;for(let i=0;i<20;i++){if(f<=-1)return c;let i,a,l;if(Math.abs(f)<u?i=r*(1+n*f)+t*(1+f*o)*n+e:(a=Math.pow(1+f,n),i=r*a+t*(1/f+o)*(a-1)+e),Math.abs(i)<u)return f;if(Math.abs(f)<u)l=r*n+t*o*n;else{a=Math.pow(1+f,n);const e=n*Math.pow(1+f,n-1);l=r*e+t*(1/f+o)*e+t*(-1/(f*f))*(a-1)}f-=i/l}return f},t.RECEIVED=function(){throw new Error("RECEIVED is not implemented")},t.REPLACE=function(n,t,r,e){return S(t=R(t),r=R(r))||"string"!=typeof n||"string"!=typeof e?a:n.substr(0,t-1)+e+n.substr(t-1+r)},t.REPT=Mt,t.RIGHT=function(n,t){return T(n,t)||(n=L(n),(t=R(t=void 0===t?1:t))instanceof Error?t:n.substring(n.length-t))},t.ROMAN=function(n){if((n=R(n))instanceof Error)return n;const t=String(n).split(""),r=["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM","","X","XX","XXX","XL","L","LX","LXX","LXXX","XC","","I","II","III","IV","V","VI","VII","VIII","IX"];let e="",o=3;for(;o--;)e=(r[+t.pop()+10*o]||"")+e;return new Array(+t.join("")+1).join("M")+e},t.ROUND=Kn,t.ROUNDDOWN=function(n,t){return T(n=R(n),t=R(t))||(n>0?1:-1)*Math.floor(Math.abs(n)*Math.pow(10,t))/Math.pow(10,t)},t.ROUNDUP=function(n,t){return T(n=R(n),t=R(t))||(n>0?1:-1)*Math.ceil(Math.abs(n)*Math.pow(10,t))/Math.pow(10,t)},t.ROW=function(n,t){return 2!==arguments.length?s:t<0?c:n instanceof Array&&"number"==typeof t?0!==n.length?e.row(n,t):void 0:a},t.ROWS=function(n){return 1!==arguments.length?s:n instanceof Array?0===n.length?0:e.rows(n):a},t.RRI=function(n,t,r){return S(n=R(n),t=R(t),r=R(r))?a:0===n||0===t?c:Math.pow(r/t,1/n)-1},t.RSQ=function(n,t){return S(n=P(M(n)),t=P(M(t)))?a:Math.pow(An(n,t),2)},t.SEARCH=function(n,t,r){let e;return"string"!=typeof n||"string"!=typeof t?a:(r=void 0===r?0:r,e=t.toLowerCase().indexOf(n.toLowerCase(),r-1)+1,0===e?a:e)},t.SEC=function(n){return(n=R(n))instanceof Error?n:1/Math.cos(n)},t.SECH=function(n){return(n=R(n))instanceof Error?n:2/(Math.exp(n)+Math.exp(-n))},t.SECOND=function(n){return(n=O(n))instanceof Error?n:n.getSeconds()},t.SERIESSUM=function(n,t,r,e){if(S(n=R(n),t=R(t),r=R(r),e=P(e)))return a;let o=e[0]*Math.pow(n,t);for(let i=1;i<e.length;i++)o+=e[i]*Math.pow(n,t+i*r);return o},t.SHEET=function(){throw new Error("SHEET is not implemented")},t.SHEETS=function(){throw new Error("SHEETS is not implemented")},t.SIGN=function(n){return(n=R(n))instanceof Error?n:n<0?-1:0===n?0:1},t.SIN=function(n){return(n=R(n))instanceof Error?n:Math.sin(n)},t.SINH=function(n){return(n=R(n))instanceof Error?n:(Math.exp(n)-Math.exp(-n))/2},t.SKEW=Pn,t.SKEWP=Dr,t.SLN=function(n,t,r){return S(n=R(n),t=R(t),r=R(r))?a:0===r?c:(n-t)/r},t.SLOPE=function(n,t){if(S(n=P(M(n)),t=P(M(t))))return a;const r=e.mean(t),o=e.mean(n),i=t.length;let u=0,f=0;for(let e=0;e<i;e++)u+=(t[e]-r)*(n[e]-o),f+=Math.pow(t[e]-r,2);return u/f},t.SMALL=Ln,t.SORT=function(n,t=1,r=1,e=!1){if(!n||!Array.isArray(n))return s;if(0===n.length)return 0;if(!(t=R(t))||t<1)return a;if(1!==(r=R(r))&&-1!==r)return a;if("boolean"!=typeof(e=D(e)))return l;const o=n=>n.sort(((n,e)=>(n=L(n[t-1]),e=L(e[t-1]),1===r?n<e?-1*r:r:n>e?r:-1*r))),i=E(n),u=e?I(i):i;return t>=1&&t<=u[0].length?e?I(o(u)):o(u):a},t.SQRT=function(n){return(n=R(n))instanceof Error?n:n<0?c:Math.sqrt(n)},t.SQRTPI=function(n){return(n=R(n))instanceof Error?n:Math.sqrt(n*Math.PI)},t.STANDARDIZE=function(n,t,r){return S(n=R(n),t=R(t),r=R(r))?a:(n-t)/r},t.STDEV=Fn,t.STDEVA=function(){const n=_n.apply(this,arguments);return Math.sqrt(n)},t.STDEVP=Or,t.STDEVPA=function(){const n=Vn.apply(this,arguments);let t=Math.sqrt(n);return isNaN(t)&&(t=c),t},t.STDEVS=xr,t.STEYX=function(n,t){if(S(n=P(M(n)),t=P(M(t))))return a;const r=e.mean(t),o=e.mean(n),i=t.length;let u=0,f=0,l=0;for(let e=0;e<i;e++)u+=Math.pow(n[e]-o,2),f+=(t[e]-r)*(n[e]-o),l+=Math.pow(t[e]-r,2);return Math.sqrt((u-f*f/l)/(i-2))},t.SUBSTITUTE=function(n,t,r,e){if(arguments.length<3)return s;if(n&&t){if(void 0===e)return n.split(t).join(r);{if(e=Math.floor(Number(e)),Number.isNaN(e)||e<=0)return a;let o=0,i=0;for(;o>-1&&n.indexOf(t,o)>-1;)if(o=n.indexOf(t,o+1),i++,o>-1&&i===e)return n.substring(0,o)+r+n.substring(o+t.length);return n}}return n},t.SUBTOTAL=function(n,t){if((n=R(n))instanceof Error)return n;switch(n){case 1:case 101:return tn(t);case 2:case 102:return fn(t);case 3:case 103:return ln(t);case 4:case 104:return wn(t);case 5:case 105:return In(t);case 6:case 106:return Wn(t);case 7:case 107:return Fn.S(t);case 8:case 108:return Fn.P(t);case 9:case 109:return Qn(t);case 10:case 110:return Un.S(t);case 11:case 111:return Un.P(t)}},t.SUM=Qn,t.SUMIF=function(n,t,r){if(n=M(n),r=r?M(r):n,n instanceof Error)return n;if(null==t||t instanceof Error)return 0;let e=0;const o="*"===t,i=o?null:X(t+"");for(let t=0;t<n.length;t++){const u=n[t],a=r[t];if(o)e+=u;else{const n=[H(u,G)].concat(i);e+=B(n)?a:0}}return e},t.SUMIFS=function(){const n=m(arguments),t=P(M(n.shift()));if(t instanceof Error)return t;const r=n,e=r.length/2;for(let n=0;n<e;n++)r[2*n]=M(r[2*n]);let o=0;for(let n=0;n<t.length;n++){let i=!1;for(let t=0;t<e;t++){const e=r[2*t][n],o=r[2*t+1];let u=!1;if(void 0===o||"*"===o)u=!0;else{const n=X(o+""),t=[H(e,G)].concat(n);u=B(t)}if(!u){i=!1;break}i=!0}i&&(o+=t[n])}return o},t.SUMPRODUCT=function(){if(!arguments||0===arguments.length)return a;const n=arguments.length+1;let t,r,e,o,i=0;for(let u=0;u<arguments[0].length;u++)if(arguments[0][u]instanceof Array)for(let e=0;e<arguments[0][u].length;e++){for(t=1,r=1;r<n;r++){const n=arguments[r-1][u][e];if(n instanceof Error)return n;if(o=R(n),o instanceof Error)return o;t*=o}i+=t}else{for(t=1,r=1;r<n;r++){const n=arguments[r-1][u];if(n instanceof Error)return n;if(e=R(n),e instanceof Error)return e;t*=e}i+=t}return i},t.SUMSQ=function(){const n=P(M(arguments));if(n instanceof Error)return n;let t=0;const r=n.length;for(let e=0;e<r;e++)t+=Q(n[e])?n[e]*n[e]:0;return t},t.SUMX2MY2=function(n,t){if(S(n=P(M(n)),t=P(M(t))))return a;let r=0;for(let e=0;e<n.length;e++)r+=n[e]*n[e]-t[e]*t[e];return r},t.SUMX2PY2=function(n,t){if(S(n=P(M(n)),t=P(M(t))))return a;let r=0;n=P(M(n)),t=P(M(t));for(let e=0;e<n.length;e++)r+=n[e]*n[e]+t[e]*t[e];return r},t.SUMXMY2=function(n,t){if(S(n=P(M(n)),t=P(M(t))))return a;let r=0;n=M(n),t=M(t);for(let e=0;e<n.length;e++)r+=Math.pow(n[e]-t[e],2);return r},t.SWITCH=function(){let n;if(arguments.length>0){const t=arguments[0],r=arguments.length-1,e=Math.floor(r/2);let o=!1;const i=r%2!=0,u=r%2==0?null:arguments[arguments.length-1];if(e)for(let r=0;r<e;r++)if(t===arguments[2*r+1]){n=arguments[2*r+2],o=!0;break}o||(n=i?u:s)}else n=a;return n},t.SYD=function(n,t,r,e){return S(n=R(n),t=R(t),r=R(r),e=R(e))?a:0===r||e<1||e>r?c:(n-t)*(r-(e=parseInt(e,10))+1)*2/(r*(r+1))},t.T=function(n){return n instanceof Error||"string"==typeof n?n:""},t.TAN=function(n){return(n=R(n))instanceof Error?n:Math.tan(n)},t.TANH=function(n){if((n=R(n))instanceof Error)return n;const t=Math.exp(2*n);return(t-1)/(t+1)},t.TBILLEQ=function(n,t,r){return S(n=O(n),t=O(t),r=R(r))?a:r<=0||n>t||t-n>31536e6?c:365*r/(360-r*ut(n,t,!1))},t.TBILLPRICE=function(n,t,r){return S(n=O(n),t=O(t),r=R(r))?a:r<=0||n>t||t-n>31536e6?c:100*(1-r*ut(n,t,!1)/360)},t.TBILLYIELD=function(n,t,r){return S(n=O(n),t=O(t),r=R(r))?a:r<=0||n>t||t-n>31536e6?c:360*(100-r)/(r*ut(n,t,!1))},t.TDIST=Rr,t.TDISTRT=Pr,t.TEXT=function(n,t){if(void 0===n||n instanceof Error||t instanceof Error)return s;if(null==t)return"";if("number"==typeof t)return String(t);if("string"!=typeof t)return a;const r=t.startsWith("$")?"$":"",e=t.endsWith("%");return e&&(n*=100),n=(n=Et(n,(t=t.replace(/%/g,"").replace(/\$/g,"")).split(".")[1].match(/0/g).length,!t.includes(","))).startsWith("-")?"-"+r+(n=n.replace("-","")):r+n,e&&(n+="%"),n},t.TEXTJOIN=function(n,t,...r){if("boolean"!=typeof t&&(t=D(t)),arguments.length<3)return s;n=null!=n?n:"";let e=M(r),o=t?e.filter((n=>n)):e;if(Array.isArray(n)){n=M(n);let t=o.map((n=>[n])),r=0;for(let e=0;e<t.length-1;e++)t[e].push(n[r]),r++,r===n.length&&(r=0);return o=M(t),o.join("")}return o.join(n)},t.TIME=function(n,t,r){return S(n=R(n),t=R(t),r=R(r))?a:n<0||t<0||r<0?c:(3600*n+60*t+r)/86400},t.TIMEVALUE=function(n){return(n=O(n))instanceof Error?n:(3600*n.getHours()+60*n.getMinutes()+n.getSeconds())/86400},t.TINV=Lr,t.TODAY=function(){return ot(new Date)},t.TRANSPOSE=function(n){return n?I(E(n)):s},t.TREND=function(n,t,r){if(S(n=P(M(n)),t=P(M(t)),r=P(M(r))))return a;const e=Mn(n,t),o=e[0],i=e[1],u=[];return r.forEach((n=>{u.push(o*n+i)})),u},t.TRIM=function(n){return(n=L(n))instanceof Error?n:n.replace(/\s+/g," ").trim()},t.TRIMMEAN=function(n,t){if(S(n=P(M(n)),t=R(t)))return a;const r=Bn(n.length*t,2)/2;return e.mean((i=(i=r)||1,(o=y(n.sort(((n,t)=>n-t)),r))&&"function"==typeof o.slice?o.slice(0,o.length-i):o));var o,i},t.TRUE=function(){return!0},t.TRUNC=function(n,t){return T(n=R(n),t=R(t))||(n>0?1:-1)*Math.floor(Math.abs(n)*Math.pow(10,t))/Math.pow(10,t)},t.TTEST=Fr,t.TYPE=function(n){return Q(n)?1:Z(n)?2:K(n)?4:W(n)?16:Array.isArray(n)?64:void 0},t.UNICHAR=Nt,t.UNICODE=wt,t.UNIQUE=J,t.UPPER=function(n){return(n=L(n))instanceof Error?n:n.toUpperCase()},t.VALUE=function(n){const t=T(n);if(t)return t;if("number"==typeof n)return n;if(q(n)||(n=""),"string"!=typeof n)return a;const r=/(%)$/.test(n)||/^(%)/.test(n);if(""===(n=(n=(n=n.replace(/^[^0-9-]{0,3}/,"")).replace(/[^0-9]{0,3}$/,"")).replace(/[ ,]/g,"")))return 0;let e=Number(n);return isNaN(e)?a:(e=e||0,r&&(e*=.01),e)},t.VAR=Un,t.VARA=_n,t.VARP=qr,t.VARPA=Vn,t.VARS=Ur,t.VDB=function(){throw new Error("VDB is not implemented")},t.VLOOKUP=nn,t.WEEKDAY=function(n,t){if((n=O(n))instanceof Error)return n;void 0===t&&(t=1);const r=n.getDay();return tt[t][r]},t.WEEKNUM=function(n,t){if((n=O(n))instanceof Error)return n;if(void 0===t&&(t=1),21===t)return at(n);const r=nt[t];let e=new Date(n.getFullYear(),0,1);const o=e.getDay()<r?1:0;return e-=24*Math.abs(e.getDay()-r)*60*60*1e3,Math.floor((n-e)/864e5/7+1)+o},t.WEIBULL=kn,t.WEIBULLDIST=_r,t.WORKDAY=lt,t.WORKDAYINTL=Vr,t.XIRR=function(n,t,r){if(S(n=P(M(n)),t=x(M(t)),r=R(r)))return a;const e=(n,t,r)=>{const e=r+1;let o=n[0];for(let r=1;r<n.length;r++)o+=n[r]/Math.pow(e,it(t[r],t[0])/365);return o},o=(n,t,r)=>{const e=r+1;let o=0;for(let r=1;r<n.length;r++){const i=it(t[r],t[0])/365;o-=i*n[r]/Math.pow(e,i+1)}return o};let i=!1,u=!1;for(let t=0;t<n.length;t++)n[t]>0&&(i=!0),n[t]<0&&(u=!0);if(!i||!u)return c;let f,l,s,h=r=r||.1,g=!0;do{s=e(n,t,h),f=h-s/o(n,t,h),l=Math.abs(f-h),h=f,g=l>1e-10&&Math.abs(s)>1e-10}while(g);return h},t.XNPV=function(n,t,r){if(S(n=R(n),t=P(M(t)),r=x(M(r))))return a;let e=0;for(let o=0;o<t.length;o++)e+=t[o]/Math.pow(1+n,it(r[o],r[0])/365);return e},t.XOR=function(){const n=M(arguments);let t=a;for(let r=0;r<n.length;r++){if(n[r]instanceof Error)return n[r];void 0!==n[r]&&null!==n[r]&&"string"!=typeof n[r]&&(t===a&&(t=0),n[r]&&t++)}return t===a?t:!!(1&Math.floor(Math.abs(t)))},t.YEAR=function(n){return(n=O(n))instanceof Error?n:n.getFullYear()},t.YEARFRAC=ht,t.YIELD=function(){throw new Error("YIELD is not implemented")},t.YIELDDISC=function(){throw new Error("YIELDDISC is not implemented")},t.YIELDMAT=function(){throw new Error("YIELDMAT is not implemented")},t.Z=jn,t.ZTEST=kr,t.utils=Wr}},t={};function r(e){var o=t[e];if(void 0!==o)return o.exports;var i=t[e]={exports:{}};return n[e].call(i.exports,i,i.exports,r),i.exports}r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(n){if("object"==typeof window)return window}}();var e=r(960);Formula=e}();

    return Formula;
})));

;(function (global, factory) {
    typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
    typeof define === 'function' && define.amd ? define(factory) :
    global.jspreadsheet = factory();
}(this, (function () {

var jspreadsheet;(function(){"use strict";var __webpack_modules__={946:function(e,t){const s=function(e){const t=this,s=[];for(let n=0;n<e.length;n++){const o=e[n].x,r=e[n].y,l=t.options.columns[o].name?t.options.columns[o].name:o;s[r]||(s[r]={row:r,data:{}}),s[r].data[l]=e[n].value}return s.filter((function(e){return null!=e}))},n=function(e,t){const s=this,n=o.call(s.parent,"onbeforesave",s.parent,s,t);if(n)t=n;else if(!1===n)return!1;jSuites.ajax({url:e,method:"POST",dataType:"json",data:{data:JSON.stringify(t)},success:function(e){o.call(s,"onsave",s.parent,s,t)}})},o=function(e){const t=this;let o=null,r=t.parent?t.parent:t;if(!r.ignoreEvents&&("function"==typeof r.config.onevent&&(o=r.config.onevent.apply(this,arguments)),"function"==typeof r.config[e]&&(o=r.config[e].apply(this,Array.prototype.slice.call(arguments,1))),"object"==typeof r.plugins)){const e=Object.keys(r.plugins);for(let t=0;t<e.length;t++){const s=e[t],n=r.plugins[s];"function"==typeof n.onevent&&(o=n.onevent.apply(this,arguments))}}if("onafterchanges"==e){const e=arguments;if("object"==typeof r.plugins&&Object.entries(r.plugins).forEach((function([,s]){"function"==typeof s.persistence&&s.persistence(t,"setValue",{data:e[2]})})),t.options.persistence){const e=1==t.options.persistence?t.options.url:t.options.persistence,o=s.call(t,arguments[2]);n.call(t,e,o)}}return o};t.A=o},206:function(e,t,s){s.d(t,{F8:function(){return l},N$:function(){return r},dr:function(){return i}});var n=s(45),o=s(268);const r=function(e){const t=this;if(t.options.filters){e=parseInt(e),t.resetSelection();let s=[];if("checkbox"==t.options.columns[e].type)s.push({id:"true",name:"True"}),s.push({id:"false",name:"False"});else{const n=[];let o=!1;for(let s=0;s<t.options.data.length;s++){const r=t.options.data[s][e],l=t.records[s][e].element.innerHTML;r&&l?n[r]=l:o=!0}const r=Object.keys(n);s=[];for(let e=0;e<r.length;e++)s.push({id:r[e],name:n[r[e]]});o&&s.push({value:"",id:"",name:"(Blanks)"})}const n=document.createElement("div");t.filter.children[e+1].innerHTML="",t.filter.children[e+1].appendChild(n),t.filter.children[e+1].style.paddingLeft="0px",t.filter.children[e+1].style.paddingRight="0px",t.filter.children[e+1].style.overflow="initial";const r={data:s,multiple:!0,autocomplete:!0,opened:!0,value:void 0!==t.filters[e]?t.filters[e]:null,width:"100%",position:1==t.options.tableOverflow||1==t.parent.config.fullscreen,onclose:function(s){i.call(t),t.filters[e]=s.dropdown.getValue(!0),t.filter.children[e+1].innerHTML=s.dropdown.getText(),t.filter.children[e+1].style.paddingLeft="",t.filter.children[e+1].style.paddingRight="",t.filter.children[e+1].style.overflow="",l.call(t,e),o.G9.call(t)}};jSuites.dropdown(n,r)}else console.log("Jspreadsheet: filters not enabled.")},l=function(e){const t=this;if(!e)for(let s=0;s<t.filter.children.length;s++)t.filters[s]&&(e=s);const s=function(e,s,n){for(let o=0;o<e.length;o++){const r=""+t.options.data[n][s],l=""+t.records[n][s].element.innerHTML;if(e[o]==r||e[o]==l)return!0}return!1},o=t.filters[e];t.results=[];for(let n=0;n<t.options.data.length;n++)s(o,e,n)&&t.results.push(n);t.results.length||(t.results=null),n.hG.call(t)},i=function(){const e=this;if(e.options.filters)for(let t=0;t<e.filter.children.length;t++)e.filter.children[t].innerHTML="&nbsp;",e.filters[t]=null;e.results=null,n.hG.call(e)}},623:function(e,t,s){s.d(t,{e:function(){return o}});var n=s(45);const o=function(e){const t=this;if(e&&(t.options.footers=e),t.options.footers){t.tfoot||(t.tfoot=document.createElement("tfoot"),t.table.appendChild(t.tfoot));for(let e=0;e<t.options.footers.length;e++){let s;if(t.tfoot.children[e])s=t.tfoot.children[e];else{s=document.createElement("tr");const e=document.createElement("td");s.appendChild(e),t.tfoot.appendChild(s)}for(let o=0;o<t.headers.length;o++){let r;if(t.options.footers[e][o]||(t.options.footers[e][o]=""),t.tfoot.children[e].children[o+1])r=t.tfoot.children[e].children[o+1];else{r=document.createElement("td"),s.appendChild(r);const e=t.options.columns[o].align||t.options.defaultColAlign||"center";r.style.textAlign=e}r.textContent=n.$x.call(t,+t.records.length+o,e,t.options.footers[e][o]),r.style.display=t.cols[o].colElement.style.display}}}}},619:function(e,t,s){s.d(t,{w:function(){return n}});const n=function(){const e=this;let t=0;if(e.options.freezeColumns>0)for(let s=0;s<e.options.freezeColumns;s++){let n;n=e.options.columns&&e.options.columns[s]&&void 0!==e.options.columns[s].width?parseInt(e.options.columns[s].width):void 0!==e.options.defaultColWidth?parseInt(e.options.defaultColWidth):100,t+=n}return t}},595:function(e,t,s){s.r(t),s.d(t,{createFromTable:function(){return d},getCaretIndex:function(){return o},getCellNameFromCoords:function(){return i},getColumnName:function(){return l},getCoordsFromCellName:function(){return a},getCoordsFromRange:function(){return c},invert:function(){return r},parseCSV:function(){return u}});var n=s(887);const o=function(e){let t;t=this.config.root?this.config.root:window;let s=0;const n=t.getSelection();if(n&&0!==n.rangeCount){const t=n.getRangeAt(0),o=t.cloneRange();o.selectNodeContents(e),o.setEnd(t.endContainer,t.endOffset),s=o.toString().length}return s},r=function(e){const t=[],s=Object.keys(e);for(let n=0;n<s.length;n++)t[e[s[n]]]=s[n];return t},l=function(e){let t,s=e+1,n="";for(;s>0;)t=(s-1)%26,n=String.fromCharCode(65+t).toString()+n,s=parseInt((s-t)/26);return n},i=function(e,t){return l(parseInt(e))+(parseInt(t)+1)},a=function(e){const t=/^[a-zA-Z]+/.exec(e);if(t){let s=0;for(let e=0;e<t[0].length;e++)s+=parseInt(t[0].charCodeAt(e)-64)*Math.pow(26,t[0].length-1-e);s--,s<0&&(s=0);let n=parseInt(/[0-9]+$/.exec(e))||null;return n>0&&n--,[s,n]}},c=function(e){const[t,s]=e.split(":");return[...a(t),...a(s)]},u=function(e,t){9==(e=e.replace(/\r?\n$|\r$|\n$/g,"")).charCodeAt(e.length-1)&&(e+="\0"),t=t||",";const s=[];let n=!1;for(let o=0,r=0,l=0;l<e.length;l++){const i=e[l],a=e[l+1];s[o]=s[o]||[],s[o][r]=s[o][r]||"",'"'==i&&n&&'"'==a?(s[o][r]+=i,++l):'"'!=i?i!=t||n?"\r"!=i||"\n"!=a||n?"\n"==i&&!n||"\r"==i&&!n?(++o,r=0):s[o][r]+=i:(++o,r=0,++l):++r:n=!n}return s},d=function(e,t){if("TABLE"==e.tagName){t||(t={}),t.columns=[],t.data=[];const s=e.querySelectorAll("colgroup > col");if(s.length)for(let e=0;e<s.length;e++){let n=s[e].style.width;n||(n=s[e].getAttribute("width")),n&&(t.columns[e]||(t.columns[e]={}),t.columns[e].width=n)}const o=function(e,s){let n=e.getBoundingClientRect();const o=n.width>50?n.width:50;t.columns[s]||(t.columns[s]={}),e.getAttribute("data-celltype")?t.columns[s].type=e.getAttribute("data-celltype"):t.columns[s].type="text",t.columns[s].width=o+"px",t.columns[s].title=e.innerHTML,e.style.textAlign&&(t.columns[s].align=e.style.textAlign),(n=e.getAttribute("name"))&&(t.columns[s].name=n),(n=e.getAttribute("id"))&&(t.columns[s].id=n),(n=e.getAttribute("data-mask"))&&(t.columns[s].mask=n)},r=[];let l=e.querySelectorAll(":scope > thead > tr");if(l.length){for(let e=0;e<l.length-1;e++){const t=[];for(let s=0;s<l[e].children.length;s++){const n={title:l[e].children[s].textContent,colspan:l[e].children[s].getAttribute("colspan")||1};t.push(n)}r.push(t)}l=l[l.length-1].children;for(let e=0;e<l.length;e++)o(l[e],e)}let i=0;const a={},c={},u={},d={};let p=e.querySelectorAll(":scope > tr, :scope > tbody > tr");for(let e=0;e<p.length;e++)if(t.data[i]=[],1!=t.parseTableFirstRowAsHeader||l.length||0!=e){for(let s=0;s<p[e].children.length;s++){let o=p[e].children[s].getAttribute("data-formula");o?"="!=o.substr(0,1)&&(o="="+o):o=p[e].children[s].innerHTML,t.data[i].push(o);const r=(0,n.t3)([s,e]),l=p[e].children[s].getAttribute("class");l&&(d[r]=l);const c=parseInt(p[e].children[s].getAttribute("colspan"))||0,h=parseInt(p[e].children[s].getAttribute("rowspan"))||0;(c||h)&&(a[r]=[c||1,h||1]),p[e].children[s].style&&"none"==p[e].children[s].style.display&&(p[e].children[s].style.display="");const m=p[e].children[s].getAttribute("style");m&&(u[r]=m),p[e].children[s].classList.contains("styleBold")&&(u[r]?u[r]+="; font-weight:bold;":u[r]="font-weight:bold;")}p[e].style&&p[e].style.height&&(c[e]={height:p[e].style.height}),i++}else for(let t=0;t<p[e].children.length;t++)o(p[e].children[t],t);if(Object.keys(r).length>0&&(t.nestedHeaders=r),Object.keys(u).length>0&&(t.style=u),Object.keys(a).length>0&&(t.mergeCells=a),Object.keys(c).length>0&&(t.rows=c),Object.keys(d).length>0&&(t.classes=d),p=e.querySelectorAll("tfoot tr"),p.length){const e=[];for(let t=0;t<p.length;t++){let s=[];for(let e=0;e<p[t].children.length;e++)s.push(p[t].children[e].textContent);e.push(s)}Object.keys(e).length>0&&(t.footers=e)}if(1==t.parseTableAutoCellType){const e=[];for(let s=0;s<t.columns.length;s++){let n=!0,o=!0;e[s]=[];for(let r=0;r<t.data.length;r++){const l=t.data[r][s];e[s][l]||(e[s][l]=0),e[s][l]++,l.length>25&&(n=!1),10==l.length&&"-"==l.substr(4,1)&&"-"==l.substr(7,1)||(o=!1)}const r=Object.keys(e[s]).length;o?t.columns[s].type="calendar":1==n&&r>1&&r<=parseInt(.1*t.data.length)&&(t.columns[s].type="dropdown",t.columns[s].source=Object.keys(e[s]))}}return t}console.log("Element is not a table")}},126:function(e,t,s){s.d(t,{Dh:function(){return c},ZS:function(){return h},tN:function(){return p}});var n=s(946),o=s(887),r=s(45),l=s(441),i=s(451),a=s(268);const c=function(e){const t=this;if(1!=t.ignoreHistory){const s=++t.historyIndex;t.history=t.history=t.history.slice(0,s+1),t.history[s]=e}},u=function(e,t){const s=this,n=t.insertBefore?+t.rowNumber:t.rowNumber+1;if(1==s.options.search&&s.results&&s.results.length!=s.rows.length&&s.resetSearch(),1==e){const e=t.numOfRows;for(let t=n;t<e+n;t++)s.rows[t].element.parentNode.removeChild(s.rows[t].element);s.records.splice(n,e),s.options.data.splice(n,e),s.rows.splice(n,e),a.at.call(s,1,n,e+n-1)}else{s.records=(0,o.Hh)(s.records,n,t.rowRecords),s.options.data=(0,o.Hh)(s.options.data,n,t.rowData),s.rows=(0,o.Hh)(s.rows,n,t.rowNode);let e=0;for(let o=n;o<t.numOfRows+n;o++)s.tbody.insertBefore(t.rowNode[e].element,s.tbody.children[o]),e++}for(let e=n;e<s.rows.length;e++)s.rows[e].y=e;for(let e=n;e<s.records.length;e++)for(let t=0;t<s.records[e].length;t++)s.records[e][t].y=e;s.options.pagination>0&&s.page(s.pageNumber),r.o8.call(s)},d=function(e,t){const s=this,n=t.insertBefore?t.columnNumber:t.columnNumber+1;if(1==e){const e=t.numOfColumns;s.options.columns.splice(n,e);for(let t=n;t<e+n;t++)s.headers[t].parentNode.removeChild(s.headers[t]),s.cols[t].colElement.parentNode.removeChild(s.cols[t].colElement);s.headers.splice(n,e),s.cols.splice(n,e);for(let o=0;o<t.data.length;o++){for(let t=n;t<e+n;t++)s.records[o][t].element.parentNode.removeChild(s.records[o][t].element);s.records[o].splice(n,e),s.options.data[o].splice(n,e)}if(s.options.footers)for(let t=0;t<s.options.footers.length;t++)s.options.footers[t].splice(n,e)}else{s.options.columns=(0,o.Hh)(s.options.columns,n,t.columns),s.headers=(0,o.Hh)(s.headers,n,t.headers),s.cols=(0,o.Hh)(s.cols,n,t.cols);let e=0;for(let o=n;o<t.numOfColumns+n;o++)s.headerContainer.insertBefore(t.headers[e],s.headerContainer.children[o+1]),s.colgroupContainer.insertBefore(t.cols[e].colElement,s.colgroupContainer.children[o+1]),e++;for(let e=0;e<t.data.length;e++){s.options.data[e]=(0,o.Hh)(s.options.data[e],n,t.data[e]),s.records[e]=(0,o.Hh)(s.records[e],n,t.records[e]);let r=0;for(let o=n;o<t.numOfColumns+n;o++)s.rows[e].element.insertBefore(t.records[e][r].element,s.rows[e].element.children[o+1]),r++}if(s.options.footers)for(let e=0;e<s.options.footers.length;e++)s.options.footers[e]=(0,o.Hh)(s.options.footers[e],n,t.footers[e])}for(let e=n;e<s.cols.length;e++)s.cols[e].x=e;for(let e=0;e<s.records.length;e++)for(let t=n;t<s.records[e].length;t++)s.records[e][t].x=t;if(s.options.nestedHeaders&&s.options.nestedHeaders.length>0&&s.options.nestedHeaders[0]&&s.options.nestedHeaders[0][0])for(let n=0;n<s.options.nestedHeaders.length;n++){let o;o=1==e?parseInt(s.options.nestedHeaders[n][s.options.nestedHeaders[n].length-1].colspan)-t.numOfColumns:parseInt(s.options.nestedHeaders[n][s.options.nestedHeaders[n].length-1].colspan)+t.numOfColumns,s.options.nestedHeaders[n][s.options.nestedHeaders[n].length-1].colspan=o,s.thead.children[n].children[s.thead.children[n].children.length-1].setAttribute("colspan",o)}r.o8.call(s)},p=function(){const e=this,t=!!e.parent.ignoreEvents,s=!!e.ignoreHistory;e.parent.ignoreEvents=!0,e.ignoreHistory=!0;const o=[];let r;if(e.historyIndex>=0)if(r=e.history[e.historyIndex--],"insertRow"==r.action)u.call(e,1,r);else if("deleteRow"==r.action)u.call(e,0,r);else if("insertColumn"==r.action)d.call(e,1,r);else if("deleteColumn"==r.action)d.call(e,0,r);else if("moveRow"==r.action)e.moveRow(r.newValue,r.oldValue);else if("moveColumn"==r.action)e.moveColumn(r.newValue,r.oldValue);else if("setMerge"==r.action)e.removeMerge(r.column,r.data);else if("setStyle"==r.action)e.setStyle(r.oldValue,null,null,1);else if("setWidth"==r.action)e.setWidth(r.column,r.oldValue);else if("setHeight"==r.action)e.setHeight(r.row,r.oldValue);else if("setHeader"==r.action)e.setHeader(r.column,r.oldValue);else if("setComments"==r.action)e.setComments(r.oldValue);else if("orderBy"==r.action){let t=[];for(let e=0;e<r.rows.length;e++)t[r.rows[e]]=e;i.Th.call(e,r.column,r.order?0:1),i.iY.call(e,t)}else if("setValue"==r.action){for(let t=0;t<r.records.length;t++)o.push({x:r.records[t].x,y:r.records[t].y,value:r.records[t].oldValue}),r.oldStyle&&e.resetStyle(r.oldStyle);e.setValue(o),r.selection&&e.updateSelectionFromCoords(r.selection[0],r.selection[1],r.selection[2],r.selection[3])}e.parent.ignoreEvents=t,e.ignoreHistory=s,n.A.call(e,"onundo",e,r)},h=function(){const e=this,t=!!e.parent.ignoreEvents,s=!!e.ignoreHistory;let o;if(e.parent.ignoreEvents=!0,e.ignoreHistory=!0,e.historyIndex<e.history.length-1)if(o=e.history[++e.historyIndex],"insertRow"==o.action)u.call(e,0,o);else if("deleteRow"==o.action)u.call(e,1,o);else if("insertColumn"==o.action)d.call(e,0,o);else if("deleteColumn"==o.action)d.call(e,1,o);else if("moveRow"==o.action)e.moveRow(o.oldValue,o.newValue);else if("moveColumn"==o.action)e.moveColumn(o.oldValue,o.newValue);else if("setMerge"==o.action)l.FU.call(e,o.column,o.colspan,o.rowspan,1);else if("setStyle"==o.action)e.setStyle(o.newValue,null,null,1);else if("setWidth"==o.action)e.setWidth(o.column,o.newValue);else if("setHeight"==o.action)e.setHeight(o.row,o.newValue);else if("setHeader"==o.action)e.setHeader(o.column,o.newValue);else if("setComments"==o.action)e.setComments(o.newValue);else if("orderBy"==o.action)i.Th.call(e,o.column,o.order),i.iY.call(e,o.rows);else if("setValue"==o.action){e.setValue(o.records);for(let t=0;t<o.records.length;t++)o.oldStyle&&e.resetStyle(o.newStyle);o.selection&&e.updateSelectionFromCoords(o.selection[0],o.selection[1],o.selection[2],o.selection[3])}e.parent.ignoreEvents=t,e.ignoreHistory=s,n.A.call(e,"onredo",e,o)}},45:function(__unused_webpack___webpack_module__,__webpack_exports__,__webpack_require__){__webpack_require__.d(__webpack_exports__,{$O:function(){return getWorksheetActive},$x:function(){return parseValue},C6:function(){return showIndex},Em:function(){return executeFormula},P9:function(){return createCell},Rs:function(){return updateScroll},TI:function(){return hideIndex},Xr:function(){return getCellFromCoords},Y5:function(){return fullscreen},am:function(){return updateTable},dw:function(){return isFormula},eN:function(){return getWorksheetInstance},hG:function(){return updateResult},ju:function(){return createNestedHeader},k9:function(){return updateCell},o8:function(){return updateTableReferences},p9:function(){return getLabel},rS:function(){return getMask},tT:function(){return getCell},xF:function(){return updateFormulaChain},yB:function(){return updateFormula}});var _dispatch_js__WEBPACK_IMPORTED_MODULE_3__=__webpack_require__(946),_selection_js__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__(268),_helpers_js__WEBPACK_IMPORTED_MODULE_4__=__webpack_require__(595),_meta_js__WEBPACK_IMPORTED_MODULE_5__=__webpack_require__(617),_freeze_js__WEBPACK_IMPORTED_MODULE_6__=__webpack_require__(619),_pagination_js__WEBPACK_IMPORTED_MODULE_7__=__webpack_require__(292),_footer_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__(623),_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__=__webpack_require__(887);const updateTable=function(){const e=this;if(e.options.minSpareRows>0){let t=0;for(let s=e.rows.length-1;s>=0;s--){let n=!1;for(let t=0;t<e.headers.length;t++)e.options.data[s][t]&&(n=!0);if(n)break;t++}e.options.minSpareRows-t>0&&e.insertRow(e.options.minSpareRows-t)}if(e.options.minSpareCols>0){let t=0;for(let s=e.headers.length-1;s>=0;s--){let n=!1;for(let t=0;t<e.rows.length;t++)e.options.data[t][s]&&(n=!0);if(n)break;t++}e.options.minSpareCols-t>0&&e.insertColumn(e.options.minSpareCols-t)}e.options.footers&&_footer_js__WEBPACK_IMPORTED_MODULE_0__.e.call(e),setTimeout((function(){_selection_js__WEBPACK_IMPORTED_MODULE_1__.Aq.call(e)}),0)},parseNumber=function(e,t){const s=t&&this.options.columns[t].decimal?this.options.columns[t].decimal:".";let n=""+e;return n=n.split(s),n[0]=n[0].match(/[+-]?[0-9]/g),n[0]&&(n[0]=n[0].join("")),n[1]&&(n[1]=n[1].match(/[0-9]*/g).join("")),n[0]&&Number.isInteger(Number(n[0]))?n[1]?Number(n[0]+"."+n[1]):Number(n[0]+".00"):null},executeFormula=function(expression,x,y){const obj=this,formulaResults=[],formulaLoopProtection=[],execute=function(expression,x,y){const parentId=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.t3)([x,y]);if(formulaLoopProtection[parentId])return console.error("Reference loop detected"),"#ERROR";formulaLoopProtection[parentId]=!0;const tokensUpdate=function(e){for(let t=0;t<e.length;t++){const s=[],n=e[t].split(":"),o=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.vu)(n[0],!0),r=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.vu)(n[1],!0);let l,i,a,c;o[0]<=r[0]?(l=o[0],i=r[0]):(l=r[0],i=o[0]),o[1]<=r[1]?(a=o[1],c=r[1]):(a=r[1],c=o[1]);for(let e=a;e<=c;e++)for(let t=l;t<=i;t++)s.push((0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.t3)([t,e]));expression=expression.replace(e[t],s.join(","))}};expression=expression.replace(/\$?([A-Z]+)\$?([0-9]+)/g,"$1$2");let tokens=expression.match(/([A-Z]+[0-9]+)\:([A-Z]+[0-9]+)/g);if(tokens&&tokens.length&&tokensUpdate(tokens),tokens=expression.match(/([A-Z]+[0-9]+)/g),tokens&&tokens.indexOf(parentId)>-1)return console.error("Self Reference detected"),"#ERROR";{const formulaExpressions={};if(tokens)for(let i=0;i<tokens.length;i++)if(obj.formula[tokens[i]]||(obj.formula[tokens[i]]=[]),obj.formula[tokens[i]].indexOf(parentId)<0&&obj.formula[tokens[i]].push(parentId),eval("typeof("+tokens[i]+') == "undefined"')){const e=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.vu)(tokens[i],1);let t;if(t=void 0!==obj.options.data[e[1]]&&void 0!==obj.options.data[e[1]][e[0]]?obj.options.data[e[1]][e[0]]:"","="==(""+t).substr(0,1)&&(void 0!==formulaResults[tokens[i]]?t=formulaResults[tokens[i]]:(t=execute(t,e[0],e[1]),formulaResults[tokens[i]]=t)),""==(""+t).trim())formulaExpressions[tokens[i]]=null;else if(t==Number(t)&&0!=obj.parent.config.autoCasting)formulaExpressions[tokens[i]]=Number(t);else{const s=parseNumber.call(obj,t,e[0]);0!=obj.parent.config.autoCasting&&s?formulaExpressions[tokens[i]]=s:formulaExpressions[tokens[i]]='"'+t+'"'}}const ret=_dispatch_js__WEBPACK_IMPORTED_MODULE_3__.A.call(obj,"onbeforeformula",obj,expression,x,y);if(!1===ret)return expression;let res;ret&&(expression=ret);try{res=formula(expression.substr(1),formulaExpressions,x,y,obj),"function"==typeof res&&(res="#ERROR")}catch(e){res="#ERROR",!0===obj.parent.config.debugFormulas&&console.log(expression.substr(1),formulaExpressions,e)}return res}};return execute(expression,x,y)},parseValue=function(e,t,s,n){const o=this;"="==(""+s).substr(0,1)&&0!=o.parent.config.parseFormulas&&(s=executeFormula.call(o,s,e,t));const r=o.options.columns&&o.options.columns[e];if(r&&!isFormula(s)){let e=null;if(e=getMask(r)){s&&s==Number(s)&&(s=Number(s));let t=jSuites.mask.render(s,e,!0);if(n&&e.mask){const o=e.mask.split(";");o[1]&&(o[1].match(new RegExp("\\[Red\\]","gi"))&&(s<0?n.classList.add("red"):n.classList.remove("red")),o[1].match(new RegExp("\\(","gi"))&&s<0&&(t="("+t+")"))}t&&(s=t)}}return s},getDropDownValue=function(e,t){const s=this,n=[];if(s.options.columns&&s.options.columns[e]&&s.options.columns[e].source){const o=[],r=s.options.columns[e].source;for(let e=0;e<r.length;e++)"object"==typeof r[e]?o[r[e].id]=r[e].name:o[r[e]]=r[e];const l=Array.isArray(t)?t:(""+t).split(";");for(let e=0;e<l.length;e++)"object"==typeof l[e]?n.push(o[l[e].id]):o[l[e]]&&n.push(o[l[e]])}else console.error("Invalid column");return n.length>0?n.join("; "):""},validDate=function(e){return"-"==(e=""+e).substr(4,1)&&"-"==e.substr(7,1)||4==(e=e.split("-"))[0].length&&e[0]==Number(e[0])&&2==e[1].length&&e[1]==Number(e[1])},stripScript=function(e){const t=new Option;t.innerHTML=e;let s=null;for(e=t.getElementsByTagName("script");s=e[0];)s.parentNode.removeChild(s);return t.innerHTML},createCell=function(e,t,s){const n=this;let o=document.createElement("td");if(o.setAttribute("data-x",e),o.setAttribute("data-y",t),"="==(""+s).substr(0,1)&&1==n.options.secureFormulas){const e=secureFormula(s);e!=s&&(s=e)}if(n.options.columns&&n.options.columns[e]&&"object"==typeof n.options.columns[e].type)!0===n.parent.config.parseHTML?o.innerHTML=s:o.textContent=s,"function"==typeof n.options.columns[e].type.createCell&&n.options.columns[e].type.createCell(o,s,parseInt(e),parseInt(t),n,n.options.columns[e]);else if(n.options.columns&&n.options.columns[e]&&"hidden"==n.options.columns[e].type)o.style.display="none",o.textContent=s;else if(n.options.columns&&n.options.columns[e]&&("checkbox"==n.options.columns[e].type||"radio"==n.options.columns[e].type)){const r=document.createElement("input");r.type=n.options.columns[e].type,r.name="c"+e,r.checked=1==s||1==s||"true"==s,r.onclick=function(){n.setValue(o,this.checked)},1!=n.options.columns[e].readOnly&&0!=n.options.editable||r.setAttribute("disabled","disabled"),o.appendChild(r),n.options.data[t][e]=r.checked}else if(n.options.columns&&n.options.columns[e]&&"calendar"==n.options.columns[e].type){let t=null;if(!validDate(s)){const o=jSuites.calendar.extractDateFromString(s,n.options.columns[e].options&&n.options.columns[e].options.format||"YYYY-MM-DD");o&&(t=o)}o.textContent=jSuites.calendar.getDateString(t||s,n.options.columns[e].options&&n.options.columns[e].options.format)}else if(n.options.columns&&n.options.columns[e]&&"dropdown"==n.options.columns[e].type)o.classList.add("jss_dropdown"),o.textContent=getDropDownValue.call(n,e,s);else if(n.options.columns&&n.options.columns[e]&&"color"==n.options.columns[e].type)if("square"==n.options.columns[e].render){const e=document.createElement("div");e.className="color",e.style.backgroundColor=s,o.appendChild(e)}else o.style.color=s,o.textContent=s;else if(n.options.columns&&n.options.columns[e]&&"image"==n.options.columns[e].type){if(s&&"data:image"==s.substr(0,10)){const e=document.createElement("img");e.src=s,o.appendChild(e)}}else n.options.columns&&n.options.columns[e]&&"html"==n.options.columns[e].type||!0===n.parent.config.parseHTML?o.innerHTML=stripScript(parseValue.call(this,e,t,s,o)):o.textContent=parseValue.call(this,e,t,s,o);n.options.columns&&n.options.columns[e]&&1==n.options.columns[e].readOnly&&(o.className="readonly");const r=n.options.columns&&n.options.columns[e]&&n.options.columns[e].align||n.options.defaultColAlign||"center";return o.style.textAlign=r,n.options.columns&&n.options.columns[e]&&0==n.options.columns[e].wordWrap||!(1==n.options.wordWrap||n.options.columns&&n.options.columns[e]&&1==n.options.columns[e].wordWrap||o.innerHTML.length>200)||(o.style.whiteSpace="pre-wrap"),e>0&&1==this.options.textOverflow&&(s||o.innerHTML?n.records[t][e-1].element.style.overflow="hidden":e==n.options.columns.length-1&&(o.style.overflow="hidden")),_dispatch_js__WEBPACK_IMPORTED_MODULE_3__.A.call(n,"oncreatecell",n,o,e,t,s),o},updateCell=function(e,t,s,n){const o=this;let r;if(1!=o.records[t][e].element.classList.contains("readonly")||n){if("="==(""+s).substr(0,1)&&1==o.options.secureFormulas){const e=secureFormula(s);e!=s&&(s=e)}const n=_dispatch_js__WEBPACK_IMPORTED_MODULE_3__.A.call(o,"onbeforechange",o,o.records[t][e].element,e,t,s);if(null!=n&&(s=n),o.options.columns&&o.options.columns[e]&&"object"==typeof o.options.columns[e].type&&"function"==typeof o.options.columns[e].type.updateCell){const n=o.options.columns[e].type.updateCell(o.records[t][e].element,s,parseInt(e),parseInt(t),o,o.options.columns[e]);void 0!==n&&(s=n)}r={x:e,y:t,col:e,row:t,value:s,oldValue:o.options.data[t][e]};let l=o.options.columns&&o.options.columns[e]&&"object"==typeof o.options.columns[e].type?o.options.columns[e].type:null;if(l)o.options.data[t][e]=s,"function"==typeof l.setValue&&l.setValue(o.records[t][e].element,s);else if(o.options.columns&&o.options.columns[e]&&("checkbox"==o.options.columns[e].type||"radio"==o.options.columns[e].type)){if("radio"==o.options.columns[e].type)for(let t=0;t<o.options.data.length;t++)o.options.data[t][e]=!1;o.records[t][e].element.children[0].checked=1==s||1==s||"true"==s||"TRUE"==s,o.options.data[t][e]=o.records[t][e].element.children[0].checked}else if(o.options.columns&&o.options.columns[e]&&"dropdown"==o.options.columns[e].type)o.options.data[t][e]=s,o.records[t][e].element.textContent=getDropDownValue.call(o,e,s);else if(o.options.columns&&o.options.columns[e]&&"calendar"==o.options.columns[e].type){let n=null;if(!validDate(s)){const t=jSuites.calendar.extractDateFromString(s,o.options.columns[e].options&&o.options.columns[e].options.format||"YYYY-MM-DD");t&&(n=t)}o.options.data[t][e]=s,o.records[t][e].element.textContent=jSuites.calendar.getDateString(n||s,o.options.columns[e].options&&o.options.columns[e].options.format)}else if(o.options.columns&&o.options.columns[e]&&"color"==o.options.columns[e].type)if(o.options.data[t][e]=s,"square"==o.options.columns[e].render){const n=document.createElement("div");n.className="color",n.style.backgroundColor=s,o.records[t][e].element.textContent="",o.records[t][e].element.appendChild(n)}else o.records[t][e].element.style.color=s,o.records[t][e].element.textContent=s;else if(o.options.columns&&o.options.columns[e]&&"image"==o.options.columns[e].type){if(s=""+s,o.options.data[t][e]=s,o.records[t][e].element.innerHTML="",s&&"data:image"==s.substr(0,10)){const n=document.createElement("img");n.src=s,o.records[t][e].element.appendChild(n)}}else o.options.data[t][e]=s,o.options.columns&&o.options.columns[e]&&"html"==o.options.columns[e].type?o.records[t][e].element.innerHTML=stripScript(parseValue.call(o,e,t,s)):!0===o.parent.config.parseHTML?o.records[t][e].element.innerHTML=stripScript(parseValue.call(o,e,t,s,o.records[t][e].element)):o.records[t][e].element.textContent=parseValue.call(o,e,t,s,o.records[t][e].element),o.options.columns&&o.options.columns[e]&&0==o.options.columns[e].wordWrap||!(1==o.options.wordWrap||o.options.columns&&o.options.columns[e]&&1==o.options.columns[e].wordWrap||o.records[t][e].element.innerHTML.length>200)?o.records[t][e].element.style.whiteSpace="":o.records[t][e].element.style.whiteSpace="pre-wrap";e>0&&(o.records[t][e-1].element.style.overflow=s?"hidden":""),o.options.columns&&o.options.columns[e]&&"function"==typeof o.options.columns[e].render&&o.options.columns[e].render(o.records[t]&&o.records[t][e]?o.records[t][e].element:null,s,parseInt(e),parseInt(t),o,o.options.columns[e]),_dispatch_js__WEBPACK_IMPORTED_MODULE_3__.A.call(o,"onchange",o,o.records[t]&&o.records[t][e]?o.records[t][e].element:null,e,t,s,r.oldValue)}else r={x:e,y:t,col:e,row:t};return r},isFormula=function(e){const t=(""+e)[0];return"="==t||"#"==t},getMask=function(e){if(e.format||e.mask||e.locale){const t={};return e.mask?t.mask=e.mask:e.format?t.mask=e.format:(t.locale=e.locale,t.options=e.options),e.decimal&&(t.options||(t.options={}),t.options={decimal:e.decimal}),t}return null},secureFormula=function(e){let t="",s=0;for(let n=0;n<e.length;n++)'"'==e[n]&&(s=0==s?1:0),t+=1==s?e[n]:e[n].toUpperCase();return t};let chainLoopProtection=[];const updateFormulaChain=function(e,t,s){const n=this,o=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.t3)([e,t]);if(n.formula[o]&&n.formula[o].length>0)if(chainLoopProtection[o])n.records[t][e].element.innerHTML="#ERROR",n.formula[o]="";else{chainLoopProtection[o]=!0;for(let e=0;e<n.formula[o].length;e++){const t=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.vu)(n.formula[o][e],!0),r=""+n.options.data[t[1]][t[0]];"="==r.substr(0,1)?s.push(updateCell.call(n,t[0],t[1],r,!0)):Object.keys(n.formula)[e]=null,updateFormulaChain.call(n,t[0],t[1],s)}}chainLoopProtection=[]},updateFormula=function(e,t){const s=/[A-Z]/,n=/[0-9]/;let o="",r=null,l=null,i="";for(let a=0;a<e.length;a++)s.exec(e[a])?(r=1,l=0,i+=e[a]):n.exec(e[a])?(l=r?1:0,i+=e[a]):(r&&l&&(i=t[i]?t[i]:i),o+=i,o+=e[a],r=0,l=0,i="");return i&&(r&&l&&(i=t[i]?t[i]:i),o+=i),o},updateFormulas=function(e){const t=this;for(let s=0;s<t.options.data.length;s++)for(let n=0;n<t.options.data[0].length;n++){const o=""+t.options.data[s][n];if("="==o.substr(0,1)){const r=updateFormula(o,e);r!=o&&(t.options.data[s][n]=r)}}const s=[],n=Object.keys(t.formula);for(let o=0;o<n.length;o++){let r=n[o];const l=t.formula[r];e[r]&&(r=e[r]),s[r]=[];for(let t=0;t<l.length;t++){let n=l[t];e[n]&&(n=e[n]),s[r].push(n)}}t.formula=s},updateTableReferences=function(){const e=this;for(let t=0;t<e.headers.length;t++)e.headers[t].getAttribute("data-x")!=t&&(e.headers[t].setAttribute("data-x",t),e.headers[t].getAttribute("title")||(e.headers[t].innerHTML=(0,_helpers_js__WEBPACK_IMPORTED_MODULE_4__.getColumnName)(t)));for(let t=0;t<e.rows.length;t++)e.rows[t]&&e.rows[t].element.getAttribute("data-y")!=t&&(e.rows[t].element.setAttribute("data-y",t),e.rows[t].element.children[0].setAttribute("data-y",t),e.rows[t].element.children[0].innerHTML=t+1);const t=[],s=[],n=function(s,n,o,r){if(s!=o&&e.records[r][o].element.setAttribute("data-x",o),n!=r&&e.records[r][o].element.setAttribute("data-y",r),s!=o||n!=r){const e=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.t3)([s,n]),l=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.t3)([o,r]);t[e]=l}};for(let t=0;t<e.records.length;t++)for(let o=0;o<e.records[0].length;o++)if(e.records[t][o]){const r=e.records[t][o].element.getAttribute("data-x"),l=e.records[t][o].element.getAttribute("data-y");if(e.records[t][o].element.getAttribute("data-merged")){const e=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.t3)([r,l]),n=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.t3)([o,t]);if(null==s[e])if(e==n)s[e]=!1;else{const i=parseInt(o-r),a=parseInt(t-l);s[e]=[n,i,a]}}else n(r,l,o,t)}const o=Object.keys(s);if(o.length)for(let t=0;t<o.length;t++)if(s[o[t]]){const r=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.vu)(o[t],!0);let l=r[0],i=r[1];n(l,i,l+s[o[t]][1],i+s[o[t]][2]);const a=o[t],c=s[o[t]][0];for(let n=0;n<e.options.mergeCells[a][2].length;n++)l=parseInt(e.options.mergeCells[a][2][n].getAttribute("data-x")),i=parseInt(e.options.mergeCells[a][2][n].getAttribute("data-y")),e.options.mergeCells[a][2][n].setAttribute("data-x",l+s[o[t]][1]),e.options.mergeCells[a][2][n].setAttribute("data-y",i+s[o[t]][2]);e.options.mergeCells[c]=e.options.mergeCells[a],delete e.options.mergeCells[a]}updateFormulas.call(e,t),_meta_js__WEBPACK_IMPORTED_MODULE_5__.hs.call(e,t),_selection_js__WEBPACK_IMPORTED_MODULE_1__.G9.call(e),updateTable.call(e)},updateScroll=function(e){const t=this,s=t.content.getBoundingClientRect(),n=s.left,o=s.top,r=s.width,l=s.height,i=t.records[t.selectedCell[3]][t.selectedCell[2]].element.getBoundingClientRect(),a=i.left,c=i.top,u=i.width,d=i.height;let p,h;0==e||1==e?(p=a-n+t.content.scrollLeft,h=c-o+t.content.scrollTop-2):(p=a-n+t.content.scrollLeft+u,h=c-o+t.content.scrollTop+d),h>t.content.scrollTop+30&&h<t.content.scrollTop+l||(h<t.content.scrollTop+30?t.content.scrollTop=h-d:t.content.scrollTop=h-(l-2));const m=_freeze_js__WEBPACK_IMPORTED_MODULE_6__.w.call(t);p>t.content.scrollLeft+m&&p<t.content.scrollLeft+r||(p<t.content.scrollLeft+30?(t.content.scrollLeft=p,t.content.scrollLeft<50&&(t.content.scrollLeft=0)):p<t.content.scrollLeft+m?t.content.scrollLeft=p-m-1:t.content.scrollLeft=p-(r-20))},updateResult=function(){const e=this;let t=0,s=0;for(t=1==e.options.lazyLoading?100:e.options.pagination>0?e.options.pagination:e.results?e.results.length:e.rows.length;e.tbody.firstChild;)e.tbody.removeChild(e.tbody.firstChild);for(let n=0;n<e.rows.length;n++)!e.results||e.results.indexOf(n)>-1?(s<t&&(e.tbody.appendChild(e.rows[n].element),s++),e.rows[n].element.style.display=""):e.rows[n].element.style.display="none";return e.options.pagination>0&&_pagination_js__WEBPACK_IMPORTED_MODULE_7__.IV.call(e),_selection_js__WEBPACK_IMPORTED_MODULE_1__.Aq.call(e),t},getCell=function(e,t){if("string"==typeof e){const s=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.vu)(e,!0);e=s[0],t=s[1]}return this.records[t][e].element},getCellFromCoords=function(e,t){return this.records[t][e].element},getLabel=function(e,t){if("string"==typeof e){const s=(0,_internalHelpers_js__WEBPACK_IMPORTED_MODULE_2__.vu)(e,!0);e=s[0],t=s[1]}return this.records[t][e].element.innerHTML},fullscreen=function(e){const t=this;null==e&&(e=!t.config.fullscreen),t.config.fullscreen!=e&&(t.config.fullscreen=e,1==e?t.element.classList.add("fullscreen"):t.element.classList.remove("fullscreen"))},showIndex=function(){this.table.classList.remove("jss_hidden_index")},hideIndex=function(){this.table.classList.add("jss_hidden_index")},createNestedHeader=function(e){const t=this,s=document.createElement("tr");s.classList.add("jss_nested");const n=document.createElement("td");n.classList.add("jss_selectall"),s.appendChild(n),e.element=s;let o=0;for(let n=0;n<e.length;n++){e[n].colspan||(e[n].colspan=1),e[n].title||(e[n].title=""),e[n].id||(e[n].id="");let r=e[n].colspan;const l=[];for(let e=0;e<r;e++)t.options.columns[o]&&"hidden"==t.options.columns[o].type&&r++,l.push(o),o++;const i=document.createElement("td");i.setAttribute("data-column",l.join(",")),i.setAttribute("colspan",e[n].colspan),i.setAttribute("align",e[n].align||"center"),i.setAttribute("id",e[n].id),i.textContent=e[n].title,s.appendChild(i)}return s},getWorksheetActive=function(){const e=this.parent?this.parent:this;return e.element.tabs?e.element.tabs.getActive():0},getWorksheetInstance=function(e){const t=void 0!==e?e:getWorksheetActive.call(this);return this.worksheets[t]}},887:function(e,t,s){s.d(t,{Hh:function(){return o},t3:function(){return l},vu:function(){return r}});var n=s(595);const o=function(e,t,s){if(t<=e.length)return e.slice(0,t).concat(s).concat(e.slice(t));const n=e.slice(0,e.length);for(;t>n.length;)n.push(void 0);return n.concat(s)},r=function(e,t){const s=/^[a-zA-Z]+/.exec(e);if(s){let n=0;for(let e=0;e<s[0].length;e++)n+=parseInt(s[0].charCodeAt(e)-64)*Math.pow(26,s[0].length-1-e);n--,n<0&&(n=0);let o=parseInt(/[0-9]+$/.exec(e));o>0&&o--,e=1==t?[n,o]:n+"-"+o}return e},l=function(e){return Array.isArray(e)||(e=e.split("-")),(0,n.getColumnName)(parseInt(e[0]))+(parseInt(e[1])+1)}},992:function(e,t,s){s.d(t,{AG:function(){return o},G_:function(){return r},p6:function(){return l},wu:function(){return n}});const n=function(e){const t=this;let s;s=1!=t.options.search&&1!=t.options.filters||!t.results?t.rows:t.results;const n=100;null!=e&&-1!=e||(e=Math.ceil(s.length/n)-1);let o=e*n,r=e*n+n;r>s.length&&(r=s.length),o=r-100,o<0&&(o=0);for(let e=o;e<r;e++)1!=t.options.search&&1!=t.options.filters||!t.results?t.tbody.appendChild(t.rows[e].element):t.tbody.appendChild(t.rows[s[e]].element),t.tbody.children.length>n&&t.tbody.removeChild(t.tbody.firstChild)},o=function(){const e=this;if(e.selectedCell){const t=parseInt(e.tbody.firstChild.getAttribute("data-y"))/100,s=parseInt(e.selectedCell[3]/100),n=parseInt(e.rows.length/100);if(t!=s&&s<=n&&!Array.prototype.indexOf.call(e.tbody.children,e.rows[e.selectedCell[3]].element))return e.loadPage(s),!0}return!1},r=function(){const e=this;let t;t=1!=e.options.search&&1!=e.options.filters||!e.results?e.rows:e.results;let s=0;if(t.length>100){let n=parseInt(e.tbody.firstChild.getAttribute("data-y"));if(1!=e.options.search&&1!=e.options.filters||!e.results||(n=t.indexOf(n)),n>0)for(let o=0;o<30;o++)n-=1,n>-1&&(1!=e.options.search&&1!=e.options.filters||!e.results?e.tbody.insertBefore(e.rows[n].element,e.tbody.firstChild):e.tbody.insertBefore(e.rows[t[n]].element,e.tbody.firstChild),e.tbody.children.length>100&&(e.tbody.removeChild(e.tbody.lastChild),s=1))}return s},l=function(){const e=this;let t;t=1!=e.options.search&&1!=e.options.filters||!e.results?e.rows:e.results;let s=0;if(t.length>100){let n=parseInt(e.tbody.lastChild.getAttribute("data-y"));if(1!=e.options.search&&1!=e.options.filters||!e.results||(n=t.indexOf(n)),n<e.rows.length-1)for(let o=0;o<=30;o++)n<t.length&&(1!=e.options.search&&1!=e.options.filters||!e.results?e.tbody.appendChild(e.rows[n].element):e.tbody.appendChild(e.rows[t[n]].element),e.tbody.children.length>100&&(e.tbody.removeChild(e.tbody.firstChild),s=1)),n+=1}return s}},441:function(e,t,s){s.d(t,{D0:function(){return c},FU:function(){return d},Lt:function(){return a},VP:function(){return h},Zp:function(){return p},fd:function(){return u}});var n=s(887),o=s(45),r=s(126),l=s(946),i=s(268);const a=function(e,t){const s=this,o=[];if(s.options.mergeCells){const r=Object.keys(s.options.mergeCells);for(let l=0;l<r.length;l++){const i=(0,n.vu)(r[l],!0),a=s.options.mergeCells[r[l]][0],c=i[0],u=i[0]+(a>1?a-1:0);null==t?c<=e&&u>=e&&o.push(r[l]):t?c<e&&u>=e&&o.push(r[l]):c<=e&&u>e&&o.push(r[l])}}return o},c=function(e,t){const s=this,o=[];if(s.options.mergeCells){const r=Object.keys(s.options.mergeCells);for(let l=0;l<r.length;l++){const i=(0,n.vu)(r[l],!0),a=s.options.mergeCells[r[l]][1],c=i[1],u=i[1]+(a>1?a-1:0);null==t?c<=e&&u>=e&&o.push(r[l]):t?c<e&&u>=e&&o.push(r[l]):c<=e&&u>e&&o.push(r[l])}}return o},u=function(e){const t=this;let s={};if(e)s=t.options.mergeCells&&t.options.mergeCells[e]?[t.options.mergeCells[e][0],t.options.mergeCells[e][1]]:null;else if(t.options.mergeCells){t.options.mergeCells;const e=Object.keys(t.options.mergeCells);for(let n=0;n<e.length;n++)s[e[n]]=[t.options.mergeCells[e[n]][0],t.options.mergeCells[e[n]][1]]}return s},d=function(e,t,s,a){const c=this;let u=!1;if(e){if("string"!=typeof e)return null}else{if(!c.highlighted.length)return alert(jSuites.translate("No cells selected")),null;{const o=parseInt(c.highlighted[0].getAttribute("data-x")),r=parseInt(c.highlighted[0].getAttribute("data-y")),l=parseInt(c.highlighted[c.highlighted.length-1].getAttribute("data-x")),i=parseInt(c.highlighted[c.highlighted.length-1].getAttribute("data-y"));e=(0,n.t3)([o,r]),t=l-o+1,s=i-r+1}}const d=(0,n.vu)(e,!0);if(c.options.mergeCells&&c.options.mergeCells[e])c.records[d[1]][d[0]].element.getAttribute("data-merged")&&(u="Cell already merged");else if((!t||t<2)&&(!s||s<2))u="Invalid merged properties";else for(let e=d[1];e<d[1]+s;e++)for(let s=d[0];s<d[0]+t;s++)(0,n.t3)([s,e]),c.records[e][s].element.getAttribute("data-merged")&&(u="There is a conflict with another merged cell");if(u)alert(jSuites.translate(u));else{t>1?c.records[d[1]][d[0]].element.setAttribute("colspan",t):t=1,s>1?c.records[d[1]][d[0]].element.setAttribute("rowspan",s):s=1,c.options.mergeCells||(c.options.mergeCells={}),c.options.mergeCells[e]=[t,s,[]],c.records[d[1]][d[0]].element.setAttribute("data-merged","true"),c.records[d[1]][d[0]].element.style.overflow="hidden";const n=[];for(let r=d[1];r<d[1]+s;r++)for(let s=d[0];s<d[0]+t;s++)d[0]==s&&d[1]==r||(n.push(c.options.data[r][s]),o.k9.call(c,s,r,"",!0),c.options.mergeCells[e][2].push(c.records[r][s].element),c.records[r][s].element.style.display="none",c.records[r][s].element=c.records[d[1]][d[0]].element);i.c6.call(c,c.records[d[1]][d[0]].element),a||(r.Dh.call(c,{action:"setMerge",column:e,colspan:t,rowspan:s,data:n}),l.A.call(c,"onmerge",c,{[e]:[t,s]}))}},p=function(e,t,s){const r=this;if(r.options.mergeCells&&r.options.mergeCells[e]){const l=(0,n.vu)(e,!0);r.records[l[1]][l[0]].element.removeAttribute("colspan"),r.records[l[1]][l[0]].element.removeAttribute("rowspan"),r.records[l[1]][l[0]].element.removeAttribute("data-merged");const a=r.options.mergeCells[e];let c,u,d=0;for(c=0;c<a[1];c++)for(u=0;u<a[0];u++)(c>0||u>0)&&(r.records[l[1]+c][l[0]+u].element=a[2][d],r.records[l[1]+c][l[0]+u].element.style.display="",t&&t[d]&&o.k9.call(r,l[0]+u,l[1]+c,t[d]),d++);i.c6.call(r,r.records[l[1]][l[0]].element,r.records[l[1]+c-1][l[0]+u-1].element),s||delete r.options.mergeCells[e]}},h=function(e){const t=this;if(t.options.mergeCells){t.options.mergeCells;const s=Object.keys(t.options.mergeCells);for(let n=0;n<s.length;n++)p.call(t,s[n],null,e)}}},617:function(e,t,s){s.d(t,{IQ:function(){return o},hs:function(){return r},iZ:function(){return l}});var n=s(946);const o=function(e,t){const s=this;return e?t?s.options.meta&&s.options.meta[e]&&s.options.meta[e][t]?s.options.meta[e][t]:null:s.options.meta&&s.options.meta[e]?s.options.meta[e]:null:s.options.meta},r=function(e){const t=this;if(t.options.meta){const s={},n=Object.keys(t.options.meta);for(let o=0;o<n.length;o++)e[n[o]]?s[e[n[o]]]=t.options.meta[n[o]]:s[n[o]]=t.options.meta[n[o]];t.options.meta=s}},l=function(e,t,s){const o=this;if(o.options.meta||(o.options.meta={}),t&&s)o.options.meta[e]||(o.options.meta[e]={}),o.options.meta[e][t]=s,n.A.call(o,"onchangemeta",o,{[e]:{[t]:s}});else{const t=Object.keys(e);for(let s=0;s<t.length;s++){o.options.meta[t[s]]||(o.options.meta[t[s]]={});const n=Object.keys(e[t[s]]);for(let r=0;r<n.length;r++)o.options.meta[t[s]][n[r]]=e[t[s]][n[r]]}n.A.call(o,"onchangemeta",o,e)}}},451:function(e,t,s){s.d(t,{My:function(){return u},Th:function(){return a},iY:function(){return c}});var n=s(126),o=s(946),r=s(45),l=s(992),i=s(206);const a=function(e,t){const s=this;for(let e=0;e<s.headers.length;e++)s.headers[e].classList.remove("arrow-up"),s.headers[e].classList.remove("arrow-down");t?s.headers[e].classList.add("arrow-up"):s.headers[e].classList.add("arrow-down")},c=function(e){const t=this;let s=[];for(let n=0;n<e.length;n++)s[n]=t.options.data[e[n]];t.options.data=s,s=[];for(let n=0;n<e.length;n++){s[n]=t.records[e[n]];for(let e=0;e<s[n].length;e++)s[n][e].y=n}t.records=s,s=[];for(let n=0;n<e.length;n++)s[n]=t.rows[e[n]],s[n].y=n;if(t.rows=s,r.o8.call(t),t.results&&t.results.length)t.searchInput.value?t.search(t.searchInput.value):i.F8.call(t);else if(t.results=null,t.pageNumber=0,t.options.pagination>0)t.page(0);else if(1==t.options.lazyLoading)l.wu.call(t,0);else for(let e=0;e<t.rows.length;e++)t.tbody.appendChild(t.rows[e].element)},u=function(e,t){const s=this;if(e>=0){if(s.options.mergeCells&&Object.keys(s.options.mergeCells).length>0){if(!confirm(jSuites.translate("This action will destroy any existing merged cells. Are you sure?")))return!1;s.destroyMerge()}t=null==t?s.headers[e].classList.contains("arrow-down")?1:0:t?1:0;let r=[];if(s.options.columns&&s.options.columns[e]&&("number"==s.options.columns[e].type||"numeric"==s.options.columns[e].type||"percentage"==s.options.columns[e].type||"autonumber"==s.options.columns[e].type||"color"==s.options.columns[e].type))for(let t=0;t<s.options.data.length;t++)r[t]=[t,Number(s.options.data[t][e])];else if(s.options.columns&&s.options.columns[e]&&("calendar"==s.options.columns[e].type||"checkbox"==s.options.columns[e].type||"radio"==s.options.columns[e].type))for(let t=0;t<s.options.data.length;t++)r[t]=[t,s.options.data[t][e]];else for(let t=0;t<s.options.data.length;t++)r[t]=[t,s.records[t][e].element.textContent.toLowerCase()];"function"!=typeof s.parent.config.sorting&&(s.parent.config.sorting=function(e){return function(t,s){const n=t[1],o=s[1];return e?""===n&&""!==o?1:""!==n&&""===o||n>o?-1:n<o?1:0:""===n&&""!==o?1:""!==n&&""===o?-1:n>o?1:n<o?-1:0}}),r=r.sort(s.parent.config.sorting(t));const l=[];for(let e=0;e<r.length;e++)l[e]=r[e][0];return n.Dh.call(s,{action:"orderBy",rows:l,column:e,order:t}),a.call(s,e,t),c.call(s,l),o.A.call(s,"onsort",s,e,t,l.map((e=>e))),!0}}},292:function(e,t,s){s.d(t,{$f:function(){return a},IV:function(){return l},MY:function(){return i},ho:function(){return r}});var n=s(946),o=s(268);const r=function(e){const t=this;return 1!=t.options.search&&1!=t.options.filters||!t.results||(e=t.results.indexOf(e)),Math.ceil((parseInt(e)+1)/parseInt(t.options.pagination))-1},l=function(){const e=this;if(e.pagination.children[0].innerHTML="",e.pagination.children[1].innerHTML="",e.options.pagination){let t;if(t=1!=e.options.search&&1!=e.options.filters||!e.results?e.rows.length:e.results.length,t){const s=Math.ceil(t/e.options.pagination);let n,o;if(e.pageNumber<6?(n=1,o=s<10?s:10):s-e.pageNumber<5?(n=s-9,o=s,n<1&&(n=1)):(n=e.pageNumber-4,o=e.pageNumber+5),n>1){const t=document.createElement("div");t.className="jss_page",t.innerHTML="<",t.title=1,e.pagination.children[1].appendChild(t)}for(let t=n;t<=o;t++){const s=document.createElement("div");s.className="jss_page",s.innerHTML=t,e.pagination.children[1].appendChild(s),e.pageNumber==t-1&&s.classList.add("jss_page_selected")}if(o<s){const t=document.createElement("div");t.className="jss_page",t.innerHTML=">",t.title=s,e.pagination.children[1].appendChild(t)}const r=function(e){const t=Array.prototype.slice.call(arguments,1);return e.replace(/{(\d+)}/g,(function(e,s){return void 0!==t[s]?t[s]:e}))};e.pagination.children[0].innerHTML=r(jSuites.translate("Showing page {0} of {1} entries"),e.pageNumber+1,s)}else e.pagination.children[0].innerHTML=jSuites.translate("No records found")}},i=function(e){const t=this,s=t.pageNumber;let r;r=1!=t.options.search&&1!=t.options.filters||!t.results?t.rows:t.results;const i=parseInt(t.options.pagination);null!=e&&-1!=e||(e=Math.ceil(r.length/i)-1),t.pageNumber=e;let a=e*i,c=e*i+i;for(c>r.length&&(c=r.length),a<0&&(a=0);t.tbody.firstChild;)t.tbody.removeChild(t.tbody.firstChild);for(let e=a;e<c;e++)1!=t.options.search&&1!=t.options.filters||!t.results?t.tbody.appendChild(t.rows[e].element):t.tbody.appendChild(t.rows[r[e]].element);t.options.pagination>0&&l.call(t),o.Aq.call(t),n.A.call(t,"onchangepage",t,e,s,t.options.pagination)},a=function(){const e=this;let t;return t=1!=e.options.search&&1!=e.options.filters||!e.results?e.rows.length:e.results.length,Math.ceil(t/e.options.pagination)}},268:function(e,t,s){s.d(t,{AH:function(){return m},Aq:function(){return u},G9:function(){return g},Jg:function(){return f},Lo:function(){return A},R5:function(){return B},Ub:function(){return _},at:function(){return w},c6:function(){return p},eO:function(){return v},ef:function(){return E},gE:function(){return d},gG:function(){return y},kA:function(){return h},kF:function(){return C},kV:function(){return D},sp:function(){return x},tW:function(){return j}});var n=s(946),o=s(619),r=s(595),l=s(126),i=s(45),a=s(887),c=s(845);const u=function(){const e=this;if(e.highlighted&&e.highlighted.length){const t=e.highlighted[e.highlighted.length-1].element,s=t.getAttribute("data-x"),n=e.content.getBoundingClientRect(),r=n.left,l=n.top,i=t.getBoundingClientRect(),a=i.left,c=i.top,u=i.width,d=i.height,p=a-r+e.content.scrollLeft+u-4,h=c-l+e.content.scrollTop+d-4;if(e.corner.style.top=h+"px",e.corner.style.left=p+"px",e.options.freezeColumns){const t=o.w.call(e);s>e.options.freezeColumns-1&&a-r+u<t?e.corner.style.display="none":0!=e.options.selectionCopy&&(e.corner.style.display="")}else 0!=e.options.selectionCopy&&(e.corner.style.display="")}else e.corner.style.top="-2000px",e.corner.style.left="-2000px";(0,c.nK)(e)},d=function(e){const t=this;let s;if(t.highlighted&&t.highlighted.length){s=1;for(let e=0;e<t.highlighted.length;e++){t.highlighted[e].element.classList.remove("highlight"),t.highlighted[e].element.classList.remove("highlight-left"),t.highlighted[e].element.classList.remove("highlight-right"),t.highlighted[e].element.classList.remove("highlight-top"),t.highlighted[e].element.classList.remove("highlight-bottom"),t.highlighted[e].element.classList.remove("highlight-selected");const s=parseInt(t.highlighted[e].element.getAttribute("data-x")),n=parseInt(t.highlighted[e].element.getAttribute("data-y"));let o,r;if(t.highlighted[e].element.getAttribute("data-merged")){const l=parseInt(t.highlighted[e].element.getAttribute("colspan")),i=parseInt(t.highlighted[e].element.getAttribute("rowspan"));o=l>0?s+(l-1):s,r=i>0?n+(i-1):n}else o=s,r=n;for(let e=s;e<=o;e++)t.headers[e]&&t.headers[e].classList.remove("selected");for(let e=n;e<=r;e++)t.rows[e]&&t.rows[e].element.classList.remove("selected")}}else s=0;return t.highlighted=[],t.selectedCell=null,t.corner.style.top="-2000px",t.corner.style.left="-2000px",1==e&&1==s&&n.A.call(t,"onblur",t),s},p=function(e,t,s){const n=e.getAttribute("data-x"),o=e.getAttribute("data-y");let r,l;t?(r=t.getAttribute("data-x"),l=t.getAttribute("data-y")):(r=n,l=o),m.call(this,n,o,r,l,s)},h=function(){const e=document.querySelectorAll(".jss_worksheet .copying");for(let t=0;t<e.length;t++)e[t].classList.remove("copying"),e[t].classList.remove("copying-left"),e[t].classList.remove("copying-right"),e[t].classList.remove("copying-top"),e[t].classList.remove("copying-bottom")},m=function(e,t,s,o,r){const l=this;if(null==t){if(t=0,o=l.rows.length-1,null==e)return}else null==e&&(e=0,s=l.options.data[0].length-1);null==s&&(s=e),null==o&&(o=t),e>=l.headers.length&&(e=l.headers.length-1),t>=l.rows.length&&(t=l.rows.length-1),s>=l.headers.length&&(s=l.headers.length-1),o>=l.rows.length&&(o=l.rows.length-1);let i,a,c,d,p=null,m=null,f=null,g=null;parseInt(e)<parseInt(s)?(i=parseInt(e),a=parseInt(s)):(i=parseInt(s),a=parseInt(e)),parseInt(t)<parseInt(o)?(c=parseInt(t),d=parseInt(o)):(c=parseInt(o),d=parseInt(t));for(let e=i;e<=a;e++)for(let t=c;t<=d;t++)if(l.records[t][e]&&l.records[t][e].element.getAttribute("data-merged")){const s=parseInt(l.records[t][e].element.getAttribute("data-x")),n=parseInt(l.records[t][e].element.getAttribute("data-y")),o=parseInt(l.records[t][e].element.getAttribute("colspan")),r=parseInt(l.records[t][e].element.getAttribute("rowspan"));o>1&&(s<i&&(i=s),s+o>a&&(a=s+o-1)),r&&(n<c&&(c=n),n+r>d&&(d=n+r-1))}for(let e=c;e<=d;e++)"none"!=l.rows[e].element.style.display&&(null==f&&(f=e),g=e);for(let e=i;e<=a;e++)for(let t=c;t<=d;t++)l.options.columns&&l.options.columns[e]&&"hidden"==l.options.columns[e].type||(null==p&&(p=e),m=e);if(p||(p=0),m||(m=0),!1===n.A.call(l,"onbeforeselection",l,p,f,m,g,r))return!1;const y=l.resetSelection();l.selectedCell=[e,t,s,o],l.records[t][e]&&l.records[t][e].element.classList.add("highlight-selected");for(let e=i;e<=a;e++)for(let t=c;t<=d;t++)"none"!=l.rows[t].element.style.display&&"none"!=l.records[t][e].element.style.display&&(l.records[t][e].element.classList.add("highlight"),l.highlighted.push(l.records[t][e]));for(let e=p;e<=m;e++)l.options.columns&&l.options.columns[e]&&"hidden"==l.options.columns[e].type||!l.cols[e].colElement.style||"none"==l.cols[e].colElement.style.display||(l.records[f]&&l.records[f][e]&&l.records[f][e].element.classList.add("highlight-top"),l.records[g]&&l.records[g][e]&&l.records[g][e].element.classList.add("highlight-bottom"),l.headers[e].classList.add("selected"));for(let e=f;e<=g;e++)l.rows[e]&&"none"!=l.rows[e].element.style.display&&(l.records[e][p].element.classList.add("highlight-left"),l.records[e][m].element.classList.add("highlight-right"),l.rows[e].element.classList.add("selected"));l.selectedContainer=[p,f,m,g],0==y&&(n.A.call(l,"onfocus",l),h()),n.A.call(l,"onselection",l,p,f,m,g,r),u.call(l)},f=function(e){const t=this;if(!t.selectedCell)return[];const s=[];for(let n=Math.min(t.selectedCell[0],t.selectedCell[2]);n<=Math.max(t.selectedCell[0],t.selectedCell[2]);n++)e&&"none"==t.headers[n].style.display||s.push(n);return s},g=function(){const e=this;e.selectedCell&&e.updateSelectionFromCoords(e.selectedCell[0],e.selectedCell[1],e.selectedCell[2],e.selectedCell[3])},y=function(){const e=this;for(let t=0;t<e.selection.length;t++)e.selection[t].classList.remove("selection"),e.selection[t].classList.remove("selection-left"),e.selection[t].classList.remove("selection-right"),e.selection[t].classList.remove("selection-top"),e.selection[t].classList.remove("selection-bottom");e.selection=[]},b=function(e){return 1==(e=""+e).length&&(e="0"+e),e},C=function(e,t){const s=this,o=s.getData(!0,!0),r=s.selectedContainer,c=parseInt(e.getAttribute("data-x")),u=parseInt(e.getAttribute("data-y")),d=parseInt(t.getAttribute("data-x")),p=parseInt(t.getAttribute("data-y")),h=[];let m,f,g=!1;r[0]==c?(m=u<r[1]?u-r[1]:1,f=0):(f=c<r[0]?c-r[0]:1,m=0);let y=0,C=0;for(let e=u;e<=p;e++)if(!s.rows[e]||"none"!=s.rows[e].element.style.display){null==o[C]&&(C=0),y=0,r[0]!=c&&(f=c<r[0]?c-r[0]:1);for(let t=c;t<=d;t++){if(s.records[e][t]&&!s.records[e][t].element.classList.contains("readonly")&&"none"!=s.records[e][t].element.style.display&&0==g){if(!s.selection.length&&""!=s.options.data[e][t]){g=!0;continue}(null==o[C]||null==o[C][y])&&(y=0);let n=o[C][y];if(n&&!o[1]&&0!=s.parent.config.autoIncrement)if(!s.options.columns||!s.options.columns[t]||s.options.columns[t].type&&"text"!=s.options.columns[t].type&&"number"!=s.options.columns[t].type){if(s.options.columns&&s.options.columns[t]&&"calendar"==s.options.columns[t].type){const e=new Date(n);e.setDate(e.getDate()+m),n=e.getFullYear()+"-"+b(parseInt(e.getMonth()+1))+"-"+b(e.getDate())+" 00:00:00"}}else if("="==(""+n).substr(0,1)){const e=n.match(/([A-Z]+[0-9]+)/g);if(e){const t=[];for(let s=0;s<e.length;s++){const n=(0,a.vu)(e[s],1);n[0]+=f,n[1]+=m,n[1]<0&&(n[1]=0);const o=(0,a.t3)([n[0],n[1]]);o!=e[s]&&(t[e[s]]=o)}t&&(n=(0,i.yB)(n,t))}}else n==Number(n)&&(n=Number(n)+m);h.push(i.k9.call(s,t,e,n)),i.xF.call(s,t,e,h)}y++,r[0]!=c&&f++}C++,m++}l.Dh.call(s,{action:"setValue",records:h,selection:s.selectedCell}),i.am.call(s);const j=h.map((function(e){return{x:e.x,y:e.y,value:e.newValue,oldValue:e.oldValue}}));n.A.call(s,"onafterchanges",s,j)},j=function(e){let t,s,n=0;if(0===e.length)return n;for(t=0;t<e.length;t++)s=e.charCodeAt(t),n=(n<<5)-n+s,n|=0;return n},w=function(e,t,s){const n=this;if(1==e){if(n.selectedCell&&(t>=n.selectedCell[1]&&t<=n.selectedCell[3]||s>=n.selectedCell[1]&&s<=n.selectedCell[3]))return void n.resetSelection()}else if(n.selectedCell&&(t>=n.selectedCell[0]&&t<=n.selectedCell[2]||s>=n.selectedCell[0]&&s<=n.selectedCell[2]))return void n.resetSelection()},B=function(e){const t=this;if(!t.selectedCell)return[];const s=[];for(let n=Math.min(t.selectedCell[1],t.selectedCell[3]);n<=Math.max(t.selectedCell[1],t.selectedCell[3]);n++)e&&"none"==t.rows[n].element.style.display||s.push(n);return s},_=function(){const e=this;e.selectedCell||(e.selectedCell=[]),e.selectedCell[0]=0,e.selectedCell[1]=0,e.selectedCell[2]=e.headers.length-1,e.selectedCell[3]=e.records.length-1,e.updateSelectionFromCoords(e.selectedCell[0],e.selectedCell[1],e.selectedCell[2],e.selectedCell[3])},A=function(){const e=this;return e.selectedCell?[Math.min(e.selectedCell[0],e.selectedCell[2]),Math.min(e.selectedCell[1],e.selectedCell[3]),Math.max(e.selectedCell[0],e.selectedCell[2]),Math.max(e.selectedCell[1],e.selectedCell[3])]:null},E=function(e){const t=this,s=A.call(t);if(!s)return[];const n=[];for(let o=s[1];o<=s[3];o++)for(let l=s[0];l<=s[2];l++)e?n.push((0,r.getCellNameFromCoords)(l,o)):n.push(t.records[o][l]);return n},v=function(){const e=this,t=A.call(e);if(!t)return"";const s=(0,r.getCellNameFromCoords)(t[0],t[1]),n=(0,r.getCellNameFromCoords)(t[2],t[3]);return s===n?e.options.worksheetName+"!"+s:e.options.worksheetName+"!"+s+":"+n},x=function(e,t){const s=A.call(this);return e>=s[0]&&e<=s[2]&&t>=s[1]&&t<=s[3]},D=function(){const e=A.call(this);return e?[e]:[]}},845:function(e,t,s){s.d(t,{Ar:function(){return d},ll:function(){return u},nK:function(){return c}});var n=s(595),o=s(45);const r=function(e,t){0!=t.options.editable?e.classList.remove("jtoolbar-disabled"):e.classList.add("jtoolbar-disabled")},l=function(){const e=[],t=this,s=function(){return o.eN.call(t)};e.push({content:"undo",onclick:function(){s().undo()}}),e.push({content:"redo",onclick:function(){s().redo()}}),e.push({content:"save",onclick:function(){const e=s();e&&e.download()}}),e.push({type:"divisor"}),e.push({type:"select",width:"120px",options:["Default","Verdana","Arial","Courier New"],render:function(e){return'<span style="font-family:'+e+'">'+e+"</span>"},onchange:function(e,t,n,o,r){const l=s();let i=l.getSelected(!0);if(i){let e=r?o:"";l.setStyle(Object.fromEntries(i.map((function(t){return[t,"font-family: "+e]}))))}},updateState:function(e,t,n){r(n,s())}}),e.push({type:"select",width:"48px",content:"format_size",options:["x-small","small","medium","large","x-large"],render:function(e){return'<span style="font-size:'+e+'">'+e+"</span>"},onchange:function(e,t,n,o){const r=s();let l=r.getSelected(!0);l&&r.setStyle(Object.fromEntries(l.map((function(e){return[e,"font-size: "+o]}))))},updateState:function(e,t,n){r(n,s())}}),e.push({type:"select",options:["left","center","right","justify"],render:function(e){return'<i class="material-icons">format_align_'+e+"</i>"},onchange:function(e,t,n,o){const r=s();let l=r.getSelected(!0);l&&r.setStyle(Object.fromEntries(l.map((function(e){return[e,"text-align: "+o]}))))},updateState:function(e,t,n){r(n,s())}}),e.push({content:"format_bold",onclick:function(e,t,n){const o=s();let r=o.getSelected(!0);r&&o.setStyle(Object.fromEntries(r.map((function(e){return[e,"font-weight:bold"]}))))},updateState:function(e,t,n){r(n,s())}}),e.push({type:"color",content:"format_color_text",k:"color",updateState:function(e,t,n){r(n,s())}}),e.push({type:"color",content:"format_color_fill",k:"background-color",updateState:function(e,t,n,o){r(n,s())}});let l=["top","middle","bottom"];return e.push({type:"select",options:["vertical_align_top","vertical_align_center","vertical_align_bottom"],render:function(e){return'<i class="material-icons">'+e+"</i>"},value:1,onchange:function(e,t,n,o,r){const i=s();let a=i.getSelected(!0);a&&i.setStyle(Object.fromEntries(a.map((function(e){return[e,"vertical-align: "+l[r]]}))))},updateState:function(e,t,n){r(n,s())}}),e.push({content:"web",tooltip:jSuites.translate("Merge the selected cells"),onclick:function(){const e=s();if(e.selectedCell&&confirm(jSuites.translate("The merged cells will retain the value of the top-left cell only. Are you sure?"))){const t=[Math.min(e.selectedCell[0],e.selectedCell[2]),Math.min(e.selectedCell[1],e.selectedCell[3]),Math.max(e.selectedCell[0],e.selectedCell[2]),Math.max(e.selectedCell[1],e.selectedCell[3])];let s=(0,n.getCellNameFromCoords)(t[0],t[1]);if(e.records[t[1]][t[0]].element.getAttribute("data-merged"))e.removeMerge(s);else{let n=t[2]-t[0]+1,o=t[3]-t[1]+1;1===n&&1===o||e.setMerge(s,n,o)}}},updateState:function(e,t,n){r(n,s())}}),e.push({type:"select",options:["border_all","border_outer","border_inner","border_horizontal","border_vertical","border_left","border_top","border_right","border_bottom","border_clear"],columns:5,render:function(e){return'<i class="material-icons">'+e+"</i>"},right:!0,onchange:function(e,t,o,r){const l=s();if(l.selectedCell){const e=[Math.min(l.selectedCell[0],l.selectedCell[2]),Math.min(l.selectedCell[1],l.selectedCell[3]),Math.max(l.selectedCell[0],l.selectedCell[2]),Math.max(l.selectedCell[1],l.selectedCell[3])];let s=r;if(e){let o=t.thickness||1,r=t.color||"black";const i=t.style||"solid";"double"===i&&(o+=2);let a={},c=e[0],u=e[1],d=e[2],p=e[3];const h=function(e,t,n){let l=["","","",""];l[0]=("border_top"===s||"border_outer"===s)&&n===u||("border_inner"===s||"border_horizontal"===s)&&n>u||"border_all"===s?"border-top: "+o+"px "+i+" "+r:"border-top: ",l[1]="border_all"!==s&&"border_right"!==s&&"border_outer"!==s||t!==d?"border-right: ":"border-right: "+o+"px "+i+" "+r,l[2]="border_all"!==s&&"border_bottom"!==s&&"border_outer"!==s||n!==p?"border-bottom: ":"border-bottom: "+o+"px "+i+" "+r,l[3]=("border_left"===s||"border_outer"===s)&&t===c||("border_inner"===s||"border_vertical"===s)&&t>c||"border_all"===s?"border-left: "+o+"px "+i+" "+r:"border-left: ",a[e]=l.join(";")};for(let t=e[1];t<=e[3];t++)for(let s=e[0];s<=e[2];s++)h((0,n.getCellNameFromCoords)(s,t),s,t),l.records[t][s].element.getAttribute("data-merged")&&h((0,n.getCellNameFromCoords)(e[0],e[1]),s,t);Object.keys(a)&&l.setStyle(a)}}},onload:function(e,t){let s=document.createElement("div"),n=document.createElement("div");s.appendChild(n);let o=jSuites.color(n,{closeOnChange:!1,onchange:function(e,s){e.parentNode.children[1].style.color=s,t.color=s}}),r=document.createElement("i");r.classList.add("material-icons"),r.innerHTML="color_lens",r.onclick=function(){o.open()},s.appendChild(r),e.children[1].appendChild(s),n=document.createElement("div"),jSuites.picker(n,{type:"select",data:[1,2,3,4,5],render:function(e){return'<div style="height: '+e+'px; width: 30px; background-color: black;"></div>'},onchange:function(e,s,n,o){t.thickness=o},width:"50px"}),e.children[1].appendChild(n);const l=document.createElement("div");jSuites.picker(l,{type:"select",data:["solid","dotted","dashed","double"],render:function(e){return"double"===e?'<div style="width: 30px; border-top: 3px '+e+' black;"></div>':'<div style="width: 30px; border-top: 2px '+e+' black;"></div>'},onchange:function(e,s,n,o){t.style=o},width:"50px"}),e.children[1].appendChild(l),n=document.createElement("div"),n.style.flex="1",e.children[1].appendChild(n)},updateState:function(e,t,n){r(n,s())}}),e.push({type:"divisor"}),e.push({content:"fullscreen",tooltip:"Toggle Fullscreen",onclick:function(e,s,n){"fullscreen"===n.children[0].textContent?(t.fullscreen(!0),n.children[0].textContent="fullscreen_exit"):(t.fullscreen(!1),n.children[0].textContent="fullscreen")},updateState:function(e,t,s,n){!0===n.parent.config.fullscreen?s.children[0].textContent="fullscreen_exit":s.children[0].textContent="fullscreen"}}),e},i=function(e){const t=this,s=e.items;for(let e=0;e<s.length;e++)s[e].tooltip&&(s[e].title=s[e].tooltip,delete s[e].tooltip),"select"==s[e].type?s[e].options?(s[e].data=s[e].options,delete s[e].options):(s[e].data=s[e].v,delete s[e].v,s[e].k&&!s[e].onchange&&(s[e].onchange=function(n,r,l){const i=o.eN.call(t),a=i.getSelected(!0);i.setStyle(Object.fromEntries(a.map((function(t){return[t,s[e].k+": "+l]}))))})):"color"==s[e].type&&(s[e].type="i",s[e].onclick=function(n,r,l){l.color||(jSuites.color(l,{onchange:function(n,r){const l=o.eN.call(t),i=l.getSelected(!0);l.setStyle(Object.fromEntries(i.map((function(t){return[t,s[e].k+": "+r]}))))},onopen:function(e){e.color.select("")}}),l.color.open())})},a=function(e){const t=this,s=document.createElement("div");return s.classList.add("jss_toolbar"),i.call(t,e),"object"==typeof t.plugins&&Object.entries(t.plugins).forEach((function([,t]){if("function"==typeof t.toolbar){const s=t.toolbar(e);s&&(e=s)}})),jSuites.toolbar(s,e),s},c=function(e){e.parent.toolbar&&e.parent.toolbar.toolbar.update(e)},u=function(){const e=this;if(e.config.toolbar&&!e.toolbar){let t;Array.isArray(e.config.toolbar)?t={items:e.config.toolbar}:"object"==typeof e.config.toolbar?t=e.config.toolbar:(t={items:l.call(e)},"function"==typeof e.config.toolbar&&(t=e.config.toolbar(t))),e.toolbar=e.element.insertBefore(a.call(e,t),e.element.children[1])}},d=function(){const e=this;e.toolbar&&(e.toolbar.parentNode.removeChild(e.toolbar),delete e.toolbar)}}},__webpack_module_cache__={};function __webpack_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var s=__webpack_module_cache__[e]={exports:{}};return __webpack_modules__[e](s,s.exports,__webpack_require__),s.exports}__webpack_require__.d=function(e,t){for(var s in t)__webpack_require__.o(t,s)&&!__webpack_require__.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},__webpack_require__.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},__webpack_require__.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var __webpack_exports__={};__webpack_require__.d(__webpack_exports__,{default:function(){return src}});const lib={jspreadsheet:{}};var libraryBase=lib,dispatch=__webpack_require__(946),internal=__webpack_require__(45),utils_history=__webpack_require__(126);const openEditor=function(e,t,s){const n=this,o=e.getAttribute("data-y"),r=e.getAttribute("data-x");dispatch.A.call(n,"oneditionstart",n,e,parseInt(r),parseInt(o)),r>0&&(n.records[o][r-1].element.style.overflow="hidden");const l=function(t){const s=e.getBoundingClientRect(),n=document.createElement(t);return n.style.width=s.width+"px",n.style.height=s.height-2+"px",n.style.minHeight=s.height-2+"px",e.classList.add("editor"),e.innerHTML="",e.appendChild(n),n};if(1==e.classList.contains("readonly"));else if(n.edition=[n.records[o][r].element,n.records[o][r].element.innerHTML,r,o],n.options.columns&&n.options.columns[r]&&"object"==typeof n.options.columns[r].type)n.options.columns[r].type.openEditor(e,n.options.data[o][r],parseInt(r),parseInt(o),n,n.options.columns[r],s),dispatch.A.call(n,"oncreateeditor",n,e,parseInt(r),parseInt(o),null,n.options.columns[r]);else if(n.options.columns&&n.options.columns[r]&&"hidden"==n.options.columns[r].type);else if(n.options.columns&&n.options.columns[r]&&("checkbox"==n.options.columns[r].type||"radio"==n.options.columns[r].type)){const t=!e.children[0].checked;n.setValue(e,t),n.edition=null}else if(n.options.columns&&n.options.columns[r]&&"dropdown"==n.options.columns[r].type){let t,s=n.options.data[o][r];n.options.columns[r].multiple&&!Array.isArray(s)&&(s=s.split(";")),t="function"==typeof n.options.columns[r].filter?n.options.columns[r].filter(n.element,e,r,o,n.options.columns[r].source):n.options.columns[r].source;const i=[];if(t)for(let e=0;e<t.length;e++)i.push(t[e]);const a=l("div");dispatch.A.call(n,"oncreateeditor",n,e,parseInt(r),parseInt(o),null,n.options.columns[r]);const c={data:i,multiple:!!n.options.columns[r].multiple,autocomplete:!!n.options.columns[r].autocomplete,opened:!0,value:s,width:"100%",height:a.style.minHeight,position:1==n.options.tableOverflow||1==n.parent.config.fullscreen,onclose:function(){closeEditor.call(n,e,!0)}};n.options.columns[r].options&&n.options.columns[r].options.type&&(c.type=n.options.columns[r].options.type),jSuites.dropdown(a,c)}else if(n.options.columns&&n.options.columns[r]&&("calendar"==n.options.columns[r].type||"color"==n.options.columns[r].type)){const t=n.options.data[o][r],s=l("input");dispatch.A.call(n,"oncreateeditor",n,e,parseInt(r),parseInt(o),null,n.options.columns[r]),s.value=t;const i=n.options.columns[r].options?{...n.options.columns[r].options}:{};if(1!=n.options.tableOverflow&&1!=n.parent.config.fullscreen||(i.position=!0),i.value=n.options.data[o][r],i.opened=!0,i.onclose=function(t,s){closeEditor.call(n,e,!0)},"color"==n.options.columns[r].type){jSuites.color(s,i);const t=e.getBoundingClientRect();i.position&&(s.nextSibling.children[1].style.top=t.top+t.height+"px",s.nextSibling.children[1].style.left=t.left+"px")}else i.format||(i.format="YYYY-MM-DD"),jSuites.calendar(s,i);s.focus()}else if(n.options.columns&&n.options.columns[r]&&"html"==n.options.columns[r].type){const t=n.options.data[o][r],s=l("div");dispatch.A.call(n,"oncreateeditor",n,e,parseInt(r),parseInt(o),null,n.options.columns[r]),s.style.position="relative";const i=document.createElement("div");i.classList.add("jss_richtext"),s.appendChild(i),jSuites.editor(i,{focus:!0,value:t});const a=e.getBoundingClientRect(),c=i.getBoundingClientRect();window.innerHeight<a.bottom+c.height?i.style.top=a.bottom-(c.height+2)+"px":i.style.top=a.top+"px",window.innerWidth<a.left+c.width?i.style.left=a.right-(c.width+2)+"px":i.style.left=a.left+"px"}else if(n.options.columns&&n.options.columns[r]&&"image"==n.options.columns[r].type){const t=e.children[0],s=l("div");dispatch.A.call(n,"oncreateeditor",n,e,parseInt(r),parseInt(o),null,n.options.columns[r]),s.style.position="relative";const i=document.createElement("div");i.classList.add("jclose"),t&&t.src&&i.appendChild(t),s.appendChild(i),jSuites.image(i,n.options.columns[r]);const a=e.getBoundingClientRect(),c=i.getBoundingClientRect();window.innerHeight<a.bottom+c.height?i.style.top=a.top-(c.height+2)+"px":i.style.top=a.top+"px",i.style.left=a.left+"px"}else{const s=1==t?"":n.options.data[o][r];let i;i=n.options.columns&&n.options.columns[r]&&0==n.options.columns[r].wordWrap||!(1==n.options.wordWrap||n.options.columns&&n.options.columns[r]&&1==n.options.columns[r].wordWrap)?l("input"):l("textarea"),dispatch.A.call(n,"oncreateeditor",n,e,parseInt(r),parseInt(o),null,n.options.columns[r]),i.focus(),i.value=s;const a=n.options.columns&&n.options.columns[r];if(!(0,internal.dw)(s)&&a){const e=(0,internal.rS)(a);if(e){if(!a.disabledMaskOnEdition)if(a.mask){const e=a.mask.split(";");i.setAttribute("data-mask",e[0])}else a.locale&&i.setAttribute("data-locale",a.locale);e.input=i,i.mask=e,jSuites.mask.render(s,e,!1)}}i.onblur=function(){closeEditor.call(n,e,!0)},i.scrollLeft=i.scrollWidth}},closeEditor=function(e,t){const s=this,n=parseInt(e.getAttribute("data-x")),o=parseInt(e.getAttribute("data-y"));let r;if(1==t){if(s.options.columns&&s.options.columns[n]&&"object"==typeof s.options.columns[n].type)r=s.options.columns[n].type.closeEditor(e,t,parseInt(n),parseInt(o),s,s.options.columns[n]);else if(s.options.columns&&s.options.columns[n]&&("checkbox"==s.options.columns[n].type||"radio"==s.options.columns[n].type||"hidden"==s.options.columns[n].type));else if(s.options.columns&&s.options.columns[n]&&"dropdown"==s.options.columns[n].type)r=e.children[0].dropdown.close(!0);else if(s.options.columns&&s.options.columns[n]&&"calendar"==s.options.columns[n].type)r=e.children[0].calendar.close(!0);else if(s.options.columns&&s.options.columns[n]&&"color"==s.options.columns[n].type)r=e.children[0].color.close(!0);else if(s.options.columns&&s.options.columns[n]&&"html"==s.options.columns[n].type)r=e.children[0].children[0].editor.getData();else if(s.options.columns&&s.options.columns[n]&&"image"==s.options.columns[n].type){const t=e.children[0].children[0].children[0];r=t&&"IMG"==t.tagName?t.src:""}else if(s.options.columns&&s.options.columns[n]&&"numeric"==s.options.columns[n].type)r=e.children[0].value,"="!=(""+r).substr(0,1)&&""==r&&(r=s.options.columns[n].allowEmpty?"":0),e.children[0].onblur=null;else{r=e.children[0].value,e.children[0].onblur=null;const t=s.options.columns&&s.options.columns[n];if(t){const e=(0,internal.rS)(t);if(e&&""!==r&&!(0,internal.dw)(r)&&"number"!=typeof r){const t=jSuites.mask.extract(r,e,!0);t&&""!==t.value&&(r=t.value)}}}s.options.data[o][n]==r?e.innerHTML=s.edition[1]:s.setValue(e,r)}else s.options.columns&&s.options.columns[n]&&"object"==typeof s.options.columns[n].type?s.options.columns[n].type.closeEditor(e,t,parseInt(n),parseInt(o),s,s.options.columns[n]):s.options.columns&&s.options.columns[n]&&"dropdown"==s.options.columns[n].type?e.children[0].dropdown.close(!0):s.options.columns&&s.options.columns[n]&&"calendar"==s.options.columns[n].type?e.children[0].calendar.close(!0):s.options.columns&&s.options.columns[n]&&"color"==s.options.columns[n].type?e.children[0].color.close(!0):e.children[0].onblur=null,e.innerHTML=s.edition&&s.edition[1]?s.edition[1]:"";dispatch.A.call(s,"oneditionend",s,e,n,o,r,t),e.classList.remove("editor"),s.edition=null},setCheckRadioValue=function(){const e=this,t=[],s=Object.keys(e.highlighted);for(let n=0;n<s.length;n++){const s=e.highlighted[n].element.getAttribute("data-x"),o=e.highlighted[n].element.getAttribute("data-y");"checkbox"!=e.options.columns[s].type&&"radio"!=e.options.columns[s].type||t.push(internal.k9.call(e,s,o,!e.options.data[o][s]))}if(t.length){utils_history.Dh.call(e,{action:"setValue",records:t,selection:e.selectedCell});const s=t.map((function(e){return{x:e.x,y:e.y,value:e.newValue,oldValue:e.oldValue}}));dispatch.A.call(e,"onafterchanges",e,s)}};var lazyLoading=__webpack_require__(992);const upGet=function(e,t){const s=this;e=parseInt(e);for(let n=(t=parseInt(t))-1;n>=0;n--)if("none"!=s.records[n][e].element.style.display&&"none"!=s.rows[n].element.style.display){if(s.records[n][e].element.getAttribute("data-merged")&&s.records[n][e].element==s.records[t][e].element)continue;t=n;break}return t},upVisible=function(e,t){const s=this;let n,o;if(0==e?(n=parseInt(s.selectedCell[0]),o=parseInt(s.selectedCell[1])):(n=parseInt(s.selectedCell[2]),o=parseInt(s.selectedCell[3])),0==t){for(let e=0;e<o;e++)if("none"!=s.records[e][n].element.style.display&&"none"!=s.rows[e].element.style.display){o=e;break}}else o=upGet.call(s,n,o);0==e?(s.selectedCell[0]=n,s.selectedCell[1]=o):(s.selectedCell[2]=n,s.selectedCell[3]=o)},up=function(e,t){const s=this;if(e?s.selectedCell[3]>0&&upVisible.call(s,1,t?0:1):(s.selectedCell[1]>0&&upVisible.call(s,0,t?0:1),s.selectedCell[2]=s.selectedCell[0],s.selectedCell[3]=s.selectedCell[1]),s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]),1==s.options.lazyLoading)if(0==s.selectedCell[1]||0==s.selectedCell[3])lazyLoading.wu.call(s,0),s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]);else if(lazyLoading.AG.call(s))s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]);else{const e=parseInt(s.tbody.firstChild.getAttribute("data-y"));s.selectedCell[1]-e<30&&(lazyLoading.G_.call(s),s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]))}else if(s.options.pagination>0){const e=s.whichPage(s.selectedCell[3]);e!=s.pageNumber&&s.page(e)}internal.Rs.call(s,1)},rightGet=function(e,t){const s=this;e=parseInt(e),t=parseInt(t);for(let n=e+1;n<s.headers.length;n++)if("none"!=s.records[t][n].element.style.display){if(s.records[t][n].element.getAttribute("data-merged")&&s.records[t][n].element==s.records[t][e].element)continue;e=n;break}return e},rightVisible=function(e,t){const s=this;let n,o;if(0==e?(n=parseInt(s.selectedCell[0]),o=parseInt(s.selectedCell[1])):(n=parseInt(s.selectedCell[2]),o=parseInt(s.selectedCell[3])),0==t){for(let e=s.headers.length-1;e>n;e--)if("none"!=s.records[o][e].element.style.display){n=e;break}}else n=rightGet.call(s,n,o);0==e?(s.selectedCell[0]=n,s.selectedCell[1]=o):(s.selectedCell[2]=n,s.selectedCell[3]=o)},right=function(e,t){const s=this;e?s.selectedCell[2]<s.headers.length-1&&rightVisible.call(s,1,t?0:1):(s.selectedCell[0]<s.headers.length-1&&rightVisible.call(s,0,t?0:1),s.selectedCell[2]=s.selectedCell[0],s.selectedCell[3]=s.selectedCell[1]),s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]),internal.Rs.call(s,2)},downGet=function(e,t){const s=this;e=parseInt(e);for(let n=(t=parseInt(t))+1;n<s.rows.length;n++)if("none"!=s.records[n][e].element.style.display&&"none"!=s.rows[n].element.style.display){if(s.records[n][e].element.getAttribute("data-merged")&&s.records[n][e].element==s.records[t][e].element)continue;t=n;break}return t},downVisible=function(e,t){const s=this;let n,o;if(0==e?(n=parseInt(s.selectedCell[0]),o=parseInt(s.selectedCell[1])):(n=parseInt(s.selectedCell[2]),o=parseInt(s.selectedCell[3])),0==t){for(let e=s.rows.length-1;e>o;e--)if("none"!=s.records[e][n].element.style.display&&"none"!=s.rows[e].element.style.display){o=e;break}}else o=downGet.call(s,n,o);0==e?(s.selectedCell[0]=n,s.selectedCell[1]=o):(s.selectedCell[2]=n,s.selectedCell[3]=o)},down=function(e,t){const s=this;if(e?s.selectedCell[3]<s.records.length-1&&downVisible.call(s,1,t?0:1):(s.selectedCell[1]<s.records.length-1&&downVisible.call(s,0,t?0:1),s.selectedCell[2]=s.selectedCell[0],s.selectedCell[3]=s.selectedCell[1]),s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]),1==s.options.lazyLoading)s.selectedCell[1]==s.records.length-1||s.selectedCell[3]==s.records.length-1?(lazyLoading.wu.call(s,-1),s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3])):lazyLoading.AG.call(s)?s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]):parseInt(s.tbody.lastChild.getAttribute("data-y"))-s.selectedCell[3]<30&&(lazyLoading.p6.call(s),s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]));else if(s.options.pagination>0){const e=s.whichPage(s.selectedCell[3]);e!=s.pageNumber&&s.page(e)}internal.Rs.call(s,3)},leftGet=function(e,t){const s=this;e=parseInt(e),t=parseInt(t);for(let n=e-1;n>=0;n--)if("none"!=s.records[t][n].element.style.display){if(s.records[t][n].element.getAttribute("data-merged")&&s.records[t][n].element==s.records[t][e].element)continue;e=n;break}return e},leftVisible=function(e,t){const s=this;let n,o;if(0==e?(n=parseInt(s.selectedCell[0]),o=parseInt(s.selectedCell[1])):(n=parseInt(s.selectedCell[2]),o=parseInt(s.selectedCell[3])),0==t){for(let e=0;e<n;e++)if("none"!=s.records[o][e].element.style.display){n=e;break}}else n=leftGet.call(s,n,o);0==e?(s.selectedCell[0]=n,s.selectedCell[1]=o):(s.selectedCell[2]=n,s.selectedCell[3]=o)},left=function(e,t){const s=this;e?s.selectedCell[2]>0&&leftVisible.call(s,1,t?0:1):(s.selectedCell[0]>0&&leftVisible.call(s,0,t?0:1),s.selectedCell[2]=s.selectedCell[0],s.selectedCell[3]=s.selectedCell[1]),s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]),internal.Rs.call(s,0)},first=function(e,t){const s=this;if(e?t?s.selectedCell[3]=0:leftVisible.call(s,1,0):(t?s.selectedCell[1]=0:leftVisible.call(s,0,0),s.selectedCell[2]=s.selectedCell[0],s.selectedCell[3]=s.selectedCell[1]),1!=s.options.lazyLoading||0!=s.selectedCell[1]&&0!=s.selectedCell[3]){if(s.options.pagination>0){const e=s.whichPage(s.selectedCell[3]);e!=s.pageNumber&&s.page(e)}}else lazyLoading.wu.call(s,0);s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]),internal.Rs.call(s,1)},last=function(e,t){const s=this;if(e?t?s.selectedCell[3]=s.records.length-1:rightVisible.call(s,1,0):(t?s.selectedCell[1]=s.records.length-1:rightVisible.call(s,0,0),s.selectedCell[2]=s.selectedCell[0],s.selectedCell[3]=s.selectedCell[1]),1!=s.options.lazyLoading||s.selectedCell[1]!=s.records.length-1&&s.selectedCell[3]!=s.records.length-1){if(s.options.pagination>0){const e=s.whichPage(s.selectedCell[3]);e!=s.pageNumber&&s.page(e)}}else lazyLoading.wu.call(s,-1);s.updateSelectionFromCoords(s.selectedCell[0],s.selectedCell[1],s.selectedCell[2],s.selectedCell[3]),internal.Rs.call(s,3)};var merges=__webpack_require__(441),selection=__webpack_require__(268),helpers=__webpack_require__(595),internalHelpers=__webpack_require__(887);const copy=function(e,t,s,n,o,r,l){const i=this;t||(t="\t");const a=new RegExp(t,"ig"),c=[];let u=[],d=[];const p=[],h=[],m=i.options.data[0].length,f=i.options.data.length;let g="",y=!1,b="",C="",j=0,w=0,B=0,_=0,A=!0;for(let t=0;t<f;t++)for(let s=0;s<m;s++)e&&!i.records[t][s].element.classList.contains("highlight")||(B<=s&&(B=s),_<=t&&(_=t));if(m===B+1&&f===_+1&&!1,o&&(1==i.parent.config.includeHeadersOnDownload||n)){if(i.options.nestedHeaders&&i.options.nestedHeaders.length>0){g=i.options.nestedHeaders;for(let e=0;e<g.length;e++){const s=[];for(let t=0;t<g[e].length;t++){const n=parseInt(g[e][t].colspan);s.push(g[e][t].title);for(let e=0;e<n-1;e++)s.push("")}C+=s.join(t)+"\r\n"}}y=!0}i.style=[];for(let s=0;s<f;s++){u=[],d=[];for(let t=0;t<m;t++)if(!e||i.records[s][t].element.classList.contains("highlight")){1==y&&c.push(i.headers[t].textContent);let e,n=i.options.data[s][t];n.match&&(n.match(a)||n.match(/,/g)||n.match(/\n/)||n.match(/\"/))&&(n=n.replace(new RegExp('"',"g"),'""'),n='"'+n+'"'),u.push(n),i.options.columns&&i.options.columns[t]&&("checkbox"==i.options.columns[t].type||"radio"==i.options.columns[t].type)?e=n:(e=i.records[s][t].element.innerHTML,e.match&&(e.match(a)||e.match(/,/g)||e.match(/\n/)||e.match(/\"/))&&(e=e.replace(new RegExp('"',"g"),'""'),e='"'+e+'"')),d.push(e),g=i.records[s][t].element.getAttribute("style"),g=g.replace("display: none;",""),i.style.push(g||"")}u.length&&(y&&(j=u.length,p.push(c.join(t))),p.push(u.join(t))),d.length&&(w++,y&&(h.push(c.join(t)),y=!1),h.push(d.join(t)))}m==j&&f==w&&(b=C);const E=b+p.join("\r\n");let v=b+h.join("\r\n");if(!s){const e=[Math.min(i.selectedCell[0],i.selectedCell[2]),Math.min(i.selectedCell[1],i.selectedCell[3]),Math.max(i.selectedCell[0],i.selectedCell[2]),Math.max(i.selectedCell[1],i.selectedCell[3])],t=dispatch.A.call(i,"oncopy",i,e,v,r);if(t)v=t;else if(!1===t)return!1;i.textarea.value=v,i.textarea.select(),document.execCommand("copy")}if(i.data=1==l?v:E,i.hashString=selection.tW.call(i,i.data),!s&&(selection.kA.call(i),i.highlighted))for(let e=0;e<i.highlighted.length;e++)i.highlighted[e].element.classList.add("copying"),i.highlighted[e].element.classList.contains("highlight-left")&&i.highlighted[e].element.classList.add("copying-left"),i.highlighted[e].element.classList.contains("highlight-right")&&i.highlighted[e].element.classList.add("copying-right"),i.highlighted[e].element.classList.contains("highlight-top")&&i.highlighted[e].element.classList.add("copying-top"),i.highlighted[e].element.classList.contains("highlight-bottom")&&i.highlighted[e].element.classList.add("copying-bottom");return i.data},paste=function(e,t,s){const n=this,o=(0,selection.tW)(s),r=o==n.hashString?n.style:null;o==n.hashString&&(s=n.data),s=(0,helpers.parseCSV)(s,"\t");const l=dispatch.A.call(n,"onbeforepaste",n,s.map((function(e){return e.map((function(e){return{value:e}}))})),e,t);if(!1===l)return!1;if(l&&(s=l),null!=e&&null!=t&&s){let o=0,l=0;const i=[],a={},c={};let u=0,d=parseInt(e),p=parseInt(t),h=null;for(;h=s[l];){for(o=0,d=parseInt(e);null!=h[o];){const e=internal.k9.call(n,d,p,h[o]);if(i.push(e),internal.xF.call(n,d,p,i),r&&r[u]){const e=(0,internalHelpers.t3)([d,p]);a[e]=r[u],c[e]=n.getStyle(e),n.records[p][d].element.setAttribute("style",r[u]),u++}if(o++,null!=h[o]){if(d>=n.headers.length-1){if(0==n.options.allowInsertColumn)break;n.insertColumn()}d=rightGet.call(n,d,p)}}if(l++,s[l]){if(p>=n.rows.length-1){if(0==n.options.allowInsertRow)break;n.insertRow()}p=downGet.call(n,e,p)}}selection.AH.call(n,e,t,d,p),utils_history.Dh.call(n,{action:"setValue",records:i,selection:n.selectedCell,newStyle:a,oldStyle:c}),internal.am.call(n);const m=[];for(let n=0;n<s.length;n++)for(let o=0;o<s[n].length;o++)m.push({x:o+e,y:n+t,value:s[n][o]});dispatch.A.call(n,"onpaste",n,m);const f=i.map((function(e){return{x:e.x,y:e.y,value:e.newValue,oldValue:e.oldValue}}));dispatch.A.call(n,"onafterchanges",n,f)}(0,selection.kA)()};var filter=__webpack_require__(206),footer=__webpack_require__(623);const getNumberOfColumns=function(){const e=this;let t=e.options.columns&&e.options.columns.length||0;if(e.options.data&&void 0!==e.options.data[0]){const s=Object.keys(e.options.data[0]);s.length>t&&(t=s.length)}return e.options.minDimensions&&e.options.minDimensions[0]>t&&(t=e.options.minDimensions[0]),t},createCellHeader=function(e){const t=this,s=t.options.columns&&t.options.columns[e]&&t.options.columns[e].width||t.options.defaultColWidth||100,n=t.options.columns&&t.options.columns[e]&&t.options.columns[e].align||t.options.defaultColAlign||"center";t.headers[e]=document.createElement("td"),t.headers[e].textContent=t.options.columns&&t.options.columns[e]&&t.options.columns[e].title||(0,helpers.getColumnName)(e),t.headers[e].setAttribute("data-x",e),t.headers[e].style.textAlign=n,t.options.columns&&t.options.columns[e]&&t.options.columns[e].title&&t.headers[e].setAttribute("title",t.headers[e].innerText),t.options.columns&&t.options.columns[e]&&t.options.columns[e].id&&t.headers[e].setAttribute("id",t.options.columns[e].id);const o=document.createElement("col");o.setAttribute("width",s),t.cols[e]={colElement:o,x:e},t.options.columns&&t.options.columns[e]&&"hidden"==t.options.columns[e].type&&(t.headers[e].style.display="none",o.style.display="none")},insertColumn=function(e,t,s,n){const o=this;if(0!=o.options.allowInsertColumn){let r,l=[];Array.isArray(e)?(r=1,e&&(l=e)):r="number"==typeof e?e:1,s=!!s;const i=Math.max(o.options.columns.length,...o.options.data.map((function(e){return e.length})))-1;(null==t||t>=parseInt(i)||t<0)&&(t=i),n||(n=[]);for(let e=0;e<r;e++)n[e]||(n[e]={});const a=[];if(Array.isArray(e)){const r=[];for(let t=0;t<o.options.data.length;t++)r.push(t<e.length?e[t]:"");const l={column:t+(s?0:1),options:Object.assign({},n[0]),data:r};a.push(l)}else for(let o=0;o<e;o++){const e={column:t+o+(s?0:1),options:Object.assign({},n[o])};a.push(e)}if(!1===dispatch.A.call(o,"onbeforeinsertcolumn",o,a))return!1;if(o.options.mergeCells&&Object.keys(o.options.mergeCells).length>0&&merges.Lt.call(o,t,s).length){if(!confirm(jSuites.translate("This action will destroy any existing merged cells. Are you sure?")))return!1;o.destroyMerge()}const c=s?t:t+1;o.options.columns=(0,internalHelpers.Hh)(o.options.columns,c,n);const u=o.headers.splice(c),d=o.cols.splice(c),p=[],h=[],m=[],f=[],g=[];for(let e=c;e<r+c;e++)createCellHeader.call(o,e),o.headerContainer.insertBefore(o.headers[e],o.headerContainer.children[e+1]),o.colgroupContainer.insertBefore(o.cols[e].colElement,o.colgroupContainer.children[e+1]),p.push(o.headers[e]),h.push(o.cols[e]);if(o.options.footers)for(let e=0;e<o.options.footers.length;e++){g[e]=[];for(let t=0;t<r;t++)g[e].push("");o.options.footers[e].splice(c,0,g[e])}for(let e=0;e<o.options.data.length;e++){const t=o.options.data[e].splice(c),s=o.records[e].splice(c);f[e]=[],m[e]=[];for(let t=c;t<r+c;t++){const s=l[e]?l[e]:"";o.options.data[e][t]=s;const n=internal.P9.call(o,t,e,o.options.data[e][t]);o.records[e][t]={element:n,y:e},o.rows[e]&&o.rows[e].element.insertBefore(n,o.rows[e].element.children[t+1]),o.options.columns&&o.options.columns[t]&&"function"==typeof o.options.columns[t].render&&o.options.columns[t].render(n,s,parseInt(t),parseInt(e),o,o.options.columns[t]),f[e].push(s),m[e].push({element:n,x:t,y:e})}Array.prototype.push.apply(o.options.data[e],t),Array.prototype.push.apply(o.records[e],s)}Array.prototype.push.apply(o.headers,u),Array.prototype.push.apply(o.cols,d);for(let e=c;e<o.cols.length;e++)o.cols[e].x=e;for(let e=0;e<o.records.length;e++)for(let t=0;t<o.records[e].length;t++)o.records[e][t].x=t;if(o.options.nestedHeaders&&o.options.nestedHeaders.length>0&&o.options.nestedHeaders[0]&&o.options.nestedHeaders[0][0])for(let e=0;e<o.options.nestedHeaders.length;e++){const t=parseInt(o.options.nestedHeaders[e][o.options.nestedHeaders[e].length-1].colspan)+r;o.options.nestedHeaders[e][o.options.nestedHeaders[e].length-1].colspan=t,o.thead.children[e].children[o.thead.children[e].children.length-1].setAttribute("colspan",t);let s=o.thead.children[e].children[o.thead.children[e].children.length-1].getAttribute("data-column");s=s.split(",");for(let e=c;e<r+c;e++)s.push(e);o.thead.children[e].children[o.thead.children[e].children.length-1].setAttribute("data-column",s)}utils_history.Dh.call(o,{action:"insertColumn",columnNumber:t,numOfColumns:r,insertBefore:s,columns:n,headers:p,cols:h,records:m,footers:g,data:f}),internal.o8.call(o),dispatch.A.call(o,"oninsertcolumn",o,a)}},moveColumn=function(e,t){const s=this;if(s.options.mergeCells&&Object.keys(s.options.mergeCells).length>0){let n;if(n=e>t?1:0,merges.Lt.call(s,e).length||merges.Lt.call(s,t,n).length){if(!confirm(jSuites.translate("This action will destroy any existing merged cells. Are you sure?")))return!1;s.destroyMerge()}}if((e=parseInt(e))>(t=parseInt(t))){s.headerContainer.insertBefore(s.headers[e],s.headers[t]),s.colgroupContainer.insertBefore(s.cols[e].colElement,s.cols[t].colElement);for(let n=0;n<s.rows.length;n++)s.rows[n].element.insertBefore(s.records[n][e].element,s.records[n][t].element)}else{s.headerContainer.insertBefore(s.headers[e],s.headers[t].nextSibling),s.colgroupContainer.insertBefore(s.cols[e].colElement,s.cols[t].colElement.nextSibling);for(let n=0;n<s.rows.length;n++)s.rows[n].element.insertBefore(s.records[n][e].element,s.records[n][t].element.nextSibling)}s.options.columns.splice(t,0,s.options.columns.splice(e,1)[0]),s.headers.splice(t,0,s.headers.splice(e,1)[0]),s.cols.splice(t,0,s.cols.splice(e,1)[0]);const n=Math.min(e,t),o=Math.max(e,t);for(let n=0;n<s.rows.length;n++)s.options.data[n].splice(t,0,s.options.data[n].splice(e,1)[0]),s.records[n].splice(t,0,s.records[n].splice(e,1)[0]);for(let e=n;e<=o;e++)s.cols[e].x=e;for(let e=0;e<s.records.length;e++)for(let t=n;t<=o;t++)s.records[e][t].x=t;if(s.options.footers)for(let n=0;n<s.options.footers.length;n++)s.options.footers[n].splice(t,0,s.options.footers[n].splice(e,1)[0]);utils_history.Dh.call(s,{action:"moveColumn",oldValue:e,newValue:t}),internal.o8.call(s),dispatch.A.call(s,"onmovecolumn",s,e,t,1)},deleteColumn=function(e,t){const s=this;if(0!=s.options.allowDeleteColumn)if(s.headers.length>1){if(null==e){const n=s.getSelectedColumns(!0);n.length?(e=parseInt(n[0]),t=parseInt(n.length)):(e=s.headers.length-1,t=1)}const n=s.options.data[0].length-1;(null==e||e>n||e<0)&&(e=n),t||(t=1),t>s.options.data[0].length-e&&(t=s.options.data[0].length-e);const o=[];for(let s=0;s<t;s++)o.push(s+e);if(!1===dispatch.A.call(s,"onbeforedeletecolumn",s,o))return!1;if(parseInt(e)>-1){let n=!1;if(s.options.mergeCells&&Object.keys(s.options.mergeCells).length>0)for(let o=e;o<e+t;o++)merges.Lt.call(s,o,null).length&&(n=!0);if(n){if(!confirm(jSuites.translate("This action will destroy any existing merged cells. Are you sure?")))return!1;s.destroyMerge()}const r=s.options.columns?s.options.columns.splice(e,t):void 0;for(let n=e;n<e+t;n++)s.cols[n].colElement.className="",s.headers[n].className="",s.cols[n].colElement.parentNode.removeChild(s.cols[n].colElement),s.headers[n].parentNode.removeChild(s.headers[n]);const l=s.headers.splice(e,t),i=s.cols.splice(e,t),a=[],c=[],u=[];for(let n=0;n<s.options.data.length;n++)for(let o=e;o<e+t;o++)s.records[n][o].element.className="",s.records[n][o].element.parentNode.removeChild(s.records[n][o].element);for(let n=0;n<s.options.data.length;n++)c[n]=s.options.data[n].splice(e,t),a[n]=s.records[n].splice(e,t);for(let t=e;t<s.cols.length;t++)s.cols[t].x=t;for(let t=0;t<s.records.length;t++)for(let n=e;n<s.records[t].length;n++)s.records[t][n].x=n;if(s.options.footers)for(let n=0;n<s.options.footers.length;n++)u[n]=s.options.footers[n].splice(e,t);if(selection.at.call(s,0,e,e+t-1),s.options.nestedHeaders&&s.options.nestedHeaders.length>0&&s.options.nestedHeaders[0]&&s.options.nestedHeaders[0][0])for(let e=0;e<s.options.nestedHeaders.length;e++){const n=parseInt(s.options.nestedHeaders[e][s.options.nestedHeaders[e].length-1].colspan)-t;s.options.nestedHeaders[e][s.options.nestedHeaders[e].length-1].colspan=n,s.thead.children[e].children[s.thead.children[e].children.length-1].setAttribute("colspan",n)}utils_history.Dh.call(s,{action:"deleteColumn",columnNumber:e,numOfColumns:t,insertBefore:1,columns:r,headers:l,cols:i,records:a,footers:u,data:c}),internal.o8.call(s),dispatch.A.call(s,"ondeletecolumn",s,o)}}else console.error("Jspreadsheet: It is not possible to delete the last column")},getWidth=function(e){const t=this;let s;if(void 0===e){s=[];for(let e=0;e<t.headers.length;e++)s.push(t.options.columns&&t.options.columns[e]&&t.options.columns[e].width||t.options.defaultColWidth||100)}else s=parseInt(t.cols[e].colElement.getAttribute("width"));return s},setWidth=function(e,t,s){const n=this;if(t){if(Array.isArray(e)){s||(s=[]);for(let o=0;o<e.length;o++){s[o]||(s[o]=parseInt(n.cols[e[o]].colElement.getAttribute("width")));const r=Array.isArray(t)&&t[o]?t[o]:t;n.cols[e[o]].colElement.setAttribute("width",r),n.options.columns||(n.options.columns=[]),n.options.columns[e[o]]||(n.options.columns[e[o]]={}),n.options.columns[e[o]].width=r}}else s||(s=parseInt(n.cols[e].colElement.getAttribute("width"))),n.cols[e].colElement.setAttribute("width",t),n.options.columns||(n.options.columns=[]),n.options.columns[e]||(n.options.columns[e]={}),n.options.columns[e].width=t;utils_history.Dh.call(n,{action:"setWidth",column:e,oldValue:s,newValue:t}),dispatch.A.call(n,"onresizecolumn",n,e,t,s),selection.Aq.call(n)}},showColumn=function(e){const t=this;Array.isArray(e)||(e=[e]);for(let s=0;s<e.length;s++){const n=e[s];t.headers[n].style.display="",t.cols[n].colElement.style.display="",t.filter&&t.filter.children.length>n+1&&(t.filter.children[n+1].style.display="");for(let e=0;e<t.options.data.length;e++)t.records[e][n].element.style.display=""}t.options.footers&&footer.e.call(t),t.resetSelection()},hideColumn=function(e){const t=this;Array.isArray(e)||(e=[e]);for(let s=0;s<e.length;s++){const n=e[s];t.headers[n].style.display="none",t.cols[n].colElement.style.display="none",t.filter&&t.filter.children.length>n+1&&(t.filter.children[n+1].style.display="none");for(let e=0;e<t.options.data.length;e++)t.records[e][n].element.style.display="none"}t.options.footers&&footer.e.call(t),t.resetSelection()},getColumnData=function(e,t){const s=this,n=[];for(let o=0;o<s.options.data.length;o++)t?n.push(s.records[o][e].element.innerHTML):n.push(s.options.data[o][e]);return n},setColumnData=function(e,t,s){const n=this;for(let o=0;o<n.rows.length;o++){const r=(0,internalHelpers.t3)([e,o]);null!=t[o]&&n.setValue(r,t[o],s)}},createRow=function(e,t){const s=this;s.records[e]||(s.records[e]=[]),t||(t=s.options.data[e]);const n={element:document.createElement("tr"),y:e};s.rows[e]=n,n.element.setAttribute("data-y",e);let o=null;s.options.defaultRowHeight&&(n.element.style.height=s.options.defaultRowHeight+"px"),s.options.rows&&s.options.rows[e]&&(s.options.rows[e].height&&(n.element.style.height=s.options.rows[e].height),s.options.rows[e].title&&(o=s.options.rows[e].title)),o||(o=parseInt(e+1));const r=document.createElement("td");r.innerHTML=o,r.setAttribute("data-y",e),r.className="jss_row",n.element.appendChild(r);const l=getNumberOfColumns.call(s);for(let o=0;o<l;o++)s.records[e][o]={element:internal.P9.call(this,o,e,t[o]),x:o,y:e},n.element.appendChild(s.records[e][o].element),s.options.columns&&s.options.columns[o]&&"function"==typeof s.options.columns[o].render&&s.options.columns[o].render(s.records[e][o].element,t[o],parseInt(o),parseInt(e),s,s.options.columns[o]);return n},insertRow=function(e,t,s){const n=this;if(0!=n.options.allowInsertRow){let o,r=[];Array.isArray(e)?(o=1,e&&(r=e)):o=void 0!==e?e:1,s=!!s;const l=n.options.data.length-1;(null==t||t>=parseInt(l)||t<0)&&(t=l);const i=[];for(let e=0;e<o;e++){const o=[];for(let e=0;e<n.options.columns.length;e++)o[e]=r[e]?r[e]:"";i.push({row:e+t+(s?0:1),data:o})}if(!1===dispatch.A.call(n,"onbeforeinsertrow",n,i))return!1;if(n.options.mergeCells&&Object.keys(n.options.mergeCells).length>0&&merges.D0.call(n,t,s).length){if(!confirm(jSuites.translate("This action will destroy any existing merged cells. Are you sure?")))return!1;n.destroyMerge()}if(1==n.options.search){if(n.results&&n.results.length!=n.rows.length){if(!confirm(jSuites.translate("This action will clear your search results. Are you sure?")))return!1;n.resetSearch()}n.results=null}const a=s?t:t+1,c=n.records.splice(a),u=n.options.data.splice(a),d=n.rows.splice(a),p=[],h=[],m=[];for(let e=a;e<o+a;e++){n.options.data[e]=[];for(let t=0;t<n.options.columns.length;t++)n.options.data[e][t]=r[t]?r[t]:"";const s=createRow.call(n,e,n.options.data[e]);d[0]?Array.prototype.indexOf.call(n.tbody.children,d[0].element)>=0&&n.tbody.insertBefore(s.element,d[0].element):Array.prototype.indexOf.call(n.tbody.children,n.rows[t].element)>=0&&n.tbody.appendChild(s.element),p.push(n.records[e]),h.push(n.options.data[e]),m.push(s)}Array.prototype.push.apply(n.records,c),Array.prototype.push.apply(n.options.data,u),Array.prototype.push.apply(n.rows,d);for(let e=a;e<n.rows.length;e++)n.rows[e].y=e;for(let e=a;e<n.records.length;e++)for(let t=0;t<n.records[e].length;t++)n.records[e][t].y=e;n.options.pagination>0&&n.page(n.pageNumber),utils_history.Dh.call(n,{action:"insertRow",rowNumber:t,numOfRows:o,insertBefore:s,rowRecords:p,rowData:h,rowNode:m}),internal.o8.call(n),dispatch.A.call(n,"oninsertrow",n,i)}},moveRow=function(e,t,s){const n=this;if(n.options.mergeCells&&Object.keys(n.options.mergeCells).length>0){let s;if(s=e>t?1:0,merges.D0.call(n,e).length||merges.D0.call(n,t,s).length){if(!confirm(jSuites.translate("This action will destroy any existing merged cells. Are you sure?")))return!1;n.destroyMerge()}}if(1==n.options.search){if(n.results&&n.results.length!=n.rows.length){if(!confirm(jSuites.translate("This action will clear your search results. Are you sure?")))return!1;n.resetSearch()}n.results=null}s||(Array.prototype.indexOf.call(n.tbody.children,n.rows[t].element)>=0?e>t?n.tbody.insertBefore(n.rows[e].element,n.rows[t].element):n.tbody.insertBefore(n.rows[e].element,n.rows[t].element.nextSibling):n.tbody.removeChild(n.rows[e].element)),n.rows.splice(t,0,n.rows.splice(e,1)[0]),n.records.splice(t,0,n.records.splice(e,1)[0]),n.options.data.splice(t,0,n.options.data.splice(e,1)[0]);const o=Math.min(e,t),r=Math.max(e,t);for(let e=o;e<=r;e++)n.rows[e].y=e;for(let e=o;e<=r;e++)for(let t=0;t<n.records[e].length;t++)n.records[e][t].y=e;n.options.pagination>0&&n.tbody.children.length!=n.options.pagination&&n.page(n.pageNumber),utils_history.Dh.call(n,{action:"moveRow",oldValue:e,newValue:t}),internal.o8.call(n),dispatch.A.call(n,"onmoverow",n,parseInt(e),parseInt(t),1)},deleteRow=function(e,t){const s=this;if(0!=s.options.allowDeleteRow)if(1==s.options.allowDeletingAllRows||s.options.data.length>1){if(null==e){const n=selection.R5.call(s);0===n.length?(e=s.options.data.length-1,t=1):(e=n[0],t=n.length)}let n=s.options.data.length-1;(null==e||e>n||e<0)&&(e=n),t||(t=1),e+t>=s.options.data.length&&(t=s.options.data.length-e);const o=[];for(let s=0;s<t;s++)o.push(s+e);if(!1===dispatch.A.call(s,"onbeforedeleterow",s,o))return!1;if(parseInt(e)>-1){let r=!1;if(s.options.mergeCells&&Object.keys(s.options.mergeCells).length>0)for(let n=e;n<e+t;n++)merges.D0.call(s,n,!1).length&&(r=!0);if(r){if(!confirm(jSuites.translate("This action will destroy any existing merged cells. Are you sure?")))return!1;s.destroyMerge()}if(1==s.options.search){if(s.results&&s.results.length!=s.rows.length){if(!confirm(jSuites.translate("This action will clear your search results. Are you sure?")))return!1;s.resetSearch()}s.results=null}1!=s.options.allowDeletingAllRows&&n+1===t&&(t--,console.error("Jspreadsheet: It is not possible to delete the last row"));for(let n=e;n<e+t;n++)Array.prototype.indexOf.call(s.tbody.children,s.rows[n].element)>=0&&(s.rows[n].element.className="",s.rows[n].element.parentNode.removeChild(s.rows[n].element));const l=s.records.splice(e,t),i=s.options.data.splice(e,t),a=s.rows.splice(e,t);for(let t=e;t<s.rows.length;t++)s.rows[t].y=t;for(let t=e;t<s.records.length;t++)for(let e=0;e<s.records[t].length;e++)s.records[t][e].y=t;s.options.pagination>0&&s.tbody.children.length!=s.options.pagination&&s.page(s.pageNumber),selection.at.call(s,1,e,e+t-1),utils_history.Dh.call(s,{action:"deleteRow",rowNumber:e,numOfRows:t,insertBefore:1,rowRecords:l,rowData:i,rowNode:a}),internal.o8.call(s),dispatch.A.call(s,"ondeleterow",s,o)}}else console.error("Jspreadsheet: It is not possible to delete the last row")},getHeight=function(e){const t=this;let s;if(void 0===e){s=[];for(let e=0;e<t.rows.length;e++){const n=t.rows[e].element.style.height;n&&(s[e]=n)}}else"object"==typeof e&&(e=$(e).getAttribute("data-y")),s=t.rows[e].element.style.height;return s},setHeight=function(e,t,s){const n=this;t>0&&(s||(s=n.rows[e].element.getAttribute("height"))||(s=n.rows[e].element.getBoundingClientRect().height),t=parseInt(t),n.rows[e].element.style.height=t+"px",n.options.rows||(n.options.rows=[]),n.options.rows[e]||(n.options.rows[e]={}),n.options.rows[e].height=t,utils_history.Dh.call(n,{action:"setHeight",row:e,oldValue:s,newValue:t}),dispatch.A.call(n,"onresizerow",n,e,t,s),selection.Aq.call(n))},showRow=function(e){const t=this;Array.isArray(e)||(e=[e]),e.forEach((function(e){t.rows[e].element.style.display=""}))},hideRow=function(e){const t=this;Array.isArray(e)||(e=[e]),e.forEach((function(e){t.rows[e].element.style.display="none"}))},getRowData=function(e,t){return t?this.records[e].map((function(e){return e.element.innerHTML})):this.options.data[e]},setRowData=function(e,t,s){const n=this;for(let o=0;o<n.headers.length;o++){const r=(0,internalHelpers.t3)([o,e]);null!=t[o]&&n.setValue(r,t[o],s)}};var version={version:"5.0.0",host:"https://bossanova.uk/jspreadsheet",license:"MIT",print:function(){return[["Jspreadsheet CE",this.version,this.host,this.license].join("\r\n")]}};const getElement=function(e){let t=0,s=0;return function e(n){n.className&&(n.classList.contains("jss_container")&&(s=n),n.classList.contains("jss_spreadsheet")&&(s=n.querySelector(":scope > .jtabs-content > .jtabs-selected"))),"THEAD"==n.tagName?t=1:"TBODY"==n.tagName&&(t=2),n.parentNode&&(s||e(n.parentNode))}(e),[s,t]},mouseUpControls=function(e){if(libraryBase.jspreadsheet.current)if(libraryBase.jspreadsheet.current.resizing){if(libraryBase.jspreadsheet.current.resizing.column){const e=parseInt(libraryBase.jspreadsheet.current.cols[libraryBase.jspreadsheet.current.resizing.column].colElement.getAttribute("width")),t=libraryBase.jspreadsheet.current.getSelectedColumns();if(t.length>1){const s=[];for(let e=0;e<t.length;e++)s.push(parseInt(libraryBase.jspreadsheet.current.cols[t[e]].colElement.getAttribute("width")));s[t.indexOf(parseInt(libraryBase.jspreadsheet.current.resizing.column))]=libraryBase.jspreadsheet.current.resizing.width,setWidth.call(libraryBase.jspreadsheet.current,t,e,s)}else setWidth.call(libraryBase.jspreadsheet.current,parseInt(libraryBase.jspreadsheet.current.resizing.column),e,libraryBase.jspreadsheet.current.resizing.width);libraryBase.jspreadsheet.current.headers[libraryBase.jspreadsheet.current.resizing.column].classList.remove("resizing");for(let e=0;e<libraryBase.jspreadsheet.current.records.length;e++)libraryBase.jspreadsheet.current.records[e][libraryBase.jspreadsheet.current.resizing.column]&&libraryBase.jspreadsheet.current.records[e][libraryBase.jspreadsheet.current.resizing.column].element.classList.remove("resizing")}else{libraryBase.jspreadsheet.current.rows[libraryBase.jspreadsheet.current.resizing.row].element.children[0].classList.remove("resizing");let e=libraryBase.jspreadsheet.current.rows[libraryBase.jspreadsheet.current.resizing.row].element.getAttribute("height");setHeight.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.resizing.row,e,libraryBase.jspreadsheet.current.resizing.height),libraryBase.jspreadsheet.current.resizing.element.classList.remove("resizing")}libraryBase.jspreadsheet.current.resizing=null}else if(libraryBase.jspreadsheet.current.dragging){if(libraryBase.jspreadsheet.current.dragging){if(libraryBase.jspreadsheet.current.dragging.column){const t=e.target.getAttribute("data-x");libraryBase.jspreadsheet.current.headers[libraryBase.jspreadsheet.current.dragging.column].classList.remove("dragging");for(let e=0;e<libraryBase.jspreadsheet.current.rows.length;e++)libraryBase.jspreadsheet.current.records[e][libraryBase.jspreadsheet.current.dragging.column]&&libraryBase.jspreadsheet.current.records[e][libraryBase.jspreadsheet.current.dragging.column].element.classList.remove("dragging");for(let e=0;e<libraryBase.jspreadsheet.current.headers.length;e++)libraryBase.jspreadsheet.current.headers[e].classList.remove("dragging-left"),libraryBase.jspreadsheet.current.headers[e].classList.remove("dragging-right");t&&libraryBase.jspreadsheet.current.dragging.column!=libraryBase.jspreadsheet.current.dragging.destination&&libraryBase.jspreadsheet.current.moveColumn(libraryBase.jspreadsheet.current.dragging.column,libraryBase.jspreadsheet.current.dragging.destination)}else{let e;libraryBase.jspreadsheet.current.dragging.element.nextSibling?(e=parseInt(libraryBase.jspreadsheet.current.dragging.element.nextSibling.getAttribute("data-y")),libraryBase.jspreadsheet.current.dragging.row<e&&(e-=1)):e=parseInt(libraryBase.jspreadsheet.current.dragging.element.previousSibling.getAttribute("data-y")),libraryBase.jspreadsheet.current.dragging.row!=libraryBase.jspreadsheet.current.dragging.destination&&moveRow.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.dragging.row,e,!0),libraryBase.jspreadsheet.current.dragging.element.classList.remove("dragging")}libraryBase.jspreadsheet.current.dragging=null}}else libraryBase.jspreadsheet.current.selectedCorner&&(libraryBase.jspreadsheet.current.selectedCorner=!1,libraryBase.jspreadsheet.current.selection.length>0&&(selection.kF.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.selection[0],libraryBase.jspreadsheet.current.selection[libraryBase.jspreadsheet.current.selection.length-1]),selection.gG.call(libraryBase.jspreadsheet.current)));libraryBase.jspreadsheet.timeControl&&(clearTimeout(libraryBase.jspreadsheet.timeControl),libraryBase.jspreadsheet.timeControl=null),libraryBase.jspreadsheet.isMouseAction=!1},mouseDownControls=function(e){let t;t=(e=e||window.event).buttons?e.buttons:e.button?e.button:e.which;const s=getElement(e.target);if(s[0]?libraryBase.jspreadsheet.current!=s[0].jssWorksheet&&(libraryBase.jspreadsheet.current&&(libraryBase.jspreadsheet.current.edition&&closeEditor.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.edition[0],!0),libraryBase.jspreadsheet.current.resetSelection()),libraryBase.jspreadsheet.current=s[0].jssWorksheet):libraryBase.jspreadsheet.current&&(libraryBase.jspreadsheet.current.edition&&closeEditor.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.edition[0],!0),e.target.classList.contains("jss_object")||(selection.gE.call(libraryBase.jspreadsheet.current,!0),libraryBase.jspreadsheet.current=null)),libraryBase.jspreadsheet.current&&1==t){if(e.target.classList.contains("jss_selectall"))libraryBase.jspreadsheet.current&&selection.Ub.call(libraryBase.jspreadsheet.current);else if(e.target.classList.contains("jss_corner"))0!=libraryBase.jspreadsheet.current.options.editable&&(libraryBase.jspreadsheet.current.selectedCorner=!0);else{if(1==s[1]){const t=e.target.getAttribute("data-x");if(t){const s=e.target.getBoundingClientRect();if(0!=libraryBase.jspreadsheet.current.options.columnResize&&s.width-e.offsetX<6){libraryBase.jspreadsheet.current.resizing={mousePosition:e.pageX,column:t,width:s.width},libraryBase.jspreadsheet.current.headers[t].classList.add("resizing");for(let e=0;e<libraryBase.jspreadsheet.current.records.length;e++)libraryBase.jspreadsheet.current.records[e][t]&&libraryBase.jspreadsheet.current.records[e][t].element.classList.add("resizing")}else if(0!=libraryBase.jspreadsheet.current.options.columnDrag&&s.height-e.offsetY<6)if(merges.Lt.call(libraryBase.jspreadsheet.current,t).length)console.error("Jspreadsheet: This column is part of a merged cell.");else{libraryBase.jspreadsheet.current.resetSelection(),libraryBase.jspreadsheet.current.dragging={element:e.target,column:t,destination:t},libraryBase.jspreadsheet.current.headers[t].classList.add("dragging");for(let e=0;e<libraryBase.jspreadsheet.current.records.length;e++)libraryBase.jspreadsheet.current.records[e][t]&&libraryBase.jspreadsheet.current.records[e][t].element.classList.add("dragging")}else{let s,n;libraryBase.jspreadsheet.current.selectedHeader&&(e.shiftKey||e.ctrlKey)?(s=libraryBase.jspreadsheet.current.selectedHeader,n=t):(libraryBase.jspreadsheet.current.selectedHeader==t&&0!=libraryBase.jspreadsheet.current.options.allowRenameColumn&&(libraryBase.jspreadsheet.timeControl=setTimeout((function(){libraryBase.jspreadsheet.current.setHeader(t)}),800)),libraryBase.jspreadsheet.current.selectedHeader=t,s=t,n=t),selection.AH.call(libraryBase.jspreadsheet.current,s,0,n,libraryBase.jspreadsheet.current.options.data.length-1,e)}}else if(e.target.parentNode.classList.contains("jss_nested")){let t,s;if(e.target.getAttribute("data-column")){const n=e.target.getAttribute("data-column").split(",");t=parseInt(n[0]),s=parseInt(n[n.length-1])}else t=0,s=libraryBase.jspreadsheet.current.options.columns.length-1;selection.AH.call(libraryBase.jspreadsheet.current,t,0,s,libraryBase.jspreadsheet.current.options.data.length-1,e)}}else libraryBase.jspreadsheet.current.selectedHeader=!1;if(2==s[1]){const t=parseInt(e.target.getAttribute("data-y"));if(e.target.classList.contains("jss_row")){const s=e.target.getBoundingClientRect();if(0!=libraryBase.jspreadsheet.current.options.rowResize&&s.height-e.offsetY<6)libraryBase.jspreadsheet.current.resizing={element:e.target.parentNode,mousePosition:e.pageY,row:t,height:s.height},e.target.parentNode.classList.add("resizing");else if(0!=libraryBase.jspreadsheet.current.options.rowDrag&&s.width-e.offsetX<6)merges.D0.call(libraryBase.jspreadsheet.current,t).length?console.error("Jspreadsheet: This row is part of a merged cell"):1==libraryBase.jspreadsheet.current.options.search&&libraryBase.jspreadsheet.current.results?console.error("Jspreadsheet: Please clear your search before perform this action"):(libraryBase.jspreadsheet.current.resetSelection(),libraryBase.jspreadsheet.current.dragging={element:e.target.parentNode,row:t,destination:t},e.target.parentNode.classList.add("dragging"));else{let s,n;libraryBase.jspreadsheet.current.selectedRow&&(e.shiftKey||e.ctrlKey)?(s=libraryBase.jspreadsheet.current.selectedRow,n=t):(libraryBase.jspreadsheet.current.selectedRow=t,s=t,n=t),selection.AH.call(libraryBase.jspreadsheet.current,null,s,null,n,e)}}else if(e.target.classList.contains("jclose")&&e.target.clientWidth-e.offsetX<50&&e.offsetY<50)closeEditor.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.edition[0],!0);else{const t=function(e){const s=e.getAttribute("data-x"),n=e.getAttribute("data-y");return s&&n?[s,n]:e.parentNode?t(e.parentNode):void 0},s=t(e.target);if(s){const t=s[0],n=s[1];libraryBase.jspreadsheet.current.edition&&(libraryBase.jspreadsheet.current.edition[2]==t&&libraryBase.jspreadsheet.current.edition[3]==n||closeEditor.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.edition[0],!0)),libraryBase.jspreadsheet.current.edition||(e.shiftKey?selection.AH.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.selectedCell[0],libraryBase.jspreadsheet.current.selectedCell[1],t,n,e):selection.AH.call(libraryBase.jspreadsheet.current,t,n,void 0,void 0,e)),libraryBase.jspreadsheet.current.selectedHeader=null,libraryBase.jspreadsheet.current.selectedRow=null}}}else libraryBase.jspreadsheet.current.selectedRow=!1;e.target.classList.contains("jss_page")&&("<"==e.target.textContent?libraryBase.jspreadsheet.current.page(0):">"==e.target.textContent?libraryBase.jspreadsheet.current.page(e.target.getAttribute("title")-1):libraryBase.jspreadsheet.current.page(e.target.textContent-1))}libraryBase.jspreadsheet.current.edition?libraryBase.jspreadsheet.isMouseAction=!1:libraryBase.jspreadsheet.isMouseAction=!0}else libraryBase.jspreadsheet.isMouseAction=!1},mouseMoveControls=function(e){let t;if(t=(e=e||window.event).buttons?e.buttons:e.button?e.button:e.which,t||(libraryBase.jspreadsheet.isMouseAction=!1),libraryBase.jspreadsheet.current)if(1==libraryBase.jspreadsheet.isMouseAction){if(libraryBase.jspreadsheet.current.resizing)if(libraryBase.jspreadsheet.current.resizing.column){const t=e.pageX-libraryBase.jspreadsheet.current.resizing.mousePosition;if(libraryBase.jspreadsheet.current.resizing.width+t>0){const e=libraryBase.jspreadsheet.current.resizing.width+t;libraryBase.jspreadsheet.current.cols[libraryBase.jspreadsheet.current.resizing.column].colElement.setAttribute("width",e),selection.Aq.call(libraryBase.jspreadsheet.current)}}else{const t=e.pageY-libraryBase.jspreadsheet.current.resizing.mousePosition;if(libraryBase.jspreadsheet.current.resizing.height+t>0){const e=libraryBase.jspreadsheet.current.resizing.height+t;libraryBase.jspreadsheet.current.rows[libraryBase.jspreadsheet.current.resizing.row].element.setAttribute("height",e),selection.Aq.call(libraryBase.jspreadsheet.current)}}else if(libraryBase.jspreadsheet.current.dragging)if(libraryBase.jspreadsheet.current.dragging.column){const t=e.target.getAttribute("data-x");if(t)if(merges.Lt.call(libraryBase.jspreadsheet.current,t).length)console.error("Jspreadsheet: This column is part of a merged cell.");else{for(let e=0;e<libraryBase.jspreadsheet.current.headers.length;e++)libraryBase.jspreadsheet.current.headers[e].classList.remove("dragging-left"),libraryBase.jspreadsheet.current.headers[e].classList.remove("dragging-right");libraryBase.jspreadsheet.current.dragging.column==t?libraryBase.jspreadsheet.current.dragging.destination=parseInt(t):e.target.clientWidth/2>e.offsetX?(libraryBase.jspreadsheet.current.dragging.column<t?libraryBase.jspreadsheet.current.dragging.destination=parseInt(t)-1:libraryBase.jspreadsheet.current.dragging.destination=parseInt(t),libraryBase.jspreadsheet.current.headers[t].classList.add("dragging-left")):(libraryBase.jspreadsheet.current.dragging.column<t?libraryBase.jspreadsheet.current.dragging.destination=parseInt(t):libraryBase.jspreadsheet.current.dragging.destination=parseInt(t)+1,libraryBase.jspreadsheet.current.headers[t].classList.add("dragging-right"))}}else{const t=e.target.getAttribute("data-y");if(t)if(merges.D0.call(libraryBase.jspreadsheet.current,t).length)console.error("Jspreadsheet: This row is part of a merged cell.");else{const t=e.target.clientHeight/2>e.offsetY?e.target.parentNode.nextSibling:e.target.parentNode;libraryBase.jspreadsheet.current.dragging.element!=t&&(e.target.parentNode.parentNode.insertBefore(libraryBase.jspreadsheet.current.dragging.element,t),libraryBase.jspreadsheet.current.dragging.destination=Array.prototype.indexOf.call(libraryBase.jspreadsheet.current.dragging.element.parentNode.children,libraryBase.jspreadsheet.current.dragging.element))}}}else{const t=e.target.getAttribute("data-x"),s=e.target.getAttribute("data-y"),n=e.target.getBoundingClientRect();libraryBase.jspreadsheet.current.cursor&&(libraryBase.jspreadsheet.current.cursor.style.cursor="",libraryBase.jspreadsheet.current.cursor=null),e.target.parentNode.parentNode&&e.target.parentNode.parentNode.className&&(e.target.parentNode.parentNode.classList.contains("resizable")&&(e.target&&t&&!s&&n.width-(e.clientX-n.left)<6?(libraryBase.jspreadsheet.current.cursor=e.target,libraryBase.jspreadsheet.current.cursor.style.cursor="col-resize"):e.target&&!t&&s&&n.height-(e.clientY-n.top)<6&&(libraryBase.jspreadsheet.current.cursor=e.target,libraryBase.jspreadsheet.current.cursor.style.cursor="row-resize")),e.target.parentNode.parentNode.classList.contains("draggable")&&(e.target&&!t&&s&&n.width-(e.clientX-n.left)<6||e.target&&t&&!s&&n.height-(e.clientY-n.top)<6)&&(libraryBase.jspreadsheet.current.cursor=e.target,libraryBase.jspreadsheet.current.cursor.style.cursor="move"))}},updateCopySelection=function(e,t){const s=this;selection.gG.call(s);const n=s.selectedContainer[0],o=s.selectedContainer[1],r=s.selectedContainer[2],l=s.selectedContainer[3];if(null!=e&&null!=t){let i,a,c,u;e-r>0?(i=parseInt(r)+1,a=parseInt(e)):(i=parseInt(e),a=parseInt(n)-1),t-l>0?(c=parseInt(l)+1,u=parseInt(t)):(c=parseInt(t),u=parseInt(o)-1),a-i<=u-c?(i=parseInt(n),a=parseInt(r)):(c=parseInt(o),u=parseInt(l));for(let e=c;e<=u;e++)for(let t=i;t<=a;t++)s.records[e][t]&&"none"!=s.rows[e].element.style.display&&"none"!=s.records[e][t].element.style.display&&(s.records[e][t].element.classList.add("selection"),s.records[c][t].element.classList.add("selection-top"),s.records[u][t].element.classList.add("selection-bottom"),s.records[e][i].element.classList.add("selection-left"),s.records[e][a].element.classList.add("selection-right"),s.selection.push(s.records[e][t].element))}},mouseOverControls=function(e){let t;if(t=(e=e||window.event).buttons?e.buttons:e.button?e.button:e.which,t||(libraryBase.jspreadsheet.isMouseAction=!1),libraryBase.jspreadsheet.current&&1==libraryBase.jspreadsheet.isMouseAction){const t=getElement(e.target);if(t[0]){if(libraryBase.jspreadsheet.current!=t[0].jssWorksheet&&libraryBase.jspreadsheet.current)return!1;let s=e.target.getAttribute("data-x");const n=e.target.getAttribute("data-y");if(libraryBase.jspreadsheet.current.resizing||libraryBase.jspreadsheet.current.dragging);else{if(1==t[1]&&libraryBase.jspreadsheet.current.selectedHeader){s=e.target.getAttribute("data-x");const t=libraryBase.jspreadsheet.current.selectedHeader,n=s;selection.AH.call(libraryBase.jspreadsheet.current,t,0,n,libraryBase.jspreadsheet.current.options.data.length-1,e)}if(2==t[1])if(e.target.classList.contains("jss_row")){if(libraryBase.jspreadsheet.current.selectedRow){const t=libraryBase.jspreadsheet.current.selectedRow,s=n;selection.AH.call(libraryBase.jspreadsheet.current,0,t,libraryBase.jspreadsheet.current.options.data[0].length-1,s,e)}}else libraryBase.jspreadsheet.current.edition||s&&n&&(libraryBase.jspreadsheet.current.selectedCorner?updateCopySelection.call(libraryBase.jspreadsheet.current,s,n):libraryBase.jspreadsheet.current.selectedCell&&selection.AH.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.selectedCell[0],libraryBase.jspreadsheet.current.selectedCell[1],s,n,e))}}}libraryBase.jspreadsheet.timeControl&&(clearTimeout(libraryBase.jspreadsheet.timeControl),libraryBase.jspreadsheet.timeControl=null)},doubleClickControls=function(e){if(libraryBase.jspreadsheet.current)if(e.target.classList.contains("jss_corner")){if(libraryBase.jspreadsheet.current.highlighted.length>0){const e=libraryBase.jspreadsheet.current.highlighted[0].element.getAttribute("data-x"),t=parseInt(libraryBase.jspreadsheet.current.highlighted[libraryBase.jspreadsheet.current.highlighted.length-1].element.getAttribute("data-y"))+1,s=libraryBase.jspreadsheet.current.highlighted[libraryBase.jspreadsheet.current.highlighted.length-1].element.getAttribute("data-x"),n=libraryBase.jspreadsheet.current.records.length-1;selection.kF.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.records[t][e].element,libraryBase.jspreadsheet.current.records[n][s].element)}}else if(e.target.classList.contains("jss_column_filter")){const t=e.target.getAttribute("data-x");filter.N$.call(libraryBase.jspreadsheet.current,t)}else{const t=getElement(e.target);if(1==t[1]&&0!=libraryBase.jspreadsheet.current.options.columnSorting){const t=e.target.getAttribute("data-x");t&&libraryBase.jspreadsheet.current.orderBy(parseInt(t))}if(2==t[1]&&0!=libraryBase.jspreadsheet.current.options.editable&&!libraryBase.jspreadsheet.current.edition){const t=function(e){if(e.parentNode){const s=e.getAttribute("data-x"),n=e.getAttribute("data-y");return s&&n?e:t(e.parentNode)}},s=t(e.target);s&&s.classList.contains("highlight")&&openEditor.call(libraryBase.jspreadsheet.current,s,void 0,e)}}},pasteControls=function(e){libraryBase.jspreadsheet.current&&libraryBase.jspreadsheet.current.selectedCell&&(libraryBase.jspreadsheet.current.edition||0!=libraryBase.jspreadsheet.current.options.editable&&(e&&e.clipboardData?(paste.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.selectedCell[0],libraryBase.jspreadsheet.current.selectedCell[1],e.clipboardData.getData("text")),e.preventDefault()):window.clipboardData&&paste.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.selectedCell[0],libraryBase.jspreadsheet.current.selectedCell[1],window.clipboardData.getData("text"))))},getRole=function(e){if(e.classList.contains("jss_selectall"))return"select-all";if(e.classList.contains("jss_corner"))return"fill-handle";let t=e;for(;!t.classList.contains("jss_spreadsheet");){if(t.classList.contains("jss_row"))return"row";if(t.classList.contains("jss_nested"))return"nested";if(t.classList.contains("jtabs-headers"))return"tabs";if(t.classList.contains("jtoolbar"))return"toolbar";if(t.classList.contains("jss_pagination"))return"pagination";if("TBODY"===t.tagName)return"cell";if("TFOOT"===t.tagName)return 0===getElementIndex(e)?"grid":"footer";if("THEAD"===t.tagName)return"header";t=t.parentElement}return"applications"},defaultContextMenu=function(e,t,s,n){const o=[];if("header"===n&&(0!=e.options.allowInsertColumn&&o.push({title:jSuites.translate("Insert a new column before"),onclick:function(){e.insertColumn(1,parseInt(t),1)}}),0!=e.options.allowInsertColumn&&o.push({title:jSuites.translate("Insert a new column after"),onclick:function(){e.insertColumn(1,parseInt(t),0)}}),0!=e.options.allowDeleteColumn&&o.push({title:jSuites.translate("Delete selected columns"),onclick:function(){e.deleteColumn(e.getSelectedColumns().length?void 0:parseInt(t))}}),0!=e.options.allowRenameColumn&&o.push({title:jSuites.translate("Rename this column"),onclick:function(){const s=e.getHeader(t),n=prompt(jSuites.translate("Column name"),s);e.setHeader(t,n)}}),0!=e.options.columnSorting&&(o.push({type:"line"}),o.push({title:jSuites.translate("Order ascending"),onclick:function(){e.orderBy(t,0)}}),o.push({title:jSuites.translate("Order descending"),onclick:function(){e.orderBy(t,1)}}))),"row"!==n&&"cell"!==n||(0!=e.options.allowInsertRow&&(o.push({title:jSuites.translate("Insert a new row before"),onclick:function(){e.insertRow(1,parseInt(s),1)}}),o.push({title:jSuites.translate("Insert a new row after"),onclick:function(){e.insertRow(1,parseInt(s))}})),0!=e.options.allowDeleteRow&&o.push({title:jSuites.translate("Delete selected rows"),onclick:function(){e.deleteRow(e.getSelectedRows().length?void 0:parseInt(s))}})),"cell"===n&&0!=e.options.allowComments){o.push({type:"line"});const n=e.records[s][t].element.getAttribute("title")||"";o.push({title:jSuites.translate(n?"Edit comments":"Add comments"),onclick:function(){const o=prompt(jSuites.translate("Comments"),n);o&&e.setComments((0,helpers.getCellNameFromCoords)(t,s),o)}}),n&&o.push({title:jSuites.translate("Clear comments"),onclick:function(){e.setComments((0,helpers.getCellNameFromCoords)(t,s),"")}})}return 0!==o.length&&o.push({type:"line"}),"header"!==n&&"row"!==n&&"cell"!==n||(o.push({title:jSuites.translate("Copy")+"...",shortcut:"Ctrl + C",onclick:function(){copy.call(e,!0)}}),navigator&&navigator.clipboard&&o.push({title:jSuites.translate("Paste")+"...",shortcut:"Ctrl + V",onclick:function(){e.selectedCell&&navigator.clipboard.readText().then((function(t){t&&paste.call(e,e.selectedCell[0],e.selectedCell[1],t)}))}})),0!=e.parent.config.allowExport&&o.push({title:jSuites.translate("Save as")+"...",shortcut:"Ctrl + S",onclick:function(){e.download()}}),0!=e.parent.config.about&&o.push({title:jSuites.translate("About"),onclick:function(){void 0===e.parent.config.about||!0===e.parent.config.about?alert(version.print()):alert(e.parent.config.about)}}),o},getElementIndex=function(e){const t=e.parentElement.children;for(let s=0;s<t.length;s++)if(e===t[s])return s;return-1},contextMenuControls=function(e){if("buttons"in(e=e||window.event)?e.buttons:e.which||e.button,libraryBase.jspreadsheet.current){const t=libraryBase.jspreadsheet.current.parent;if(libraryBase.jspreadsheet.current.edition)e.preventDefault();else if(t.contextMenu.contextmenu.close(),libraryBase.jspreadsheet.current){const s=getRole(e.target);let n=null,o=null;if("cell"===s){let t=e.target;for(;"TD"!==t.tagName;)t=t.parentNode;o=t.getAttribute("data-y"),n=t.getAttribute("data-x"),(!libraryBase.jspreadsheet.current.selectedCell||n<parseInt(libraryBase.jspreadsheet.current.selectedCell[0])||n>parseInt(libraryBase.jspreadsheet.current.selectedCell[2])||o<parseInt(libraryBase.jspreadsheet.current.selectedCell[1])||o>parseInt(libraryBase.jspreadsheet.current.selectedCell[3]))&&selection.AH.call(libraryBase.jspreadsheet.current,n,o,n,o,e)}else if("row"===s||"header"===s)"row"===s?o=e.target.getAttribute("data-y"):n=e.target.getAttribute("data-x"),(!libraryBase.jspreadsheet.current.selectedCell||n<parseInt(libraryBase.jspreadsheet.current.selectedCell[0])||n>parseInt(libraryBase.jspreadsheet.current.selectedCell[2])||o<parseInt(libraryBase.jspreadsheet.current.selectedCell[1])||o>parseInt(libraryBase.jspreadsheet.current.selectedCell[3]))&&selection.AH.call(libraryBase.jspreadsheet.current,n,o,n,o,e);else if("nested"===s){const t=e.target.getAttribute("data-column").split(",");n=getElementIndex(e.target)-1,o=getElementIndex(e.target.parentElement),libraryBase.jspreadsheet.current.selectedCell&&t[0]==parseInt(libraryBase.jspreadsheet.current.selectedCell[0])&&t[t.length-1]==parseInt(libraryBase.jspreadsheet.current.selectedCell[2])&&null==libraryBase.jspreadsheet.current.selectedCell[1]&&null==libraryBase.jspreadsheet.current.selectedCell[3]||selection.AH.call(libraryBase.jspreadsheet.current,t[0],null,t[t.length-1],null,e)}else"select-all"===s?selection.Ub.call(libraryBase.jspreadsheet.current):"tabs"===s?n=getElementIndex(e.target):"footer"===s&&(n=getElementIndex(e.target)-1,o=getElementIndex(e.target.parentElement));let r=defaultContextMenu(libraryBase.jspreadsheet.current,parseInt(n),parseInt(o),s);if("function"==typeof t.config.contextMenu){const l=t.config.contextMenu(libraryBase.jspreadsheet.current,n,o,e,r,s,n,o);if(l)r=l;else if(!1===l)return}"object"==typeof t.plugins&&Object.entries(t.plugins).forEach((function([,t]){if("function"==typeof t.contextMenu){const l=t.contextMenu(libraryBase.jspreadsheet.current,null!==n?parseInt(n):null,null!==o?parseInt(o):null,e,r,s,null!==n?parseInt(n):null,null!==o?parseInt(o):null);l&&(r=l)}})),t.contextMenu.contextmenu.open(e,r),e.preventDefault()}}},touchStartControls=function(e){const t=getElement(e.target);if(t[0]?libraryBase.jspreadsheet.current!=t[0].jssWorksheet&&(libraryBase.jspreadsheet.current&&libraryBase.jspreadsheet.current.resetSelection(),libraryBase.jspreadsheet.current=t[0].jssWorksheet):libraryBase.jspreadsheet.current&&(libraryBase.jspreadsheet.current.resetSelection(),libraryBase.jspreadsheet.current=null),libraryBase.jspreadsheet.current&&!libraryBase.jspreadsheet.current.edition){const t=e.target.getAttribute("data-x"),s=e.target.getAttribute("data-y");t&&s&&(selection.AH.call(libraryBase.jspreadsheet.current,t,s,void 0,void 0,e),libraryBase.jspreadsheet.timeControl=setTimeout((function(){"color"==libraryBase.jspreadsheet.current.options.columns[t].type?libraryBase.jspreadsheet.tmpElement=null:libraryBase.jspreadsheet.tmpElement=e.target,openEditor.call(libraryBase.jspreadsheet.current,e.target,!1,e)}),500))}},touchEndControls=function(e){libraryBase.jspreadsheet.timeControl&&(clearTimeout(libraryBase.jspreadsheet.timeControl),libraryBase.jspreadsheet.timeControl=null,libraryBase.jspreadsheet.tmpElement&&"INPUT"==libraryBase.jspreadsheet.tmpElement.children[0].tagName&&libraryBase.jspreadsheet.tmpElement.children[0].focus(),libraryBase.jspreadsheet.tmpElement=null)},cutControls=function(e){libraryBase.jspreadsheet.current&&(libraryBase.jspreadsheet.current.edition||(copy.call(libraryBase.jspreadsheet.current,!0,void 0,void 0,void 0,void 0,!0),0!=libraryBase.jspreadsheet.current.options.editable&&libraryBase.jspreadsheet.current.setValue(libraryBase.jspreadsheet.current.highlighted.map((function(e){return e.element})),"")))},copyControls=function(e){libraryBase.jspreadsheet.current&&copyControls.enabled&&(libraryBase.jspreadsheet.current.edition||copy.call(libraryBase.jspreadsheet.current,!0))},validLetter=function(e){return e.match(/([\u0041-\u005A\u0061-\u007A\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC-\u0400-\u04FF']+)/g)?1:0},keyDownControls=function(e){if(libraryBase.jspreadsheet.current){if(libraryBase.jspreadsheet.current.edition)if(27==e.which)libraryBase.jspreadsheet.current.edition&&closeEditor.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.edition[0],!1),e.preventDefault();else if(13==e.which)if(libraryBase.jspreadsheet.current.options.columns&&libraryBase.jspreadsheet.current.options.columns[libraryBase.jspreadsheet.current.edition[2]]&&"calendar"==libraryBase.jspreadsheet.current.options.columns[libraryBase.jspreadsheet.current.edition[2]].type)closeEditor.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.edition[0],!0);else if(libraryBase.jspreadsheet.current.options.columns&&libraryBase.jspreadsheet.current.options.columns[libraryBase.jspreadsheet.current.edition[2]]&&"dropdown"==libraryBase.jspreadsheet.current.options.columns[libraryBase.jspreadsheet.current.edition[2]].type);else if((1==libraryBase.jspreadsheet.current.options.wordWrap||libraryBase.jspreadsheet.current.options.columns&&libraryBase.jspreadsheet.current.options.columns[libraryBase.jspreadsheet.current.edition[2]]&&1==libraryBase.jspreadsheet.current.options.columns[libraryBase.jspreadsheet.current.edition[2]].wordWrap||libraryBase.jspreadsheet.current.options.data[libraryBase.jspreadsheet.current.edition[3]][libraryBase.jspreadsheet.current.edition[2]]&&libraryBase.jspreadsheet.current.options.data[libraryBase.jspreadsheet.current.edition[3]][libraryBase.jspreadsheet.current.edition[2]].length>200)&&e.altKey){const e=libraryBase.jspreadsheet.current.edition[0].children[0];let t=libraryBase.jspreadsheet.current.edition[0].children[0].value;const s=e.selectionStart;t=t.slice(0,s)+"\n"+t.slice(s),e.value=t,e.focus(),e.selectionStart=s+1,e.selectionEnd=s+1}else libraryBase.jspreadsheet.current.edition[0].children[0].blur();else 9==e.which&&(libraryBase.jspreadsheet.current.options.columns&&libraryBase.jspreadsheet.current.options.columns[libraryBase.jspreadsheet.current.edition[2]]&&["calendar","html"].includes(libraryBase.jspreadsheet.current.options.columns[libraryBase.jspreadsheet.current.edition[2]].type)?closeEditor.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.edition[0],!0):libraryBase.jspreadsheet.current.edition[0].children[0].blur());if(!libraryBase.jspreadsheet.current.edition&&libraryBase.jspreadsheet.current.selectedCell)if(37==e.which)left.call(libraryBase.jspreadsheet.current,e.shiftKey,e.ctrlKey),e.preventDefault();else if(39==e.which)right.call(libraryBase.jspreadsheet.current,e.shiftKey,e.ctrlKey),e.preventDefault();else if(38==e.which)up.call(libraryBase.jspreadsheet.current,e.shiftKey,e.ctrlKey),e.preventDefault();else if(40==e.which)down.call(libraryBase.jspreadsheet.current,e.shiftKey,e.ctrlKey),e.preventDefault();else if(36==e.which)first.call(libraryBase.jspreadsheet.current,e.shiftKey,e.ctrlKey),e.preventDefault();else if(35==e.which)last.call(libraryBase.jspreadsheet.current,e.shiftKey,e.ctrlKey),e.preventDefault();else if(46==e.which||8==e.which)0!=libraryBase.jspreadsheet.current.options.editable&&(libraryBase.jspreadsheet.current.selectedRow?0!=libraryBase.jspreadsheet.current.options.allowDeleteRow&&confirm(jSuites.translate("Are you sure to delete the selected rows?"))&&libraryBase.jspreadsheet.current.deleteRow():libraryBase.jspreadsheet.current.selectedHeader?0!=libraryBase.jspreadsheet.current.options.allowDeleteColumn&&confirm(jSuites.translate("Are you sure to delete the selected columns?"))&&libraryBase.jspreadsheet.current.deleteColumn():libraryBase.jspreadsheet.current.setValue(libraryBase.jspreadsheet.current.highlighted.map((function(e){return e.element})),""));else if(13==e.which)e.shiftKey?up.call(libraryBase.jspreadsheet.current):(0!=libraryBase.jspreadsheet.current.options.allowInsertRow&&0!=libraryBase.jspreadsheet.current.options.allowManualInsertRow&&libraryBase.jspreadsheet.current.selectedCell[1]==libraryBase.jspreadsheet.current.options.data.length-1&&libraryBase.jspreadsheet.current.insertRow(),down.call(libraryBase.jspreadsheet.current)),e.preventDefault();else if(9==e.which)e.shiftKey?left.call(libraryBase.jspreadsheet.current):(0!=libraryBase.jspreadsheet.current.options.allowInsertColumn&&0!=libraryBase.jspreadsheet.current.options.allowManualInsertColumn&&libraryBase.jspreadsheet.current.selectedCell[0]==libraryBase.jspreadsheet.current.options.data[0].length-1&&libraryBase.jspreadsheet.current.insertColumn(),right.call(libraryBase.jspreadsheet.current)),e.preventDefault();else if(!e.ctrlKey&&!e.metaKey||e.shiftKey){if(libraryBase.jspreadsheet.current.selectedCell&&0!=libraryBase.jspreadsheet.current.options.editable){const t=libraryBase.jspreadsheet.current.selectedCell[1],s=libraryBase.jspreadsheet.current.selectedCell[0];32==e.keyCode?(e.preventDefault(),"checkbox"==libraryBase.jspreadsheet.current.options.columns[s].type||"radio"==libraryBase.jspreadsheet.current.options.columns[s].type?setCheckRadioValue.call(libraryBase.jspreadsheet.current):openEditor.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.records[t][s].element,!0,e)):113==e.keyCode?openEditor.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.records[t][s].element,!1,e):(8==e.keyCode||e.keyCode>=48&&e.keyCode<=57||e.keyCode>=96&&e.keyCode<=111||e.keyCode>=187&&e.keyCode<=190||(String.fromCharCode(e.keyCode)==e.key||String.fromCharCode(e.keyCode).toLowerCase()==e.key.toLowerCase())&&validLetter(String.fromCharCode(e.keyCode)))&&(openEditor.call(libraryBase.jspreadsheet.current,libraryBase.jspreadsheet.current.records[t][s].element,!0,e),libraryBase.jspreadsheet.current.options.columns&&libraryBase.jspreadsheet.current.options.columns[s]&&"calendar"==libraryBase.jspreadsheet.current.options.columns[s].type&&e.preventDefault())}}else 65==e.which?(selection.Ub.call(libraryBase.jspreadsheet.current),e.preventDefault()):83==e.which?(libraryBase.jspreadsheet.current.download(),e.preventDefault()):89==e.which?(libraryBase.jspreadsheet.current.redo(),e.preventDefault()):90==e.which?(libraryBase.jspreadsheet.current.undo(),e.preventDefault()):67==e.which?(copy.call(libraryBase.jspreadsheet.current,!0),e.preventDefault()):88==e.which?(0!=libraryBase.jspreadsheet.current.options.editable?cutControls():copyControls(),e.preventDefault()):86==e.which&&pasteControls();else e.target.classList.contains("jss_search")&&(libraryBase.jspreadsheet.timeControl&&clearTimeout(libraryBase.jspreadsheet.timeControl),libraryBase.jspreadsheet.timeControl=setTimeout((function(){libraryBase.jspreadsheet.current.search(e.target.value)}),200))}},wheelControls=function(e){const t=this;1==t.options.lazyLoading&&null==libraryBase.jspreadsheet.timeControlLoading&&(libraryBase.jspreadsheet.timeControlLoading=setTimeout((function(){t.content.scrollTop+t.content.clientHeight>=t.content.scrollHeight-10?lazyLoading.p6.call(t)&&(t.content.scrollTop+t.content.clientHeight>t.content.scrollHeight-10&&(t.content.scrollTop=t.content.scrollTop-t.content.clientHeight),selection.Aq.call(t)):t.content.scrollTop<=t.content.clientHeight&&lazyLoading.G_.call(t)&&(t.content.scrollTop<10&&(t.content.scrollTop=t.content.scrollTop+t.content.clientHeight),selection.Aq.call(t)),libraryBase.jspreadsheet.timeControlLoading=null}),100))};let scrollLeft=0;const updateFreezePosition=function(){const e=this;scrollLeft=e.content.scrollLeft;let t=0;if(scrollLeft>50)for(let s=0;s<e.options.freezeColumns;s++){if(s>0&&(!e.options.columns||!e.options.columns[s-1]||"hidden"!==e.options.columns[s-1].type)){let n;n=e.options.columns&&e.options.columns[s-1]&&void 0!==e.options.columns[s-1].width?parseInt(e.options.columns[s-1].width):void 0!==e.options.defaultColWidth?parseInt(e.options.defaultColWidth):100,t+=parseInt(n)}e.headers[s].classList.add("jss_freezed"),e.headers[s].style.left=t+"px";for(let t=0;t<e.rows.length;t++)if(e.rows[t]&&e.records[t][s]){const n=scrollLeft+(s>0?e.records[t][s-1].element.style.width:0)-51+"px";e.records[t][s].element.classList.add("jss_freezed"),e.records[t][s].element.style.left=n}}else for(let t=0;t<e.options.freezeColumns;t++){e.headers[t].classList.remove("jss_freezed"),e.headers[t].style.left="";for(let s=0;s<e.rows.length;s++)e.records[s][t]&&(e.records[s][t].element.classList.remove("jss_freezed"),e.records[s][t].element.style.left="")}selection.Aq.call(e)},scrollControls=function(e){const t=this;wheelControls.call(t),t.options.freezeColumns>0&&t.content.scrollLeft!=scrollLeft&&updateFreezePosition.call(t),1!=t.options.lazyLoading&&1!=t.options.tableOverflow||t.edition&&"jdropdown"!=e.target.className.substr(0,9)&&closeEditor.call(t,t.edition[0],!0)},setEvents=function(e){destroyEvents(e),e.addEventListener("mouseup",mouseUpControls),e.addEventListener("mousedown",mouseDownControls),e.addEventListener("mousemove",mouseMoveControls),e.addEventListener("mouseover",mouseOverControls),e.addEventListener("dblclick",doubleClickControls),e.addEventListener("paste",pasteControls),e.addEventListener("contextmenu",contextMenuControls),e.addEventListener("touchstart",touchStartControls),e.addEventListener("touchend",touchEndControls),e.addEventListener("touchcancel",touchEndControls),e.addEventListener("touchmove",touchEndControls),document.addEventListener("keydown",keyDownControls)},destroyEvents=function(e){e.removeEventListener("mouseup",mouseUpControls),e.removeEventListener("mousedown",mouseDownControls),e.removeEventListener("mousemove",mouseMoveControls),e.removeEventListener("mouseover",mouseOverControls),e.removeEventListener("dblclick",doubleClickControls),e.removeEventListener("paste",pasteControls),e.removeEventListener("contextmenu",contextMenuControls),e.removeEventListener("touchstart",touchStartControls),e.removeEventListener("touchend",touchEndControls),e.removeEventListener("touchcancel",touchEndControls),document.removeEventListener("keydown",keyDownControls)};var toolbar=__webpack_require__(845),pagination=__webpack_require__(292);const setData=function(e){const t=this;if(e&&(t.options.data=e),t.options.data||(t.options.data=[]),t.options.data&&t.options.data[0]&&!Array.isArray(t.options.data[0])){e=[];for(let s=0;s<t.options.data.length;s++){const n=[];for(let e=0;e<t.options.columns.length;e++)n[e]=t.options.data[s][t.options.columns[e].name];e.push(n)}t.options.data=e}let s=0,n=0;const o=t.options.columns&&t.options.columns.length||0,r=t.options.data.length,l=t.options.minDimensions[0],i=t.options.minDimensions[1],a=l>o?l:o,c=i>r?i:r;for(s=0;s<c;s++)for(n=0;n<a;n++)null==t.options.data[s]&&(t.options.data[s]=[]),null==t.options.data[s][n]&&(t.options.data[s][n]="");let u,d;for(t.rows=[],t.results=null,t.records=[],t.history=[],t.historyIndex=-1,t.tbody.innerHTML="",1==t.options.lazyLoading?(u=0,d=t.options.data.length<100?t.options.data.length:100,t.options.pagination&&(t.options.pagination=!1,console.error("Jspreadsheet: Pagination will be disable due the lazyLoading"))):t.options.pagination?(t.pageNumber||(t.pageNumber=0),t.options.pagination,u=t.options.pagination*t.pageNumber,d=t.options.pagination*t.pageNumber+t.options.pagination,t.options.data.length<d&&(d=t.options.data.length)):(u=0,d=t.options.data.length),s=0;s<t.options.data.length;s++){const e=createRow.call(t,s,t.options.data[s]);s>=u&&s<d&&t.tbody.appendChild(e.element)}if(1==t.options.lazyLoading||t.options.pagination&&pagination.IV.call(t),t.options.mergeCells){const e=Object.keys(t.options.mergeCells);for(let s=0;s<e.length;s++){const n=t.options.mergeCells[e[s]];merges.FU.call(t,e[s],n[0],n[1],1)}}internal.am.call(t)},getValue=function(e,t){const s=this;let n,o;if("string"!=typeof e)return null;n=(e=(0,internalHelpers.vu)(e,!0))[0],o=e[1];let r=null;return null!=n&&null!=o&&(s.records[o]&&s.records[o][n]&&t?r=s.records[o][n].element.innerHTML:s.options.data[o]&&"undefined"!=s.options.data[o][n]&&(r=s.options.data[o][n])),r},getValueFromCoords=function(e,t,s){const n=this;let o=null;return null!=e&&null!=t&&(n.records[t]&&n.records[t][e]&&s?o=n.records[t][e].element.innerHTML:n.options.data[t]&&"undefined"!=n.options.data[t][e]&&(o=n.options.data[t][e])),o},setValue=function(e,t,s){const n=this,o=[];if("string"==typeof e){const r=(0,internalHelpers.vu)(e,!0),l=r[0],i=r[1];o.push(internal.k9.call(n,l,i,t,s)),internal.xF.call(n,l,i,o)}else{let r=null,l=null;if(e&&e.getAttribute&&(r=e.getAttribute("data-x"),l=e.getAttribute("data-y")),null!=r&&null!=l)o.push(internal.k9.call(n,r,l,t,s)),internal.xF.call(n,r,l,o);else{const r=Object.keys(e);if(r.length>0)for(let l=0;l<r.length;l++){let r,i;if("string"==typeof e[l]){const t=(0,internalHelpers.vu)(e[l],!0);r=t[0],i=t[1]}else null!=e[l].x&&null!=e[l].y?(r=e[l].x,i=e[l].y,null!=e[l].value&&(t=e[l].value)):(r=e[l].getAttribute("data-x"),i=e[l].getAttribute("data-y"));null!=r&&null!=i&&(o.push(internal.k9.call(n,r,i,t,s)),internal.xF.call(n,r,i,o))}}}utils_history.Dh.call(n,{action:"setValue",records:o,selection:n.selectedCell}),internal.am.call(n);const r=o.map((function(e){return{x:e.x,y:e.y,value:e.newValue,oldValue:e.oldValue}}));dispatch.A.call(n,"onafterchanges",n,r)},setValueFromCoords=function(e,t,s,n){const o=this,r=[];r.push(internal.k9.call(o,e,t,s,n)),internal.xF.call(o,e,t,r),utils_history.Dh.call(o,{action:"setValue",records:r,selection:o.selectedCell}),internal.am.call(o);const l=r.map((function(e){return{x:e.x,y:e.y,value:e.newValue,oldValue:e.oldValue}}));dispatch.A.call(o,"onafterchanges",o,l)},getData=function(e,t,s,n){const o=this,r=[];let l=0,i=0;const a=Math.max(...o.options.data.map((function(e){return e.length}))),c=o.options.data.length;for(let s=0;s<c;s++){l=0;for(let n=0;n<a;n++)e&&!o.records[s][n].element.classList.contains("highlight")||(r[i]||(r[i]=[]),r[i][l]=t?o.records[s][n].element.innerHTML:o.options.data[s][n],l++);l>0&&i++}return s?r.map((function(e){return e.join(s)})).join("\r\n")+"\r\n":n?r.map((function(e){const t={};return e.forEach((function(e,s){t[s]=e})),t})):r},getDataFromRange=function(e,t){const s=this,n=(0,helpers.getCoordsFromRange)(e),o=[];for(let e=n[1];e<=n[3];e++){o.push([]);for(let r=n[0];r<=n[2];r++)t?o[o.length-1].push(s.records[e][r].element.innerHTML):o[o.length-1].push(s.options.data[e][r])}return o},search=function(e){const t=this;if(t.options.filters&&filter.dr.call(t),t.resetSelection(),t.pageNumber=0,t.results=[],e){t.searchInput.value!==e&&(t.searchInput.value=e);const s=function(e,s,n){for(let o=0;o<e.length;o++)if((""+e[o]).toLowerCase().search(s)>=0||(""+t.records[n][o].element.innerHTML).toLowerCase().search(s)>=0)return!0;return!1},n=function(e){-1==t.results.indexOf(e)&&t.results.push(e)};let o=e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&");o=new RegExp(o,"i"),t.options.data.forEach((function(e,r){if(s(e,o,r)){const e=merges.D0.call(t,r);if(e.length)for(let s=0;s<e.length;s++){const o=(0,internalHelpers.vu)(e[s],!0);for(let r=0;r<t.options.mergeCells[e[s]][1];r++)n(o[1]+r)}else n(r)}}))}else t.results=null;internal.hG.call(t)},resetSearch=function(){const e=this;e.searchInput.value="",e.search(""),e.results=null},getHeader=function(e){return this.headers[e].textContent},getHeaders=function(e){const t=this,s=[];for(let e=0;e<t.headers.length;e++)s.push(t.getHeader(e));return e?s:s.join(t.options.csvDelimiter)},setHeader=function(e,t){const s=this;if(s.headers[e]){const n=s.headers[e].textContent,o=s.options.columns&&s.options.columns[e]&&s.options.columns[e].title||"";t||(t=(0,helpers.getColumnName)(e)),s.headers[e].textContent=t,s.headers[e].setAttribute("title",t),s.options.columns||(s.options.columns=[]),s.options.columns[e]||(s.options.columns[e]={}),s.options.columns[e].title=t,utils_history.Dh.call(s,{action:"setHeader",column:e,oldValue:n,newValue:t}),dispatch.A.call(s,"onchangeheader",s,parseInt(e),t,o)}},getStyle=function(e,t){const s=this;if(e)return e=(0,internalHelpers.vu)(e,!0),t?s.records[e[1]][e[0]].element.style[t]:s.records[e[1]][e[0]].element.getAttribute("style");{const e={},n=s.options.data[0].length,o=s.options.data.length;for(let r=0;r<o;r++)for(let o=0;o<n;o++){const n=t?s.records[r][o].element.style[t]:s.records[r][o].element.getAttribute("style");n&&(e[(0,internalHelpers.t3)([o,r])]=n)}return e}},setStyle=function(e,t,s,n,o){const r=this,l={},i={},a=function(e,t,s){const o=(0,internalHelpers.vu)(e,!0);if(r.records[o[1]]&&r.records[o[1]][o[0]]&&(0==r.records[o[1]][o[0]].element.classList.contains("readonly")||n)){const a=r.records[o[1]][o[0]].element.style[t];a!=s||n?r.records[o[1]][o[0]].element.style[t]=s:(s="",r.records[o[1]][o[0]].element.style[t]=""),i[e]||(i[e]=[]),l[e]||(l[e]=[]),i[e].push([t+":"+a]),l[e].push([t+":"+s])}};if(t&&s)"string"==typeof e&&a(e,t,s);else{const t=Object.keys(e);for(let s=0;s<t.length;s++){let n=e[t[s]];"string"==typeof n&&(n=n.split(";"));for(let e=0;e<n.length;e++)"string"==typeof n[e]&&(n[e]=n[e].split(":")),n[e][0].trim()&&a(t[s],n[e][0].trim(),n[e][1])}}let c=Object.keys(i);for(let e=0;e<c.length;e++)i[c[e]]=i[c[e]].join(";");c=Object.keys(l);for(let e=0;e<c.length;e++)l[c[e]]=l[c[e]].join(";");o||utils_history.Dh.call(r,{action:"setStyle",oldValue:i,newValue:l}),dispatch.A.call(r,"onchangestyle",r,l)},resetStyle=function(e,t){const s=this,n=Object.keys(e);for(let e=0;e<n.length;e++){const t=(0,internalHelpers.vu)(n[e],!0);s.records[t[1]]&&s.records[t[1]][t[0]]&&s.records[t[1]][t[0]].element.setAttribute("style","")}s.setStyle(e,null,null,null,t)},download=function(e,t){const s=this;if(0==s.parent.config.allowExport)console.error("Export not allowed");else{let n="";n+=copy.call(s,!1,s.options.csvDelimiter,!0,e,!0,void 0,t);const o=new Blob(["\ufeff"+n],{type:"text/csv;charset=utf-8;"});if(window.navigator&&window.navigator.msSaveOrOpenBlob)window.navigator.msSaveOrOpenBlob(o,(s.options.csvFileName||s.options.worksheetName)+".csv");else{const e=document.createElement("a"),t=URL.createObjectURL(o);e.href=t,e.setAttribute("download",(s.options.csvFileName||s.options.worksheetName)+".csv"),document.body.appendChild(e),e.click(),e.parentNode.removeChild(e)}}},getComments=function(e){const t=this;if(e)return"string"!=typeof e?getComments.call(t):(e=(0,internalHelpers.vu)(e,!0),t.records[e[1]][e[0]].element.getAttribute("title")||"");{const e={};for(let s=0;s<t.options.data.length;s++)for(let n=0;n<t.options.columns.length;n++){const o=t.records[s][n].element.getAttribute("title");o&&(e[(0,internalHelpers.t3)([n,s])]=o)}return e}},setComments=function(e,t){const s=this;let n;n="string"==typeof e?{[e]:t}:e;const o={};Object.entries(n).forEach((function([e,t]){const n=(0,helpers.getCoordsFromCellName)(e);o[e]=s.records[n[1]][n[0]].element.getAttribute("title"),s.records[n[1]][n[0]].element.setAttribute("title",t||""),t?(s.records[n[1]][n[0]].element.classList.add("jss_comments"),s.options.comments||(s.options.comments={}),s.options.comments[e]=t):(s.records[n[1]][n[0]].element.classList.remove("jss_comments"),s.options.comments&&s.options.comments[e]&&delete s.options.comments[e])})),utils_history.Dh.call(s,{action:"setComments",newValue:n,oldValue:o}),dispatch.A.call(s,"oncomments",s,n,o)};var orderBy=__webpack_require__(451);const getWorksheetConfig=function(){return this.options},getSpreadsheetConfig=function(){return this.config},setConfig=function(e,t){const s=this,n=Object.keys(e);let o;s.parent?o=s.parent:(t=!0,o=s),n.forEach((function(n){t?(o.config[n]=e[n],"toolbar"===n&&(!0===e[n]?o.showToolbar():!1===e[n]&&o.hideToolbar())):s.options[n]=e[n]}))};var meta=__webpack_require__(617);const setReadOnly=function(e,t){const s=this;let n;if("string"==typeof e){const t=(0,helpers.getCoordsFromCellName)(e);n=s.records[t[1]][t[0]]}else{const t=parseInt(e.getAttribute("data-x")),o=parseInt(e.getAttribute("data-y"));n=s.records[o][t]}t?n.element.classList.add("readonly"):n.element.classList.remove("readonly")},isReadOnly=function(e,t){if("string"==typeof e&&void 0===t){const s=(0,helpers.getCoordsFromCellName)(e);[e,t]=s}return this.records[t][e].element.classList.contains("readonly")},setWorksheetFunctions=function(e){for(let t=0;t<worksheetPublicMethodsLength;t++){const[s,n]=worksheetPublicMethods[t];e[s]=n.bind(e)}},createTable=function(){let e=this;setWorksheetFunctions(e),e.table=document.createElement("table"),e.thead=document.createElement("thead"),e.tbody=document.createElement("tbody"),e.headers=[],e.cols=[],e.content=document.createElement("div"),e.content.classList.add("jss_content"),e.content.onscroll=function(t){scrollControls.call(e,t)},e.content.onwheel=function(t){wheelControls.call(e,t)};const t=document.createElement("div"),s=document.createElement("label");s.innerHTML=jSuites.translate("Search")+": ",t.appendChild(s),e.searchInput=document.createElement("input"),e.searchInput.classList.add("jss_search"),s.appendChild(e.searchInput),e.searchInput.onfocus=function(){e.resetSelection()};const n=document.createElement("div");if(e.options.pagination>0&&e.options.paginationOptions&&e.options.paginationOptions.length>0){e.paginationDropdown=document.createElement("select"),e.paginationDropdown.classList.add("jss_pagination_dropdown"),e.paginationDropdown.onchange=function(){e.options.pagination=parseInt(this.value),e.page(0)};for(let t=0;t<e.options.paginationOptions.length;t++){const s=document.createElement("option");s.value=e.options.paginationOptions[t],s.innerHTML=e.options.paginationOptions[t],e.paginationDropdown.appendChild(s)}e.paginationDropdown.value=e.options.pagination,n.appendChild(document.createTextNode(jSuites.translate("Show "))),n.appendChild(e.paginationDropdown),n.appendChild(document.createTextNode(jSuites.translate("entries")))}const o=document.createElement("div");o.classList.add("jss_filter"),o.appendChild(n),o.appendChild(t),e.colgroupContainer=document.createElement("colgroup");let r=document.createElement("col");if(r.setAttribute("width","50"),e.colgroupContainer.appendChild(r),e.options.nestedHeaders&&e.options.nestedHeaders.length>0&&e.options.nestedHeaders[0]&&e.options.nestedHeaders[0][0])for(let t=0;t<e.options.nestedHeaders.length;t++)e.thead.appendChild(internal.ju.call(e,e.options.nestedHeaders[t]));e.headerContainer=document.createElement("tr"),r=document.createElement("td"),r.classList.add("jss_selectall"),e.headerContainer.appendChild(r);const l=getNumberOfColumns.call(e);for(let t=0;t<l;t++)createCellHeader.call(e,t),e.headerContainer.appendChild(e.headers[t]),e.colgroupContainer.appendChild(e.cols[t].colElement);if(e.thead.appendChild(e.headerContainer),1==e.options.filters){e.filter=document.createElement("tr");const t=document.createElement("td");e.filter.appendChild(t);for(let t=0;t<e.options.columns.length;t++){const s=document.createElement("td");s.innerHTML="&nbsp;",s.setAttribute("data-x",t),s.className="jss_column_filter","hidden"==e.options.columns[t].type&&(s.style.display="none"),e.filter.appendChild(s)}e.thead.appendChild(e.filter)}e.table=document.createElement("table"),e.table.classList.add("jss_worksheet"),e.table.setAttribute("cellpadding","0"),e.table.setAttribute("cellspacing","0"),e.table.setAttribute("unselectable","yes"),e.table.appendChild(e.colgroupContainer),e.table.appendChild(e.thead),e.table.appendChild(e.tbody),e.options.textOverflow||e.table.classList.add("jss_overflow"),e.corner=document.createElement("div"),e.corner.className="jss_corner",e.corner.setAttribute("unselectable","on"),e.corner.setAttribute("onselectstart","return false"),0==e.options.selectionCopy&&(e.corner.style.display="none"),e.textarea=document.createElement("textarea"),e.textarea.className="jss_textarea",e.textarea.id="jss_textarea",e.textarea.tabIndex="-1",e.textarea.ariaHidden="true";const i=document.createElement("a");i.setAttribute("href","https://bossanova.uk/jspreadsheet/"),e.ads=document.createElement("div"),e.ads.className="jss_about";const a=document.createElement("span");a.innerHTML="Jspreadsheet CE",i.appendChild(a),e.ads.appendChild(i),document.createElement("div").classList.add("jss_table"),e.pagination=document.createElement("div"),e.pagination.classList.add("jss_pagination");const c=document.createElement("div"),u=document.createElement("div");if(e.pagination.appendChild(c),e.pagination.appendChild(u),e.options.pagination||(e.pagination.style.display="none"),1==e.options.search&&e.element.appendChild(o),e.content.appendChild(e.table),e.content.appendChild(e.corner),e.content.appendChild(e.textarea),e.element.appendChild(e.content),e.element.appendChild(e.pagination),e.element.appendChild(e.ads),e.element.classList.add("jss_container"),e.element.jssWorksheet=e,e.element.jspreadsheet=e,1==e.options.tableOverflow&&(e.options.tableHeight&&(e.content.style["overflow-y"]="auto",e.content.style["box-shadow"]="rgb(221 221 221) 2px 2px 5px 0.1px",e.content.style.maxHeight="string"==typeof e.options.tableHeight?e.options.tableHeight:e.options.tableHeight+"px"),e.options.tableWidth&&(e.content.style["overflow-x"]="auto",e.content.style.width="string"==typeof e.options.tableWidth?e.options.tableWidth:e.options.tableWidth+"px")),1!=e.options.tableOverflow&&e.parent.config.toolbar&&e.element.classList.add("with-toolbar"),0!=e.options.columnDrag&&e.thead.classList.add("draggable"),0!=e.options.columnResize&&e.thead.classList.add("resizable"),0!=e.options.rowDrag&&e.tbody.classList.add("draggable"),0!=e.options.rowResize&&e.tbody.classList.add("resizable"),e.setData.call(e),e.options.style&&(e.setStyle(e.options.style,null,null,1,1),delete e.options.style),Object.defineProperty(e.options,"style",{enumerable:!0,configurable:!0,get(){return e.getStyle()}}),e.options.comments&&e.setComments(e.options.comments),e.options.classes){const t=Object.keys(e.options.classes);for(let s=0;s<t.length;s++){const n=(0,internalHelpers.vu)(t[s],!0);e.records[n[1]][n[0]].element.classList.add(e.options.classes[t[s]])}}},prepareTable=function(){const e=this;1==e.options.lazyLoading&&1!=e.options.tableOverflow&&1!=e.parent.config.fullscreen&&(console.error("Jspreadsheet: The lazyloading only works when tableOverflow = yes or fullscreen = yes"),e.options.lazyLoading=!1),e.options.columns||(e.options.columns=[]);let t,s=e.options.columns.length;if(e.options.data&&void 0!==e.options.data[0])if(Array.isArray(e.options.data[0])){const t=e.options.data[0].length;t>s&&(s=t)}else t=Object.keys(e.options.data[0]),t.length>s&&(s=t.length);e.options.minDimensions||(e.options.minDimensions=[0,0]),e.options.minDimensions[0]>s&&(s=e.options.minDimensions[0]);const n=[];for(let o=0;o<s;o++)e.options.columns[o]||(e.options.columns[o]={}),!e.options.columns[o].name&&t&&t[o]&&(e.options.columns[o].name=t[o]),"dropdown"==e.options.columns[o].type&&e.options.columns[o].url&&n.push({url:e.options.columns[o].url,index:o,method:"GET",dataType:"json",success:function(t){e.options.columns[this.index].source||(e.options.columns[this.index].source=[]);for(let s=0;s<t.length;s++)e.options.columns[this.index].source.push(t[s])}});n.length?jSuites.ajax(n,(function(){createTable.call(e)})):createTable.call(e)},getNextDefaultWorksheetName=function(e){const t=/^Sheet(\d+)$/;let s=0;return e.worksheets.forEach((function(e){const n=t.exec(e.options.worksheetName);n&&(s=Math.max(s,parseInt(n[1])))})),"Sheet"+(s+1)},buildWorksheet=async function(){const e=this,t=(e.element,e.parent);"object"==typeof t.plugins&&Object.entries(t.plugins).forEach((function([,t]){"function"==typeof t.beforeinit&&t.beforeinit(e)})),libraryBase.jspreadsheet.current=e;const s=[];if(e.options.csv){const t=new Promise((t=>{jSuites.ajax({url:e.options.csv,method:"GET",dataType:"text",success:function(s){const n=(0,helpers.parseCSV)(s,e.options.csvDelimiter);if(1==e.options.csvHeaders&&n.length>0){const t=n.shift();if(t.length>0){e.options.columns||(e.options.columns=[]);for(let s=0;s<t.length;s++)e.options.columns[s]||(e.options.columns[s]={}),void 0===e.options.columns[s].title&&(e.options.columns[s].title=t[s])}}e.options.data=n,prepareTable.call(e),t()}})}));s.push(t)}else if(e.options.url){const t=new Promise((t=>{jSuites.ajax({url:e.options.url,method:"GET",dataType:"json",success:function(s){e.options.data=s.data?s.data:s,prepareTable.call(e),t()}})}));s.push(t)}else prepareTable.call(e);await Promise.all(s),"object"==typeof t.plugins&&Object.entries(t.plugins).forEach((function([,t]){"function"==typeof t.init&&t.init(e)}))},createWorksheetObj=function(e){const t=this.parent;e.worksheetName||(e.worksheetName=getNextDefaultWorksheetName(this.parent));const s={parent:t,options:e,filters:[],formula:[],history:[],selection:[],historyIndex:-1};return t.config.worksheets.push(s.options),t.worksheets.push(s),s},createWorksheet=function(e){const t=this.parent;t.creationThroughJss=!0,createWorksheetObj.call(this,e),t.element.tabs.create(e.worksheetName)},openWorksheet=function(e){this.parent.element.tabs.open(e)},deleteWorksheet=function(e){const t=this;t.parent.element.tabs.remove(e);const s=t.parent.worksheets.splice(e,1)[0];dispatch.A.call(t.parent,"ondeleteworksheet",s,e)},worksheetPublicMethods=[["selectAll",selection.Ub],["updateSelectionFromCoords",function(e,t,s,n){return selection.AH.call(this,e,t,s,n)}],["resetSelection",function(){return selection.gE.call(this)}],["getSelection",selection.Lo],["getSelected",selection.ef],["getSelectedColumns",selection.Jg],["getSelectedRows",selection.R5],["getData",getData],["setData",setData],["getValue",getValue],["getValueFromCoords",getValueFromCoords],["setValue",setValue],["setValueFromCoords",setValueFromCoords],["getWidth",getWidth],["setWidth",function(e,t){return setWidth.call(this,e,t)}],["insertRow",insertRow],["moveRow",function(e,t){return moveRow.call(this,e,t)}],["deleteRow",deleteRow],["hideRow",hideRow],["showRow",showRow],["getRowData",getRowData],["setRowData",setRowData],["getHeight",getHeight],["setHeight",function(e,t){return setHeight.call(this,e,t)}],["getMerge",merges.fd],["setMerge",function(e,t,s){return merges.FU.call(this,e,t,s)}],["destroyMerge",function(){return merges.VP.call(this)}],["removeMerge",function(e,t){return merges.Zp.call(this,e,t)}],["search",search],["resetSearch",resetSearch],["getHeader",getHeader],["getHeaders",getHeaders],["setHeader",setHeader],["getStyle",getStyle],["setStyle",function(e,t,s,n){return setStyle.call(this,e,t,s,n)}],["resetStyle",resetStyle],["insertColumn",insertColumn],["moveColumn",moveColumn],["deleteColumn",deleteColumn],["getColumnData",getColumnData],["setColumnData",setColumnData],["whichPage",pagination.ho],["page",pagination.MY],["download",download],["getComments",getComments],["setComments",setComments],["orderBy",orderBy.My],["undo",utils_history.tN],["redo",utils_history.ZS],["getCell",internal.tT],["getCellFromCoords",internal.Xr],["getLabel",internal.p9],["getConfig",getWorksheetConfig],["setConfig",setConfig],["getMeta",function(e){return meta.IQ.call(this,e)}],["setMeta",meta.iZ],["showColumn",showColumn],["hideColumn",hideColumn],["showIndex",internal.C6],["hideIndex",internal.TI],["getWorksheetActive",internal.$O],["openEditor",openEditor],["closeEditor",closeEditor],["createWorksheet",createWorksheet],["openWorksheet",openWorksheet],["deleteWorksheet",deleteWorksheet],["copy",function(e){e?cutControls():copy.call(this,!0)}],["paste",paste],["executeFormula",internal.Em],["getDataFromRange",getDataFromRange],["quantiyOfPages",pagination.$f],["getRange",selection.eO],["isSelected",selection.sp],["setReadOnly",setReadOnly],["isReadOnly",isReadOnly],["getHighlighted",selection.kV],["dispatch",dispatch.A],["down",down],["first",first],["last",last],["left",left],["right",right],["up",up],["openFilter",filter.N$],["resetFilters",filter.dr]],worksheetPublicMethodsLength=worksheetPublicMethods.length,factory=function(){},createWorksheets=async function(e,t,s){let n=t.worksheets;if(!n)throw new Error("JSS: worksheets are not defined");{let o={animation:!0,onbeforecreate:function(t,s){return s||getNextDefaultWorksheetName(e)},oncreate:function(s,n){if(e.creationThroughJss)e.creationThroughJss=!1;else{const t=s.tabs.headers.children[s.tabs.headers.children.length-2].innerHTML;createWorksheetObj.call(e.worksheets[0],{minDimensions:[10,15],worksheetName:t})}const o=e.worksheets[e.worksheets.length-1];o.element=n,buildWorksheet.call(o).then((function(){(0,toolbar.nK)(o),dispatch.A.call(o,"oncreateworksheet",o,t,e.worksheets.length-1)}))},onchange:function(t,s,n){0!=e.worksheets.length&&e.worksheets[n]&&(0,toolbar.nK)(e.worksheets[n])}};1==t.tabs?o.allowCreate=!0:o.hideHeaders=!0,o.data=[];let r=1;for(let e=0;e<n.length;e++)n[e].worksheetName||(n[e].worksheetName="Sheet"+r++),o.data.push({title:n[e].worksheetName,content:""});s.classList.add("jss_spreadsheet");const l=jSuites.tabs(s,o),i=t.style;delete t.style;for(let t=0;t<n.length;t++)n[t].style&&Object.entries(n[t].style).forEach((function([e,s]){"number"==typeof s&&(n[t].style[e]=i[s])})),e.worksheets.push({parent:e,element:l.content.children[t],options:n[t],filters:[],formula:[],history:[],selection:[],historyIndex:-1}),await buildWorksheet.call(e.worksheets[t])}};factory.spreadsheet=async function(e,t,s){if("TABLE"==e.tagName){t||(t={}),t.worksheets||(t.worksheets=[]);const s=(0,helpers.createFromTable)(e,t.worksheets[0]);t.worksheets[0]=s;const n=document.createElement("div");e.parentNode.insertBefore(n,e),e.remove(),e=n}let n={worksheets:s,config:t,element:e,el:e};return n.contextMenu=document.createElement("div"),n.contextMenu.className="jss_contextmenu",n.getWorksheetActive=internal.$O.bind(n),n.fullscreen=internal.Y5.bind(n),n.showToolbar=toolbar.ll.bind(n),n.hideToolbar=toolbar.Ar.bind(n),n.getConfig=getSpreadsheetConfig.bind(n),n.setConfig=setConfig.bind(n),n.setPlugins=function(e){n.plugins||(n.plugins={}),"object"==typeof e&&Object.entries(e).forEach((function([e,t]){n.plugins[e]=t.call(libraryBase.jspreadsheet,n,{},n.config)}))},n.setPlugins(t.plugins),await createWorksheets(n,t,e),n.element.appendChild(n.contextMenu),jSuites.contextmenu(n.contextMenu,{onclick:function(){n.contextMenu.contextmenu.close(!1)}}),1==n.config.fullscreen&&n.element.classList.add("fullscreen"),toolbar.ll.call(n),t.root?setEvents(t.root):setEvents(document),e.spreadsheet=n,n},factory.worksheet=function(e,t,s){let n={parent:e,options:{}};return void 0===s?e.worksheets.push(n):e.worksheets.splice(s,0,n),Object.assign(n.options,t),n};var utils_factory=factory;libraryBase.jspreadsheet=function(e,t){try{let s=[];return utils_factory.spreadsheet(e,t,s).then((e=>{libraryBase.jspreadsheet.spreadsheet.push(e),dispatch.A.call(e,"onload",e)})),s}catch(e){console.error(e)}},libraryBase.jspreadsheet.getWorksheetInstanceByName=function(e,t){const s=libraryBase.jspreadsheet.spreadsheet.find((e=>e.config.namespace===t));if(s)return{};if(null==e){const e=s.worksheets.map((e=>[e.options.worksheetName,e]));return Object.fromEntries(e)}return s.worksheets.find((t=>t.options.worksheetName===e))},libraryBase.jspreadsheet.setDictionary=function(e){jSuites.setDictionary(e)},libraryBase.jspreadsheet.destroy=function(e,t){if(e.spreadsheet){const s=libraryBase.jspreadsheet.spreadsheet.indexOf(e.spreadsheet);libraryBase.jspreadsheet.spreadsheet.splice(s,1);const n=e.spreadsheet.config.root||document;e.spreadsheet=null,e.innerHTML="",t&&destroyEvents(n)}},libraryBase.jspreadsheet.destroyAll=function(){for(let e=0;e<libraryBase.jspreadsheet.spreadsheet.length;e++){const t=libraryBase.jspreadsheet.spreadsheet[e];libraryBase.jspreadsheet.destroy(t.element)}},libraryBase.jspreadsheet.current=null,libraryBase.jspreadsheet.spreadsheet=[],libraryBase.jspreadsheet.helpers={},libraryBase.jspreadsheet.version=function(){return version},Object.entries(helpers).forEach((([e,t])=>{libraryBase.jspreadsheet.helpers[e]=t}));var src=libraryBase.jspreadsheet;jspreadsheet=__webpack_exports__.default})();

    return jspreadsheet;
})));