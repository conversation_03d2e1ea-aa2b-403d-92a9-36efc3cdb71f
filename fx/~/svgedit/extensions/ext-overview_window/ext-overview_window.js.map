{"version": 3, "file": "ext-overview_window.js", "sources": ["../../../../src/editor/extensions/ext-overview_window/dragmove/dragmove.js", "../../../../src/editor/extensions/ext-overview_window/ext-overview_window.js"], "sourcesContent": ["// https://github.com/knadh/dragmove.js\n// Kailash Nadh (c) 2020.\n// MIT License.\n// can't use npm version as the dragmove is different.\n\nlet _loaded = false\nconst _callbacks = []\nconst _isTouch = window.ontouchstart !== undefined\n\nexport const dragmove = function (target, handler, parent, onStart, onEnd, onDrag) {\n  // Register a global event to capture mouse moves (once).\n  if (!_loaded) {\n    document.addEventListener(_isTouch ? 'touchmove' : 'mousemove', function (e) {\n      let c = e\n      if (e.touches) {\n        c = e.touches[0]\n      }\n\n      // On mouse move, dispatch the coords to all registered callbacks.\n      for (let i = 0; i < _callbacks.length; i++) {\n        _callbacks[i](c.clientX, c.clientY)\n      }\n    })\n  }\n\n  _loaded = true\n  let isMoving = false; let hasStarted = false\n  let startX = 0; let startY = 0; let lastX = 0; let lastY = 0\n\n  // On the first click and hold, record the offset of the pointer in relation\n  // to the point of click inside the element.\n  handler.addEventListener(_isTouch ? 'touchstart' : 'mousedown', function (e) {\n    e.stopPropagation()\n    e.preventDefault()\n    if (target.dataset.dragEnabled === 'false') {\n      return\n    }\n\n    let c = e\n    if (e.touches) {\n      c = e.touches[0]\n    }\n\n    isMoving = true\n    startX = target.offsetLeft - c.clientX\n    startY = target.offsetTop - c.clientY\n  })\n\n  // On leaving click, stop moving.\n  document.addEventListener(_isTouch ? 'touchend' : 'mouseup', function () {\n    if (onEnd && hasStarted) {\n      onEnd(target, parent, parseInt(target.style.left), parseInt(target.style.top))\n    }\n\n    isMoving = false\n    hasStarted = false\n  })\n\n  // On leaving click, stop moving.\n  document.addEventListener(_isTouch ? 'touchmove' : 'mousemove', function () {\n    if (onDrag && hasStarted) {\n      onDrag(target, parseInt(target.style.left), parseInt(target.style.top))\n    }\n  })\n\n  // Register mouse-move callback to move the element.\n  _callbacks.push(function move (x, y) {\n    if (!isMoving) {\n      return\n    }\n\n    if (!hasStarted) {\n      hasStarted = true\n      if (onStart) {\n        onStart(target, lastX, lastY)\n      }\n    }\n\n    lastX = x + startX\n    lastY = y + startY\n\n    // If boundary checking is on, don't let the element cross the viewport.\n    if (target.dataset.dragBoundary === 'true') {\n      if (lastX < 1 || lastX >= window.innerWidth - target.offsetWidth) {\n        return\n      }\n      if (lastY < 1 || lastY >= window.innerHeight - target.offsetHeight) {\n        return\n      }\n    }\n\n    target.style.left = lastX + 'px'\n    target.style.top = lastY + 'px'\n  })\n}\n\nexport { dragmove as default }\n", "/**\n * @file ext-overview_window.js\n *\n * @license MIT\n *\n * @copyright 2013 <PERSON>\n *\n */\nimport { dragmove } from './dragmove/dragmove.js'\n\nexport default {\n  name: 'overview_window',\n  init ({ _$ }) {\n    const svgEditor = this\n    const { $id, $click } = svgEditor.svgCanvas\n    const overviewWindowGlobals = {}\n\n    // Define and insert the base html element.\n    const propsWindowHtml =\n      '<div id=\"overview_window_content_pane\" style=\"width:100%; word-wrap:break-word;  display:inline-block; margin-top:20px;\">' +\n        '<div id=\"overview_window_content\" style=\"position:relative; padding-left:15px; top:0px;\">' +\n          '<div style=\"background-color:#A0A0A0; display:inline-block; overflow:visible;\">' +\n            '<svg id=\"overviewMiniView\" width=\"132\" height=\"100\" x=\"0\" y=\"0\" viewBox=\"0 0 4800 3600\" ' +\n                'xmlns=\"http://www.w3.org/2000/svg\" ' +\n                'xmlns:xlink=\"http://www.w3.org/1999/xlink\">' +\n              '<use x=\"0\" y=\"0\" xlink:href=\"#svgroot\"> </use>' +\n            '</svg>' +\n            '<div id=\"overview_window_view_box\" style=\"min-width:50px; min-height:50px; position:absolute; top:30px; left:30px; z-index:5; background-color:rgba(255,0,102,0.3);\">' +\n            '</div>' +\n          '</div>' +\n        '</div>' +\n      '</div>'\n    $id('sidepanel_content').insertAdjacentHTML('beforeend', propsWindowHtml)\n\n    // Define dynamic animation of the view box.\n    const updateViewBox = () => {\n      const { workarea } = svgEditor\n      const portHeight = parseFloat(getComputedStyle(workarea, null).height.replace('px', ''))\n      const portWidth = parseFloat(getComputedStyle(workarea, null).width.replace('px', ''))\n      const portX = workarea.scrollLeft\n      const portY = workarea.scrollTop\n      const windowWidth = parseFloat(getComputedStyle($id('svgcanvas'), null).width.replace('px', ''))\n      const windowHeight = parseFloat(getComputedStyle($id('svgcanvas'), null).height.replace('px', ''))\n      const overviewWidth = parseFloat(getComputedStyle($id('overviewMiniView'), null).width.replace('px', ''))\n      const overviewHeight = parseFloat(getComputedStyle($id('overviewMiniView'), null).height.replace('px', ''))\n\n      const viewBoxX = portX / windowWidth * overviewWidth\n      const viewBoxY = portY / windowHeight * overviewHeight\n      const viewBoxWidth = portWidth / windowWidth * overviewWidth\n      const viewBoxHeight = portHeight / windowHeight * overviewHeight\n\n      $id('overview_window_view_box').style.minWidth = viewBoxWidth + 'px'\n      $id('overview_window_view_box').style.minHeight = viewBoxHeight + 'px'\n      $id('overview_window_view_box').style.top = viewBoxY + 'px'\n      $id('overview_window_view_box').style.left = viewBoxX + 'px'\n    }\n    $id('workarea').addEventListener('scroll', () => {\n      if (!(overviewWindowGlobals.viewBoxDragging)) {\n        updateViewBox()\n      }\n    })\n    $id('workarea').addEventListener('resize', updateViewBox)\n    updateViewBox()\n\n    // Compensate for changes in zoom and canvas size.\n    const updateViewDimensions = function () {\n      const viewWidth = parseFloat(getComputedStyle($id('svgroot'), null).width.replace('px', ''))\n      const viewHeight = parseFloat(getComputedStyle($id('svgroot'), null).height.replace('px', ''))\n\n      const viewX = 640\n      const viewY = 480\n\n      const svgWidthOld = parseFloat(getComputedStyle($id('overviewMiniView'), null).width.replace('px', ''))\n      const svgHeightNew = viewHeight / viewWidth * svgWidthOld\n      $id('overviewMiniView').setAttribute('viewBox', viewX + ' ' + viewY + ' ' + viewWidth + ' ' + viewHeight)\n      $id('overviewMiniView').setAttribute('height', svgHeightNew)\n      updateViewBox()\n    }\n    updateViewDimensions()\n\n    // Set up the overview window as a controller for the view port.\n    overviewWindowGlobals.viewBoxDragging = false\n    const updateViewPortFromViewBox = function () {\n      const windowWidth = parseFloat(getComputedStyle($id('svgcanvas'), null).width.replace('px', ''))\n      const windowHeight = parseFloat(getComputedStyle($id('svgcanvas'), null).height.replace('px', ''))\n      const overviewWidth = parseFloat(getComputedStyle($id('overviewMiniView'), null).width.replace('px', ''))\n      const overviewHeight = parseFloat(getComputedStyle($id('overviewMiniView'), null).height.replace('px', ''))\n      const viewBoxX = parseFloat(getComputedStyle($id('overview_window_view_box'), null).getPropertyValue('left').replace('px', ''))\n      const viewBoxY = parseFloat(getComputedStyle($id('overview_window_view_box'), null).getPropertyValue('top').replace('px', ''))\n\n      const portX = viewBoxX / overviewWidth * windowWidth\n      const portY = viewBoxY / overviewHeight * windowHeight\n      $id('workarea').scrollLeft = portX\n      $id('workarea').scrollTop = portY\n    }\n    const onStart = () => {\n      overviewWindowGlobals.viewBoxDragging = true\n      updateViewPortFromViewBox()\n    }\n    const onEnd = (el, parent, _x, _y) => {\n      if ((el.offsetLeft + el.offsetWidth) > parseFloat(getComputedStyle(parent, null).width.replace('px', ''))) {\n        el.style.left = (parseFloat(getComputedStyle(parent, null).width.replace('px', '')) - el.offsetWidth) + 'px'\n      } else if (el.offsetLeft < 0) {\n        el.style.left = '0px'\n      }\n      if ((el.offsetTop + el.offsetHeight) > parseFloat(getComputedStyle(parent, null).height.replace('px', ''))) {\n        el.style.top = (parseFloat(getComputedStyle(parent, null).height.replace('px', '')) - el.offsetHeight) + 'px'\n      } else if (el.offsetTop < 0) {\n        el.style.top = '0px'\n      }\n      overviewWindowGlobals.viewBoxDragging = false\n      updateViewPortFromViewBox()\n    }\n    const onDrag = function () {\n      updateViewPortFromViewBox()\n    }\n    const dragElem = document.querySelector('#overview_window_view_box')\n    const parentElem = document.querySelector('#overviewMiniView')\n    dragmove(dragElem, dragElem, parentElem, onStart, onEnd, onDrag)\n\n    $click($id('overviewMiniView'), (evt) => {\n      // Firefox doesn't support evt.offsetX and evt.offsetY.\n      const mouseX = (evt.offsetX || evt.originalEvent.layerX)\n      const mouseY = (evt.offsetY || evt.originalEvent.layerY)\n      const overviewWidth = parseFloat(getComputedStyle($id('overviewMiniView'), null).width.replace('px', ''))\n      const overviewHeight = parseFloat(getComputedStyle($id('overviewMiniView'), null).height.replace('px', ''))\n      const viewBoxWidth = parseFloat(getComputedStyle($id('overview_window_view_box'), null).getPropertyValue('min-width').replace('px', ''))\n      const viewBoxHeight = parseFloat(getComputedStyle($id('overview_window_view_box'), null).getPropertyValue('min-height').replace('px', ''))\n\n      let viewBoxX = mouseX - 0.5 * viewBoxWidth\n      let viewBoxY = mouseY - 0.5 * viewBoxHeight\n      // deal with constraints\n      if (viewBoxX < 0) {\n        viewBoxX = 0\n      }\n      if (viewBoxY < 0) {\n        viewBoxY = 0\n      }\n      if (viewBoxX + viewBoxWidth > overviewWidth) {\n        viewBoxX = overviewWidth - viewBoxWidth\n      }\n      if (viewBoxY + viewBoxHeight > overviewHeight) {\n        viewBoxY = overviewHeight - viewBoxHeight\n      }\n      $id('overview_window_view_box').style.top = viewBoxY + 'px'\n      $id('overview_window_view_box').style.left = viewBoxX + 'px'\n      updateViewPortFromViewBox()\n    })\n\n    return {\n      name: 'overview window',\n      canvasUpdated: updateViewDimensions,\n      workareaResized: updateViewBox\n    }\n  }\n}\n"], "names": ["_loaded", "_callbacks", "_isTouch", "undefined", "window", "ontouchstart", "extOverview_window", "name", "init", "_ref", "svgEditor", "this", "$id", "$click", "svgCanvas", "overviewWindowGlobals", "insertAdjacentHTML", "updateViewBox", "workarea", "portHeight", "parseFloat", "getComputedStyle", "height", "replace", "portWidth", "width", "portX", "scrollLeft", "portY", "scrollTop", "windowWidth", "windowHeight", "overviewWidth", "overviewHeight", "viewBoxX", "viewBoxY", "viewBoxWidth", "viewBoxHeight", "style", "min<PERSON><PERSON><PERSON>", "minHeight", "top", "left", "addEventListener", "viewBoxDragging", "updateViewDimensions", "viewWidth", "viewHeight", "svgHeightNew", "setAttribute", "viewX", "updateViewPortFromViewBox", "getPropertyValue", "dragElem", "document", "querySelector", "target", "handler", "parent", "onStart", "onEnd", "onDrag", "e", "c", "touches", "i", "length", "clientX", "clientY", "isMoving", "hasStarted", "startX", "startY", "lastX", "lastY", "stopPropagation", "preventDefault", "dataset", "dragEnabled", "offsetLeft", "offsetTop", "parseInt", "push", "move", "x", "y", "dragBoundary", "innerWidth", "offsetWidth", "innerHeight", "offsetHeight", "dragmove", "el", "_x", "_y", "evt", "mouseX", "offsetX", "originalEvent", "layerX", "mouseY", "offsetY", "layerY", "canvasUpdated", "workareaResized"], "mappings": "AAKA,IAAIA,GAAU,EACd,MAAMC,EAAa,GACbC,OAAmCC,IAAxBC,OAAOC;;;;;;;;;ACGxB,IAAeC,EAAA,CACbC,KAAM,kBACNC,KAAcC,GACZ,MAAMC,EAAYC,MACZC,IAAEA,EAAFC,OAAOA,GAAWH,EAAUI,UAC5BC,EAAwB,GAiB9BH,EAAI,qBAAqBI,mBAAmB,YAb1C,4rBAgBF,MAAMC,cAAgB,KACpB,MAAMC,SAAEA,GAAaR,EACfS,EAAaC,WAAWC,iBAAiBH,EAAU,MAAMI,OAAOC,QAAQ,KAAM,KAC9EC,EAAYJ,WAAWC,iBAAiBH,EAAU,MAAMO,MAAMF,QAAQ,KAAM,KAC5EG,EAAQR,EAASS,WACjBC,EAAQV,EAASW,UACjBC,EAAcV,WAAWC,iBAAiBT,EAAI,aAAc,MAAMa,MAAMF,QAAQ,KAAM,KACtFQ,EAAeX,WAAWC,iBAAiBT,EAAI,aAAc,MAAMU,OAAOC,QAAQ,KAAM,KACxFS,EAAgBZ,WAAWC,iBAAiBT,EAAI,oBAAqB,MAAMa,MAAMF,QAAQ,KAAM,KAC/FU,EAAiBb,WAAWC,iBAAiBT,EAAI,oBAAqB,MAAMU,OAAOC,QAAQ,KAAM,KAEjGW,EAAWR,EAAQI,EAAcE,EACjCG,EAAWP,EAAQG,EAAeE,EAClCG,EAAeZ,EAAYM,EAAcE,EACzCK,EAAgBlB,EAAaY,EAAeE,EAElDrB,EAAI,4BAA4B0B,MAAMC,SAAWH,EAAe,KAChExB,EAAI,4BAA4B0B,MAAME,UAAYH,EAAgB,KAClEzB,EAAI,4BAA4B0B,MAAMG,IAAMN,EAAW,KACvDvB,EAAI,4BAA4B0B,MAAMI,KAAOR,EAAW,MAE1DtB,EAAI,YAAY+B,iBAAiB,UAAU,KACnC5B,EAAsB6B,iBAC1B3B,mBAGJL,EAAI,YAAY+B,iBAAiB,SAAU1B,eAC3CA,gBAGA,MAAM4B,qBAAuB,WAC3B,MAAMC,EAAY1B,WAAWC,iBAAiBT,EAAI,WAAY,MAAMa,MAAMF,QAAQ,KAAM,KAClFwB,EAAa3B,WAAWC,iBAAiBT,EAAI,WAAY,MAAMU,OAAOC,QAAQ,KAAM,KAMpFyB,EAAeD,EAAaD,EADd1B,WAAWC,iBAAiBT,EAAI,oBAAqB,MAAMa,MAAMF,QAAQ,KAAM,KAEnGX,EAAI,oBAAoBqC,aAAa,UAAWC,WAA4BJ,EAAY,IAAMC,GAC9FnC,EAAI,oBAAoBqC,aAAa,SAAUD,GAC/C/B,iBAEF4B,uBAGA9B,EAAsB6B,iBAAkB,EACxC,MAAMO,0BAA4B,WAChC,MAAMrB,EAAcV,WAAWC,iBAAiBT,EAAI,aAAc,MAAMa,MAAMF,QAAQ,KAAM,KACtFQ,EAAeX,WAAWC,iBAAiBT,EAAI,aAAc,MAAMU,OAAOC,QAAQ,KAAM,KACxFS,EAAgBZ,WAAWC,iBAAiBT,EAAI,oBAAqB,MAAMa,MAAMF,QAAQ,KAAM,KAC/FU,EAAiBb,WAAWC,iBAAiBT,EAAI,oBAAqB,MAAMU,OAAOC,QAAQ,KAAM,KAIjGG,EAHWN,WAAWC,iBAAiBT,EAAI,4BAA6B,MAAMwC,iBAAiB,QAAQ7B,QAAQ,KAAM,KAGlGS,EAAgBF,EACnCF,EAHWR,WAAWC,iBAAiBT,EAAI,4BAA6B,MAAMwC,iBAAiB,OAAO7B,QAAQ,KAAM,KAGjGU,EAAiBF,EAC1CnB,EAAI,YAAYe,WAAaD,EAC7Bd,EAAI,YAAYiB,UAAYD,GAuBxByB,EAAWC,SAASC,cAAc,6BAiCxC,OD5IoB,SAAUC,EAAQC,EAASC,EAAQC,EAASC,EAAOC,GAEpE7D,GACHsD,SAASX,iBAAiBzC,EAAW,YAAc,aAAa,SAAU4D,GACxE,IAAIC,EAAID,EACJA,EAAEE,UACJD,EAAID,EAAEE,QAAQ,IAIhB,IAAK,IAAIC,EAAI,EAAGA,EAAIhE,EAAWiE,OAAQD,IACrChE,EAAWgE,GAAGF,EAAEI,QAASJ,EAAEK,YAKjCpE,GAAU,EACV,IAAIqE,GAAW,EAAWC,GAAa,EACnCC,EAAS,EAAOC,EAAS,EAAOC,EAAQ,EAAOC,EAAQ,EAI3DjB,EAAQd,iBAAiBzC,EAAW,aAAe,aAAa,SAAU4D,GAGxE,GAFAA,EAAEa,kBACFb,EAAEc,iBACiC,UAA/BpB,EAAOqB,QAAQC,YACjB,OAGF,IAAIf,EAAID,EACJA,EAAEE,UACJD,EAAID,EAAEE,QAAQ,IAGhBK,GAAW,EACXE,EAASf,EAAOuB,WAAahB,EAAEI,QAC/BK,EAAShB,EAAOwB,UAAYjB,EAAEK,WAIhCd,SAASX,iBAAiBzC,EAAW,WAAa,WAAW,WACvD0D,GAASU,GACXV,EAAMJ,EAAQE,EAAQuB,SAASzB,EAAOlB,MAAMI,MAAOuC,SAASzB,EAAOlB,MAAMG,MAG3E4B,GAAW,EACXC,GAAa,KAIfhB,SAASX,iBAAiBzC,EAAW,YAAc,aAAa,WAC1D2D,GAAUS,GACZT,EAAOL,EAAQyB,SAASzB,EAAOlB,MAAMI,MAAOuC,SAASzB,EAAOlB,MAAMG,SAKtExC,EAAWiF,MAAK,SAASC,KAAMC,EAAGC,GAChC,GAAKhB,EAAL,CAeA,GAXKC,IACHA,GAAa,EACTX,GACFA,EAAQH,EAAQiB,EAAOC,IAI3BD,EAAQW,EAAIb,EACZG,EAAQW,EAAIb,EAGwB,SAAhChB,EAAOqB,QAAQS,aAAyB,CAC1C,GAAIb,EAAQ,GAAKA,GAASrE,OAAOmF,WAAa/B,EAAOgC,YACnD,OAEF,GAAId,EAAQ,GAAKA,GAAStE,OAAOqF,YAAcjC,EAAOkC,aACpD,OAIJlC,EAAOlB,MAAMI,KAAO+B,EAAQ,KAC5BjB,EAAOlB,MAAMG,IAAMiC,EAAQ,SC0B3BiB,CAAStC,EAAUA,EADAC,SAASC,cAAc,sBAtB1B,KACdxC,EAAsB6B,iBAAkB,EACxCO,+BAEY,CAACyC,EAAIlC,EAAQmC,EAAIC,KACxBF,EAAGb,WAAaa,EAAGJ,YAAepE,WAAWC,iBAAiBqC,EAAQ,MAAMjC,MAAMF,QAAQ,KAAM,KACnGqE,EAAGtD,MAAMI,KAAQtB,WAAWC,iBAAiBqC,EAAQ,MAAMjC,MAAMF,QAAQ,KAAM,KAAOqE,EAAGJ,YAAe,KAC/FI,EAAGb,WAAa,IACzBa,EAAGtD,MAAMI,KAAO,OAEbkD,EAAGZ,UAAYY,EAAGF,aAAgBtE,WAAWC,iBAAiBqC,EAAQ,MAAMpC,OAAOC,QAAQ,KAAM,KACpGqE,EAAGtD,MAAMG,IAAOrB,WAAWC,iBAAiBqC,EAAQ,MAAMpC,OAAOC,QAAQ,KAAM,KAAOqE,EAAGF,aAAgB,KAChGE,EAAGZ,UAAY,IACxBY,EAAGtD,MAAMG,IAAM,OAEjB1B,EAAsB6B,iBAAkB,EACxCO,+BAEa,WACbA,+BAMFtC,EAAOD,EAAI,qBAAsBmF,IAE/B,MAAMC,EAAUD,EAAIE,SAAWF,EAAIG,cAAcC,OAC3CC,EAAUL,EAAIM,SAAWN,EAAIG,cAAcI,OAC3CtE,EAAgBZ,WAAWC,iBAAiBT,EAAI,oBAAqB,MAAMa,MAAMF,QAAQ,KAAM,KAC/FU,EAAiBb,WAAWC,iBAAiBT,EAAI,oBAAqB,MAAMU,OAAOC,QAAQ,KAAM,KACjGa,EAAehB,WAAWC,iBAAiBT,EAAI,4BAA6B,MAAMwC,iBAAiB,aAAa7B,QAAQ,KAAM,KAC9Hc,EAAgBjB,WAAWC,iBAAiBT,EAAI,4BAA6B,MAAMwC,iBAAiB,cAAc7B,QAAQ,KAAM,KAEtI,IAAIW,EAAW8D,EAAS,GAAM5D,EAC1BD,EAAWiE,EAAS,GAAM/D,EAE1BH,EAAW,IACbA,EAAW,GAETC,EAAW,IACbA,EAAW,GAETD,EAAWE,EAAeJ,IAC5BE,EAAWF,EAAgBI,GAEzBD,EAAWE,EAAgBJ,IAC7BE,EAAWF,EAAiBI,GAE9BzB,EAAI,4BAA4B0B,MAAMG,IAAMN,EAAW,KACvDvB,EAAI,4BAA4B0B,MAAMI,KAAOR,EAAW,KACxDiB,+BAGK,CACL5C,KAAM,kBACNgG,cAAe1D,qBACf2D,gBAAiBvF"}