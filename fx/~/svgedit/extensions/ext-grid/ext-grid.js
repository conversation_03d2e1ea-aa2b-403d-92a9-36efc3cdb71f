/**
 * @file ext-grid.js
 *
 * @license Apache-2.0
 *
 * @copyright 2010 Redou Mine, 2010 Alexis <PERSON>
 *
 */
const loadExtensionTranslation=async function(e){let r;const a=e.configObj.pref("lang");try{r=await function __variableDynamicImportRuntime0__(e){switch(e){case"./locale/en.js":return Promise.resolve().then((function(){return t}));case"./locale/fr.js":return Promise.resolve().then((function(){return n}));case"./locale/tr.js":return Promise.resolve().then((function(){return o}));case"./locale/zh-CN.js":return Promise.resolve().then((function(){return i}));default:return new Promise((function(t,n){("function"==typeof queueMicrotask?queueMicrotask:setTimeout)(n.bind(null,new Error("Unknown variable dynamic import: "+e)))}))}}("./locale/".concat(a,".js"))}catch(e){console.warn("Missing translation (".concat(a,") for ").concat("grid"," - using 'en'")),r=await Promise.resolve().then((function(){return t}))}e.i18next.addResourceBundle(a,"grid",r.default)};var e={name:"grid",async init(){const e=this;await loadExtensionTranslation(e);const{svgCanvas:t}=e,{$id:n,$click:o,NS:i}=t,r=n("svgcanvas").ownerDocument,{assignAttributes:a}=t,s=document.createElement("canvas"),c=n("canvasBackground"),l=t.getTypeMap(),d=[.01,.1,1,10,100,1e3];let u=e.configObj.curConfig.showGrid||!1;s.style.display="none",e.$svgEditor.appendChild(s);const g=r.createElementNS(i.SVG,"svg");a(g,{id:"canvasGrid",width:"100%",height:"100%",x:0,y:0,overflow:"visible",display:"none"}),c.appendChild(g);const h=r.createElementNS(i.SVG,"defs"),p=r.createElementNS(i.SVG,"pattern");a(p,{id:"gridpattern",patternUnits:"userSpaceOnUse",x:0,y:0,width:100,height:100});const f=r.createElementNS(i.SVG,"image");a(f,{x:0,y:0,width:100,height:100}),p.append(f),h.append(p),n("canvasGrid").appendChild(h);const m=r.createElementNS(i.SVG,"rect");a(m,{width:"100%",height:"100%",x:0,y:0,"stroke-width":0,stroke:"none",fill:"url(#gridpattern)",style:"pointer-events: none; display:visible;"}),n("canvasGrid").appendChild(m);const updateGrid=n=>{const o=l[e.configObj.curConfig.baseUnit]*n,i=100/o;let r=1;d.some((e=>(r=e,i<=e)));const a=r*o;s.width=a,s.height=a;const c=s.getContext("2d"),u=.5,g=a/10;c.globalAlpha=.2,c.strokeStyle=e.configObj.curConfig.gridColor;for(let e=1;e<10;e++){const t=Math.round(g*e)+.5,n=0;c.moveTo(t,a),c.lineTo(t,n),c.moveTo(a,t),c.lineTo(n,t)}c.stroke(),c.beginPath(),c.globalAlpha=.5,c.moveTo(u,a),c.lineTo(u,0),c.moveTo(a,u),c.lineTo(0,u),c.stroke();const h=s.toDataURL("image/png");f.setAttribute("width",a),f.setAttribute("height",a),f.parentNode.setAttribute("width",a),f.parentNode.setAttribute("height",a),t.setHref(f,h)},gridUpdate=()=>{u&&updateGrid(t.getZoom()),n("canvasGrid").style.display=u?"block":"none",n("view_grid").pressed=u};return{name:e.i18next.t("".concat("grid",":name")),zoomChanged(e){u&&updateGrid(e)},callback(){const t=document.createElement("template"),i="".concat("grid",":buttons.0.title");t.innerHTML='\n          <se-button id="view_grid" title="'.concat(i,'" src="grid.svg"></se-button>\n        '),n("editor_panel").append(t.content.cloneNode(!0)),o(n("view_grid"),(()=>{e.configObj.curConfig.showGrid=u=!u,gridUpdate()})),u&&gridUpdate()}}}},t=Object.freeze({__proto__:null,default:{name:"View Grid",buttons:[{title:"Show/Hide Grid"}]}}),n=Object.freeze({__proto__:null,default:{name:"Grille",buttons:[{title:"Afficher/Cacher Grille"}]}}),o=Object.freeze({__proto__:null,default:{name:"Izgarayı Görüntüle",buttons:[{title:"Izgara Göster/Gizle"}]}}),i=Object.freeze({__proto__:null,default:{name:"网格视图",buttons:[{title:"显示/隐藏网格"}]}});export{e as default};
//# sourceMappingURL=ext-grid.js.map
