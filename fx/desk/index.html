<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FX Desk - Рабочий стол</title>
    <script type="module" src="/fx.js"></script>
</head>
<body>
    <fx-desk id="desk"></fx-desk>

    <script type="module">
        import './desk.js';
    
        setTimeout(() => {
            if (!desk.items?.length) {
                desk.addItem({
                    label: 'Добро пожаловать!',
                    icon: 'mdi:hand-wave',
                    backgroundColor: '#e8f5e8',
                    borderColor: '#4caf50',
                    x: 100,
                    y: 100,
                    width: 160,
                    height: 100
                })
                
                desk.addItem({
                    label: 'Рабочий стол FX',
                    icon: 'mdi:desktop-mac',
                    backgroundColor: '#e3f2fd',
                    borderColor: '#2196f3',
                    x: 300,
                    y: 150,
                    width: 180,
                    height: 120
                })

                desk.$update();
            }
        }, 100)
    </script>
</body>
</html>
