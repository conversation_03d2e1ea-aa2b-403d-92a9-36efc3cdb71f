import { FxElement, html, css } from '../../fx.js';
import '../button/button.js';

customElements.define('fx-calendar', class FxCalendar extends FxElement {
    static properties = {
        base: { type: Object },
        items: { type: Array, default: [] },
        period: { type: Array, local: true },
        start: { type: Number, default: -1, notify: 'period-changed' },
        end: { type: Number, default: 0, notify: 'period-changed' },
        simpleMode: { type: Boolean, local: true },
        calendarData: { type: Object, local: true }
    }
    get _periods() {
        if (!this.period) return '';
        const p0 = this.period[0].split('-').reverse().join('-');
        if (this.period[0] === this.period[1]) return p0;
        const p1 = this.period[1].split('-').reverse().join('-');
        return p0 + ' ... ' + p1;
    }

    constructor() {
        super();
        this.listen('period-changed', (e) => {
            this.items = [];
            const currentYear = new Date().getFullYear(),
                startYear = currentYear + this.start,
                endYear = currentYear + this.end;
            for (let y = startYear; y <= endYear; y++) {
                for (let m = 0; m < 12; m++) {
                    this.items.push({ date: new Date(y, m), str: FX.dates(new Date(y, m)).monthStr, m, y });
                }
            }
            this.currentDate = FX.dates().short;
            this.period = this.period || [this.currentDate, this.currentDate];
            this.$update();
            setTimeout(() => this._clickCurrent(), 100);
        })
    }
    async firstUpdated() {
        super.firstUpdated();
        await new Promise((r) => setTimeout(r, 0));
        this.base?.listen('select-item-calendar', (e) => this.getCalendarData(this.base, e.detail.item));
    }

    async getCalendarData(base, item) {
        let children = FX.allItems(item, true);
        const bsAllPersons = await base.getBsFlatByKeys(children);
        let calendarData = { ds: {}, de: {}, ev: {} };
        const type = 'item:',
            flat = bsAllPersons;
        Object.keys(flat).filter(i => i.startsWith(type)).forEach(key => {
            const doc = flat[key].doc;
            if (doc.dateStart && doc.dateStartInCalendar) {
                let d = doc.dateStart.substr(5, 5).split('-');
                d = (d[0] - 1) + '-' + (+d[1]);
                calendarData.ds[d] ||= { count: 0, items: [] };
                calendarData.ds[d].count += 1;
                calendarData.ds[d].items.push(flat[key]);
            }
            if (doc.dateEnd && doc.dateEndInCalendar) {
                let d = doc.dateEnd.substr(5, 5).split('-');
                d = (d[0] - 1) + '-' + (+d[1]);
                calendarData.de[d] ||= { count: 0, items: [] };
                calendarData.de[d].count += 1;
                calendarData.de[d].items.push(flat[key]);
            }
            Object.values(doc.spouses || {}).map(w => {
                if (w.startWedding && w.startInCalendar) {
                    let d = w.startWedding.substr(5, 5).split('-');
                    d = (d[0] - 1) + '-' + (+d[1]);
                    calendarData.ev[d] ||= { count: 0, item: flat[key], items: [], label: 'Свадьба' };
                    calendarData.ev[d].count += 1;
                    calendarData.ev[d].items.push({ label: 'Свадьба - ', group: flat[key].label + ' + ' + flat[w._id]?.label, date1: w.startWedding });
                }
                if (w.endWedding && w.endInCalendar) {
                    let d = w.endWedding.substr(5, 5).split('-');
                    d = (d[0] - 1) + '-' + (+d[1]);
                    calendarData.ev[d] ||= { count: 0, item: flat[key], items: [], label: 'Развод' };
                    calendarData.ev[d].count += 1;
                    calendarData.ev[d].items.push({ label: 'Развод - ', group: flat[key].label + ' + ' + flat[w._id]?.label, date1: w.endWedding });
                }
            })
            if (flat[key].doc.events?.length) {
                flat[key].doc.events.forEach(i => {
                    if (i?.showEvent) {
                        let d = i.date1.substr(5, 5).split('-');
                        d = (d[0] - 1) + '-' + (+d[1]);
                        calendarData.ev[d] ||= { count: 0, item: flat[key], items: [] };
                        calendarData.ev[d].count += 1;
                        calendarData.ev[d].items.push(i);
                    }
                })
            }
        })
        this.calendarData = calendarData;
        this.$update();
    }
    _clickSelected(e) {
        this.$id(FX.dates(new Date(this.period?.[0])).monthStr).scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
    _clickCurrent(e) {
        this.$id(FX.dates().monthStr).scrollIntoView({ behavior: 'smooth', block: "end" });
    }

    static styles = css`
        :host {
            display: flex;
            flex-direction: column;
            flex: 1;
            width: 100%;
            height: 100%;
            position: relative;
            /* color: #505050; */
            user-select: none;
            overflow: hidden;
        }
        .box {
            display: flex;
            margin: 0 4px 4px 4px;
            z-index: 1;
        }
        .cell {
            display: flex;
            align-items: center;
            justify-content: center;
            width: calc(1/7 * 100%);
            height: 40px;
            box-shadow: 0 0 1px 0 gray;
        }
    `

    render() {
        return html`
            <div style="height: 28px; z-index: 1; padding: 3px; display: flex; align-items: center">
                <div style="cursor: pointer;" @click="${this._clickSelected}">${this._periods}</div>
                <div style="flex: 1"></div>
                <fx-button @click="${this._clickCurrent}" name="cb-airport-location" title="show current month"></fx-button>
            </div>
            <div class="box br panel">
                ${['пн', 'вт', 'ср', 'чт', 'пт', 'сб', 'вс'].map(i => html`<div class="fs horizontal w100 h12 mb2 center">${i}</div>`)}
            </div>
            <div class="flex overflow">
                ${this.items.map(i => html`<fx-calendar-month .item = ${i} id="${i.str}"></fx-calendar-month>`)}
            </div>
        `
    }
})

customElements.define('fx-calendar-month', class FxCalendarMonth extends FxElement {
    static properties = {
        item: { type: Object },
        date: { type: Object },
        period: { type: Array, local: true },
        showMonth: { type: Boolean, default: true, reflect: true },
        simpleMode: { type: Boolean, local: true }
    }
    get date() { return this._date = this._date || this.item?.date || thid.date || new Date() }
    get year() { return this._year = this._year = this.date.getFullYear() }
    get month() { return this._month = this._month = this.date.getMonth() }
    get days() { return new Date(this.year, this.month + 1, 0).getDate() }
    get weekday() { return new Date(this.year, this.month, 1).getDay() || 7 }
    get monthStr() {
        const m = ['январь', 'февраль', 'март', 'апрель', 'май', 'июнь', 'июль', 'август', 'сентябрь', 'октябрь', 'ноябрь', 'декабрь'];
        return m[this.month] + ', ' + this.year;
    }
    get calendar() {
        let arr = [...Array(this.weekday - 1).keys()].map(i => -1);
        arr = [...arr, ...Array(this.days).keys()];
        const length = arr.length > 28 && arr.length < 35 ? 35 - arr.length : arr.length > 35 ? 42 - arr.length : 0;
        if (length)
            arr = [...arr.map(i => i), ...[...Array(length).keys()].map(i => -1)];
        return [...arr.map(i => ++i)];
    }

    _selectMonth() {
        if (this.simpleMode) return;
        this.period[0] = FX.dates(new Date(this.year, this.month, 1)).short;
        this.period[1] = FX.dates(new Date(this.year, this.month, this.days)).short;
        this.$update();
    }
    _selectYear() {
        if (this.simpleMode) return;
        this.period[0] = FX.dates(new Date(this.year, 0, 1)).short;
        this.period[1] = FX.dates(new Date(this.year, 11, 31)).short;
        this.$update();
    }

    static styles = css`
        :host{
            font-family: Arial;
            /* color: gray; */
        }
        .box {
            position: relative;
            display: flex;
            flex-wrap: wrap;
            border: 1px solid lightgray;
            margin: 4px;
        }
        .month {
            margin: 8px 0;
            /* color: #505050; */
            cursor: pointer;
        }
        .cell {
            width: calc(1/7 * 100%);
        }
    `

    render() {
        return html`
            ${this.$0.hideCalendar ? html`` : html`
                ${!this.showMonth ? html`` : html`
                    <span class="month" @click="${this._selectMonth}" style="margin-left: 8px">${this.monthStr.split(' ')[0]}</span>
                    <span class="month" @click="${this._selectYear}">${this.monthStr.split(' ')[1]}</span>
                `}
                <div class="box">
                    ${this.calendar.map(i => html`
                        <fx-calendar-cell class="cell" .day="${i}" .year="${this.year}" .month="${this.month}" .days="${this.days}"></fx-calendar-cell>
                    `)}
                </div>
            `}
        `;
    }
})

customElements.define('fx-calendar-cell', class extends FxElement {
    static properties = {
        day: { type: Number, default: 0 },
        month: { type: Number },
        year: { type: Number },
        days: { type: Number, default: 0 },
        period: { type: Array, local: true },
        dragMarker: { type: Object, local: true },
        simpleMode: { type: Boolean, local: true },
        calendarData: { type: Object, local: true }
    }
    get ds() { return this.calendarData?.ds?.[this.month + '-' + this.day] || '' }
    get de() { return this.calendarData?.de?.[this.month + '-' + this.day] || '' }
    get ev() { return this.calendarData?.ev?.[this.month + '-' + this.day] || '' }
    get isToday() { return this.day > 0 && FX.dates().short === this.dateStr }
    get date() { return this._date = this._date = new Date(this.year, this.month, this.day) }
    get dateS() {
        if (this.period)
            return this._dateS = this._dateS = new Date(+this.period[0].split('-')[0], this.period[0].split('-')[1] - 1, +this.period[0].split('-')[2]);
    }
    get dateE() {
        if (this.period)
            return this._dateE = this._dateE = new Date(+this.period[1].split('-')[0], this.period[1].split('-')[1] - 1, +this.period[1].split('-')[2]);
    }
    get dateStr() { return this._dateStr = this._dateStr = FX.dates(this.date).short }
    get isStart() { return this.day > 0 && this.period && this.dateStr === this.period?.[0] }
    get isEnd() { return this.day > 0 && this.period && this.dateStr === this.period?.[1] }
    get inPeriod() { return this.day > 0 && this.date >= this.dateS && this.date <= this.dateE }
    get color() { return `hsla(${this.month * 25}, 70%, 70%, 0.2)` }

    _click(e) {
        if (this.day > 0) {
            this.period[0] = this.period[1] = this.dateStr;
            this.$update();
        }
    }
    async showInfo(e) {
        e.stopPropagation();
        const id = e.target.id;
        if (this[id]?.items?.length) {
            const info = new CalendarInfo;
            const label = this.dateStr + '  -  ' + (id === 'ds' ? 'birthday' : id === 'de' ? 'day of death' : 'events');
            await FX.show('dropdown', info, { id, dd: this[id] }, { label, showHeader: true, align: 'left', target: e.target, intersect: true }, 'fx', 'fx');
        }
    }

    static styles = css`
        :host {
            position: relative;
        }
        .cell {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
        }
        .date:hover {
            filter: brightness(.75) contrast(200%);
        }
        .marker {
            position: absolute; 
            bottom: 0;
            width: 12px; 
            height: 12px; 
            border-radius: 50%;
            cursor: pointer;
        }
        .start {
            left: 0;
            background-color: lightgreen;
        }
        .end {
            right: 0;
            background-color: lightblue;
        }
        .dd {
            width: 16px;
            height: 16px;
            position: absolute; 
            font-size: 10px;
            border: 1px solid;
            border-radius: 50%;
            text-align: center;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-color: darkgray;
        }
    `

    render() {
        return html`
            ${this.$0.hideCalendar ? html`` : html`
                <div class="cell ${this.day > 0 ? 'date' : ''}" style="color: ${this.inPeriod ? 'white' : ''}; background-color: ${this.inPeriod ? 'var(--fx-color-selected)' : this.color}; cursor: ${this.day > 0 ? 'pointer' : 'inset'}; box-shadow: ${this.isToday ? 'inset 0 0 2px 1px blue' : '0 0 1px 0 gray'}" @dragover="${this._dragover}" @drop="${this._drop}" @click="${this._click}">${this.day < 1 ? '' : this.day}</div>
                <div id="ds" class="dd" style="top: 1px; left: 1px; color: red; display: ${this.ds ? 'flex' : 'none'}; background-color: yellow" @click=${e => this.showInfo(e)}>${this.ds?.count || ''}</div>
                <div id="de" class="dd" style="bottom: 1px; left: 1px; color: gray;  background-color: #eee; display: ${this.de ? 'flex' : 'none'}" @click=${e => this.showInfo(e)}>${this.de?.count || ''}</div>
                <div id="ev" class="dd" style="top: 1px; right: 1px; background-color: lightgreen; color: green; display: ${this.ev ? 'flex' : 'none'}" @click=${e => this.showInfo(e)}>${this.ev?.count || ''}</div>
                ${this.day < 1 || !this.isStart || this.simpleMode ? html`` : html`
                    <div id="start" draggable="true" class="marker start" @dragstart="${this._dragstart}" @touchstart="${this._dragstart}"></div>
                `}
                ${this.day < 1 || !this.isEnd || this.simpleMode ? html`` : html`
                    <div id="end" draggable="true" class="marker end" @dragstart="${this._dragstart}" ></div>
                `}
            `}
        `;
    }

    _dragstart(e) {
        if (this.simpleMode) return;
        this.dragMarker = { id: e.target.id, date: this.date, s: new Date(this.period[0]), e: new Date(this.period[1]) };
    }
    _dragover(e) {
        if (this.day > 0) {
            if (this.dragMarker?.id === 'start' && this.date < this.dragMarker.e)
                e.preventDefault();
            if (this.dragMarker?.id === 'end' && this.date > this.dragMarker.s)
                e.preventDefault();
        }
    }
    _drop(e) {
        e.preventDefault();
        if (this.day > 0) {
            if (this.dragMarker?.id === 'start')
                this.period[0] = this.dateStr;
            if (this.dragMarker?.id === 'end')
                this.period[1] = this.dateStr;
            this.$update();
        }
    }
})

class CalendarInfo extends FxElement {
    static properties = {
        dd: { type: Object },
        id: { type: String }
    }

    years(i) {
        const currentYear = new Date().getFullYear();
        if (this.id === 'ds') {
            const d1 = (new Date(i.doc.dateStart)).getTime();
            const d2 = (new Date(i.doc.dateStart)).setFullYear(currentYear);
            const age = (Math.abs(d2 - d1) / 1000 / 60 / 60 / 24 / 365.25).toFixed(2);
            let death = '';
            if (i.doc.dateEnd) {
                death = (new Date(i.doc.dateEnd)).getTime();
                death = (Math.abs(death - d1) / 1000 / 60 / 60 / 24 / 365.25).toFixed(2);
            }
            return 'age: ' + age + (death ? ', age of death: ' + death : '');
        }
        if (this.id === 'de') {
            const d1 = (new Date(i.doc.dateStart)).getTime();
            const d2 = (new Date(i.doc.dateEnd)).getTime();
            const d3 = (new Date(i.doc.dateStart)).setFullYear(currentYear);
            const age = (Math.abs(d2 - d1) / 1000 / 60 / 60 / 24 / 365.25).toFixed(2);
            const passed = (Math.abs(d3 - d2) / 1000 / 60 / 60 / 24 / 365.25).toFixed(2);
            return 'age of death: ' + age + ', passed: ' + passed + ', age: ' + ((+age) + (+passed)).toFixed(2);
        }
        if (this.id === 'ev') {
            const d1 = (new Date(i.date1 || i.doc.date1)).getTime();
            const d3 = (new Date(i.date1 || i.doc.date1)).setFullYear(currentYear);
            const passed = (Math.abs(d3 - d1) / 1000 / 60 / 60 / 24 / 365.25).toFixed(2);
            return 'passed: ' + passed + ' year';
        }
    }

    static styles = css`
        :host {
            display: flex;
            background: var(--fx-background);
            border: 1px solid gray;
            /* color: gray; */
        }
    `

    render() {
        return html`
            <div class="vertical flex" style="min-width: 200px;">
                ${this.dd?.items?.length ? html`
                    ${this.dd?.items.map(i => html`
                        <div class="vertical" style="padding: 4px 8px; border-bottom: 1px solid darkgray">
                            <div>${i.label || i.doc.label} ${i.group || i.doc.group} (${(i.doc?.dateStart || i.date1 || i.doc.date1)?.split?.('T')[0]}${(this.id === 'ds' || this.id === 'de') && i.doc?.dateEnd ? '...' + i.doc.dateEnd.split('T')[0] : ''})</div>
                            <div>${this.years(i)}</div>
                        </div>
                    `)}
                ` : html``}
            </div>
        `
    }
}
customElements.define('fx-calendar-info', CalendarInfo);

// FX.showCachedIcons = true;
// FX.showLoadIcons = true;

const usedIcons =
{
    "cb-airport-location": "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 32 32\"><path fill=\"currentColor\" d=\"M30 15h-6.07A8.008 8.008 0 0 0 17 8.07V2h-2v6.07A8.008 8.008 0 0 0 8.07 15H2v2h6.07A8.008 8.008 0 0 0 15 23.93V30h2v-6.07A8.008 8.008 0 0 0 23.93 17H30Zm-14 7a6 6 0 1 1 6-6a6.007 6.007 0 0 1-6 6Z\"/></svg>"
}

FX.setIcons(usedIcons);
