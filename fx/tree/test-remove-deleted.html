<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест удаления помеченных элементов</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        
        fx-tree {
            height: 400px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        
        .info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Тест функции удаления помеченных элементов</h1>
        
        <div class="info">
            <strong>Инструкция:</strong>
            <ol>
                <li>Отметьте несколько элементов галочками</li>
                <li>Нажмите красную кнопку "X" для пометки к удалению</li>
                <li>Появится кнопка корзины - нажмите её для окончательного удаления</li>
                <li>Элементы без родителей попадут в папку "~~~"</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>Дерево для тестирования</h3>
            <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                <button class="btn-primary" onclick="createTestData()">Создать тестовые данные</button>
                <button class="btn-warning" onclick="markSomeForDeletion()">Пометить некоторые к удалению</button>
                <button class="btn-success" onclick="showTreeStructure()">Показать структуру</button>
                <button class="btn-danger" onclick="clearAll()">Очистить всё</button>
            </div>
            
            <fx-tree id="testTree"></fx-tree>
        </div>

        <div class="test-section">
            <h3>Информация о дереве</h3>
            <div id="treeInfo">Загрузите тестовые данные...</div>
        </div>
    </div>

    <script type="module">
        import '/fx.js';
        import '/fx/tree/tree.js';

        const tree = document.getElementById('testTree');
        
        // Создание тестовых данных
        window.createTestData = function() {
            const testData = {
                _id: 'root',
                label: 'Корневая папка',
                expanded: true,
                items: [
                    {
                        _id: 'folder1',
                        label: 'Папка 1',
                        expanded: true,
                        items: [
                            { _id: 'item1_1', label: 'Элемент 1.1' },
                            { _id: 'item1_2', label: 'Элемент 1.2' },
                            {
                                _id: 'subfolder1',
                                label: 'Подпапка 1',
                                expanded: true,
                                items: [
                                    { _id: 'item1_1_1', label: 'Элемент 1.1.1' },
                                    { _id: 'item1_1_2', label: 'Элемент 1.1.2' }
                                ]
                            }
                        ]
                    },
                    {
                        _id: 'folder2',
                        label: 'Папка 2',
                        expanded: true,
                        items: [
                            { _id: 'item2_1', label: 'Элемент 2.1' },
                            { _id: 'item2_2', label: 'Элемент 2.2' }
                        ]
                    },
                    { _id: 'item_root', label: 'Корневой элемент' }
                ]
            };
            
            tree.item = testData;
            updateTreeInfo();
        };

        // Пометка некоторых элементов к удалению
        window.markSomeForDeletion = function() {
            const flatItems = tree.flatItems;
            if (flatItems['folder1']) {
                flatItems['folder1']._deleted = true;
            }
            if (flatItems['item2_1']) {
                flatItems['item2_1']._deleted = true;
            }
            tree.$update();
            updateTreeInfo();
        };

        // Показ структуры дерева
        window.showTreeStructure = function() {
            console.log('Структура дерева:', tree.item);
            console.log('Плоский список:', tree.flatItems);
            console.log('Все элементы:', tree.allItems);
            console.log('К удалению:', tree.toDelete);
            updateTreeInfo();
        };

        // Очистка всех данных
        window.clearAll = function() {
            tree.item = { _id: 'root', label: 'Пустое дерево', items: [] };
            updateTreeInfo();
        };

        // Обновление информации о дереве
        function updateTreeInfo() {
            const info = document.getElementById('treeInfo');
            const allCount = tree.allItems ? tree.allItems.length : 0;
            const deletedCount = tree.toDelete ? tree.toDelete.length : 0;
            
            info.innerHTML = `
                <strong>Статистика дерева:</strong><br>
                Всего элементов: ${allCount}<br>
                Помечено к удалению: ${deletedCount}<br>
                Есть осиротевшие папки "~~~": ${tree.item.items ? tree.item.items.some(i => i.label === '~~~') : false}
            `;
        }

        // Слушаем события дерева
        tree.addEventListener('removed-deleted', (e) => {
            console.log('Удалены помеченные элементы:', e.detail);
            alert(`Удалены помеченные элементы. Осиротевших элементов: ${e.detail.orphanedCount}`);
            updateTreeInfo();
        });

        tree.addEventListener('delete', (e) => {
            console.log('Событие delete:', e.detail);
            updateTreeInfo();
        });

        // Инициализация
        createTestData();
    </script>
</body>
</html>
