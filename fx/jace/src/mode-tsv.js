ace.define("ace/mode/csv_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],function(e,t,n){"use strict";var r=e("../lib/oop"),i=e("./text_highlight_rules").TextHighlightRules,s=function(){i.call(this)};r.inherits(s,i),t.CsvHighlightRules=s}),ace.define("ace/mode/csv",["require","exports","module","ace/lib/oop","ace/mode/text","ace/lib/lang","ace/mode/csv_highlight_rules"],function(e,t,n){"use strict";function f(e,t,n){var r=[],i=e.split(n.separatorRegex),s=n.spliter,o=n.quote||'"',u=(t||"start").split("-"),f=parseInt(u[1])||0,l=u[0]=="string",c=!l;for(var h=0;h<i.length;h++){var p=i[h];if(p){var d=!1;p==s&&!l?(f++,c=!0,d=!0):p==o?c?(l=!0,c=!1):l&&(i[h+1]==""&&i[h+2]==o?(p=o+o,h+=2):l=!1):c=!1,r.push({value:p,type:a[f%a.length]+".csv_"+f+(d?".csv_separator":"")})}}return{tokens:r,state:l?"string-"+f:"start"}}var r=e("../lib/oop"),i=e("./text").Mode,s=e("../lib/lang").escapeRegExp,o=e("./csv_highlight_rules").CsvHighlightRules,u=function(e){this.HighlightRules=o,e||(e={});var t=[e.splitter||",",e.quote||'"'].map(s).join("|");this.$tokenizer={getLineTokens:function(e,t,n){return f(e,t,this.options)},options:{quotes:e.quote||'"',separatorRegex:new RegExp("("+t+")"),spliter:e.splitter||","},states:{}},this.$highlightRules=new this.HighlightRules};r.inherits(u,i),function(){this.getTokenizer=function(){return this.$tokenizer},this.$id="ace/mode/csv"}.call(u.prototype),t.Mode=u;var a=["keyword","text","string","string.regex","variable","constant.numeric"]}),ace.define("ace/mode/tsv_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],function(e,t,n){"use strict";var r=e("../lib/oop"),i=e("./text_highlight_rules").TextHighlightRules,s=function(){i.call(this)};r.inherits(s,i),t.TsvHighlightRules=s}),ace.define("ace/mode/tsv",["require","exports","module","ace/mode/csv","ace/mode/tsv_highlight_rules"],function(e,t,n){"use strict";var r=e("./csv").Mode,i=e("./tsv_highlight_rules").TsvHighlightRules,s=function(e){var t=new r({splitter:"	",quote:'"'});return t.HighlightRules=i,t.$id="ace/mode/tsv",t};t.Mode=s});                (function() {
                    ace.require(["ace/mode/tsv"], function(m) {
                        if (typeof module == "object" && typeof exports == "object" && module) {
                            module.exports = m;
                        }
                    });
                })();
            