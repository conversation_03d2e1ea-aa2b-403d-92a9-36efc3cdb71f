{"version": 3, "file": "ext-opensave.js", "sources": ["../../../../node_modules/browser-fs-access/dist/index.modern.js", "../../../../src/editor/extensions/ext-opensave/ext-opensave.js", "../../../../src/editor/extensions/ext-opensave/locale/en.js", "../../../../src/editor/extensions/ext-opensave/locale/fr.js", "../../../../src/editor/extensions/ext-opensave/locale/tr.js", "../../../../src/editor/extensions/ext-opensave/locale/zh-CN.js"], "sourcesContent": ["const e=(()=>{if(\"undefined\"==typeof self)return!1;if(\"top\"in self&&self!==top)try{top}catch(e){return!1}else if(\"showOpenFilePicker\"in self)return\"showOpenFilePicker\";return!1})(),t=e?Promise.resolve().then(function(){return l}):Promise.resolve().then(function(){return v});async function n(...e){return(await t).default(...e)}const r=e?Promise.resolve().then(function(){return y}):Promise.resolve().then(function(){return b});async function i(...e){return(await r).default(...e)}const a=e?Promise.resolve().then(function(){return m}):Promise.resolve().then(function(){return _});async function o(...e){return(await a).default(...e)}const s=async e=>{const t=await e.getFile();return t.handle=e,t};var c=async(e=[{}])=>{Array.isArray(e)||(e=[e]);const t=[];e.forEach((e,n)=>{t[n]={description:e.description||\"Files\",accept:{}},e.mimeTypes?e.mimeTypes.map(r=>{t[n].accept[r]=e.extensions||[]}):t[n].accept[\"*/*\"]=e.extensions||[]});const n=await window.showOpenFilePicker({id:e[0].id,startIn:e[0].startIn,types:t,multiple:e[0].multiple||!1,excludeAcceptAllOption:e[0].excludeAcceptAllOption||!1}),r=await Promise.all(n.map(s));return e[0].multiple?r:r[0]},l={__proto__:null,default:c};function u(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+\" is not an object.\"));var t=e.done;return Promise.resolve(e.value).then(function(e){return{value:e,done:t}})}return u=function(e){this.s=e,this.n=e.next},u.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new u(e)}const p=async(e,t,n=e.name,r)=>{const i=[],a=[];var o,s=!1,c=!1;try{for(var l,d=function(e){var t,n,r,i=2;for(\"undefined\"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);i--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new u(t.call(e));n=\"@@asyncIterator\",r=\"@@iterator\"}throw new TypeError(\"Object is not async iterable\")}(e.values());s=!(l=await d.next()).done;s=!1){const o=l.value,s=`${n}/${o.name}`;\"file\"===o.kind?a.push(o.getFile().then(t=>(t.directoryHandle=e,t.handle=o,Object.defineProperty(t,\"webkitRelativePath\",{configurable:!0,enumerable:!0,get:()=>s})))):\"directory\"!==o.kind||!t||r&&r(o)||i.push(p(o,t,s,r))}}catch(e){c=!0,o=e}finally{try{s&&null!=d.return&&await d.return()}finally{if(c)throw o}}return[...(await Promise.all(i)).flat(),...await Promise.all(a)]};var d=async(e={})=>{e.recursive=e.recursive||!1,e.mode=e.mode||\"read\";const t=await window.showDirectoryPicker({id:e.id,startIn:e.startIn,mode:e.mode});return p(t,e.recursive,void 0,e.skipDirectory)},y={__proto__:null,default:d},f=async(e,t=[{}],n=null,r=!1,i=null)=>{Array.isArray(t)||(t=[t]),t[0].fileName=t[0].fileName||\"Untitled\";const a=[];let o=null;if(e instanceof Blob&&e.type?o=e.type:e.headers&&e.headers.get(\"content-type\")&&(o=e.headers.get(\"content-type\")),t.forEach((e,t)=>{a[t]={description:e.description||\"Files\",accept:{}},e.mimeTypes?(0===t&&o&&e.mimeTypes.push(o),e.mimeTypes.map(n=>{a[t].accept[n]=e.extensions||[]})):o?a[t].accept[o]=e.extensions||[]:a[t].accept[\"*/*\"]=e.extensions||[]}),n)try{await n.getFile()}catch(e){if(n=null,r)throw e}const s=n||await window.showSaveFilePicker({suggestedName:t[0].fileName,id:t[0].id,startIn:t[0].startIn,types:a,excludeAcceptAllOption:t[0].excludeAcceptAllOption||!1});!n&&i&&i(s);const c=await s.createWritable();if(\"stream\"in e){const t=e.stream();return await t.pipeTo(c),s}return\"body\"in e?(await e.body.pipeTo(c),s):(await c.write(await e),await c.close(),s)},m={__proto__:null,default:f},w=async(e=[{}])=>(Array.isArray(e)||(e=[e]),new Promise((t,n)=>{const r=document.createElement(\"input\");r.type=\"file\";const i=[...e.map(e=>e.mimeTypes||[]),...e.map(e=>e.extensions||[])].join();r.multiple=e[0].multiple||!1,r.accept=i||\"\",r.style.display=\"none\",document.body.append(r);const a=e=>{\"function\"==typeof o&&o(),t(e)},o=e[0].legacySetup&&e[0].legacySetup(a,()=>o(n),r),s=()=>{window.removeEventListener(\"focus\",s),r.remove()};r.addEventListener(\"click\",()=>{window.addEventListener(\"focus\",s)}),r.addEventListener(\"change\",()=>{window.removeEventListener(\"focus\",s),r.remove(),a(r.multiple?Array.from(r.files):r.files[0])}),r.click()})),v={__proto__:null,default:w},h=async(e=[{}])=>(Array.isArray(e)||(e=[e]),e[0].recursive=e[0].recursive||!1,new Promise((t,n)=>{const r=document.createElement(\"input\");r.type=\"file\",r.webkitdirectory=!0;const i=e=>{\"function\"==typeof a&&a(),t(e)},a=e[0].legacySetup&&e[0].legacySetup(i,()=>a(n),r);r.addEventListener(\"change\",()=>{let t=Array.from(r.files);e[0].recursive?e[0].recursive&&e[0].skipDirectory&&(t=t.filter(t=>t.webkitRelativePath.split(\"/\").every(t=>!e[0].skipDirectory({name:t,kind:\"directory\"})))):t=t.filter(e=>2===e.webkitRelativePath.split(\"/\").length),i(t)}),r.click()})),b={__proto__:null,default:h},P=async(e,t={})=>{Array.isArray(t)&&(t=t[0]);const n=document.createElement(\"a\");let r=e;\"body\"in e&&(r=await async function(e,t){const n=e.getReader(),r=new ReadableStream({start:e=>async function t(){return n.read().then(({done:n,value:r})=>{if(!n)return e.enqueue(r),t();e.close()})}()}),i=new Response(r),a=await i.blob();return n.releaseLock(),new Blob([a],{type:t})}(e.body,e.headers.get(\"content-type\"))),n.download=t.fileName||\"Untitled\",n.href=URL.createObjectURL(await r);const i=()=>{\"function\"==typeof a&&a()},a=t.legacySetup&&t.legacySetup(i,()=>a(),n);return n.addEventListener(\"click\",()=>{setTimeout(()=>URL.revokeObjectURL(n.href),3e4),i()}),n.click(),null},_={__proto__:null,default:P};export{i as directoryOpen,h as directoryOpenLegacy,d as directoryOpenModern,n as fileOpen,w as fileOpenLegacy,c as fileOpenModern,o as fileSave,P as fileSaveLegacy,f as fileSaveModern,e as supported};\n", "/* globals seConfirm */\n/**\n * @file ext-opensave.js\n *\n * @license MIT\n *\n * @copyright 2020 OptimistikSAS\n *\n */\n\n/**\n   * @type {module:svgcanvas.EventHandler}\n   * @param {external:Window} wind\n   * @param {module:svgcanvas.SvgCanvas#event:saved} svg The SVG source\n   * @listens module:svgcanvas.SvgCanvas#event:saved\n   * @returns {void}\n   */\nimport { fileOpen, fileSave } from 'browser-fs-access'\n\nconst name = 'opensave'\nlet handle = null\n\nconst loadExtensionTranslation = async function (svgEditor) {\n  let translationModule\n  const lang = svgEditor.configObj.pref('lang')\n  try {\n    translationModule = await import(`./locale/${lang}.js`)\n  } catch (_error) {\n    console.warn(`Missing translation (${lang}) for ${name} - using 'en'`)\n    translationModule = await import('./locale/en.js')\n  }\n  svgEditor.i18next.addResourceBundle(lang, 'translation', translationModule.default, true, true)\n}\n\nexport default {\n  name,\n  async init (_S) {\n    const svgEditor = this\n    const { svgCanvas } = svgEditor\n    const { $id, $click } = svgCanvas\n    await loadExtensionTranslation(svgEditor)\n    /**\n    * @param {Event} e\n    * @returns {void}\n    */\n    const importImage = (e) => {\n      $id('se-prompt-dialog').title = this.i18next.t('notification.loadingImage')\n      $id('se-prompt-dialog').setAttribute('close', false)\n      e.stopPropagation()\n      e.preventDefault()\n      const file = (e.type === 'drop') ? e.dataTransfer.files[0] : e.currentTarget.files[0]\n      if (!file) {\n        $id('se-prompt-dialog').setAttribute('close', true)\n        return\n      }\n\n      if (!file.type.includes('image')) {\n        return\n      }\n      // Detected an image\n      // svg handling\n      let reader\n      if (file.type.includes('svg')) {\n        reader = new FileReader()\n        reader.onloadend = (ev) => {\n          const newElement = this.svgCanvas.importSvgString(ev.target.result, true)\n          this.svgCanvas.alignSelectedElements('m', 'page')\n          this.svgCanvas.alignSelectedElements('c', 'page')\n          // highlight imported element, otherwise we get strange empty selectbox\n          this.svgCanvas.selectOnly([newElement])\n          $id('se-prompt-dialog').setAttribute('close', true)\n        }\n        reader.readAsText(file)\n      } else {\n        // bitmap handling\n        reader = new FileReader()\n        reader.onloadend = ({ target: { result } }) => {\n          /**\n              * Insert the new image until we know its dimensions.\n              * @param {Float} imageWidth\n              * @param {Float} imageHeight\n              * @returns {void}\n              */\n          const insertNewImage = (imageWidth, imageHeight) => {\n            const newImage = this.svgCanvas.addSVGElementsFromJson({\n              element: 'image',\n              attr: {\n                x: 0,\n                y: 0,\n                width: imageWidth,\n                height: imageHeight,\n                id: this.svgCanvas.getNextId(),\n                style: 'pointer-events:inherit'\n              }\n            })\n            this.svgCanvas.setHref(newImage, result)\n            this.svgCanvas.selectOnly([newImage])\n            this.svgCanvas.alignSelectedElements('m', 'page')\n            this.svgCanvas.alignSelectedElements('c', 'page')\n            this.topPanel.updateContextPanel()\n            $id('se-prompt-dialog').setAttribute('close', true)\n          }\n          // create dummy img so we know the default dimensions\n          let imgWidth = 100\n          let imgHeight = 100\n          const img = new Image()\n          img.style.opacity = 0\n          img.addEventListener('load', () => {\n            imgWidth = img.offsetWidth || img.naturalWidth || img.width\n            imgHeight = img.offsetHeight || img.naturalHeight || img.height\n            insertNewImage(imgWidth, imgHeight)\n          })\n          img.src = result\n        }\n        reader.readAsDataURL(file)\n      }\n    }\n    // create an input with type file to open the filesystem dialog\n    const imgImport = document.createElement('input')\n    imgImport.type = 'file'\n    imgImport.addEventListener('change', importImage)\n    // dropping a svg file will import it in the svg as well\n    this.workarea.addEventListener('drop', importImage)\n\n    const clickClear = async function () {\n      const [x, y] = svgEditor.configObj.curConfig.dimensions\n      const ok = await seConfirm(svgEditor.i18next.t('notification.QwantToClear'))\n      if (ok === 'Cancel') {\n        return\n      }\n      svgEditor.leftPanel.clickSelect()\n      svgEditor.svgCanvas.clear()\n      svgEditor.svgCanvas.setResolution(x, y)\n      svgEditor.updateCanvas(true)\n      svgEditor.zoomImage()\n      svgEditor.layersPanel.populateLayers()\n      svgEditor.topPanel.updateContextPanel()\n      svgEditor.topPanel.updateTitle('untitled.svg')\n    }\n\n    /**\n     * By default,  this.editor.svgCanvas.open() is a no-op. It is up to an extension\n     *  mechanism (opera widget, etc.) to call `setCustomHandlers()` which\n     *  will make it do something.\n     * @returns {void}\n     */\n    const clickOpen = async function () {\n      // ask user before clearing an unsaved SVG\n      const response = await svgEditor.openPrep()\n      if (response === 'Cancel') { return }\n      svgCanvas.clear()\n      try {\n        const blob = await fileOpen({\n          mimeTypes: ['image/*']\n        })\n        const svgContent = await blob.text()\n        await svgEditor.loadSvgString(svgContent)\n        svgEditor.updateCanvas()\n        handle = blob.handle\n        svgEditor.topPanel.updateTitle(blob.name)\n        svgEditor.svgCanvas.runExtensions('onOpenedDocument', {\n          name: blob.name,\n          lastModified: blob.lastModified,\n          size: blob.size,\n          type: blob.type\n        })\n      } catch (err) {\n        if (err.name !== 'AbortError') {\n          return console.error(err)\n        }\n      }\n    }\n    const b64toBlob = (b64Data, contentType = '', sliceSize = 512) => {\n      const byteCharacters = atob(b64Data)\n      const byteArrays = []\n      for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {\n        const slice = byteCharacters.slice(offset, offset + sliceSize)\n        const byteNumbers = new Array(slice.length)\n        for (let i = 0; i < slice.length; i++) {\n          byteNumbers[i] = slice.charCodeAt(i)\n        }\n        const byteArray = new Uint8Array(byteNumbers)\n        byteArrays.push(byteArray)\n      }\n      const blob = new Blob(byteArrays, { type: contentType })\n      return blob\n    }\n\n    /**\n     *\n     * @returns {void}\n     */\n    const clickSave = async function (type) {\n      const $editorDialog = $id('se-svg-editor-dialog')\n      const editingsource = $editorDialog.getAttribute('dialog') === 'open'\n      if (editingsource) {\n        svgEditor.saveSourceEditor()\n      } else {\n        // In the future, more options can be provided here\n        const saveOpts = {\n          images: svgEditor.configObj.pref('img_save'),\n          round_digits: 2\n        }\n        // remove the selected outline before serializing\n        svgCanvas.clearSelection()\n        // Update save options if provided\n        if (saveOpts) {\n          const saveOptions = svgCanvas.mergeDeep(svgCanvas.getSvgOption(), saveOpts)\n          for (const [key, value] of Object.entries(saveOptions)) {\n            svgCanvas.setSvgOption(key, value)\n          }\n        }\n        svgCanvas.setSvgOption('apply', true)\n\n        // no need for doctype, see https://jwatt.org/svg/authoring/#doctype-declaration\n        const svg = '<?xml version=\"1.0\"?>\\n' + svgCanvas.svgCanvasToString()\n        const b64Data = svgCanvas.encode64(svg)\n        const blob = b64toBlob(b64Data, 'image/svg+xml')\n        try {\n          if (type === 'save' && handle !== null) {\n            const throwIfExistingHandleNotGood = false\n            handle = await fileSave(blob, {\n              fileName: 'untitled.svg',\n              extensions: ['.svg']\n            }, handle, throwIfExistingHandleNotGood)\n          } else {\n            handle = await fileSave(blob, {\n              fileName: svgEditor.title,\n              extensions: ['.svg']\n            })\n          }\n          svgEditor.topPanel.updateTitle(handle.name)\n          svgCanvas.runExtensions('onSavedDocument', {\n            name: handle.name,\n            kind: handle.kind\n          })\n        } catch (err) {\n          if (err.name !== 'AbortError') {\n            return console.error(err)\n          }\n        }\n      }\n    }\n\n    return {\n      name: svgEditor.i18next.t(`${name}:name`),\n      // The callback should be used to load the DOM with the appropriate UI items\n      callback () {\n        const buttonTemplate = `\n        <se-menu-item id=\"tool_clear\" label=\"opensave.new_doc\" shortcut=\"N\" src=\"new.svg\"></se-menu-item>`\n        svgCanvas.insertChildAtIndex($id('main_button'), buttonTemplate, 0)\n        const openButtonTemplate = '<se-menu-item id=\"tool_open\" label=\"opensave.open_image_doc\" src=\"open.svg\"></se-menu-item>'\n        svgCanvas.insertChildAtIndex($id('main_button'), openButtonTemplate, 1)\n        const saveButtonTemplate = '<se-menu-item id=\"tool_save\" label=\"opensave.save_doc\" shortcut=\"S\" src=\"saveImg.svg\"></se-menu-item>'\n        svgCanvas.insertChildAtIndex($id('main_button'), saveButtonTemplate, 2)\n        const saveAsButtonTemplate = '<se-menu-item id=\"tool_save_as\" label=\"opensave.save_as_doc\" src=\"saveImg.svg\"></se-menu-item>'\n        svgCanvas.insertChildAtIndex($id('main_button'), saveAsButtonTemplate, 3)\n        const importButtonTemplate = '<se-menu-item id=\"tool_import\" label=\"tools.import_doc\" src=\"importImg.svg\"></se-menu-item>'\n        svgCanvas.insertChildAtIndex($id('main_button'), importButtonTemplate, 4)\n\n        // handler\n        $click($id('tool_clear'), clickClear.bind(this))\n        $click($id('tool_open'), clickOpen.bind(this))\n        $click($id('tool_save'), clickSave.bind(this, 'save'))\n        $click($id('tool_save_as'), clickSave.bind(this, 'saveas'))\n        $click($id('tool_import'), () => imgImport.click())\n      }\n    }\n  }\n}\n", "export default {\n  opensave: {\n    new_doc: 'New Image',\n    open_image_doc: 'Open SVG',\n    save_doc: 'Save SVG',\n    save_as_doc: 'Save as SVG'\n  }\n}\n", "export default {\n  opensave: {\n    new_doc: 'Nouvelle image',\n    open_image_doc: 'Ouv<PERSON>r le SVG',\n    save_doc: 'Enregistrer l\\'image',\n    save_as_doc: 'Enregistrer en tant qu\\'image'\n  }\n}\n", "export default {\n  opensave: {\n    new_doc: '<PERSON><PERSON>m',\n    open_image_doc: 'SVG Aç',\n    save_doc: 'SVG Kaydet',\n    save_as_doc: 'SVG olarak <PERSON>'\n  }\n}\n", "export default {\n  opensave: {\n    new_doc: '新图片',\n    open_image_doc: '打开 SVG',\n    save_doc: '保存图像',\n    save_as_doc: '另存为图像'\n  }\n}\n"], "names": ["e", "self", "top", "t", "Promise", "resolve", "then", "l", "v", "y", "b", "a", "m", "_", "async", "o", "default", "s", "getFile", "handle", "__proto__", "arguments", "length", "undefined", "Array", "isArray", "for<PERSON>ach", "n", "description", "accept", "mimeTypes", "map", "r", "extensions", "window", "showOpenFilePicker", "id", "startIn", "types", "multiple", "excludeAcceptAllOption", "all", "u", "Object", "reject", "TypeError", "done", "value", "this", "next", "prototype", "apply", "return", "throw", "p", "name", "i", "c", "d", "Symbol", "asyncIterator", "iterator", "call", "values", "kind", "push", "directoryHandle", "defineProperty", "configurable", "enumerable", "get", "flat", "recursive", "mode", "showDirectoryPicker", "skipDirectory", "fileName", "Blob", "type", "headers", "showSaveFilePicker", "<PERSON><PERSON><PERSON>", "createWritable", "stream", "pipeTo", "body", "write", "close", "document", "createElement", "join", "style", "display", "append", "legacySetup", "removeEventListener", "remove", "addEventListener", "from", "files", "click", "webkitdirectory", "filter", "webkitRelativePath", "split", "every", "<PERSON><PERSON><PERSON><PERSON>", "ReadableStream", "start", "read", "_ref", "enqueue", "Response", "blob", "releaseLock", "download", "href", "URL", "createObjectURL", "setTimeout", "revokeObjectURL", "loadExtensionTranslation", "svgEditor", "translationModule", "lang", "config<PERSON><PERSON><PERSON>", "pref", "__variableDynamicImportRuntime0__", "concat", "_error", "console", "warn", "en$1", "i18next", "addResourceBundle", "extOpensave", "_S", "svgCanvas", "$id", "$click", "importImage", "title", "setAttribute", "stopPropagation", "preventDefault", "file", "dataTransfer", "currentTarget", "includes", "reader", "FileReader", "onloadend", "ev", "newElement", "importSvgString", "target", "result", "alignSelectedElements", "selectOnly", "readAsText", "insertNewImage", "imageWidth", "imageHeight", "newImage", "addSVGElementsFrom<PERSON>son", "element", "attr", "x", "width", "height", "getNextId", "set<PERSON><PERSON>f", "topPanel", "updateContextPanel", "imgWidth", "imgHeight", "img", "Image", "opacity", "offsetWidth", "naturalWidth", "offsetHeight", "naturalHeight", "src", "readAsDataURL", "imgImport", "workarea", "clickClear", "curConfig", "dimensions", "seConfirm", "leftPanel", "clickSelect", "clear", "setResolution", "updateCanvas", "zoomImage", "layersPanel", "populateLayers", "updateTitle", "clickOpen", "openPrep", "fileOpen", "svgContent", "text", "loadSvgString", "runExtensions", "lastModified", "size", "err", "error", "clickSave", "getAttribute", "saveSourceEditor", "saveOpts", "images", "round_digits", "clearSelection", "saveOptions", "mergeDeep", "getSvgOption", "key", "entries", "setSvgOption", "svg", "svgCanvasToString", "b64Data", "contentType", "sliceSize", "byteCharacters", "atob", "byteArrays", "offset", "slice", "byteNumbers", "charCodeAt", "byteArray", "Uint8Array", "b64toBlob", "encode64", "throwIfExistingHandleNotGood", "fileSave", "callback", "insertChildAtIndex", "bind", "opensave", "new_doc", "open_image_doc", "save_doc", "save_as_doc"], "mappings": "AAAA,MAAMA,EAAE,MAAM,GAAG,oBAAoBC,KAAK,OAAM,EAAG,GAAG,QAAQA,MAAMA,OAAOC,IAAI,IAAIA,IAAI,MAAMF,GAAG,OAAM,OAAQ,GAAG,uBAAuBC,KAAK,MAAM,qBAAqB,OAAM,GAAtK,GAA6KE,EAAEH,EAAEI,QAAQC,UAAUC,MAAK,WAAW,OAAOC,KAAIH,QAAQC,UAAUC,MAAK,WAAW,OAAOE,KAAiER,EAAEI,QAAQC,UAAUC,MAAK,WAAW,OAAOG,KAAIL,QAAQC,UAAUC,MAAK,WAAW,OAAOI,KAAyD,MAAMC,EAAEX,EAAEI,QAAQC,UAAUC,MAAK,WAAW,OAAOM,KAAIR,QAAQC,UAAUC,MAAK,WAAW,OAAOO,KAAIC,eAAeC,IAAQ,aAAaJ,GAAGK,sBAAc,MAAMC,EAAEH,MAAAA,IAAU,MAAMX,QAAQH,EAAEkB,UAAU,OAAOf,EAAEgB,OAAOnB,EAAEG,GAAG,IAAycI,EAAE,CAACa,UAAU,KAAKJ,QAArdF,iBAAe,IAATd,EAASqB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAP,CAAC,IAAOG,MAAMC,QAAQzB,KAAKA,EAAE,CAACA,IAAI,MAAMG,EAAE,GAAGH,EAAE0B,SAAQ,CAAC1B,EAAE2B,KAAKxB,EAAEwB,GAAG,CAACC,YAAY5B,EAAE4B,aAAa,QAAQC,OAAO,IAAI7B,EAAE8B,UAAU9B,EAAE8B,UAAUC,KAAIC,IAAI7B,EAAEwB,GAAGE,OAAOG,GAAGhC,EAAEiC,YAAY,MAAK9B,EAAEwB,GAAGE,OAAO,OAAO7B,EAAEiC,YAAY,MAAK,MAAMN,QAAQO,OAAOC,mBAAmB,CAACC,GAAGpC,EAAE,GAAGoC,GAAGC,QAAQrC,EAAE,GAAGqC,QAAQC,MAAMnC,EAAEoC,SAASvC,EAAE,GAAGuC,WAAU,EAAGC,uBAAuBxC,EAAE,GAAGwC,yBAAwB,IAAKR,QAAQ5B,QAAQqC,IAAId,EAAEI,IAAId,IAAI,OAAOjB,EAAE,GAAGuC,SAASP,EAAEA,EAAE,KAAiC,SAASU,EAAE1C,GAAG,SAASG,EAAEH,GAAG,GAAG2C,OAAO3C,KAAKA,EAAE,OAAOI,QAAQwC,OAAO,IAAIC,UAAU7C,EAAE,uBAAuB,IAAIG,EAAEH,EAAE8C,KAAK,OAAO1C,QAAQC,QAAQL,EAAE+C,OAAOzC,MAAK,SAASN,GAAG,MAAM,CAAC+C,MAAM/C,EAAE8C,KAAK3C,MAAK,OAAOuC,EAAE,SAAS1C,GAAGgD,KAAK/B,EAAEjB,EAAEgD,KAAKrB,EAAE3B,EAAEiD,MAAMP,EAAEQ,UAAU,CAACjC,EAAE,KAAKU,EAAE,KAAKsB,KAAK,WAAW,OAAO9C,EAAE6C,KAAKrB,EAAEwB,MAAMH,KAAK/B,EAAEI,aAAa+B,OAAO,SAASpD,GAAG,IAAI2B,EAAEqB,KAAK/B,EAAEmC,OAAO,YAAO,IAASzB,EAAEvB,QAAQC,QAAQ,CAAC0C,MAAM/C,EAAE8C,MAAK,IAAK3C,EAAEwB,EAAEwB,MAAMH,KAAK/B,EAAEI,aAAagC,MAAM,SAASrD,GAAG,IAAI2B,EAAEqB,KAAK/B,EAAEmC,OAAO,YAAO,IAASzB,EAAEvB,QAAQwC,OAAO5C,GAAGG,EAAEwB,EAAEwB,MAAMH,KAAK/B,EAAEI,cAAc,IAAIqB,EAAE1C,GAAG,MAAMsD,EAAExC,eAAMd,EAAEG,GAAe,IAAbwB,EAAaN,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAXrB,EAAEuD,KAAKvB,EAAIX,UAAAC,OAAA,EAAAD,UAAA,QAAAE,EAAC,MAAMiC,EAAE,GAAG7C,EAAE,GAAG,IAAII,EAAEE,GAAE,EAAGwC,GAAE,EAAG,IAAI,IAAI,IAAIlD,EAAEmD,EAAE,SAAS1D,GAAG,IAAIG,EAAEwB,EAAEK,EAAEwB,EAAE,EAAE,IAAI,oBAAoBG,SAAShC,EAAEgC,OAAOC,cAAc5B,EAAE2B,OAAOE,UAAUL,KAAK,CAAC,GAAG7B,GAAG,OAAOxB,EAAEH,EAAE2B,IAAI,OAAOxB,EAAE2D,KAAK9D,GAAG,GAAGgC,GAAG,OAAO7B,EAAEH,EAAEgC,IAAI,OAAO,IAAIU,EAAEvC,EAAE2D,KAAK9D,IAAI2B,EAAE,kBAAkBK,EAAE,aAAa,MAAM,IAAIa,UAAU,gCAArP,CAAsR7C,EAAE+D,UAAU9C,IAAIV,QAAQmD,EAAET,QAAQH,KAAK7B,GAAE,EAAG,CAAC,MAAMF,EAAER,EAAEwC,MAAM9B,EAAKU,GAAAA,OAAAA,cAAKZ,EAAEwC,MAAO,SAASxC,EAAEiD,KAAKrD,EAAEsD,KAAKlD,EAAEG,UAAUZ,MAAKH,IAAIA,EAAE+D,gBAAgBlE,EAAEG,EAAEgB,OAAOJ,EAAE4B,OAAOwB,eAAehE,EAAE,qBAAqB,CAACiE,cAAa,EAAGC,YAAW,EAAGC,IAAI,IAAIrD,QAAO,cAAcF,EAAEiD,OAAO7D,GAAG6B,GAAGA,EAAEjB,IAAIyC,EAAES,KAAKX,EAAEvC,EAAEZ,EAAEc,EAAEe,KAAK,MAAMhC,GAAGyD,GAAE,EAAG1C,EAAEf,EAAS,QAAC,IAAIiB,GAAG,MAAMyC,EAAEN,cAAcM,EAAEN,SAAgB,QAAC,GAAGK,EAAE,MAAM1C,GAAG,MAAM,WAAWX,QAAQqC,IAAIe,IAAIe,gBAAgBnE,QAAQqC,IAAI9B,KAAK,IAAwMF,EAAE,CAACW,UAAU,KAAKJ,QAApNF,iBAAa,IAAPd,yDAAE,GAAMA,EAAEwE,UAAUxE,EAAEwE,YAAW,EAAGxE,EAAEyE,KAAKzE,EAAEyE,MAAM,OAAO,MAAMtE,QAAQ+B,OAAOwC,oBAAoB,CAACtC,GAAGpC,EAAEoC,GAAGC,QAAQrC,EAAEqC,QAAQoC,KAAKzE,EAAEyE,OAAO,OAAOnB,EAAEnD,EAAEH,EAAEwE,eAAU,EAAOxE,EAAE2E,iBAAg7B/D,EAAE,CAACQ,UAAU,KAAKJ,QAAn5BF,eAAMd,GAA8B,IAA5BG,EAA4BkB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAA1B,CAAC,IAAIM,yDAAE,KAAKK,EAAEX,UAAAC,OAAA,QAAAC,IAAAF,UAAA,IAAAA,UAAA,GAAGmC,yDAAE,KAAQhC,MAAMC,QAAQtB,KAAKA,EAAE,CAACA,IAAIA,EAAE,GAAGyE,SAASzE,EAAE,GAAGyE,UAAU,WAAW,MAAMjE,EAAE,GAAG,IAAII,EAAE,KAAK,GAAGf,aAAa6E,MAAM7E,EAAE8E,KAAK/D,EAAEf,EAAE8E,KAAK9E,EAAE+E,SAAS/E,EAAE+E,QAAQT,IAAI,kBAAkBvD,EAAEf,EAAE+E,QAAQT,IAAI,iBAAiBnE,EAAEuB,SAAQ,CAAC1B,EAAEG,KAAKQ,EAAER,GAAG,CAACyB,YAAY5B,EAAE4B,aAAa,QAAQC,OAAO,IAAI7B,EAAE8B,WAAW,IAAI3B,GAAGY,GAAGf,EAAE8B,UAAUmC,KAAKlD,GAAGf,EAAE8B,UAAUC,KAAIJ,IAAIhB,EAAER,GAAG0B,OAAOF,GAAG3B,EAAEiC,YAAY,OAAMlB,EAAEJ,EAAER,GAAG0B,OAAOd,GAAGf,EAAEiC,YAAY,GAAGtB,EAAER,GAAG0B,OAAO,OAAO7B,EAAEiC,YAAY,MAAKN,EAAE,UAAUA,EAAET,UAAU,MAAMlB,GAAG,GAAG2B,EAAE,KAAKK,EAAE,MAAMhC,EAAE,MAAMiB,EAAEU,SAASO,OAAO8C,mBAAmB,CAACC,cAAc9E,EAAE,GAAGyE,SAASxC,GAAGjC,EAAE,GAAGiC,GAAGC,QAAQlC,EAAE,GAAGkC,QAAQC,MAAM3B,EAAE6B,uBAAuBrC,EAAE,GAAGqC,yBAAwB,KAAMb,GAAG6B,GAAGA,EAAEvC,GAAG,MAAMwC,QAAQxC,EAAEiE,iBAAiB,GAAG,WAAWlF,EAAE,CAAC,MAAMG,EAAEH,EAAEmF,SAAS,aAAahF,EAAEiF,OAAO3B,GAAGxC,EAAE,MAAM,SAASjB,SAASA,EAAEqF,KAAKD,OAAO3B,GAAGxC,UAAUwC,EAAE6B,YAAYtF,SAASyD,EAAE8B,QAAQtE,KAAyqBT,EAAE,CAACY,UAAU,KAAKJ,QAAxpBF,iBAAA,IAAMd,EAANqB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAQ,CAAC,IAAT,OAAgBG,MAAMC,QAAQzB,KAAKA,EAAE,CAACA,IAAI,IAAII,SAAQ,CAACD,EAAEwB,KAAK,MAAMK,EAAEwD,SAASC,cAAc,SAASzD,EAAE8C,KAAK,OAAO,MAAMtB,EAAE,IAAIxD,EAAE+B,KAAI/B,GAAGA,EAAE8B,WAAW,QAAO9B,EAAE+B,KAAI/B,GAAGA,EAAEiC,YAAY,MAAKyD,OAAO1D,EAAEO,SAASvC,EAAE,GAAGuC,WAAU,EAAGP,EAAEH,OAAO2B,GAAG,GAAGxB,EAAE2D,MAAMC,QAAQ,OAAOJ,SAASH,KAAKQ,OAAO7D,GAAG,MAAMrB,EAAEX,IAAI,mBAAmBe,GAAGA,IAAIZ,EAAEH,IAAIe,EAAEf,EAAE,GAAG8F,aAAa9F,EAAE,GAAG8F,YAAYnF,GAAE,IAAII,EAAEY,IAAGK,GAAGf,EAAE,KAAKiB,OAAO6D,oBAAoB,QAAQ9E,GAAGe,EAAEgE,UAAUhE,EAAEiE,iBAAiB,SAAQ,KAAK/D,OAAO+D,iBAAiB,QAAQhF,MAAKe,EAAEiE,iBAAiB,UAAS,KAAK/D,OAAO6D,oBAAoB,QAAQ9E,GAAGe,EAAEgE,SAASrF,EAAEqB,EAAEO,SAASf,MAAM0E,KAAKlE,EAAEmE,OAAOnE,EAAEmE,MAAM,OAAMnE,EAAEoE,aAA0lB1F,EAAE,CAACU,UAAU,KAAKJ,QAAlkBF,iBAAA,IAAMd,EAANqB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAQ,CAAC,IAAT,OAAgBG,MAAMC,QAAQzB,KAAKA,EAAE,CAACA,IAAIA,EAAE,GAAGwE,UAAUxE,EAAE,GAAGwE,YAAW,EAAG,IAAIpE,SAAQ,CAACD,EAAEwB,KAAK,MAAMK,EAAEwD,SAASC,cAAc,SAASzD,EAAE8C,KAAK,OAAO9C,EAAEqE,iBAAgB,EAAG,MAAM7C,EAAExD,IAAI,mBAAmBW,GAAGA,IAAIR,EAAEH,IAAIW,EAAEX,EAAE,GAAG8F,aAAa9F,EAAE,GAAG8F,YAAYtC,GAAE,IAAI7C,EAAEgB,IAAGK,GAAGA,EAAEiE,iBAAiB,UAAS,KAAK,IAAI9F,EAAEqB,MAAM0E,KAAKlE,EAAEmE,OAAOnG,EAAE,GAAGwE,UAAUxE,EAAE,GAAGwE,WAAWxE,EAAE,GAAG2E,gBAAgBxE,EAAEA,EAAEmG,QAAOnG,GAAGA,EAAEoG,mBAAmBC,MAAM,KAAKC,OAAMtG,IAAIH,EAAE,GAAG2E,cAAc,CAACpB,KAAKpD,EAAE6D,KAAK,mBAAiB7D,EAAEA,EAAEmG,QAAOtG,GAAG,IAAIA,EAAEuG,mBAAmBC,MAAM,KAAKlF,SAAQkC,EAAErD,MAAK6B,EAAEoE,aAA2sBvF,EAAE,CAACO,UAAU,KAAKJ,QAAnrBF,eAAMd,GAAS,IAAPG,yDAAE,GAAMqB,MAAMC,QAAQtB,KAAKA,EAAEA,EAAE,IAAI,MAAMwB,EAAE6D,SAASC,cAAc,KAAK,IAAIzD,EAAEhC,EAAE,SAASA,IAAIgC,QAAQlB,eAAed,EAAEG,GAAG,MAAMwB,EAAE3B,EAAE0G,YAAY1E,EAAE,IAAI2E,eAAe,CAACC,MAAM5G,GAAGc,eAAeX,IAAI,OAAOwB,EAAEkF,OAAOvG,MAAKwG,IAAoB,IAAlBhE,KAAKnB,EAAEoB,MAAMf,GAAK8E,EAAC,IAAInF,EAAE,OAAO3B,EAAE+G,QAAQ/E,GAAG7B,IAAIH,EAAEuF,WAA7FzE,KAA4G0C,EAAE,IAAIwD,SAAShF,GAAGrB,QAAQ6C,EAAEyD,OAAO,OAAOtF,EAAEuF,cAAc,IAAIrC,KAAK,CAAClE,GAAG,CAACmE,KAAK3E,IAAlQW,CAAuQd,EAAEqF,KAAKrF,EAAE+E,QAAQT,IAAI,kBAAkB3C,EAAEwF,SAAShH,EAAEyE,UAAU,WAAWjD,EAAEyF,KAAKC,IAAIC,sBAAsBtF,GAAG,MAAMwB,EAAE,KAAK,mBAAmB7C,GAAGA,KAAKA,EAAER,EAAE2F,aAAa3F,EAAE2F,YAAYtC,GAAE,IAAI7C,KAAIgB,GAAG,OAAOA,EAAEsE,iBAAiB,SAAQ,KAAKsB,YAAW,IAAIF,IAAIG,gBAAgB7F,EAAEyF,OAAM,KAAK5D,OAAM7B,EAAEyE,QAAQ,OCmBnhL,MAAM7C,EAAO,WACb,IAAIpC,EAAS,KAEb,MAAMsG,yBAA2B3G,eAAgB4G,GAC/C,IAAIC,EACJ,MAAMC,EAAOF,EAAUG,UAAUC,KAAK,QACtC,IACEH,0hBAA0BI,CAAM,YAAAC,OAAaJ,EAA7C,QACA,MAAOK,GACPC,QAAQC,KAA6BP,wBAAAA,OAAAA,mBAAarE,EAAlD,kBACAoE,QAA0BvH,QAAAC,UAAAC,MAAA,WAAA,OAAA8H,KAE5BV,EAAUW,QAAQC,kBAAkBV,EAAM,cAAeD,EAAkB3G,SAAS,GAAM,IAG5F,IAAeuH,EAAA,CACbhF,KAAAA,EACAzC,WAAY0H,GACV,MAAMd,EAAY1E,MACZyF,UAAEA,GAAcf,GAChBgB,IAAEA,EAAFC,OAAOA,GAAWF,QAClBhB,yBAAyBC,GAK/B,MAAMkB,YAAe5I,IACnB0I,EAAI,oBAAoBG,MAAQ7F,KAAKqF,QAAQlI,EAAE,6BAC/CuI,EAAI,oBAAoBI,aAAa,SAAS,GAC9C9I,EAAE+I,kBACF/I,EAAEgJ,iBACF,MAAMC,EAAmB,SAAXjJ,EAAE8E,KAAmB9E,EAAEkJ,aAAa/C,MAAM,GAAKnG,EAAEmJ,cAAchD,MAAM,GACnF,IAAK8C,EAEH,YADAP,EAAI,oBAAoBI,aAAa,SAAS,GAIhD,IAAKG,EAAKnE,KAAKsE,SAAS,SACtB,OAIF,IAAIC,EACAJ,EAAKnE,KAAKsE,SAAS,QACrBC,EAAS,IAAIC,WACbD,EAAOE,UAAaC,IAClB,MAAMC,EAAazG,KAAKyF,UAAUiB,gBAAgBF,EAAGG,OAAOC,QAAQ,GACpE5G,KAAKyF,UAAUoB,sBAAsB,IAAK,QAC1C7G,KAAKyF,UAAUoB,sBAAsB,IAAK,QAE1C7G,KAAKyF,UAAUqB,WAAW,CAACL,IAC3Bf,EAAI,oBAAoBI,aAAa,SAAS,IAEhDO,EAAOU,WAAWd,KAGlBI,EAAS,IAAIC,WACbD,EAAOE,UAAYzC,IAA4B,IAAzB6C,QAAQC,OAAEA,IAAe9C,EAO7C,MAAMkD,eAAiB,CAACC,EAAYC,KAClC,MAAMC,EAAWnH,KAAKyF,UAAU2B,uBAAuB,CACrDC,QAAS,QACTC,KAAM,CACJC,EAAG,EACH9J,EAAG,EACH+J,MAAOP,EACPQ,OAAQP,EACR9H,GAAIY,KAAKyF,UAAUiC,YACnB/E,MAAO,4BAGX3C,KAAKyF,UAAUkC,QAAQR,EAAUP,GACjC5G,KAAKyF,UAAUqB,WAAW,CAACK,IAC3BnH,KAAKyF,UAAUoB,sBAAsB,IAAK,QAC1C7G,KAAKyF,UAAUoB,sBAAsB,IAAK,QAC1C7G,KAAK4H,SAASC,qBACdnC,EAAI,oBAAoBI,aAAa,SAAS,IAGhD,IAAIgC,EAAW,IACXC,EAAY,IAChB,MAAMC,EAAM,IAAIC,MAChBD,EAAIrF,MAAMuF,QAAU,EACpBF,EAAI/E,iBAAiB,QAAQ,KAC3B6E,EAAWE,EAAIG,aAAeH,EAAII,cAAgBJ,EAAIR,MACtDO,EAAYC,EAAIK,cAAgBL,EAAIM,eAAiBN,EAAIP,OACzDT,eAAec,EAAUC,MAE3BC,EAAIO,IAAM3B,GAEZP,EAAOmC,cAAcvC,KAInBwC,EAAYjG,SAASC,cAAc,SACzCgG,EAAU3G,KAAO,OACjB2G,EAAUxF,iBAAiB,SAAU2C,aAErC5F,KAAK0I,SAASzF,iBAAiB,OAAQ2C,aAEvC,MAAM+C,WAAa7K,iBACjB,MAAOyJ,EAAG9J,GAAKiH,EAAUG,UAAU+D,UAAUC,WAElC,iBADMC,UAAUpE,EAAUW,QAAQlI,EAAE,gCAI/CuH,EAAUqE,UAAUC,cACpBtE,EAAUe,UAAUwD,QACpBvE,EAAUe,UAAUyD,cAAc3B,EAAG9J,GACrCiH,EAAUyE,cAAa,GACvBzE,EAAU0E,YACV1E,EAAU2E,YAAYC,iBACtB5E,EAAUkD,SAASC,qBACnBnD,EAAUkD,SAAS2B,YAAY,kBAS3BC,UAAY1L,iBAGhB,GAAiB,iBADM4G,EAAU+E,WACjC,CACAhE,EAAUwD,QACV,IACE,MAAMhF,QDxJqQnG,eAAea,IAAQ,aAAaxB,GAAGa,sBCwJ/R0L,CAAS,CAC1B5K,UAAW,CAAC,aAER6K,QAAmB1F,EAAK2F,aACxBlF,EAAUmF,cAAcF,GAC9BjF,EAAUyE,eACVhL,EAAS8F,EAAK9F,OACduG,EAAUkD,SAAS2B,YAAYtF,EAAK1D,MACpCmE,EAAUe,UAAUqE,cAAc,mBAAoB,CACpDvJ,KAAM0D,EAAK1D,KACXwJ,aAAc9F,EAAK8F,aACnBC,KAAM/F,EAAK+F,KACXlI,KAAMmC,EAAKnC,OAEb,MAAOmI,GACP,GAAiB,eAAbA,EAAI1J,KACN,OAAO2E,QAAQgF,MAAMD,MAwBrBE,UAAYrM,eAAgBgE,GAGhC,GAD+D,SADzC4D,EAAI,wBACU0E,aAAa,UAE/C1F,EAAU2F,uBACL,CAEL,MAAMC,EAAW,CACfC,OAAQ7F,EAAUG,UAAUC,KAAK,YACjC0F,aAAc,GAKhB,GAFA/E,EAAUgF,iBAENH,EAAU,CACZ,MAAMI,EAAcjF,EAAUkF,UAAUlF,EAAUmF,eAAgBN,GAClE,IAAK,MAAOO,EAAK9K,KAAUJ,OAAOmL,QAAQJ,GACxCjF,EAAUsF,aAAaF,EAAK9K,GAGhC0F,EAAUsF,aAAa,SAAS,GAGhC,MAAMC,EAAM,0BAA4BvF,EAAUwF,oBAE5ChH,EA7CQ,SAACiH,GAA+C,IAAtCC,yDAAc,GAAIC,yDAAY,IACxD,MAAMC,EAAiBC,KAAKJ,GACtBK,EAAa,GACnB,IAAK,IAAIC,EAAS,EAAGA,EAASH,EAAe/M,OAAQkN,GAAUJ,EAAW,CACxE,MAAMK,EAAQJ,EAAeI,MAAMD,EAAQA,EAASJ,GAC9CM,EAAc,IAAIlN,MAAMiN,EAAMnN,QACpC,IAAK,IAAIkC,EAAI,EAAGA,EAAIiL,EAAMnN,OAAQkC,IAChCkL,EAAYlL,GAAKiL,EAAME,WAAWnL,GAEpC,MAAMoL,EAAY,IAAIC,WAAWH,GACjCH,EAAWtK,KAAK2K,GAGlB,OADa,IAAI/J,KAAK0J,EAAY,CAAEzJ,KAAMqJ,IAiC3BW,CADGrG,EAAUsG,SAASf,GACH,iBAChC,IACE,GAAa,SAATlJ,GAA8B,OAAX3D,EAAiB,CACtC,MAAM6N,GAA+B,EACrC7N,QAAe8N,EAAShI,EAAM,CAC5BrC,SAAU,eACV3C,WAAY,CAAC,SACZd,EAAQ6N,QAEX7N,QAAe8N,EAAShI,EAAM,CAC5BrC,SAAU8C,EAAUmB,MACpB5G,WAAY,CAAC,UAGjByF,EAAUkD,SAAS2B,YAAYpL,EAAOoC,MACtCkF,EAAUqE,cAAc,kBAAmB,CACzCvJ,KAAMpC,EAAOoC,KACbS,KAAM7C,EAAO6C,OAEf,MAAOiJ,GACP,GAAiB,eAAbA,EAAI1J,KACN,OAAO2E,QAAQgF,MAAMD,MAM7B,MAAO,CACL1J,KAAMmE,EAAUW,QAAQlI,EAAlB,GAAA6H,OAAuBzE,EADxB,UAGL2L,WAGEzG,EAAU0G,mBAAmBzG,EAAI,eAFjC,8GAEiE,GAEjED,EAAU0G,mBAAmBzG,EAAI,eADN,8FAC0C,GAErED,EAAU0G,mBAAmBzG,EAAI,eADN,wGAC0C,GAErED,EAAU0G,mBAAmBzG,EAAI,eADJ,iGAC0C,GAEvED,EAAU0G,mBAAmBzG,EAAI,eADJ,8FAC0C,GAGvEC,EAAOD,EAAI,cAAeiD,WAAWyD,KAAKpM,OAC1C2F,EAAOD,EAAI,aAAc8D,UAAU4C,KAAKpM,OACxC2F,EAAOD,EAAI,aAAcyE,UAAUiC,KAAKpM,KAAM,SAC9C2F,EAAOD,EAAI,gBAAiByE,UAAUiC,KAAKpM,KAAM,WACjD2F,EAAOD,EAAI,gBAAgB,IAAM+C,EAAUrF,sDCzQpC,CACbiJ,SAAU,CACRC,QAAS,YACTC,eAAgB,WAChBC,SAAU,WACVC,YAAa,0DCLF,CACbJ,SAAU,CACRC,QAAS,iBACTC,eAAgB,gBAChBC,SAAU,sBACVC,YAAa,2ECLF,CACbJ,SAAU,CACRC,QAAS,aACTC,eAAgB,SAChBC,SAAU,aACVC,YAAa,gECLF,CACbJ,SAAU,CACRC,QAAS,MACTC,eAAgB,SAChBC,SAAU,OACVC,YAAa"}