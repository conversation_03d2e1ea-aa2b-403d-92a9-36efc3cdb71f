import { css } from '/fx.js';

export const $styles = {
    table: css`
        ::-webkit-scrollbar { width: var(--scrollW, 0px)!important; height: var(--scrollH, 4px)!important; }
        :host {
            display: block;
            position: relative;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }
        .table-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        .table-header, .table-footer {
            display: flex;
            position: relative;
            background-color: var(--fx-table-header-background, #f5f5f5);
            border-bottom: 1px solid var(--fx-border-color, #ccc);
            flex-shrink: 0;
            z-index: 2;
        }
        .table-footer {
            border-top: 1px solid var(--fx-border-color, #ccc);
            border-bottom: none;
        }
        .table-body {
            flex: 1;
            position: relative;
            overflow: auto;
            display: flex;
        }
        .header-scrollable, .footer-scrollable, .body-scrollable {
            display: flex;
            overflow: hidden;
            flex: 1;
        }
        .body-scrollable {
            overflow: auto;
        }
        .virtual-scroller {
            position: relative;
            width: 100%;
        }
        .visible-rows {
            position: relative;
            width: 100%;
        }
        .table-row {
            display: flex;
            position: relative;
            width: 100%;
            /* min-width: max-content; */
            /* border-bottom: 1px solid var(--fx-border-color, #eee); */
        }
        .table-row.even-row.ns {
            background-color: var(--fx-table-even-row-color);
        }
        .table-row.odd-row.ns {
            background-color: var(--fx-table-odd-row-color);
        }
        .table-row.selected {
            background-color: var(--fx-color-selected, #209CEE) !important;
            color: white !important;
            position: relative !important;
        }
        .header-cell, .table-cell, .footer-cell {
            position: relative;
            padding: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            background-color: inherit;
            box-sizing: border-box;
            justify-content: start;
        }
        .table-row.selected .table-cell {
            color: white !important;
            background-color: inherit !important;
        }
        .table-row.selected fx-table-cell {
            color: white !important;
        }
        .selected-cell {
            color: white !important;
            background-color: inherit !important;
            position: relative;
            z-index: 5;
        }
        .header-cell {
            position: relative;
            padding: 0 4px;
            /* font-weight: bold; */
            display: flex;
            align-items: center;
            border-right: 1px solid var(--fx-border-color);
            cursor: pointer;
            user-select: none;
            box-sizing: border-box;
        }
        .sort-indicator {
            margin-left: 5px;
            font-size: 0.8em;
        }
        .resizer {
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            width: 5px;
            cursor: col-resize;
            z-index: 1;
            /* border-right: 1px solid var(--fx-border-color, #ccc); */
        }
        .resizer:hover, .resizer:active {
            background-color: var(--fx-color-selected, #0077ff);
        }
        .fixed-left-header, .fixed-left-columns, .fixed-left-footer,
        .fixed-right-header, .fixed-right-columns, .fixed-right-footer {
            display: flex;
            position: sticky;
            background-color: var(--fx-background-color, #f5f5f5);
            z-index: 3;
        }
        .fixed-left-header, .fixed-left-columns, .fixed-left-footer {
            left: 0;
            border-right: 1px solid var(--fx-border-color, #ccc);
        }
        .fixed-right-header, .fixed-right-columns, .fixed-right-footer {
            right: 0;
            border-left: 1px solid var(--fx-border-color, #ccc);
        }
        .fixed-left-columns, .fixed-right-columns {
            overflow: hidden;
        }
        .dragging {
            opacity: 0.5;
        }
        .drag-over {
            border-left: 2px solid var(--fx-color-selected, #0077ff);
        }
        .header-content, .footer-content {
            position: absolute;
            right: 4px;
            top: 50%;
            transform: translateY(-50%);
        }
        .top-search, .bottom-nav {
            display: flex;
            align-items: center;
        }
        .top-search input, .top-search select, .top-search button {
            font-size: 0.9em;
        }
        .navigation-buttons {
            display: flex;
            align-items: center;
        }
        .nav-button {
            margin: 0 1px;
            padding: 2px 4px;
            height: 24px;
            min-width: 32px;
            border: 1px solid var(--fx-border-color, #ccc);
            border-radius: 4px;
            background: transparent;
            cursor: pointer;
            font-size: 0.8em;
            white-space: nowrap;
        }
        .nav-button:hover:not([disabled]) {
            background-color: var(--fx-hover-color, #f0f0f0);
        }
        .nav-button[disabled] {
            opacity: 1;
            cursor: default;
            /* cursor: not-allowed; */
        }
        .current-position-input {
            font-size: 0.9em;
            min-width: 50px;
            max-width: 70px;
            text-align: center;
            height: 24px;
            border: 1px solid var(--fx-border-color, #ccc);
            border-radius: 4px;
            outline: none;
            padding: 0 4px;
        }
        .total-rows-label {
            font-size: 0.8em;
            white-space: nowrap;
        }
    `,
    cell: css`
        :host {
            box-sizing: border-box;
            position: relative;
            cursor: pointer;
        }
        :host([selected]) {
            color: white !important;
        }
        .cell {
            background-color: transparent;
            position: relative;
            display: flex;
            flex: 1;
            overflow: hidden;
            outline: none;
            box-sizing: border-box;
        }
        .input, select {
            padding: 0;
            background-color: transparent!important;
            border-radius: 0;
            outline: none;
            border: none;
            font-family: Arial;
            font-size: medium;
        }
        .input:focus {
            outline: none !important;
        }
        textarea {
            outline: none;
            -moz-appearance: none;
            resize: none;
            border: none;
            font-size: 14px;
            font-family: Arial;;
            width: 100%;
            height: 100%;
            background: transparent;
        }
    `
}
