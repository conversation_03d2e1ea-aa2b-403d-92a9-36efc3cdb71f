import { FxElement, html, css } from '../../fx.js';

customElements.define('fx-jfamily', class FxJFamily extends FxElement {
    render() {
        return html`
            <iframe ref="editor" srcdoc=${this.srcdoc} style="border: none; width: 100%; height: 100%"></iframe>
        `
    }
    static properties = {
        data: { type: Array }
    }

    async firstUpdated() {
        super.firstUpdated();
        const response = await fetch('/fx/~/family-chart/create-tree/index.html')
        this.srcdoc = await response.text();
        setTimeout(() => {
            const doc = this.$qs('iframe').contentDocument;
            if (this.cell?.data || this.data) {
                doc.store.update.data(this.cell?.data || this.data); 
                doc.store.update.tree();
            }
            doc.addEventListener('changed', e => {
                if (this.cell)
                    this.cell.data = this.data = e.detail;
            })
        }, 500)
        this.$update();
    }

})
