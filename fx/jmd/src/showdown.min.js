/*! showdown v 2.0.0-alpha1 - 24-10-2018 */
(function(){function getDefaultOpts(simple){"use strict";var defaultOptions={omitExtraWLInCodeBlocks:{defaultValue:!1,describe:"Omit the default extra whiteline added to code blocks",type:"boolean"},noHeaderId:{defaultValue:!1,describe:"Turn on/off generated header id",type:"boolean"},prefixHeaderId:{defaultValue:!1,describe:"Add a prefix to the generated header ids. Passing a string will prefix that string to the header id. Setting to true will add a generic 'section-' prefix",type:"string"},rawPrefixHeaderId:{defaultValue:!1,describe:'Setting this option to true will prevent showdown from modifying the prefix. This might result in malformed IDs (if, for instance, the " char is used in the prefix)',type:"boolean"},ghCompatibleHeaderId:{defaultValue:!1,describe:"Generate header ids compatible with github style (spaces are replaced with dashes, a bunch of non alphanumeric chars are removed)",type:"boolean"},rawHeaderId:{defaultValue:!1,describe:"Remove only spaces, ' and \" from generated header ids (including prefixes), replacing them with dashes (-). WARNING: This might result in malformed ids",type:"boolean"},headerLevelStart:{defaultValue:!1,describe:"The header blocks level start",type:"integer"},parseImgDimensions:{defaultValue:!1,describe:"Turn on/off image dimension parsing",type:"boolean"},simplifiedAutoLink:{defaultValue:!1,describe:"Turn on/off GFM autolink style",type:"boolean"},literalMidWordUnderscores:{defaultValue:!1,describe:"Parse midword underscores as literal underscores",type:"boolean"},literalMidWordAsterisks:{defaultValue:!1,describe:"Parse midword asterisks as literal asterisks",type:"boolean"},strikethrough:{defaultValue:!1,describe:"Turn on/off strikethrough support",type:"boolean"},tables:{defaultValue:!1,describe:"Turn on/off tables support",type:"boolean"},tablesHeaderId:{defaultValue:!1,describe:"Add an id to table headers",type:"boolean"},ghCodeBlocks:{defaultValue:!0,describe:"Turn on/off GFM fenced code blocks support",type:"boolean"},tasklists:{defaultValue:!1,describe:"Turn on/off GFM tasklist support",type:"boolean"},smoothLivePreview:{defaultValue:!1,describe:"Prevents weird effects in live previews due to incomplete input",type:"boolean"},smartIndentationFix:{defaultValue:!1,description:"Tries to smartly fix indentation in es6 strings",type:"boolean"},disableForced4SpacesIndentedSublists:{defaultValue:!1,description:"Disables the requirement of indenting nested sublists by 4 spaces",type:"boolean"},simpleLineBreaks:{defaultValue:!1,description:"Parses simple line breaks as <br> (GFM Style)",type:"boolean"},requireSpaceBeforeHeadingText:{defaultValue:!1,description:"Makes adding a space between `#` and the header text mandatory (GFM Style)",type:"boolean"},ghMentions:{defaultValue:!1,description:"Enables github @mentions",type:"boolean"},ghMentionsLink:{defaultValue:"https://github.com/{u}",description:"Changes the link generated by @mentions. Only applies if ghMentions option is enabled.",type:"string"},encodeEmails:{defaultValue:!0,description:"Encode e-mail addresses through the use of Character Entities, transforming ASCII e-mail addresses into its equivalent decimal entities",type:"boolean"},openLinksInNewWindow:{defaultValue:!1,description:"Open all links in new windows",type:"boolean"},backslashEscapesHTMLTags:{defaultValue:!1,description:"Support for HTML Tag escaping. ex: <div>foo</div>",type:"boolean"},emoji:{defaultValue:!1,description:"Enable emoji support. Ex: `this is a :smile: emoji`",type:"boolean"},underline:{defaultValue:!1,description:"Enable support for underline. Syntax is double or triple underscores: `__underline word__`. With this option enabled, underscores no longer parses into `<em>` and `<strong>`",type:"boolean"},completeHTMLDocument:{defaultValue:!1,description:"Outputs a complete html document, including `<html>`, `<head>` and `<body>` tags",type:"boolean"},metadata:{defaultValue:!1,description:"Enable support for document metadata (defined at the top of the document between `«««` and `»»»` or between `---` and `---`).",type:"boolean"},splitAdjacentBlockquotes:{defaultValue:!1,description:"Split adjacent blockquote blocks",type:"boolean"}};if(!1===simple)return JSON.parse(JSON.stringify(defaultOptions));var ret={};for(var opt in defaultOptions)defaultOptions.hasOwnProperty(opt)&&(ret[opt]=defaultOptions[opt].defaultValue);return ret}var showdown={},parsers={},extensions={},globalOptions=getDefaultOpts(!0),setFlavor="vanilla",flavor={github:{omitExtraWLInCodeBlocks:!0,simplifiedAutoLink:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,disableForced4SpacesIndentedSublists:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghCompatibleHeaderId:!0,ghMentions:!0,backslashEscapesHTMLTags:!0,emoji:!0,splitAdjacentBlockquotes:!0},original:{noHeaderId:!0,ghCodeBlocks:!1},ghost:{omitExtraWLInCodeBlocks:!0,parseImgDimensions:!0,simplifiedAutoLink:!0,literalMidWordUnderscores:!0,strikethrough:!0,tables:!0,tablesHeaderId:!0,ghCodeBlocks:!0,tasklists:!0,smoothLivePreview:!0,simpleLineBreaks:!0,requireSpaceBeforeHeadingText:!0,ghMentions:!1,encodeEmails:!0},vanilla:getDefaultOpts(!0),allOn:function(){"use strict";var options=getDefaultOpts(!0),ret={};for(var opt in options)options.hasOwnProperty(opt)&&(ret[opt]=!0);return ret}()};function validate(extension,name){"use strict";var errMsg=name?"Error in "+name+" extension->":"Error in unnamed extension",ret={valid:!0,error:""};showdown.helper.isArray(extension)||(extension=[extension]);for(var i=0;i<extension.length;++i){var baseMsg=errMsg+" sub-extension "+i+": ",ext=extension[i];if("object"!=typeof ext)return ret.valid=!1,ret.error=baseMsg+"must be an object, but "+typeof ext+" given",ret;if(!showdown.helper.isString(ext.type))return ret.valid=!1,ret.error=baseMsg+'property "type" must be a string, but '+typeof ext.type+" given",ret;var type=ext.type=ext.type.toLowerCase();if("language"===type&&(type=ext.type="lang"),"html"===type&&(type=ext.type="output"),"lang"!==type&&"output"!==type&&"listener"!==type)return ret.valid=!1,ret.error=baseMsg+"type "+type+' is not recognized. Valid values: "lang/language", "output/html" or "listener"',ret;if("listener"===type){if(showdown.helper.isUndefined(ext.listeners))return ret.valid=!1,ret.error=baseMsg+'. Extensions of type "listener" must have a property called "listeners"',ret}else if(showdown.helper.isUndefined(ext.filter)&&showdown.helper.isUndefined(ext.regex))return ret.valid=!1,ret.error=baseMsg+type+' extensions must define either a "regex" property or a "filter" method',ret;if(ext.listeners){if("object"!=typeof ext.listeners)return ret.valid=!1,ret.error=baseMsg+'"listeners" property must be an object but '+typeof ext.listeners+" given",ret;for(var ln in ext.listeners)if(ext.listeners.hasOwnProperty(ln)&&"function"!=typeof ext.listeners[ln])return ret.valid=!1,ret.error=baseMsg+'"listeners" property must be an hash of [event name]: [callback]. listeners.'+ln+" must be a function but "+typeof ext.listeners[ln]+" given",ret}if(ext.filter){if("function"!=typeof ext.filter)return ret.valid=!1,ret.error=baseMsg+'"filter" must be a function, but '+typeof ext.filter+" given",ret}else if(ext.regex){if(showdown.helper.isString(ext.regex)&&(ext.regex=new RegExp(ext.regex,"g")),!(ext.regex instanceof RegExp))return ret.valid=!1,ret.error=baseMsg+'"regex" property must either be a string or a RegExp object, but '+typeof ext.regex+" given",ret;if(showdown.helper.isUndefined(ext.replace))return ret.valid=!1,ret.error=baseMsg+'"regex" extensions must implement a replace string or function',ret}}return ret}if(showdown.helper={},showdown.extensions={},showdown.setOption=function(key,value){"use strict";return globalOptions[key]=value,this},showdown.getOption=function(key){"use strict";return globalOptions[key]},showdown.getOptions=function(){"use strict";return globalOptions},showdown.resetOptions=function(){"use strict";globalOptions=getDefaultOpts(!0)},showdown.setFlavor=function(name){"use strict";if(!flavor.hasOwnProperty(name))throw Error(name+" flavor was not found");showdown.resetOptions();var preset=flavor[name];for(var option in setFlavor=name,preset)preset.hasOwnProperty(option)&&(globalOptions[option]=preset[option])},showdown.getFlavor=function(){"use strict";return setFlavor},showdown.getFlavorOptions=function(name){"use strict";if(flavor.hasOwnProperty(name))return flavor[name]},showdown.getDefaultOptions=function(simple){"use strict";return getDefaultOpts(simple)},showdown.subParser=function(name,func){"use strict";if(!showdown.helper.isString(name))throw Error("showdown.subParser function first argument must be a string (the name of the subparser)");if(void 0===func){if(parsers.hasOwnProperty(name))return parsers[name];throw Error("SubParser named "+name+" not registered!")}parsers[name]=func},showdown.extension=function(name,ext){"use strict";if(!showdown.helper.isString(name))throw Error("Extension 'name' must be a string");if(name=showdown.helper.stdExtName(name),showdown.helper.isUndefined(ext)){if(!extensions.hasOwnProperty(name))throw Error("Extension named "+name+" is not registered!");return extensions[name]}"function"==typeof ext&&(ext=ext()),showdown.helper.isArray(ext)||(ext=[ext]);var validExtension=validate(ext,name);if(!validExtension.valid)throw Error(validExtension.error);extensions[name]=ext},showdown.getAllExtensions=function(){"use strict";return extensions},showdown.removeExtension=function(name){"use strict";delete extensions[name]},showdown.resetExtensions=function(){"use strict";extensions={}},showdown.validateExtension=function(ext){"use strict";var validateExtension=validate(ext,null);return!!validateExtension.valid||(console.warn(validateExtension.error),!1)},showdown.hasOwnProperty("helper")||(showdown.helper={}),void 0===this.document&&void 0===this.window){var jsdom=require("jsdom");this.window=new jsdom.JSDOM("",{}).window}function escapeCharactersCallback(wholeMatch,m1){"use strict";return"¨E"+m1.charCodeAt(0)+"E"}showdown.helper.document=this.window.document,showdown.helper.isString=function(a){"use strict";return"string"==typeof a||a instanceof String},showdown.helper.isFunction=function(a){"use strict";return a&&"[object Function]"==={}.toString.call(a)},showdown.helper.isArray=function(a){"use strict";return Array.isArray(a)},showdown.helper.isUndefined=function(value){"use strict";return void 0===value},showdown.helper.forEach=function(obj,callback){"use strict";if(showdown.helper.isUndefined(obj))throw new Error("obj param is required");if(showdown.helper.isUndefined(callback))throw new Error("callback param is required");if(!showdown.helper.isFunction(callback))throw new Error("callback param must be a function/closure");if("function"==typeof obj.forEach)obj.forEach(callback);else if(showdown.helper.isArray(obj))for(var i=0;i<obj.length;i++)callback(obj[i],i,obj);else{if("object"!=typeof obj)throw new Error("obj does not seem to be an array or an iterable object");for(var prop in obj)obj.hasOwnProperty(prop)&&callback(obj[prop],prop,obj)}},showdown.helper.stdExtName=function(s){"use strict";return s.replace(/[_?*+\/\\.^-]/g,"").replace(/\s/g,"").toLowerCase()},showdown.helper.escapeCharactersCallback=escapeCharactersCallback,showdown.helper.escapeCharacters=function(text,charsToEscape,afterBackslash){"use strict";var regexString="(["+charsToEscape.replace(/([\[\]\\])/g,"\\$1")+"])";afterBackslash&&(regexString="\\\\"+regexString);var regex=new RegExp(regexString,"g");return text=text.replace(regex,escapeCharactersCallback)};var rgxFindMatchPos=function(str,left,right,flags){"use strict";var t,s,m,start,end,f=flags||"",g=f.indexOf("g")>-1,x=new RegExp(left+"|"+right,"g"+f.replace(/g/g,"")),l=new RegExp(left,f.replace(/g/g,"")),pos=[];do{for(t=0;m=x.exec(str);)if(l.test(m[0]))t++||(start=(s=x.lastIndex)-m[0].length);else if(t&&!--t){end=m.index+m[0].length;var obj={left:{start:start,end:s},match:{start:s,end:m.index},right:{start:m.index,end:end},wholeMatch:{start:start,end:end}};if(pos.push(obj),!g)return pos}}while(t&&(x.lastIndex=s));return pos};showdown.helper.matchRecursiveRegExp=function(str,left,right,flags){"use strict";for(var matchPos=rgxFindMatchPos(str,left,right,flags),results=[],i=0;i<matchPos.length;++i)results.push([str.slice(matchPos[i].wholeMatch.start,matchPos[i].wholeMatch.end),str.slice(matchPos[i].match.start,matchPos[i].match.end),str.slice(matchPos[i].left.start,matchPos[i].left.end),str.slice(matchPos[i].right.start,matchPos[i].right.end)]);return results},showdown.helper.replaceRecursiveRegExp=function(str,replacement,left,right,flags){"use strict";if(!showdown.helper.isFunction(replacement)){var repStr=replacement;replacement=function(){return repStr}}var matchPos=rgxFindMatchPos(str,left,right,flags),finalStr=str,lng=matchPos.length;if(lng>0){var bits=[];0!==matchPos[0].wholeMatch.start&&bits.push(str.slice(0,matchPos[0].wholeMatch.start));for(var i=0;i<lng;++i)bits.push(replacement(str.slice(matchPos[i].wholeMatch.start,matchPos[i].wholeMatch.end),str.slice(matchPos[i].match.start,matchPos[i].match.end),str.slice(matchPos[i].left.start,matchPos[i].left.end),str.slice(matchPos[i].right.start,matchPos[i].right.end))),i<lng-1&&bits.push(str.slice(matchPos[i].wholeMatch.end,matchPos[i+1].wholeMatch.start));matchPos[lng-1].wholeMatch.end<str.length&&bits.push(str.slice(matchPos[lng-1].wholeMatch.end)),finalStr=bits.join("")}return finalStr},showdown.helper.regexIndexOf=function(str,regex,fromIndex){"use strict";if(!showdown.helper.isString(str))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";if(regex instanceof RegExp==!1)throw"InvalidArgumentError: second parameter of showdown.helper.regexIndexOf function must be an instance of RegExp";var indexOf=str.substring(fromIndex||0).search(regex);return indexOf>=0?indexOf+(fromIndex||0):indexOf},showdown.helper.splitAtIndex=function(str,index){"use strict";if(!showdown.helper.isString(str))throw"InvalidArgumentError: first parameter of showdown.helper.regexIndexOf function must be a string";return[str.substring(0,index),str.substring(index)]},showdown.helper.encodeEmailAddress=function(mail){"use strict";var encode=[function(ch){return"&#"+ch.charCodeAt(0)+";"},function(ch){return"&#x"+ch.charCodeAt(0).toString(16)+";"},function(ch){return ch}];return mail=mail.replace(/./g,(function(ch){if("@"===ch)ch=encode[Math.floor(2*Math.random())](ch);else{var r=Math.random();ch=r>.9?encode[2](ch):r>.45?encode[1](ch):encode[0](ch)}return ch}))},showdown.helper.padEnd=function(str,targetLength,padString){"use strict";return targetLength>>=0,padString=String(padString||" "),str.length>targetLength?String(str):((targetLength-=str.length)>padString.length&&(padString+=padString.repeat(targetLength/padString.length)),String(str)+padString.slice(0,targetLength))},showdown.helper.unescapeHTMLEntities=function(txt){"use strict";return txt.replace(/&quot;/g,'"').replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&")},showdown.helper._hashHTMLSpan=function(html,globals){return"¨C"+(globals.gHtmlSpans.push(html)-1)+"C"},showdown.helper.Event=function(name,text,params){"use strict";var regexp=params.regexp||null,matches=params.matches||{},options=params.options||{},converter=params.converter||null,globals=params.globals||{};this.getName=function(){return name},this.getEventName=function(){return name},this._stopExecution=!1,this.parsedText=params.parsedText||null,this.getRegexp=function(){return regexp},this.getOptions=function(){return options},this.getConverter=function(){return converter},this.getGlobals=function(){return globals},this.getCapturedText=function(){return text},this.getText=function(){return text},this.setText=function(newText){text=newText},this.getMatches=function(){return matches},this.setMatches=function(newMatches){matches=newMatches},this.preventDefault=function(bool){this._stopExecution=!bool}},"undefined"==typeof console&&(console={warn:function(msg){"use strict";alert(msg)},log:function(msg){"use strict";alert(msg)},error:function(msg){"use strict";throw msg}}),showdown.helper.regexes={asteriskDashTildeAndColon:/([*_:~])/g,asteriskDashAndTilde:/([*_~])/g},showdown.helper.emojis={"+1":"👍","-1":"👎",100:"💯",1234:"🔢","1st_place_medal":"🥇","2nd_place_medal":"🥈","3rd_place_medal":"🥉","8ball":"🎱",a:"🅰️",ab:"🆎",abc:"🔤",abcd:"🔡",accept:"🉑",aerial_tramway:"🚡",airplane:"✈️",alarm_clock:"⏰",alembic:"⚗️",alien:"👽",ambulance:"🚑",amphora:"🏺",anchor:"⚓️",angel:"👼",anger:"💢",angry:"😠",anguished:"😧",ant:"🐜",apple:"🍎",aquarius:"♒️",aries:"♈️",arrow_backward:"◀️",arrow_double_down:"⏬",arrow_double_up:"⏫",arrow_down:"⬇️",arrow_down_small:"🔽",arrow_forward:"▶️",arrow_heading_down:"⤵️",arrow_heading_up:"⤴️",arrow_left:"⬅️",arrow_lower_left:"↙️",arrow_lower_right:"↘️",arrow_right:"➡️",arrow_right_hook:"↪️",arrow_up:"⬆️",arrow_up_down:"↕️",arrow_up_small:"🔼",arrow_upper_left:"↖️",arrow_upper_right:"↗️",arrows_clockwise:"🔃",arrows_counterclockwise:"🔄",art:"🎨",articulated_lorry:"🚛",artificial_satellite:"🛰",astonished:"😲",athletic_shoe:"👟",atm:"🏧",atom_symbol:"⚛️",avocado:"🥑",b:"🅱️",baby:"👶",baby_bottle:"🍼",baby_chick:"🐤",baby_symbol:"🚼",back:"🔙",bacon:"🥓",badminton:"🏸",baggage_claim:"🛄",baguette_bread:"🥖",balance_scale:"⚖️",balloon:"🎈",ballot_box:"🗳",ballot_box_with_check:"☑️",bamboo:"🎍",banana:"🍌",bangbang:"‼️",bank:"🏦",bar_chart:"📊",barber:"💈",baseball:"⚾️",basketball:"🏀",basketball_man:"⛹️",basketball_woman:"⛹️&zwj;♀️",bat:"🦇",bath:"🛀",bathtub:"🛁",battery:"🔋",beach_umbrella:"🏖",bear:"🐻",bed:"🛏",bee:"🐝",beer:"🍺",beers:"🍻",beetle:"🐞",beginner:"🔰",bell:"🔔",bellhop_bell:"🛎",bento:"🍱",biking_man:"🚴",bike:"🚲",biking_woman:"🚴&zwj;♀️",bikini:"👙",biohazard:"☣️",bird:"🐦",birthday:"🎂",black_circle:"⚫️",black_flag:"🏴",black_heart:"🖤",black_joker:"🃏",black_large_square:"⬛️",black_medium_small_square:"◾️",black_medium_square:"◼️",black_nib:"✒️",black_small_square:"▪️",black_square_button:"🔲",blonde_man:"👱",blonde_woman:"👱&zwj;♀️",blossom:"🌼",blowfish:"🐡",blue_book:"📘",blue_car:"🚙",blue_heart:"💙",blush:"😊",boar:"🐗",boat:"⛵️",bomb:"💣",book:"📖",bookmark:"🔖",bookmark_tabs:"📑",books:"📚",boom:"💥",boot:"👢",bouquet:"💐",bowing_man:"🙇",bow_and_arrow:"🏹",bowing_woman:"🙇&zwj;♀️",bowling:"🎳",boxing_glove:"🥊",boy:"👦",bread:"🍞",bride_with_veil:"👰",bridge_at_night:"🌉",briefcase:"💼",broken_heart:"💔",bug:"🐛",building_construction:"🏗",bulb:"💡",bullettrain_front:"🚅",bullettrain_side:"🚄",burrito:"🌯",bus:"🚌",business_suit_levitating:"🕴",busstop:"🚏",bust_in_silhouette:"👤",busts_in_silhouette:"👥",butterfly:"🦋",cactus:"🌵",cake:"🍰",calendar:"📆",call_me_hand:"🤙",calling:"📲",camel:"🐫",camera:"📷",camera_flash:"📸",camping:"🏕",cancer:"♋️",candle:"🕯",candy:"🍬",canoe:"🛶",capital_abcd:"🔠",capricorn:"♑️",car:"🚗",card_file_box:"🗃",card_index:"📇",card_index_dividers:"🗂",carousel_horse:"🎠",carrot:"🥕",cat:"🐱",cat2:"🐈",cd:"💿",chains:"⛓",champagne:"🍾",chart:"💹",chart_with_downwards_trend:"📉",chart_with_upwards_trend:"📈",checkered_flag:"🏁",cheese:"🧀",cherries:"🍒",cherry_blossom:"🌸",chestnut:"🌰",chicken:"🐔",children_crossing:"🚸",chipmunk:"🐿",chocolate_bar:"🍫",christmas_tree:"🎄",church:"⛪️",cinema:"🎦",circus_tent:"🎪",city_sunrise:"🌇",city_sunset:"🌆",cityscape:"🏙",cl:"🆑",clamp:"🗜",clap:"👏",clapper:"🎬",classical_building:"🏛",clinking_glasses:"🥂",clipboard:"📋",clock1:"🕐",clock10:"🕙",clock1030:"🕥",clock11:"🕚",clock1130:"🕦",clock12:"🕛",clock1230:"🕧",clock130:"🕜",clock2:"🕑",clock230:"🕝",clock3:"🕒",clock330:"🕞",clock4:"🕓",clock430:"🕟",clock5:"🕔",clock530:"🕠",clock6:"🕕",clock630:"🕡",clock7:"🕖",clock730:"🕢",clock8:"🕗",clock830:"🕣",clock9:"🕘",clock930:"🕤",closed_book:"📕",closed_lock_with_key:"🔐",closed_umbrella:"🌂",cloud:"☁️",cloud_with_lightning:"🌩",cloud_with_lightning_and_rain:"⛈",cloud_with_rain:"🌧",cloud_with_snow:"🌨",clown_face:"🤡",clubs:"♣️",cocktail:"🍸",coffee:"☕️",coffin:"⚰️",cold_sweat:"😰",comet:"☄️",computer:"💻",computer_mouse:"🖱",confetti_ball:"🎊",confounded:"😖",confused:"😕",congratulations:"㊗️",construction:"🚧",construction_worker_man:"👷",construction_worker_woman:"👷&zwj;♀️",control_knobs:"🎛",convenience_store:"🏪",cookie:"🍪",cool:"🆒",policeman:"👮",copyright:"©️",corn:"🌽",couch_and_lamp:"🛋",couple:"👫",couple_with_heart_woman_man:"💑",couple_with_heart_man_man:"👨&zwj;❤️&zwj;👨",couple_with_heart_woman_woman:"👩&zwj;❤️&zwj;👩",couplekiss_man_man:"👨&zwj;❤️&zwj;💋&zwj;👨",couplekiss_man_woman:"💏",couplekiss_woman_woman:"👩&zwj;❤️&zwj;💋&zwj;👩",cow:"🐮",cow2:"🐄",cowboy_hat_face:"🤠",crab:"🦀",crayon:"🖍",credit_card:"💳",crescent_moon:"🌙",cricket:"🏏",crocodile:"🐊",croissant:"🥐",crossed_fingers:"🤞",crossed_flags:"🎌",crossed_swords:"⚔️",crown:"👑",cry:"😢",crying_cat_face:"😿",crystal_ball:"🔮",cucumber:"🥒",cupid:"💘",curly_loop:"➰",currency_exchange:"💱",curry:"🍛",custard:"🍮",customs:"🛃",cyclone:"🌀",dagger:"🗡",dancer:"💃",dancing_women:"👯",dancing_men:"👯&zwj;♂️",dango:"🍡",dark_sunglasses:"🕶",dart:"🎯",dash:"💨",date:"📅",deciduous_tree:"🌳",deer:"🦌",department_store:"🏬",derelict_house:"🏚",desert:"🏜",desert_island:"🏝",desktop_computer:"🖥",male_detective:"🕵️",diamond_shape_with_a_dot_inside:"💠",diamonds:"♦️",disappointed:"😞",disappointed_relieved:"😥",dizzy:"💫",dizzy_face:"😵",do_not_litter:"🚯",dog:"🐶",dog2:"🐕",dollar:"💵",dolls:"🎎",dolphin:"🐬",door:"🚪",doughnut:"🍩",dove:"🕊",dragon:"🐉",dragon_face:"🐲",dress:"👗",dromedary_camel:"🐪",drooling_face:"🤤",droplet:"💧",drum:"🥁",duck:"🦆",dvd:"📀","e-mail":"📧",eagle:"🦅",ear:"👂",ear_of_rice:"🌾",earth_africa:"🌍",earth_americas:"🌎",earth_asia:"🌏",egg:"🥚",eggplant:"🍆",eight_pointed_black_star:"✴️",eight_spoked_asterisk:"✳️",electric_plug:"🔌",elephant:"🐘",email:"✉️",end:"🔚",envelope_with_arrow:"📩",euro:"💶",european_castle:"🏰",european_post_office:"🏤",evergreen_tree:"🌲",exclamation:"❗️",expressionless:"😑",eye:"👁",eye_speech_bubble:"👁&zwj;🗨",eyeglasses:"👓",eyes:"👀",face_with_head_bandage:"🤕",face_with_thermometer:"🤒",fist_oncoming:"👊",factory:"🏭",fallen_leaf:"🍂",family_man_woman_boy:"👪",family_man_boy:"👨&zwj;👦",family_man_boy_boy:"👨&zwj;👦&zwj;👦",family_man_girl:"👨&zwj;👧",family_man_girl_boy:"👨&zwj;👧&zwj;👦",family_man_girl_girl:"👨&zwj;👧&zwj;👧",family_man_man_boy:"👨&zwj;👨&zwj;👦",family_man_man_boy_boy:"👨&zwj;👨&zwj;👦&zwj;👦",family_man_man_girl:"👨&zwj;👨&zwj;👧",family_man_man_girl_boy:"👨&zwj;👨&zwj;👧&zwj;👦",family_man_man_girl_girl:"👨&zwj;👨&zwj;👧&zwj;👧",family_man_woman_boy_boy:"👨&zwj;👩&zwj;👦&zwj;👦",family_man_woman_girl:"👨&zwj;👩&zwj;👧",family_man_woman_girl_boy:"👨&zwj;👩&zwj;👧&zwj;👦",family_man_woman_girl_girl:"👨&zwj;👩&zwj;👧&zwj;👧",family_woman_boy:"👩&zwj;👦",family_woman_boy_boy:"👩&zwj;👦&zwj;👦",family_woman_girl:"👩&zwj;👧",family_woman_girl_boy:"👩&zwj;👧&zwj;👦",family_woman_girl_girl:"👩&zwj;👧&zwj;👧",family_woman_woman_boy:"👩&zwj;👩&zwj;👦",family_woman_woman_boy_boy:"👩&zwj;👩&zwj;👦&zwj;👦",family_woman_woman_girl:"👩&zwj;👩&zwj;👧",family_woman_woman_girl_boy:"👩&zwj;👩&zwj;👧&zwj;👦",family_woman_woman_girl_girl:"👩&zwj;👩&zwj;👧&zwj;👧",fast_forward:"⏩",fax:"📠",fearful:"😨",feet:"🐾",female_detective:"🕵️&zwj;♀️",ferris_wheel:"🎡",ferry:"⛴",field_hockey:"🏑",file_cabinet:"🗄",file_folder:"📁",film_projector:"📽",film_strip:"🎞",fire:"🔥",fire_engine:"🚒",fireworks:"🎆",first_quarter_moon:"🌓",first_quarter_moon_with_face:"🌛",fish:"🐟",fish_cake:"🍥",fishing_pole_and_fish:"🎣",fist_raised:"✊",fist_left:"🤛",fist_right:"🤜",flags:"🎏",flashlight:"🔦",fleur_de_lis:"⚜️",flight_arrival:"🛬",flight_departure:"🛫",floppy_disk:"💾",flower_playing_cards:"🎴",flushed:"😳",fog:"🌫",foggy:"🌁",football:"🏈",footprints:"👣",fork_and_knife:"🍴",fountain:"⛲️",fountain_pen:"🖋",four_leaf_clover:"🍀",fox_face:"🦊",framed_picture:"🖼",free:"🆓",fried_egg:"🍳",fried_shrimp:"🍤",fries:"🍟",frog:"🐸",frowning:"😦",frowning_face:"☹️",frowning_man:"🙍&zwj;♂️",frowning_woman:"🙍",middle_finger:"🖕",fuelpump:"⛽️",full_moon:"🌕",full_moon_with_face:"🌝",funeral_urn:"⚱️",game_die:"🎲",gear:"⚙️",gem:"💎",gemini:"♊️",ghost:"👻",gift:"🎁",gift_heart:"💝",girl:"👧",globe_with_meridians:"🌐",goal_net:"🥅",goat:"🐐",golf:"⛳️",golfing_man:"🏌️",golfing_woman:"🏌️&zwj;♀️",gorilla:"🦍",grapes:"🍇",green_apple:"🍏",green_book:"📗",green_heart:"💚",green_salad:"🥗",grey_exclamation:"❕",grey_question:"❔",grimacing:"😬",grin:"😁",grinning:"😀",guardsman:"💂",guardswoman:"💂&zwj;♀️",guitar:"🎸",gun:"🔫",haircut_woman:"💇",haircut_man:"💇&zwj;♂️",hamburger:"🍔",hammer:"🔨",hammer_and_pick:"⚒",hammer_and_wrench:"🛠",hamster:"🐹",hand:"✋",handbag:"👜",handshake:"🤝",hankey:"💩",hatched_chick:"🐥",hatching_chick:"🐣",headphones:"🎧",hear_no_evil:"🙉",heart:"❤️",heart_decoration:"💟",heart_eyes:"😍",heart_eyes_cat:"😻",heartbeat:"💓",heartpulse:"💗",hearts:"♥️",heavy_check_mark:"✔️",heavy_division_sign:"➗",heavy_dollar_sign:"💲",heavy_heart_exclamation:"❣️",heavy_minus_sign:"➖",heavy_multiplication_x:"✖️",heavy_plus_sign:"➕",helicopter:"🚁",herb:"🌿",hibiscus:"🌺",high_brightness:"🔆",high_heel:"👠",hocho:"🔪",hole:"🕳",honey_pot:"🍯",horse:"🐴",horse_racing:"🏇",hospital:"🏥",hot_pepper:"🌶",hotdog:"🌭",hotel:"🏨",hotsprings:"♨️",hourglass:"⌛️",hourglass_flowing_sand:"⏳",house:"🏠",house_with_garden:"🏡",houses:"🏘",hugs:"🤗",hushed:"😯",ice_cream:"🍨",ice_hockey:"🏒",ice_skate:"⛸",icecream:"🍦",id:"🆔",ideograph_advantage:"🉐",imp:"👿",inbox_tray:"📥",incoming_envelope:"📨",tipping_hand_woman:"💁",information_source:"ℹ️",innocent:"😇",interrobang:"⁉️",iphone:"📱",izakaya_lantern:"🏮",jack_o_lantern:"🎃",japan:"🗾",japanese_castle:"🏯",japanese_goblin:"👺",japanese_ogre:"👹",jeans:"👖",joy:"😂",joy_cat:"😹",joystick:"🕹",kaaba:"🕋",key:"🔑",keyboard:"⌨️",keycap_ten:"🔟",kick_scooter:"🛴",kimono:"👘",kiss:"💋",kissing:"😗",kissing_cat:"😽",kissing_closed_eyes:"😚",kissing_heart:"😘",kissing_smiling_eyes:"😙",kiwi_fruit:"🥝",koala:"🐨",koko:"🈁",label:"🏷",large_blue_circle:"🔵",large_blue_diamond:"🔷",large_orange_diamond:"🔶",last_quarter_moon:"🌗",last_quarter_moon_with_face:"🌜",latin_cross:"✝️",laughing:"😆",leaves:"🍃",ledger:"📒",left_luggage:"🛅",left_right_arrow:"↔️",leftwards_arrow_with_hook:"↩️",lemon:"🍋",leo:"♌️",leopard:"🐆",level_slider:"🎚",libra:"♎️",light_rail:"🚈",link:"🔗",lion:"🦁",lips:"👄",lipstick:"💄",lizard:"🦎",lock:"🔒",lock_with_ink_pen:"🔏",lollipop:"🍭",loop:"➿",loud_sound:"🔊",loudspeaker:"📢",love_hotel:"🏩",love_letter:"💌",low_brightness:"🔅",lying_face:"🤥",m:"Ⓜ️",mag:"🔍",mag_right:"🔎",mahjong:"🀄️",mailbox:"📫",mailbox_closed:"📪",mailbox_with_mail:"📬",mailbox_with_no_mail:"📭",man:"👨",man_artist:"👨&zwj;🎨",man_astronaut:"👨&zwj;🚀",man_cartwheeling:"🤸&zwj;♂️",man_cook:"👨&zwj;🍳",man_dancing:"🕺",man_facepalming:"🤦&zwj;♂️",man_factory_worker:"👨&zwj;🏭",man_farmer:"👨&zwj;🌾",man_firefighter:"👨&zwj;🚒",man_health_worker:"👨&zwj;⚕️",man_in_tuxedo:"🤵",man_judge:"👨&zwj;⚖️",man_juggling:"🤹&zwj;♂️",man_mechanic:"👨&zwj;🔧",man_office_worker:"👨&zwj;💼",man_pilot:"👨&zwj;✈️",man_playing_handball:"🤾&zwj;♂️",man_playing_water_polo:"🤽&zwj;♂️",man_scientist:"👨&zwj;🔬",man_shrugging:"🤷&zwj;♂️",man_singer:"👨&zwj;🎤",man_student:"👨&zwj;🎓",man_teacher:"👨&zwj;🏫",man_technologist:"👨&zwj;💻",man_with_gua_pi_mao:"👲",man_with_turban:"👳",tangerine:"🍊",mans_shoe:"👞",mantelpiece_clock:"🕰",maple_leaf:"🍁",martial_arts_uniform:"🥋",mask:"😷",massage_woman:"💆",massage_man:"💆&zwj;♂️",meat_on_bone:"🍖",medal_military:"🎖",medal_sports:"🏅",mega:"📣",melon:"🍈",memo:"📝",men_wrestling:"🤼&zwj;♂️",menorah:"🕎",mens:"🚹",metal:"🤘",metro:"🚇",microphone:"🎤",microscope:"🔬",milk_glass:"🥛",milky_way:"🌌",minibus:"🚐",minidisc:"💽",mobile_phone_off:"📴",money_mouth_face:"🤑",money_with_wings:"💸",moneybag:"💰",monkey:"🐒",monkey_face:"🐵",monorail:"🚝",moon:"🌔",mortar_board:"🎓",mosque:"🕌",motor_boat:"🛥",motor_scooter:"🛵",motorcycle:"🏍",motorway:"🛣",mount_fuji:"🗻",mountain:"⛰",mountain_biking_man:"🚵",mountain_biking_woman:"🚵&zwj;♀️",mountain_cableway:"🚠",mountain_railway:"🚞",mountain_snow:"🏔",mouse:"🐭",mouse2:"🐁",movie_camera:"🎥",moyai:"🗿",mrs_claus:"🤶",muscle:"💪",mushroom:"🍄",musical_keyboard:"🎹",musical_note:"🎵",musical_score:"🎼",mute:"🔇",nail_care:"💅",name_badge:"📛",national_park:"🏞",nauseated_face:"🤢",necktie:"👔",negative_squared_cross_mark:"❎",nerd_face:"🤓",neutral_face:"😐",new:"🆕",new_moon:"🌑",new_moon_with_face:"🌚",newspaper:"📰",newspaper_roll:"🗞",next_track_button:"⏭",ng:"🆖",no_good_man:"🙅&zwj;♂️",no_good_woman:"🙅",night_with_stars:"🌃",no_bell:"🔕",no_bicycles:"🚳",no_entry:"⛔️",no_entry_sign:"🚫",no_mobile_phones:"📵",no_mouth:"😶",no_pedestrians:"🚷",no_smoking:"🚭","non-potable_water":"🚱",nose:"👃",notebook:"📓",notebook_with_decorative_cover:"📔",notes:"🎶",nut_and_bolt:"🔩",o:"⭕️",o2:"🅾️",ocean:"🌊",octopus:"🐙",oden:"🍢",office:"🏢",oil_drum:"🛢",ok:"🆗",ok_hand:"👌",ok_man:"🙆&zwj;♂️",ok_woman:"🙆",old_key:"🗝",older_man:"👴",older_woman:"👵",om:"🕉",on:"🔛",oncoming_automobile:"🚘",oncoming_bus:"🚍",oncoming_police_car:"🚔",oncoming_taxi:"🚖",open_file_folder:"📂",open_hands:"👐",open_mouth:"😮",open_umbrella:"☂️",ophiuchus:"⛎",orange_book:"📙",orthodox_cross:"☦️",outbox_tray:"📤",owl:"🦉",ox:"🐂",package:"📦",page_facing_up:"📄",page_with_curl:"📃",pager:"📟",paintbrush:"🖌",palm_tree:"🌴",pancakes:"🥞",panda_face:"🐼",paperclip:"📎",paperclips:"🖇",parasol_on_ground:"⛱",parking:"🅿️",part_alternation_mark:"〽️",partly_sunny:"⛅️",passenger_ship:"🛳",passport_control:"🛂",pause_button:"⏸",peace_symbol:"☮️",peach:"🍑",peanuts:"🥜",pear:"🍐",pen:"🖊",pencil2:"✏️",penguin:"🐧",pensive:"😔",performing_arts:"🎭",persevere:"😣",person_fencing:"🤺",pouting_woman:"🙎",phone:"☎️",pick:"⛏",pig:"🐷",pig2:"🐖",pig_nose:"🐽",pill:"💊",pineapple:"🍍",ping_pong:"🏓",pisces:"♓️",pizza:"🍕",place_of_worship:"🛐",plate_with_cutlery:"🍽",play_or_pause_button:"⏯",point_down:"👇",point_left:"👈",point_right:"👉",point_up:"☝️",point_up_2:"👆",police_car:"🚓",policewoman:"👮&zwj;♀️",poodle:"🐩",popcorn:"🍿",post_office:"🏣",postal_horn:"📯",postbox:"📮",potable_water:"🚰",potato:"🥔",pouch:"👝",poultry_leg:"🍗",pound:"💷",rage:"😡",pouting_cat:"😾",pouting_man:"🙎&zwj;♂️",pray:"🙏",prayer_beads:"📿",pregnant_woman:"🤰",previous_track_button:"⏮",prince:"🤴",princess:"👸",printer:"🖨",purple_heart:"💜",purse:"👛",pushpin:"📌",put_litter_in_its_place:"🚮",question:"❓",rabbit:"🐰",rabbit2:"🐇",racehorse:"🐎",racing_car:"🏎",radio:"📻",radio_button:"🔘",radioactive:"☢️",railway_car:"🚃",railway_track:"🛤",rainbow:"🌈",rainbow_flag:"🏳️&zwj;🌈",raised_back_of_hand:"🤚",raised_hand_with_fingers_splayed:"🖐",raised_hands:"🙌",raising_hand_woman:"🙋",raising_hand_man:"🙋&zwj;♂️",ram:"🐏",ramen:"🍜",rat:"🐀",record_button:"⏺",recycle:"♻️",red_circle:"🔴",registered:"®️",relaxed:"☺️",relieved:"😌",reminder_ribbon:"🎗",repeat:"🔁",repeat_one:"🔂",rescue_worker_helmet:"⛑",restroom:"🚻",revolving_hearts:"💞",rewind:"⏪",rhinoceros:"🦏",ribbon:"🎀",rice:"🍚",rice_ball:"🍙",rice_cracker:"🍘",rice_scene:"🎑",right_anger_bubble:"🗯",ring:"💍",robot:"🤖",rocket:"🚀",rofl:"🤣",roll_eyes:"🙄",roller_coaster:"🎢",rooster:"🐓",rose:"🌹",rosette:"🏵",rotating_light:"🚨",round_pushpin:"📍",rowing_man:"🚣",rowing_woman:"🚣&zwj;♀️",rugby_football:"🏉",running_man:"🏃",running_shirt_with_sash:"🎽",running_woman:"🏃&zwj;♀️",sa:"🈂️",sagittarius:"♐️",sake:"🍶",sandal:"👡",santa:"🎅",satellite:"📡",saxophone:"🎷",school:"🏫",school_satchel:"🎒",scissors:"✂️",scorpion:"🦂",scorpius:"♏️",scream:"😱",scream_cat:"🙀",scroll:"📜",seat:"💺",secret:"㊙️",see_no_evil:"🙈",seedling:"🌱",selfie:"🤳",shallow_pan_of_food:"🥘",shamrock:"☘️",shark:"🦈",shaved_ice:"🍧",sheep:"🐑",shell:"🐚",shield:"🛡",shinto_shrine:"⛩",ship:"🚢",shirt:"👕",shopping:"🛍",shopping_cart:"🛒",shower:"🚿",shrimp:"🦐",signal_strength:"📶",six_pointed_star:"🔯",ski:"🎿",skier:"⛷",skull:"💀",skull_and_crossbones:"☠️",sleeping:"😴",sleeping_bed:"🛌",sleepy:"😪",slightly_frowning_face:"🙁",slightly_smiling_face:"🙂",slot_machine:"🎰",small_airplane:"🛩",small_blue_diamond:"🔹",small_orange_diamond:"🔸",small_red_triangle:"🔺",small_red_triangle_down:"🔻",smile:"😄",smile_cat:"😸",smiley:"😃",smiley_cat:"😺",smiling_imp:"😈",smirk:"😏",smirk_cat:"😼",smoking:"🚬",snail:"🐌",snake:"🐍",sneezing_face:"🤧",snowboarder:"🏂",snowflake:"❄️",snowman:"⛄️",snowman_with_snow:"☃️",sob:"😭",soccer:"⚽️",soon:"🔜",sos:"🆘",sound:"🔉",space_invader:"👾",spades:"♠️",spaghetti:"🍝",sparkle:"❇️",sparkler:"🎇",sparkles:"✨",sparkling_heart:"💖",speak_no_evil:"🙊",speaker:"🔈",speaking_head:"🗣",speech_balloon:"💬",speedboat:"🚤",spider:"🕷",spider_web:"🕸",spiral_calendar:"🗓",spiral_notepad:"🗒",spoon:"🥄",squid:"🦑",stadium:"🏟",star:"⭐️",star2:"🌟",star_and_crescent:"☪️",star_of_david:"✡️",stars:"🌠",station:"🚉",statue_of_liberty:"🗽",steam_locomotive:"🚂",stew:"🍲",stop_button:"⏹",stop_sign:"🛑",stopwatch:"⏱",straight_ruler:"📏",strawberry:"🍓",stuck_out_tongue:"😛",stuck_out_tongue_closed_eyes:"😝",stuck_out_tongue_winking_eye:"😜",studio_microphone:"🎙",stuffed_flatbread:"🥙",sun_behind_large_cloud:"🌥",sun_behind_rain_cloud:"🌦",sun_behind_small_cloud:"🌤",sun_with_face:"🌞",sunflower:"🌻",sunglasses:"😎",sunny:"☀️",sunrise:"🌅",sunrise_over_mountains:"🌄",surfing_man:"🏄",surfing_woman:"🏄&zwj;♀️",sushi:"🍣",suspension_railway:"🚟",sweat:"😓",sweat_drops:"💦",sweat_smile:"😅",sweet_potato:"🍠",swimming_man:"🏊",swimming_woman:"🏊&zwj;♀️",symbols:"🔣",synagogue:"🕍",syringe:"💉",taco:"🌮",tada:"🎉",tanabata_tree:"🎋",taurus:"♉️",taxi:"🚕",tea:"🍵",telephone_receiver:"📞",telescope:"🔭",tennis:"🎾",tent:"⛺️",thermometer:"🌡",thinking:"🤔",thought_balloon:"💭",ticket:"🎫",tickets:"🎟",tiger:"🐯",tiger2:"🐅",timer_clock:"⏲",tipping_hand_man:"💁&zwj;♂️",tired_face:"😫",tm:"™️",toilet:"🚽",tokyo_tower:"🗼",tomato:"🍅",tongue:"👅",top:"🔝",tophat:"🎩",tornado:"🌪",trackball:"🖲",tractor:"🚜",traffic_light:"🚥",train:"🚋",train2:"🚆",tram:"🚊",triangular_flag_on_post:"🚩",triangular_ruler:"📐",trident:"🔱",triumph:"😤",trolleybus:"🚎",trophy:"🏆",tropical_drink:"🍹",tropical_fish:"🐠",truck:"🚚",trumpet:"🎺",tulip:"🌷",tumbler_glass:"🥃",turkey:"🦃",turtle:"🐢",tv:"📺",twisted_rightwards_arrows:"🔀",two_hearts:"💕",two_men_holding_hands:"👬",two_women_holding_hands:"👭",u5272:"🈹",u5408:"🈴",u55b6:"🈺",u6307:"🈯️",u6708:"🈷️",u6709:"🈶",u6e80:"🈵",u7121:"🈚️",u7533:"🈸",u7981:"🈲",u7a7a:"🈳",umbrella:"☔️",unamused:"😒",underage:"🔞",unicorn:"🦄",unlock:"🔓",up:"🆙",upside_down_face:"🙃",v:"✌️",vertical_traffic_light:"🚦",vhs:"📼",vibration_mode:"📳",video_camera:"📹",video_game:"🎮",violin:"🎻",virgo:"♍️",volcano:"🌋",volleyball:"🏐",vs:"🆚",vulcan_salute:"🖖",walking_man:"🚶",walking_woman:"🚶&zwj;♀️",waning_crescent_moon:"🌘",waning_gibbous_moon:"🌖",warning:"⚠️",wastebasket:"🗑",watch:"⌚️",water_buffalo:"🐃",watermelon:"🍉",wave:"👋",wavy_dash:"〰️",waxing_crescent_moon:"🌒",wc:"🚾",weary:"😩",wedding:"💒",weight_lifting_man:"🏋️",weight_lifting_woman:"🏋️&zwj;♀️",whale:"🐳",whale2:"🐋",wheel_of_dharma:"☸️",wheelchair:"♿️",white_check_mark:"✅",white_circle:"⚪️",white_flag:"🏳️",white_flower:"💮",white_large_square:"⬜️",white_medium_small_square:"◽️",white_medium_square:"◻️",white_small_square:"▫️",white_square_button:"🔳",wilted_flower:"🥀",wind_chime:"🎐",wind_face:"🌬",wine_glass:"🍷",wink:"😉",wolf:"🐺",woman:"👩",woman_artist:"👩&zwj;🎨",woman_astronaut:"👩&zwj;🚀",woman_cartwheeling:"🤸&zwj;♀️",woman_cook:"👩&zwj;🍳",woman_facepalming:"🤦&zwj;♀️",woman_factory_worker:"👩&zwj;🏭",woman_farmer:"👩&zwj;🌾",woman_firefighter:"👩&zwj;🚒",woman_health_worker:"👩&zwj;⚕️",woman_judge:"👩&zwj;⚖️",woman_juggling:"🤹&zwj;♀️",woman_mechanic:"👩&zwj;🔧",woman_office_worker:"👩&zwj;💼",woman_pilot:"👩&zwj;✈️",woman_playing_handball:"🤾&zwj;♀️",woman_playing_water_polo:"🤽&zwj;♀️",woman_scientist:"👩&zwj;🔬",woman_shrugging:"🤷&zwj;♀️",woman_singer:"👩&zwj;🎤",woman_student:"👩&zwj;🎓",woman_teacher:"👩&zwj;🏫",woman_technologist:"👩&zwj;💻",woman_with_turban:"👳&zwj;♀️",womans_clothes:"👚",womans_hat:"👒",women_wrestling:"🤼&zwj;♀️",womens:"🚺",world_map:"🗺",worried:"😟",wrench:"🔧",writing_hand:"✍️",x:"❌",yellow_heart:"💛",yen:"💴",yin_yang:"☯️",yum:"😋",zap:"⚡️",zipper_mouth_face:"🤐",zzz:"💤",octocat:'<img width="20" height="20" align="absmiddle" src="https://assets-cdn.github.com/images/icons/emoji/octocat.png">',showdown:'<img width="20" height="20" align="absmiddle" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAS1BMVEX///8jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS0jJS3b1q3b1q3b1q3b1q3b1q3b1q3b1q3b1q0565CIAAAAGXRSTlMAQHCAYCCw/+DQwPCQUBAwoHCAEP+wwFBgS2fvBgAAAUZJREFUeAHs1cGy7BAUheFFsEDw/k97VTq3T6ge2EmdM+pvrP6Iwd74XV9Kb52xuMU4/uc1YNgZLFOeV8FGdhGrNk5SEgUyPxAEdj4LlMRDyhVAMVEa2M7TBSeVZAFPdqHgzSZJwPKgcLFLAooHDJo4EDCw4gAtBoJA5UFj4Ng5LOGLwVXZuoIlji/jeQHFk7+baHxrCjeUwB9+s88KndvlhcyBN5BSkYNQIVVb4pV+Npm7hhuKDs/uMP5KxT3WzSNNLIuuoDpMmuAVMruMSeDyQBi24DTr43LAY7ILA1QYaWkgfHzFthYYzg67SQsCbB8GhJUEGCtO9n0rSaCLxgJQjS/JSgMTg2eBDEHAJ+H350AsjYNYscrErgI2e/l+mdR967TCX/v6N0EhPECYCP0i+IAoYQOE8BogNhQMEMdrgAQWHaMAAGi5I5euoY9NAAAAAElFTkSuQmCC">'},showdown.subParser("makehtml.blockGamut",(function(text,options,globals){"use strict";return text=globals.converter._dispatch("makehtml.blockGamut.before",text,options,globals).getText(),text=showdown.subParser("makehtml.blockQuotes")(text,options,globals),text=showdown.subParser("makehtml.headers")(text,options,globals),text=showdown.subParser("makehtml.horizontalRule")(text,options,globals),text=showdown.subParser("makehtml.lists")(text,options,globals),text=showdown.subParser("makehtml.codeBlocks")(text,options,globals),text=showdown.subParser("makehtml.tables")(text,options,globals),text=showdown.subParser("makehtml.hashHTMLBlocks")(text,options,globals),text=showdown.subParser("makehtml.paragraphs")(text,options,globals),text=globals.converter._dispatch("makehtml.blockGamut.after",text,options,globals).getText()})),showdown.subParser("makehtml.blockQuotes",(function(text,options,globals){"use strict";text=globals.converter._dispatch("makehtml.blockQuotes.before",text,options,globals).getText(),text+="\n\n";var rgx=/(^ {0,3}>[ \t]?.+\n(.+\n)*\n*)+/gm;return options.splitAdjacentBlockquotes&&(rgx=/^ {0,3}>[\s\S]*?(?:\n\n)/gm),text=text.replace(rgx,(function(bq){return bq=(bq=(bq=bq.replace(/^[ \t]*>[ \t]?/gm,"")).replace(/¨0/g,"")).replace(/^[ \t]+$/gm,""),bq=showdown.subParser("makehtml.githubCodeBlocks")(bq,options,globals),bq=(bq=(bq=showdown.subParser("makehtml.blockGamut")(bq,options,globals)).replace(/(^|\n)/g,"$1  ")).replace(/(\s*<pre>[^\r]+?<\/pre>)/gm,(function(wholeMatch,m1){var pre=m1;return pre=(pre=pre.replace(/^  /gm,"¨0")).replace(/¨0/g,"")})),showdown.subParser("makehtml.hashBlock")("<blockquote>\n"+bq+"\n</blockquote>",options,globals)})),text=globals.converter._dispatch("makehtml.blockQuotes.after",text,options,globals).getText()})),showdown.subParser("makehtml.codeBlocks",(function(text,options,globals){"use strict";text=globals.converter._dispatch("makehtml.codeBlocks.before",text,options,globals).getText();return text=(text=(text+="¨0").replace(/(?:\n\n|^)((?:(?:[ ]{4}|\t).*\n+)+)(\n*[ ]{0,3}[^ \t\n]|(?=¨0))/g,(function(wholeMatch,m1,m2){var codeblock=m1,nextChar=m2,end="\n";return codeblock=showdown.subParser("makehtml.outdent")(codeblock,options,globals),codeblock=showdown.subParser("makehtml.encodeCode")(codeblock,options,globals),codeblock=(codeblock=(codeblock=showdown.subParser("makehtml.detab")(codeblock,options,globals)).replace(/^\n+/g,"")).replace(/\n+$/g,""),options.omitExtraWLInCodeBlocks&&(end=""),codeblock="<pre><code>"+codeblock+end+"</code></pre>",showdown.subParser("makehtml.hashBlock")(codeblock,options,globals)+nextChar}))).replace(/¨0/,""),text=globals.converter._dispatch("makehtml.codeBlocks.after",text,options,globals).getText()})),showdown.subParser("makehtml.codeSpans",(function(text,options,globals){"use strict";return void 0===(text=globals.converter._dispatch("makehtml.codeSpans.before",text,options,globals).getText())&&(text=""),text=text.replace(/(^|[^\\])(`+)([^\r]*?[^`])\2(?!`)/gm,(function(wholeMatch,m1,m2,m3){var c=m3;return c=(c=c.replace(/^([ \t]*)/g,"")).replace(/[ \t]*$/g,""),c=m1+"<code>"+(c=showdown.subParser("makehtml.encodeCode")(c,options,globals))+"</code>",c=showdown.subParser("makehtml.hashHTMLSpans")(c,options,globals)})),text=globals.converter._dispatch("makehtml.codeSpans.after",text,options,globals).getText()})),showdown.subParser("makehtml.completeHTMLDocument",(function(text,options,globals){"use strict";if(!options.completeHTMLDocument)return text;text=globals.converter._dispatch("makehtml.completeHTMLDocument.before",text,options,globals).getText();var doctype="html",doctypeParsed="<!DOCTYPE HTML>\n",title="",charset='<meta charset="utf-8">\n',lang="",metadata="";for(var meta in void 0!==globals.metadata.parsed.doctype&&(doctypeParsed="<!DOCTYPE "+globals.metadata.parsed.doctype+">\n","html"!==(doctype=globals.metadata.parsed.doctype.toString().toLowerCase())&&"html5"!==doctype||(charset='<meta charset="utf-8">')),globals.metadata.parsed)if(globals.metadata.parsed.hasOwnProperty(meta))switch(meta.toLowerCase()){case"doctype":break;case"title":title="<title>"+globals.metadata.parsed.title+"</title>\n";break;case"charset":charset="html"===doctype||"html5"===doctype?'<meta charset="'+globals.metadata.parsed.charset+'">\n':'<meta name="charset" content="'+globals.metadata.parsed.charset+'">\n';break;case"language":case"lang":lang=' lang="'+globals.metadata.parsed[meta]+'"',metadata+='<meta name="'+meta+'" content="'+globals.metadata.parsed[meta]+'">\n';break;default:metadata+='<meta name="'+meta+'" content="'+globals.metadata.parsed[meta]+'">\n'}return text=doctypeParsed+"<html"+lang+">\n<head>\n"+title+charset+metadata+"</head>\n<body>\n"+text.trim()+"\n</body>\n</html>",text=globals.converter._dispatch("makehtml.completeHTMLDocument.after",text,options,globals).getText()})),showdown.subParser("makehtml.detab",(function(text,options,globals){"use strict";return text=(text=(text=(text=(text=(text=globals.converter._dispatch("makehtml.detab.before",text,options,globals).getText()).replace(/\t(?=\t)/g,"    ")).replace(/\t/g,"¨A¨B")).replace(/¨B(.+?)¨A/g,(function(wholeMatch,m1){for(var leadingText=m1,numSpaces=4-leadingText.length%4,i=0;i<numSpaces;i++)leadingText+=" ";return leadingText}))).replace(/¨A/g,"    ")).replace(/¨B/g,""),text=globals.converter._dispatch("makehtml.detab.after",text,options,globals).getText()})),showdown.subParser("makehtml.ellipsis",(function(text,options,globals){"use strict";return text=(text=globals.converter._dispatch("makehtml.ellipsis.before",text,options,globals).getText()).replace(/\.\.\./g,"…"),text=globals.converter._dispatch("makehtml.ellipsis.after",text,options,globals).getText()})),showdown.subParser("makehtml.emoji",(function(text,options,globals){"use strict";if(!options.emoji)return text;return text=(text=globals.converter._dispatch("makehtml.emoji.before",text,options,globals).getText()).replace(/:([\S]+?):/g,(function(wm,emojiCode){return showdown.helper.emojis.hasOwnProperty(emojiCode)?showdown.helper.emojis[emojiCode]:wm})),text=globals.converter._dispatch("makehtml.emoji.after",text,options,globals).getText()})),showdown.subParser("makehtml.encodeAmpsAndAngles",(function(text,options,globals){"use strict";return text=(text=(text=(text=(text=globals.converter._dispatch("makehtml.encodeAmpsAndAngles.before",text,options,globals).getText()).replace(/&(?!#?[xX]?(?:[0-9a-fA-F]+|\w+);)/g,"&amp;")).replace(/<(?![a-z\/?$!])/gi,"&lt;")).replace(/</g,"&lt;")).replace(/>/g,"&gt;"),text=globals.converter._dispatch("makehtml.encodeAmpsAndAngles.after",text,options,globals).getText()})),showdown.subParser("makehtml.encodeBackslashEscapes",(function(text,options,globals){"use strict";return text=(text=(text=globals.converter._dispatch("makehtml.encodeBackslashEscapes.before",text,options,globals).getText()).replace(/\\(\\)/g,showdown.helper.escapeCharactersCallback)).replace(/\\([`*_{}\[\]()>#+.!~=|:-])/g,showdown.helper.escapeCharactersCallback),text=globals.converter._dispatch("makehtml.encodeBackslashEscapes.after",text,options,globals).getText()})),showdown.subParser("makehtml.encodeCode",(function(text,options,globals){"use strict";return text=(text=globals.converter._dispatch("makehtml.encodeCode.before",text,options,globals).getText()).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/([*_{}\[\]\\=~-])/g,showdown.helper.escapeCharactersCallback),text=globals.converter._dispatch("makehtml.encodeCode.after",text,options,globals).getText()})),showdown.subParser("makehtml.escapeSpecialCharsWithinTagAttributes",(function(text,options,globals){"use strict";return text=(text=(text=globals.converter._dispatch("makehtml.escapeSpecialCharsWithinTagAttributes.before",text,options,globals).getText()).replace(/<\/?[a-z\d_:-]+(?:[\s]+[\s\S]+?)?>/gi,(function(wholeMatch){return wholeMatch.replace(/(.)<\/?code>(?=.)/g,"$1`").replace(/([\\`*_~=|])/g,showdown.helper.escapeCharactersCallback)}))).replace(/<!(--(?:(?:[^>-]|-[^>])(?:[^-]|-[^-])*)--)>/gi,(function(wholeMatch){return wholeMatch.replace(/([\\`*_~=|])/g,showdown.helper.escapeCharactersCallback)})),text=globals.converter._dispatch("makehtml.escapeSpecialCharsWithinTagAttributes.after",text,options,globals).getText()})),showdown.subParser("makehtml.githubCodeBlocks",(function(text,options,globals){"use strict";return options.ghCodeBlocks?(text=globals.converter._dispatch("makehtml.githubCodeBlocks.before",text,options,globals).getText(),text=(text=(text+="¨0").replace(/(?:^|\n)(?: {0,3})(```+|~~~+)(?: *)([^\s`~]*)\n([\s\S]*?)\n(?: {0,3})\1/g,(function(wholeMatch,delim,language,codeblock){var end=options.omitExtraWLInCodeBlocks?"":"\n";return codeblock=showdown.subParser("makehtml.encodeCode")(codeblock,options,globals),codeblock="<pre><code"+(language?' class="'+language+" language-"+language+'"':"")+">"+(codeblock=(codeblock=(codeblock=showdown.subParser("makehtml.detab")(codeblock,options,globals)).replace(/^\n+/g,"")).replace(/\n+$/g,""))+end+"</code></pre>",codeblock=showdown.subParser("makehtml.hashBlock")(codeblock,options,globals),"\n\n¨G"+(globals.ghCodeBlocks.push({text:wholeMatch,codeblock:codeblock})-1)+"G\n\n"}))).replace(/¨0/,""),globals.converter._dispatch("makehtml.githubCodeBlocks.after",text,options,globals).getText()):text})),showdown.subParser("makehtml.hashBlock",(function(text,options,globals){"use strict";return text=(text=globals.converter._dispatch("makehtml.hashBlock.before",text,options,globals).getText()).replace(/(^\n+|\n+$)/g,""),text="\n\n¨K"+(globals.gHtmlBlocks.push(text)-1)+"K\n\n",text=globals.converter._dispatch("makehtml.hashBlock.after",text,options,globals).getText()})),showdown.subParser("makehtml.hashCodeTags",(function(text,options,globals){"use strict";text=globals.converter._dispatch("makehtml.hashCodeTags.before",text,options,globals).getText();return text=showdown.helper.replaceRecursiveRegExp(text,(function(wholeMatch,match,left,right){var codeblock=left+showdown.subParser("makehtml.encodeCode")(match,options,globals)+right;return"¨C"+(globals.gHtmlSpans.push(codeblock)-1)+"C"}),"<code\\b[^>]*>","</code>","gim"),text=globals.converter._dispatch("makehtml.hashCodeTags.after",text,options,globals).getText()})),showdown.subParser("makehtml.hashElement",(function(text,options,globals){"use strict";return function(wholeMatch,m1){var blockText=m1;return blockText=(blockText=(blockText=blockText.replace(/\n\n/g,"\n")).replace(/^\n/,"")).replace(/\n+$/g,""),blockText="\n\n¨K"+(globals.gHtmlBlocks.push(blockText)-1)+"K\n\n"}})),showdown.subParser("makehtml.hashHTMLBlocks",(function(text,options,globals){"use strict";text=globals.converter._dispatch("makehtml.hashHTMLBlocks.before",text,options,globals).getText();var blockTags=["pre","div","h1","h2","h3","h4","h5","h6","blockquote","table","dl","ol","ul","script","noscript","form","fieldset","iframe","math","style","section","header","footer","nav","article","aside","address","audio","canvas","figure","hgroup","output","video","p"],repFunc=function(wholeMatch,match,left,right){var txt=wholeMatch;return-1!==left.search(/\bmarkdown\b/)&&(txt=left+globals.converter.makeHtml(match)+right),"\n\n¨K"+(globals.gHtmlBlocks.push(txt)-1)+"K\n\n"};options.backslashEscapesHTMLTags&&(text=text.replace(/\\<(\/?[^>]+?)>/g,(function(wm,inside){return"&lt;"+inside+"&gt;"})));for(var i=0;i<blockTags.length;++i)for(var opTagPos,rgx1=new RegExp("^ {0,3}(<"+blockTags[i]+"\\b[^>]*>)","im"),patLeft="<"+blockTags[i]+"\\b[^>]*>",patRight="</"+blockTags[i]+">";-1!==(opTagPos=showdown.helper.regexIndexOf(text,rgx1));){var subTexts=showdown.helper.splitAtIndex(text,opTagPos),newSubText1=showdown.helper.replaceRecursiveRegExp(subTexts[1],repFunc,patLeft,patRight,"im");if(newSubText1===subTexts[1])break;text=subTexts[0].concat(newSubText1)}return text=text.replace(/(\n {0,3}(<(hr)\b([^<>])*?\/?>)[ \t]*(?=\n{2,}))/g,showdown.subParser("makehtml.hashElement")(text,options,globals)),text=(text=showdown.helper.replaceRecursiveRegExp(text,(function(txt){return"\n\n¨K"+(globals.gHtmlBlocks.push(txt)-1)+"K\n\n"}),"^ {0,3}\x3c!--","--\x3e","gm")).replace(/(?:\n\n)( {0,3}(?:<([?%])[^\r]*?\2>)[ \t]*(?=\n{2,}))/g,showdown.subParser("makehtml.hashElement")(text,options,globals)),text=globals.converter._dispatch("makehtml.hashHTMLBlocks.after",text,options,globals).getText()})),showdown.subParser("makehtml.hashHTMLSpans",(function(text,options,globals){"use strict";return text=(text=(text=(text=(text=globals.converter._dispatch("makehtml.hashHTMLSpans.before",text,options,globals).getText()).replace(/<[^>]+?\/>/gi,(function(wm){return showdown.helper._hashHTMLSpan(wm,globals)}))).replace(/<([^>]+?)>[\s\S]*?<\/\1>/g,(function(wm){return showdown.helper._hashHTMLSpan(wm,globals)}))).replace(/<([^>]+?)\s[^>]+?>[\s\S]*?<\/\1>/g,(function(wm){return showdown.helper._hashHTMLSpan(wm,globals)}))).replace(/<[^>]+?>/gi,(function(wm){return showdown.helper._hashHTMLSpan(wm,globals)})),text=globals.converter._dispatch("makehtml.hashHTMLSpans.after",text,options,globals).getText()})),showdown.subParser("makehtml.unhashHTMLSpans",(function(text,options,globals){"use strict";text=globals.converter._dispatch("makehtml.unhashHTMLSpans.before",text,options,globals).getText();for(var i=0;i<globals.gHtmlSpans.length;++i){for(var repText=globals.gHtmlSpans[i],limit=0;/¨C(\d+)C/.test(repText);){var num=RegExp.$1;if(repText=repText.replace("¨C"+num+"C",globals.gHtmlSpans[num]),10===limit){console.error("maximum nesting of 10 spans reached!!!");break}++limit}text=text.replace("¨C"+i+"C",repText)}return text=globals.converter._dispatch("makehtml.unhashHTMLSpans.after",text,options,globals).getText()})),showdown.subParser("makehtml.hashPreCodeTags",(function(text,options,globals){"use strict";text=globals.converter._dispatch("makehtml.hashPreCodeTags.before",text,options,globals).getText();return text=showdown.helper.replaceRecursiveRegExp(text,(function(wholeMatch,match,left,right){var codeblock=left+showdown.subParser("makehtml.encodeCode")(match,options,globals)+right;return"\n\n¨G"+(globals.ghCodeBlocks.push({text:wholeMatch,codeblock:codeblock})-1)+"G\n\n"}),"^ {0,3}<pre\\b[^>]*>\\s*<code\\b[^>]*>","^ {0,3}</code>\\s*</pre>","gim"),text=globals.converter._dispatch("makehtml.hashPreCodeTags.after",text,options,globals).getText()})),showdown.subParser("makehtml.headers",(function(text,options,globals){"use strict";text=globals.converter._dispatch("makehtml.headers.before",text,options,globals).getText();var headerLevelStart=isNaN(parseInt(options.headerLevelStart))?1:parseInt(options.headerLevelStart),setextRegexH1=options.smoothLivePreview?/^(.+)[ \t]*\n={2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n=+[ \t]*\n+/gm,setextRegexH2=options.smoothLivePreview?/^(.+)[ \t]*\n-{2,}[ \t]*\n+/gm:/^(.+)[ \t]*\n-+[ \t]*\n+/gm;text=(text=text.replace(setextRegexH1,(function(wholeMatch,m1){var spanGamut=showdown.subParser("makehtml.spanGamut")(m1,options,globals),hID=options.noHeaderId?"":' id="'+headerId(m1)+'"',hashBlock="<h"+headerLevelStart+hID+">"+spanGamut+"</h"+headerLevelStart+">";return showdown.subParser("makehtml.hashBlock")(hashBlock,options,globals)}))).replace(setextRegexH2,(function(matchFound,m1){var spanGamut=showdown.subParser("makehtml.spanGamut")(m1,options,globals),hID=options.noHeaderId?"":' id="'+headerId(m1)+'"',hLevel=headerLevelStart+1,hashBlock="<h"+hLevel+hID+">"+spanGamut+"</h"+hLevel+">";return showdown.subParser("makehtml.hashBlock")(hashBlock,options,globals)}));var atxStyle=options.requireSpaceBeforeHeadingText?/^(#{1,6})[ \t]+(.+?)[ \t]*#*\n+/gm:/^(#{1,6})[ \t]*(.+?)[ \t]*#*\n+/gm;function headerId(m){var title,prefix;if(options.customizedHeaderId){var match=m.match(/\{([^{]+?)}\s*$/);match&&match[1]&&(m=match[1])}return title=m,prefix=showdown.helper.isString(options.prefixHeaderId)?options.prefixHeaderId:!0===options.prefixHeaderId?"section-":"",options.rawPrefixHeaderId||(title=prefix+title),title=options.ghCompatibleHeaderId?title.replace(/ /g,"-").replace(/&amp;/g,"").replace(/¨T/g,"").replace(/¨D/g,"").replace(/[&+$,\/:;=?@"#{}|^¨~\[\]`\\*)(%.!'<>]/g,"").toLowerCase():options.rawHeaderId?title.replace(/ /g,"-").replace(/&amp;/g,"&").replace(/¨T/g,"¨").replace(/¨D/g,"$").replace(/["']/g,"-").toLowerCase():title.replace(/[^\w]/g,"").toLowerCase(),options.rawPrefixHeaderId&&(title=prefix+title),globals.hashLinkCounts[title]?title=title+"-"+globals.hashLinkCounts[title]++:globals.hashLinkCounts[title]=1,title}return text=text.replace(atxStyle,(function(wholeMatch,m1,m2){var hText=m2;options.customizedHeaderId&&(hText=m2.replace(/\s?\{([^{]+?)}\s*$/,""));var span=showdown.subParser("makehtml.spanGamut")(hText,options,globals),hID=options.noHeaderId?"":' id="'+headerId(m2)+'"',hLevel=headerLevelStart-1+m1.length,header="<h"+hLevel+hID+">"+span+"</h"+hLevel+">";return showdown.subParser("makehtml.hashBlock")(header,options,globals)})),text=globals.converter._dispatch("makehtml.headers.after",text,options,globals).getText()})),showdown.subParser("makehtml.horizontalRule",(function(text,options,globals){"use strict";text=globals.converter._dispatch("makehtml.horizontalRule.before",text,options,globals).getText();var key=showdown.subParser("makehtml.hashBlock")("<hr />",options,globals);return text=(text=(text=text.replace(/^ {0,2}( ?-){3,}[ \t]*$/gm,key)).replace(/^ {0,2}( ?\*){3,}[ \t]*$/gm,key)).replace(/^ {0,2}( ?_){3,}[ \t]*$/gm,key),text=globals.converter._dispatch("makehtml.horizontalRule.after",text,options,globals).getText()})),showdown.subParser("makehtml.images",(function(text,options,globals){"use strict";function writeImageTag(wholeMatch,altText,linkId,url,width,height,m5,title){var gUrls=globals.gUrls,gTitles=globals.gTitles,gDims=globals.gDimensions;if(linkId=linkId.toLowerCase(),title||(title=""),wholeMatch.search(/\(<?\s*>? ?(['"].*['"])?\)$/m)>-1)url="";else if(""===url||null===url){if(""!==linkId&&null!==linkId||(linkId=altText.toLowerCase().replace(/ ?\n/g," ")),url="#"+linkId,showdown.helper.isUndefined(gUrls[linkId]))return wholeMatch;url=gUrls[linkId],showdown.helper.isUndefined(gTitles[linkId])||(title=gTitles[linkId]),showdown.helper.isUndefined(gDims[linkId])||(width=gDims[linkId].width,height=gDims[linkId].height)}altText=altText.replace(/"/g,"&quot;").replace(showdown.helper.regexes.asteriskDashTildeAndColon,showdown.helper.escapeCharactersCallback);var result='<img src="'+(url=url.replace(showdown.helper.regexes.asteriskDashTildeAndColon,showdown.helper.escapeCharactersCallback))+'" alt="'+altText+'"';return title&&showdown.helper.isString(title)&&(result+=' title="'+(title=title.replace(/"/g,"&quot;").replace(showdown.helper.regexes.asteriskDashTildeAndColon,showdown.helper.escapeCharactersCallback))+'"'),width&&height&&(result+=' width="'+(width="*"===width?"auto":width)+'"',result+=' height="'+(height="*"===height?"auto":height)+'"'),result+=" />"}return text=(text=(text=(text=(text=(text=globals.converter._dispatch("makehtml.images.before",text,options,globals).getText()).replace(/!\[([^\]]*?)] ?(?:\n *)?\[([\s\S]*?)]()()()()()/g,writeImageTag)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,(function(wholeMatch,altText,linkId,url,width,height,m5,title){return writeImageTag(wholeMatch,altText,linkId,url=url.replace(/\s/g,""),width,height,m5,title)}))).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<([^>]*)>(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(?:(["'])([^"]*?)\6))?[ \t]?\)/g,writeImageTag)).replace(/!\[([^\]]*?)][ \t]*()\([ \t]?<?([\S]+?(?:\([\S]*?\)[\S]*?)?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*(?:(["'])([^"]*?)\6)?[ \t]?\)/g,writeImageTag)).replace(/!\[([^\[\]]+)]()()()()()/g,writeImageTag),text=globals.converter._dispatch("makehtml.images.after",text,options,globals).getText()})),showdown.subParser("makehtml.italicsAndBold",(function(text,options,globals){"use strict";function parseInside(txt,left,right){return left+txt+right}return text=globals.converter._dispatch("makehtml.italicsAndBold.before",text,options,globals).getText(),text=(text=(text=(text=options.literalMidWordUnderscores?(text=(text=text.replace(/\b___(\S[\s\S]*?)___\b/g,(function(wm,txt){return parseInside(txt,"<strong><em>","</em></strong>")}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(wm,txt){return parseInside(txt,"<strong>","</strong>")}))).replace(/\b_(\S[\s\S]*?)_\b/g,(function(wm,txt){return parseInside(txt,"<em>","</em>")})):(text=(text=text.replace(/___(\S[\s\S]*?)___/g,(function(wm,m){return/\S$/.test(m)?parseInside(m,"<strong><em>","</em></strong>"):wm}))).replace(/__(\S[\s\S]*?)__/g,(function(wm,m){return/\S$/.test(m)?parseInside(m,"<strong>","</strong>"):wm}))).replace(/_([^\s_][\s\S]*?)_/g,(function(wm,m){return/\S$/.test(m)?parseInside(m,"<em>","</em>"):wm}))).replace(/\*\*\*(\S[\s\S]*?)\*\*\*/g,(function(wm,m){return/\S$/.test(m)?parseInside(m,"<strong><em>","</em></strong>"):wm}))).replace(/\*\*(\S[\s\S]*?)\*\*/g,(function(wm,m){return/\S$/.test(m)?parseInside(m,"<strong>","</strong>"):wm}))).replace(/\*([^\s*][\s\S]*?)\*/g,(function(wm,m){return/\S$/.test(m)?parseInside(m,"<em>","</em>"):wm})),text=globals.converter._dispatch("makehtml.italicsAndBold.after",text,options,globals).getText()})),function(){function replaceAnchorTag(rgx,evtRootName,options,globals,emptyCase){return emptyCase=!!emptyCase,function(wholeMatch,text,id,url,m5,m6,title){return/\n\n/.test(wholeMatch)?wholeMatch:writeAnchorTag(createEvent(rgx,evtRootName+".captureStart",wholeMatch,text,id,url,title,options,globals),options,globals,emptyCase)}}function createEvent(rgx,evtName,wholeMatch,text,id,url,title,options,globals){return globals.converter._dispatch(evtName,wholeMatch,options,globals,{regexp:rgx,matches:{wholeMatch:wholeMatch,text:text,id:id,url:url,title:title}})}function writeAnchorTag(evt,options,globals,emptyCase){var wholeMatch=evt.getMatches().wholeMatch,text=evt.getMatches().text,id=evt.getMatches().id,url=evt.getMatches().url,title=evt.getMatches().title,target="";if(title||(title=""),id=id?id.toLowerCase():"",emptyCase)url="";else if(!url){if(id||(id=text.toLowerCase().replace(/ ?\n/g," ")),url="#"+id,showdown.helper.isUndefined(globals.gUrls[id]))return wholeMatch;url=globals.gUrls[id],showdown.helper.isUndefined(globals.gTitles[id])||(title=globals.gTitles[id])}url=url.replace(showdown.helper.regexes.asteriskDashTildeAndColon,showdown.helper.escapeCharactersCallback),""!==title&&null!==title&&(title=' title="'+(title=(title=title.replace(/"/g,"&quot;")).replace(showdown.helper.regexes.asteriskDashTildeAndColon,showdown.helper.escapeCharactersCallback))+'"'),options.openLinksInNewWindow&&!/^#/.test(url)&&(target=' target="¨E95Eblank"'),text=showdown.subParser("makehtml.codeSpans")(text,options,globals),text=showdown.subParser("makehtml.emoji")(text,options,globals),text=showdown.subParser("makehtml.underline")(text,options,globals),text=showdown.subParser("makehtml.italicsAndBold")(text,options,globals),text=showdown.subParser("makehtml.strikethrough")(text,options,globals),text=showdown.subParser("makehtml.ellipsis")(text,options,globals);var result='<a href="'+url+'"'+title+target+">"+(text=showdown.subParser("makehtml.hashHTMLSpans")(text,options,globals))+"</a>";return result=showdown.subParser("makehtml.hashHTMLSpans")(result,options,globals)}showdown.subParser("makehtml.links",(function(text,options,globals){return text=globals.converter._dispatch("makehtml.links.start",text,options,globals).getText(),text=showdown.subParser("makehtml.links.reference")(text,options,globals),text=showdown.subParser("makehtml.links.inline")(text,options,globals),text=showdown.subParser("makehtml.links.referenceShortcut")(text,options,globals),text=showdown.subParser("makehtml.links.angleBrackets")(text,options,globals),text=(text=(text=showdown.subParser("makehtml.links.ghMentions")(text,options,globals)).replace(/<a\s[^>]*>[\s\S]*<\/a>/g,(function(wholeMatch){return showdown.helper._hashHTMLSpan(wholeMatch,globals)}))).replace(/<img\s[^>]*\/?>/g,(function(wholeMatch){return showdown.helper._hashHTMLSpan(wholeMatch,globals)})),text=showdown.subParser("makehtml.links.naked")(text,options,globals),text=globals.converter._dispatch("makehtml.links.end",text,options,globals).getText()})),showdown.subParser("makehtml.links.inline",(function(text,options,globals){var evtRootName=evtRootName+".inline",rgxEmpty=/\[(.*?)]()()()()\(<? ?>? ?(?:["'](.*)["'])?\)/g,rgxCrazy=/\[((?:\[[^\]]*]|[^\[\]])*)]()\s?\([ \t]?<([^>]*)>(?:[ \t]*((["'])([^"]*?)\5))?[ \t]?\)/g,rgx2=/\[([\S ]*?)]\s?()\( *<?([^\s'"]*?(?:\([\S]*?\)[\S]*?)?)>?\s*(?:()(['"])(.*?)\5)? *\)/g,rgx3=/\[([\S ]*?)]\s?()\( *<?([^\s'"]*?(?:\([\S]*?\)[\S]*?)?)>?\s+()()\((.*?)\) *\)/g;return text=(text=(text=(text=(text=globals.converter._dispatch(evtRootName+".start",text,options,globals).getText()).replace(rgxEmpty,replaceAnchorTag(rgxEmpty,evtRootName,options,globals,!0))).replace(rgxCrazy,replaceAnchorTag(rgxCrazy,evtRootName,options,globals))).replace(rgx2,replaceAnchorTag(rgx2,evtRootName,options,globals))).replace(rgx3,replaceAnchorTag(rgx3,evtRootName,options,globals)),text=globals.converter._dispatch(evtRootName+".end",text,options,globals).getText()})),showdown.subParser("makehtml.links.reference",(function(text,options,globals){var evtRootName=evtRootName+".reference",rgx=/\[((?:\[[^\]]*]|[^\[\]])*)] ?(?:\n *)?\[(.*?)]()()()()/g;return text=(text=globals.converter._dispatch(evtRootName+".start",text,options,globals).getText()).replace(rgx,replaceAnchorTag(rgx,evtRootName,options,globals)),text=globals.converter._dispatch(evtRootName+".end",text,options,globals).getText()})),showdown.subParser("makehtml.links.referenceShortcut",(function(text,options,globals){var evtRootName=evtRootName+".referenceShortcut",rgx=/\[([^\[\]]+)]()()()()()/g;return text=(text=globals.converter._dispatch(evtRootName+".start",text,options,globals).getText()).replace(rgx,replaceAnchorTag(rgx,evtRootName,options,globals)),text=globals.converter._dispatch(evtRootName+".end",text,options,globals).getText()})),showdown.subParser("makehtml.links.ghMentions",(function(text,options,globals){var evtRootName=evtRootName+"ghMentions";if(!options.ghMentions)return text;text=globals.converter._dispatch(evtRootName+".start",text,options,globals).getText();var rgx=/(^|\s)(\\)?(@([a-z\d]+(?:[a-z\d._-]+?[a-z\d]+)*))/gi;return text=text.replace(rgx,(function(wholeMatch,st,escape,mentions,username){if("\\"===escape)return st+mentions;if(!showdown.helper.isString(options.ghMentionsLink))throw new Error("ghMentionsLink option must be a string");var url=options.ghMentionsLink.replace(/{u}/g,username);return st+writeAnchorTag(createEvent(rgx,evtRootName+".captureStart",wholeMatch,mentions,null,url,null,options,globals),options,globals)})),text=globals.converter._dispatch(evtRootName+".end",text,options,globals).getText()})),showdown.subParser("makehtml.links.angleBrackets",(function(text,options,globals){var evtRootName="makehtml.links.angleBrackets";text=globals.converter._dispatch(evtRootName+".start",text,options,globals).getText();var urlRgx=/<(((?:https?|ftp):\/\/|www\.)[^'">\s]+)>/gi;text=text.replace(urlRgx,(function(wholeMatch,url,urlStart){return writeAnchorTag(createEvent(urlRgx,evtRootName+".captureStart",wholeMatch,url,null,url="www."===urlStart?"http://"+url:url,null,options,globals),options,globals)}));var mailRgx=/<(?:mailto:)?([-.\w]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)>/gi;return text=text.replace(mailRgx,(function(wholeMatch,mail){var url="mailto:";return mail=showdown.subParser("makehtml.unescapeSpecialChars")(mail,options,globals),options.encodeEmails?(url=showdown.helper.encodeEmailAddress(url+mail),mail=showdown.helper.encodeEmailAddress(mail)):url+=mail,writeAnchorTag(createEvent(mailRgx,evtRootName+".captureStart",wholeMatch,mail,null,url,null,options,globals),options,globals)})),text=globals.converter._dispatch(evtRootName+".end",text,options,globals).getText()})),showdown.subParser("makehtml.links.naked",(function(text,options,globals){if(!options.simplifiedAutoLink)return text;var evtRootName="makehtml.links.naked";text=globals.converter._dispatch(evtRootName+".start",text,options,globals).getText();var urlRgx=/([_*~]*?)(((?:https?|ftp):\/\/|www\.)[^\s<>"'`´.-][^\s<>"'`´]*?\.[a-z\d.]+[^\s<>"']*)\1/gi;text=text.replace(urlRgx,(function(wholeMatch,leadingMDChars,url,urlPrefix){for(var suffix="",i=url.length-1;i>=0;--i){var char=url.charAt(i);if(/[_*~,;:.!?]/.test(char))url=url.slice(0,-1),suffix=char+suffix;else if(/\)/.test(char)){var opPar=url.match(/\(/g)||[],clPar=url.match(/\)/g);if(!(opPar.length<clPar.length))break;url=url.slice(0,-1),suffix=char+suffix}else{if(!/]/.test(char))break;var opPar2=url.match(/\[/g)||[],clPar2=url.match(/\]/g);if(!(opPar2.length<clPar2.length))break;url=url.slice(0,-1),suffix=char+suffix}}var text=url;return url="www."===urlPrefix?"http://"+url:url,text=text.replace(showdown.helper.regexes.asteriskDashTildeAndColon,showdown.helper.escapeCharactersCallback),leadingMDChars+writeAnchorTag(createEvent(urlRgx,evtRootName+".captureStart",wholeMatch,text,null,url,null,options,globals),options,globals)+suffix+leadingMDChars}));var mailRgx=/(^|\s)(?:mailto:)?([A-Za-z0-9!#$%&'*+-/=?^_`{|}~.]+@[-a-z0-9]+(\.[-a-z0-9]+)*\.[a-z]+)(?=$|\s)/gim;return text=text.replace(mailRgx,(function(wholeMatch,leadingChar,mail){var url="mailto:";return mail=showdown.subParser("makehtml.unescapeSpecialChars")(mail,options,globals),options.encodeEmails?(url=showdown.helper.encodeEmailAddress(url+mail),mail=showdown.helper.encodeEmailAddress(mail)):url+=mail,leadingChar+writeAnchorTag(createEvent(mailRgx,evtRootName+".captureStart",wholeMatch,mail,null,url,null,options,globals),options,globals)})),text=globals.converter._dispatch(evtRootName+".end",text,options,globals).getText()}))}(),showdown.subParser("makehtml.lists",(function(text,options,globals){"use strict";function processListItems(listStr,trimTrailing){globals.gListLevel++,listStr=listStr.replace(/\n{2,}$/,"\n");var rgx=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0| {0,3}([*+-]|\d+[.])[ \t]+))/gm,isParagraphed=/\n[ \t]*\n(?!¨0)/.test(listStr+="¨0");return options.disableForced4SpacesIndentedSublists&&(rgx=/(\n)?(^ {0,3})([*+-]|\d+[.])[ \t]+((\[(x|X| )?])?[ \t]*[^\r]+?(\n{1,2}))(?=\n*(¨0|\2([*+-]|\d+[.])[ \t]+))/gm),listStr=(listStr=listStr.replace(rgx,(function(wholeMatch,m1,m2,m3,m4,taskbtn,checked){checked=checked&&""!==checked.trim();var item=showdown.subParser("makehtml.outdent")(m4,options,globals),bulletStyle="";return taskbtn&&options.tasklists&&(bulletStyle=' class="task-list-item" style="list-style-type: none;"',item=item.replace(/^[ \t]*\[(x|X| )?]/m,(function(){var otp='<input type="checkbox" disabled style="margin: 0px 0.35em 0.25em -1.6em; vertical-align: middle;"';return checked&&(otp+=" checked"),otp+=">"}))),item=item.replace(/^([-*+]|\d\.)[ \t]+[\S\n ]*/g,(function(wm2){return"¨A"+wm2})),/^#+.+\n.+/.test(item)&&(item=item.replace(/^(#+.+)$/m,"$1\n")),m1||item.search(/\n{2,}/)>-1?(item=showdown.subParser("makehtml.githubCodeBlocks")(item,options,globals),item=showdown.subParser("makehtml.blockGamut")(item,options,globals)):(item=(item=showdown.subParser("makehtml.lists")(item,options,globals)).replace(/\n$/,""),item=(item=showdown.subParser("makehtml.hashHTMLBlocks")(item,options,globals)).replace(/\n\n+/g,"\n\n"),item=isParagraphed?showdown.subParser("makehtml.paragraphs")(item,options,globals):showdown.subParser("makehtml.spanGamut")(item,options,globals)),item="<li"+bulletStyle+">"+(item=item.replace("¨A",""))+"</li>\n"}))).replace(/¨0/g,""),globals.gListLevel--,trimTrailing&&(listStr=listStr.replace(/\s+$/,"")),listStr}function styleStartNumber(list,listType){if("ol"===listType){var res=list.match(/^ *(\d+)\./);if(res&&"1"!==res[1])return' start="'+res[1]+'"'}return""}function parseConsecutiveLists(list,listType,trimTrailing){var olRgx=options.disableForced4SpacesIndentedSublists?/^ ?\d+\.[ \t]/gm:/^ {0,3}\d+\.[ \t]/gm,ulRgx=options.disableForced4SpacesIndentedSublists?/^ ?[*+-][ \t]/gm:/^ {0,3}[*+-][ \t]/gm,counterRxg="ul"===listType?olRgx:ulRgx,result="";if(-1!==list.search(counterRxg))!function parseCL(txt){var pos=txt.search(counterRxg),style=styleStartNumber(list,listType);-1!==pos?(result+="\n\n<"+listType+style+">\n"+processListItems(txt.slice(0,pos),!!trimTrailing)+"</"+listType+">\n",counterRxg="ul"===(listType="ul"===listType?"ol":"ul")?olRgx:ulRgx,parseCL(txt.slice(pos))):result+="\n\n<"+listType+style+">\n"+processListItems(txt,!!trimTrailing)+"</"+listType+">\n"}(list);else{var style=styleStartNumber(list,listType);result="\n\n<"+listType+style+">\n"+processListItems(list,!!trimTrailing)+"</"+listType+">\n"}return result}return text=globals.converter._dispatch("lists.before",text,options,globals).getText(),text+="¨0",text=(text=globals.gListLevel?text.replace(/^(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(wholeMatch,list,m2){return parseConsecutiveLists(list,m2.search(/[*+-]/g)>-1?"ul":"ol",!0)})):text.replace(/(\n\n|^\n?)(( {0,3}([*+-]|\d+[.])[ \t]+)[^\r]+?(¨0|\n{2,}(?=\S)(?![ \t]*(?:[*+-]|\d+[.])[ \t]+)))/gm,(function(wholeMatch,m1,list,m3){return parseConsecutiveLists(list,m3.search(/[*+-]/g)>-1?"ul":"ol",!1)}))).replace(/¨0/,""),text=globals.converter._dispatch("makehtml.lists.after",text,options,globals).getText()})),showdown.subParser("makehtml.metadata",(function(text,options,globals){"use strict";if(!options.metadata)return text;function parseMetadataContents(content){globals.metadata.raw=content,(content=(content=content.replace(/&/g,"&amp;").replace(/"/g,"&quot;")).replace(/\n {4}/g," ")).replace(/^([\S ]+): +([\s\S]+?)$/gm,(function(wm,key,value){return globals.metadata.parsed[key]=value,""}))}return text=(text=(text=(text=globals.converter._dispatch("makehtml.metadata.before",text,options,globals).getText()).replace(/^\s*«««+(\S*?)\n([\s\S]+?)\n»»»+\n/,(function(wholematch,format,content){return parseMetadataContents(content),"¨M"}))).replace(/^\s*---+(\S*?)\n([\s\S]+?)\n---+\n/,(function(wholematch,format,content){return format&&(globals.metadata.format=format),parseMetadataContents(content),"¨M"}))).replace(/¨M/g,""),text=globals.converter._dispatch("makehtml.metadata.after",text,options,globals).getText()})),showdown.subParser("makehtml.outdent",(function(text,options,globals){"use strict";return text=(text=(text=globals.converter._dispatch("makehtml.outdent.before",text,options,globals).getText()).replace(/^(\t|[ ]{1,4})/gm,"¨0")).replace(/¨0/g,""),text=globals.converter._dispatch("makehtml.outdent.after",text,options,globals).getText()})),showdown.subParser("makehtml.paragraphs",(function(text,options,globals){"use strict";for(var grafs=(text=(text=(text=globals.converter._dispatch("makehtml.paragraphs.before",text,options,globals).getText()).replace(/^\n+/g,"")).replace(/\n+$/g,"")).split(/\n{2,}/g),grafsOut=[],end=grafs.length,i=0;i<end;i++){var str=grafs[i];str.search(/¨(K|G)(\d+)\1/g)>=0?grafsOut.push(str):str.search(/\S/)>=0&&(str=(str=showdown.subParser("makehtml.spanGamut")(str,options,globals)).replace(/^([ \t]*)/g,"<p>"),str+="</p>",grafsOut.push(str))}for(end=grafsOut.length,i=0;i<end;i++){for(var blockText="",grafsOutIt=grafsOut[i],codeFlag=!1;/¨(K|G)(\d+)\1/.test(grafsOutIt);){var delim=RegExp.$1,num=RegExp.$2;blockText=(blockText="K"===delim?globals.gHtmlBlocks[num]:codeFlag?showdown.subParser("makehtml.encodeCode")(globals.ghCodeBlocks[num].text,options,globals):globals.ghCodeBlocks[num].codeblock).replace(/\$/g,"$$$$"),grafsOutIt=grafsOutIt.replace(/(\n\n)?¨(K|G)\d+\2(\n\n)?/,blockText),/^<pre\b[^>]*>\s*<code\b[^>]*>/.test(grafsOutIt)&&(codeFlag=!0)}grafsOut[i]=grafsOutIt}return text=(text=(text=grafsOut.join("\n")).replace(/^\n+/g,"")).replace(/\n+$/g,""),globals.converter._dispatch("makehtml.paragraphs.after",text,options,globals).getText()})),showdown.subParser("makehtml.runExtension",(function(ext,text,options,globals){"use strict";if(ext.filter)text=ext.filter(text,globals.converter,options);else if(ext.regex){var re=ext.regex;re instanceof RegExp||(re=new RegExp(re,"g")),text=text.replace(re,ext.replace)}return text})),showdown.subParser("makehtml.spanGamut",(function(text,options,globals){"use strict";return text=globals.converter._dispatch("makehtml.span.before",text,options,globals).getText(),text=showdown.subParser("makehtml.codeSpans")(text,options,globals),text=showdown.subParser("makehtml.escapeSpecialCharsWithinTagAttributes")(text,options,globals),text=showdown.subParser("makehtml.encodeBackslashEscapes")(text,options,globals),text=showdown.subParser("makehtml.images")(text,options,globals),text=globals.converter._dispatch("smakehtml.links.before",text,options,globals).getText(),text=showdown.subParser("makehtml.links")(text,options,globals),text=globals.converter._dispatch("smakehtml.links.after",text,options,globals).getText(),text=showdown.subParser("makehtml.emoji")(text,options,globals),text=showdown.subParser("makehtml.underline")(text,options,globals),text=showdown.subParser("makehtml.italicsAndBold")(text,options,globals),text=showdown.subParser("makehtml.strikethrough")(text,options,globals),text=showdown.subParser("makehtml.ellipsis")(text,options,globals),text=showdown.subParser("makehtml.hashHTMLSpans")(text,options,globals),text=showdown.subParser("makehtml.encodeAmpsAndAngles")(text,options,globals),options.simpleLineBreaks?/\n\n¨K/.test(text)||(text=text.replace(/\n+/g,"<br />\n")):text=text.replace(/  +\n/g,"<br />\n"),text=globals.converter._dispatch("makehtml.spanGamut.after",text,options,globals).getText()})),showdown.subParser("makehtml.strikethrough",(function(text,options,globals){"use strict";return options.strikethrough&&(text=(text=globals.converter._dispatch("makehtml.strikethrough.before",text,options,globals).getText()).replace(/(?:~){2}([\s\S]+?)(?:~){2}/g,(function(wm,txt){return"<del>"+txt+"</del>"})),text=globals.converter._dispatch("makehtml.strikethrough.after",text,options,globals).getText()),text})),showdown.subParser("makehtml.stripLinkDefinitions",(function(text,options,globals){"use strict";var replaceFunc=function(wholeMatch,linkId,url,width,height,blankLines,title){return linkId=linkId.toLowerCase(),url.match(/^data:.+?\/.+?;base64,/)?globals.gUrls[linkId]=url.replace(/\s/g,""):globals.gUrls[linkId]=showdown.subParser("makehtml.encodeAmpsAndAngles")(url,options,globals),blankLines?blankLines+title:(title&&(globals.gTitles[linkId]=title.replace(/"|'/g,"&quot;")),options.parseImgDimensions&&width&&height&&(globals.gDimensions[linkId]={width:width,height:height}),"")};return text=(text=(text=(text+="¨0").replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?(data:.+?\/.+?;base64,[A-Za-z0-9+/=\n]+?)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n\n|(?=¨0)|(?=\n\[))/gm,replaceFunc)).replace(/^ {0,3}\[(.+)]:[ \t]*\n?[ \t]*<?([^>\s]+)>?(?: =([*\d]+[A-Za-z%]{0,4})x([*\d]+[A-Za-z%]{0,4}))?[ \t]*\n?[ \t]*(?:(\n*)["|'(](.+?)["|')][ \t]*)?(?:\n+|(?=¨0))/gm,replaceFunc)).replace(/¨0/,"")})),showdown.subParser("makehtml.tables",(function(text,options,globals){"use strict";if(!options.tables)return text;function parseCells(cell,style){return"<td"+style+">"+showdown.subParser("makehtml.spanGamut")(cell,options,globals)+"</td>\n"}function parseTable(rawTable){var i,tableLines=rawTable.split("\n");for(i=0;i<tableLines.length;++i)/^ {0,3}\|/.test(tableLines[i])&&(tableLines[i]=tableLines[i].replace(/^ {0,3}\|/,"")),/\|[ \t]*$/.test(tableLines[i])&&(tableLines[i]=tableLines[i].replace(/\|[ \t]*$/,"")),tableLines[i]=showdown.subParser("makehtml.codeSpans")(tableLines[i],options,globals);var sLine,header,style,id,rawHeaders=tableLines[0].split("|").map((function(s){return s.trim()})),rawStyles=tableLines[1].split("|").map((function(s){return s.trim()})),rawCells=[],headers=[],styles=[],cells=[];for(tableLines.shift(),tableLines.shift(),i=0;i<tableLines.length;++i)""!==tableLines[i].trim()&&rawCells.push(tableLines[i].split("|").map((function(s){return s.trim()})));if(rawHeaders.length<rawStyles.length)return rawTable;for(i=0;i<rawStyles.length;++i)styles.push((sLine=rawStyles[i],/^:[ \t]*--*$/.test(sLine)?' style="text-align:left;"':/^--*[ \t]*:[ \t]*$/.test(sLine)?' style="text-align:right;"':/^:[ \t]*--*[ \t]*:$/.test(sLine)?' style="text-align:center;"':""));for(i=0;i<rawHeaders.length;++i)showdown.helper.isUndefined(styles[i])&&(styles[i]=""),headers.push((header=rawHeaders[i],style=styles[i],id=void 0,id="",header=header.trim(),(options.tablesHeaderId||options.tableHeaderId)&&(id=' id="'+header.replace(/ /g,"_").toLowerCase()+'"'),"<th"+id+style+">"+(header=showdown.subParser("makehtml.spanGamut")(header,options,globals))+"</th>\n"));for(i=0;i<rawCells.length;++i){for(var row=[],ii=0;ii<headers.length;++ii)showdown.helper.isUndefined(rawCells[i][ii]),row.push(parseCells(rawCells[i][ii],styles[ii]));cells.push(row)}return function(headers,cells){for(var tb="<table>\n<thead>\n<tr>\n",tblLgn=headers.length,i=0;i<tblLgn;++i)tb+=headers[i];for(tb+="</tr>\n</thead>\n<tbody>\n",i=0;i<cells.length;++i){tb+="<tr>\n";for(var ii=0;ii<tblLgn;++ii)tb+=cells[i][ii];tb+="</tr>\n"}return tb+="</tbody>\n</table>\n"}(headers,cells)}return text=(text=(text=(text=globals.converter._dispatch("makehtml.tables.before",text,options,globals).getText()).replace(/\\(\|)/g,showdown.helper.escapeCharactersCallback)).replace(/^ {0,3}\|?.+\|.+\n {0,3}\|?[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*:?[ \t]*(?:[-=]){2,}[\s\S]+?(?:\n\n|¨0)/gm,parseTable)).replace(/^ {0,3}\|.+\|[ \t]*\n {0,3}\|[ \t]*:?[ \t]*(?:[-=]){2,}[ \t]*:?[ \t]*\|[ \t]*\n( {0,3}\|.+\|[ \t]*\n)*(?:\n|¨0)/gm,parseTable),text=globals.converter._dispatch("makehtml.tables.after",text,options,globals).getText()})),showdown.subParser("makehtml.underline",(function(text,options,globals){"use strict";return options.underline?(text=globals.converter._dispatch("makehtml.underline.before",text,options,globals).getText(),text=(text=options.literalMidWordUnderscores?(text=text.replace(/\b___(\S[\s\S]*?)___\b/g,(function(wm,txt){return"<u>"+txt+"</u>"}))).replace(/\b__(\S[\s\S]*?)__\b/g,(function(wm,txt){return"<u>"+txt+"</u>"})):(text=text.replace(/___(\S[\s\S]*?)___/g,(function(wm,m){return/\S$/.test(m)?"<u>"+m+"</u>":wm}))).replace(/__(\S[\s\S]*?)__/g,(function(wm,m){return/\S$/.test(m)?"<u>"+m+"</u>":wm}))).replace(/(_)/g,showdown.helper.escapeCharactersCallback),text=globals.converter._dispatch("makehtml.underline.after",text,options,globals).getText()):text})),showdown.subParser("makehtml.unescapeSpecialChars",(function(text,options,globals){"use strict";return text=(text=globals.converter._dispatch("makehtml.unescapeSpecialChars.before",text,options,globals).getText()).replace(/¨E(\d+)E/g,(function(wholeMatch,m1){var charCodeToReplace=parseInt(m1);return String.fromCharCode(charCodeToReplace)})),text=globals.converter._dispatch("makehtml.unescapeSpecialChars.after",text,options,globals).getText()})),showdown.subParser("makeMarkdown.blockquote",(function(node,globals){"use strict";var txt="";if(node.hasChildNodes())for(var children=node.childNodes,childrenLength=children.length,i=0;i<childrenLength;++i){var innerTxt=showdown.subParser("makeMarkdown.node")(children[i],globals);""!==innerTxt&&(txt+=innerTxt)}return txt="> "+(txt=txt.trim()).split("\n").join("\n> ")})),showdown.subParser("makeMarkdown.codeBlock",(function(node,globals){"use strict";var lang=node.getAttribute("language"),num=node.getAttribute("precodenum");return"```"+lang+"\n"+globals.preList[num]+"\n```"})),showdown.subParser("makeMarkdown.codeSpan",(function(node){"use strict";return"`"+node.innerHTML+"`"})),showdown.subParser("makeMarkdown.emphasis",(function(node,globals){"use strict";var txt="";if(node.hasChildNodes()){txt+="*";for(var children=node.childNodes,childrenLength=children.length,i=0;i<childrenLength;++i)txt+=showdown.subParser("makeMarkdown.node")(children[i],globals);txt+="*"}return txt})),showdown.subParser("makeMarkdown.header",(function(node,globals,headerLevel){"use strict";var headerMark=new Array(headerLevel+1).join("#"),txt="";if(node.hasChildNodes()){txt=headerMark+" ";for(var children=node.childNodes,childrenLength=children.length,i=0;i<childrenLength;++i)txt+=showdown.subParser("makeMarkdown.node")(children[i],globals)}return txt})),showdown.subParser("makeMarkdown.hr",(function(){"use strict";return"---"})),showdown.subParser("makeMarkdown.image",(function(node){"use strict";var txt="";return node.hasAttribute("src")&&(txt+="!["+node.getAttribute("alt")+"](",txt+="<"+node.getAttribute("src")+">",node.hasAttribute("width")&&node.hasAttribute("height")&&(txt+=" ="+node.getAttribute("width")+"x"+node.getAttribute("height")),node.hasAttribute("title")&&(txt+=' "'+node.getAttribute("title")+'"'),txt+=")"),txt})),showdown.subParser("makeMarkdown.links",(function(node,globals){"use strict";var txt="";if(node.hasChildNodes()&&node.hasAttribute("href")){var children=node.childNodes,childrenLength=children.length;txt="[";for(var i=0;i<childrenLength;++i)txt+=showdown.subParser("makeMarkdown.node")(children[i],globals);txt+="](",txt+="<"+node.getAttribute("href")+">",node.hasAttribute("title")&&(txt+=' "'+node.getAttribute("title")+'"'),txt+=")"}return txt})),showdown.subParser("makeMarkdown.list",(function(node,globals,type){"use strict";var txt="";if(!node.hasChildNodes())return"";for(var listItems=node.childNodes,listItemsLenght=listItems.length,listNum=node.getAttribute("start")||1,i=0;i<listItemsLenght;++i)if(void 0!==listItems[i].tagName&&"li"===listItems[i].tagName.toLowerCase()){txt+=("ol"===type?listNum.toString()+". ":"- ")+showdown.subParser("makeMarkdown.listItem")(listItems[i],globals),++listNum}return txt.trim()})),showdown.subParser("makeMarkdown.listItem",(function(node,globals){"use strict";for(var listItemTxt="",children=node.childNodes,childrenLenght=children.length,i=0;i<childrenLenght;++i)listItemTxt+=showdown.subParser("makeMarkdown.node")(children[i],globals);return/\n$/.test(listItemTxt)?listItemTxt=listItemTxt.split("\n").join("\n    ").replace(/^ {4}$/gm,"").replace(/\n\n+/g,"\n\n"):listItemTxt+="\n",listItemTxt})),showdown.subParser("makeMarkdown.node",(function(node,globals,spansOnly){"use strict";spansOnly=spansOnly||!1;var txt="";if(3===node.nodeType)return showdown.subParser("makeMarkdown.txt")(node,globals);if(8===node.nodeType)return"\x3c!--"+node.data+"--\x3e\n\n";if(1!==node.nodeType)return"";switch(node.tagName.toLowerCase()){case"h1":spansOnly||(txt=showdown.subParser("makeMarkdown.header")(node,globals,1)+"\n\n");break;case"h2":spansOnly||(txt=showdown.subParser("makeMarkdown.header")(node,globals,2)+"\n\n");break;case"h3":spansOnly||(txt=showdown.subParser("makeMarkdown.header")(node,globals,3)+"\n\n");break;case"h4":spansOnly||(txt=showdown.subParser("makeMarkdown.header")(node,globals,4)+"\n\n");break;case"h5":spansOnly||(txt=showdown.subParser("makeMarkdown.header")(node,globals,5)+"\n\n");break;case"h6":spansOnly||(txt=showdown.subParser("makeMarkdown.header")(node,globals,6)+"\n\n");break;case"p":spansOnly||(txt=showdown.subParser("makeMarkdown.paragraph")(node,globals)+"\n\n");break;case"blockquote":spansOnly||(txt=showdown.subParser("makeMarkdown.blockquote")(node,globals)+"\n\n");break;case"hr":spansOnly||(txt=showdown.subParser("makeMarkdown.hr")(node,globals)+"\n\n");break;case"ol":spansOnly||(txt=showdown.subParser("makeMarkdown.list")(node,globals,"ol")+"\n\n");break;case"ul":spansOnly||(txt=showdown.subParser("makeMarkdown.list")(node,globals,"ul")+"\n\n");break;case"precode":spansOnly||(txt=showdown.subParser("makeMarkdown.codeBlock")(node,globals)+"\n\n");break;case"pre":spansOnly||(txt=showdown.subParser("makeMarkdown.pre")(node,globals)+"\n\n");break;case"table":spansOnly||(txt=showdown.subParser("makeMarkdown.table")(node,globals)+"\n\n");break;case"code":txt=showdown.subParser("makeMarkdown.codeSpan")(node,globals);break;case"em":case"i":txt=showdown.subParser("makeMarkdown.emphasis")(node,globals);break;case"strong":case"b":txt=showdown.subParser("makeMarkdown.strong")(node,globals);break;case"del":txt=showdown.subParser("makeMarkdown.strikethrough")(node,globals);break;case"a":txt=showdown.subParser("makeMarkdown.links")(node,globals);break;case"img":txt=showdown.subParser("makeMarkdown.image")(node,globals);break;default:txt=node.outerHTML+"\n\n"}return txt})),showdown.subParser("makeMarkdown.paragraph",(function(node,globals){"use strict";var txt="";if(node.hasChildNodes())for(var children=node.childNodes,childrenLength=children.length,i=0;i<childrenLength;++i)txt+=showdown.subParser("makeMarkdown.node")(children[i],globals);return txt=txt.trim()})),showdown.subParser("makeMarkdown.pre",(function(node,globals){"use strict";var num=node.getAttribute("prenum");return"<pre>"+globals.preList[num]+"</pre>"})),showdown.subParser("makeMarkdown.strikethrough",(function(node,globals){"use strict";var txt="";if(node.hasChildNodes()){txt+="~~";for(var children=node.childNodes,childrenLength=children.length,i=0;i<childrenLength;++i)txt+=showdown.subParser("makeMarkdown.node")(children[i],globals);txt+="~~"}return txt})),showdown.subParser("makeMarkdown.strong",(function(node,globals){"use strict";var txt="";if(node.hasChildNodes()){txt+="**";for(var children=node.childNodes,childrenLength=children.length,i=0;i<childrenLength;++i)txt+=showdown.subParser("makeMarkdown.node")(children[i],globals);txt+="**"}return txt})),showdown.subParser("makeMarkdown.table",(function(node,globals){"use strict";var i,ii,txt="",tableArray=[[],[]],headings=node.querySelectorAll("thead>tr>th"),rows=node.querySelectorAll("tbody>tr");for(i=0;i<headings.length;++i){var headContent=showdown.subParser("makeMarkdown.tableCell")(headings[i],globals),allign="---";if(headings[i].hasAttribute("style"))switch(headings[i].getAttribute("style").toLowerCase().replace(/\s/g,"")){case"text-align:left;":allign=":---";break;case"text-align:right;":allign="---:";break;case"text-align:center;":allign=":---:"}tableArray[0][i]=headContent.trim(),tableArray[1][i]=allign}for(i=0;i<rows.length;++i){var r=tableArray.push([])-1,cols=rows[i].getElementsByTagName("td");for(ii=0;ii<headings.length;++ii){var cellContent=" ";void 0!==cols[ii]&&(cellContent=showdown.subParser("makeMarkdown.tableCell")(cols[ii],globals)),tableArray[r].push(cellContent)}}var cellSpacesCount=3;for(i=0;i<tableArray.length;++i)for(ii=0;ii<tableArray[i].length;++ii){var strLen=tableArray[i][ii].length;strLen>cellSpacesCount&&(cellSpacesCount=strLen)}for(i=0;i<tableArray.length;++i){for(ii=0;ii<tableArray[i].length;++ii)1===i?":"===tableArray[i][ii].slice(-1)?tableArray[i][ii]=showdown.helper.padEnd(tableArray[i][ii].slice(-1),cellSpacesCount-1,"-")+":":tableArray[i][ii]=showdown.helper.padEnd(tableArray[i][ii],cellSpacesCount,"-"):tableArray[i][ii]=showdown.helper.padEnd(tableArray[i][ii],cellSpacesCount);txt+="| "+tableArray[i].join(" | ")+" |\n"}return txt.trim()})),showdown.subParser("makeMarkdown.tableCell",(function(node,globals){"use strict";var txt="";if(!node.hasChildNodes())return"";for(var children=node.childNodes,childrenLength=children.length,i=0;i<childrenLength;++i)txt+=showdown.subParser("makeMarkdown.node")(children[i],globals,!0);return txt.trim()})),showdown.subParser("makeMarkdown.txt",(function(node){"use strict";var txt=node.nodeValue;return txt=(txt=txt.replace(/ +/g," ")).replace(/¨NBSP;/g," "),txt=(txt=(txt=(txt=(txt=(txt=(txt=(txt=(txt=showdown.helper.unescapeHTMLEntities(txt)).replace(/([*_~|`])/g,"\\$1")).replace(/^(\s*)>/g,"\\$1>")).replace(/^#/gm,"\\#")).replace(/^(\s*)([-=]{3,})(\s*)$/,"$1\\$2$3")).replace(/^( {0,3}\d+)\./gm,"$1\\.")).replace(/^( {0,3})([+-])/gm,"$1\\$2")).replace(/]([\s]*)\(/g,"\\]$1\\(")).replace(/^ {0,3}\[([\S \t]*?)]:/gm,"\\[$1]:")})),showdown.Converter=function(converterOptions){"use strict";var options={},langExtensions=[],outputModifiers=[],listeners={},setConvFlavor=setFlavor,metadata={parsed:{},raw:"",format:""};function _parseExtension(ext,name){if(name=name||null,showdown.helper.isString(ext)){if(name=ext=showdown.helper.stdExtName(ext),showdown.extensions[ext])return console.warn("DEPRECATION WARNING: "+ext+" is an old extension that uses a deprecated loading method.Please inform the developer that the extension should be updated!"),void function(ext,name){"function"==typeof ext&&(ext=ext(new showdown.Converter));showdown.helper.isArray(ext)||(ext=[ext]);var valid=validate(ext,name);if(!valid.valid)throw Error(valid.error);for(var i=0;i<ext.length;++i)switch(ext[i].type){case"lang":langExtensions.push(ext[i]);break;case"output":outputModifiers.push(ext[i]);break;default:throw Error("Extension loader error: Type unrecognized!!!")}}(showdown.extensions[ext],ext);if(showdown.helper.isUndefined(extensions[ext]))throw Error('Extension "'+ext+'" could not be loaded. It was either not found or is not a valid extension.');ext=extensions[ext]}"function"==typeof ext&&(ext=ext()),showdown.helper.isArray(ext)||(ext=[ext]);var validExt=validate(ext,name);if(!validExt.valid)throw Error(validExt.error);for(var i=0;i<ext.length;++i){switch(ext[i].type){case"lang":langExtensions.push(ext[i]);break;case"output":outputModifiers.push(ext[i])}if(ext[i].hasOwnProperty("listeners"))for(var ln in ext[i].listeners)ext[i].listeners.hasOwnProperty(ln)&&listen(ln,ext[i].listeners[ln])}}function listen(name,callback){if(!showdown.helper.isString(name))throw Error("Invalid argument in converter.listen() method: name must be a string, but "+typeof name+" given");if("function"!=typeof callback)throw Error("Invalid argument in converter.listen() method: callback must be a function, but "+typeof callback+" given");name=name.toLowerCase(),listeners.hasOwnProperty(name)||(listeners[name]=[]),listeners[name].push(callback)}!function(){for(var gOpt in converterOptions=converterOptions||{},globalOptions)globalOptions.hasOwnProperty(gOpt)&&(options[gOpt]=globalOptions[gOpt]);if("object"!=typeof converterOptions)throw Error("Converter expects the passed parameter to be an object, but "+typeof converterOptions+" was passed instead.");for(var opt in converterOptions)converterOptions.hasOwnProperty(opt)&&(options[opt]=converterOptions[opt]);options.extensions&&showdown.helper.forEach(options.extensions,_parseExtension)}(),this._dispatch=function(evtName,text,options,globals,pParams){evtName=evtName.toLowerCase();var params=pParams||{};params.converter=this,params.text=text,params.options=options,params.globals=globals;var event=new showdown.helper.Event(evtName,text,params);if(listeners.hasOwnProperty(evtName))for(var ei=0;ei<listeners[evtName].length;++ei){var nText=listeners[evtName][ei](event);nText&&void 0!==nText&&event.setText(nText)}return event},this.listen=function(name,callback){return listen(name,callback),this},this.makeHtml=function(text){if(!text)return text;var globals={gHtmlBlocks:[],gHtmlMdBlocks:[],gHtmlSpans:[],gUrls:{},gTitles:{},gDimensions:{},gListLevel:0,hashLinkCounts:{},langExtensions:langExtensions,outputModifiers:outputModifiers,converter:this,ghCodeBlocks:[],metadata:{parsed:{},raw:"",format:""}};return text=(text=(text=(text=(text=text.replace(/¨/g,"¨T")).replace(/\$/g,"¨D")).replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/\u00A0/g,"&nbsp;"),options.smartIndentationFix&&(text=function(text){var rsp=text.match(/^\s*/)[0].length,rgx=new RegExp("^\\s{0,"+rsp+"}","gm");return text.replace(rgx,"")}(text)),text="\n\n"+text+"\n\n",text=(text=showdown.subParser("makehtml.detab")(text,options,globals)).replace(/^[ \t]+$/gm,""),showdown.helper.forEach(langExtensions,(function(ext){text=showdown.subParser("makehtml.runExtension")(ext,text,options,globals)})),text=showdown.subParser("makehtml.metadata")(text,options,globals),text=showdown.subParser("makehtml.hashPreCodeTags")(text,options,globals),text=showdown.subParser("makehtml.githubCodeBlocks")(text,options,globals),text=showdown.subParser("makehtml.hashHTMLBlocks")(text,options,globals),text=showdown.subParser("makehtml.hashCodeTags")(text,options,globals),text=showdown.subParser("makehtml.stripLinkDefinitions")(text,options,globals),text=showdown.subParser("makehtml.blockGamut")(text,options,globals),text=showdown.subParser("makehtml.unhashHTMLSpans")(text,options,globals),text=(text=(text=showdown.subParser("makehtml.unescapeSpecialChars")(text,options,globals)).replace(/¨D/g,"$$")).replace(/¨T/g,"¨"),text=showdown.subParser("makehtml.completeHTMLDocument")(text,options,globals),showdown.helper.forEach(outputModifiers,(function(ext){text=showdown.subParser("makehtml.runExtension")(ext,text,options,globals)})),metadata=globals.metadata,text},this.makeMarkdown=function(src){src=(src=(src=src.replace(/\r\n/g,"\n")).replace(/\r/g,"\n")).replace(/>[ \t]+</,">¨NBSP;<");var doc=showdown.helper.document.createElement("div");doc.innerHTML=src;var globals={preList:function(doc){for(var pres=doc.querySelectorAll("pre"),presPH=[],i=0;i<pres.length;++i)if(1===pres[i].childElementCount&&"code"===pres[i].firstChild.tagName.toLowerCase()){var content=pres[i].firstChild.innerHTML.trim(),language=pres[i].firstChild.getAttribute("data-language")||"";if(""===language)for(var classes=pres[i].firstChild.className.split(" "),c=0;c<classes.length;++c){var matches=classes[c].match(/^language-(.+)$/);if(null!==matches){language=matches[1];break}}content=showdown.helper.unescapeHTMLEntities(content),presPH.push(content),pres[i].outerHTML='<precode language="'+language+'" precodenum="'+i.toString()+'"></precode>'}else presPH.push(pres[i].innerHTML),pres[i].innerHTML="",pres[i].setAttribute("prenum",i.toString());return presPH}(doc)};!function clean(node){for(var n=0;n<node.childNodes.length;++n){var child=node.childNodes[n];3===child.nodeType?/\S/.test(child.nodeValue)?(child.nodeValue=child.nodeValue.split("\n").join(" "),child.nodeValue=child.nodeValue.replace(/(\s)+/g,"$1")):(node.removeChild(child),--n):1===child.nodeType&&clean(child)}}(doc);for(var nodes=doc.childNodes,mdDoc="",i=0;i<nodes.length;i++)mdDoc+=showdown.subParser("makeMarkdown.node")(nodes[i],globals);return mdDoc},this.setOption=function(key,value){options[key]=value},this.getOption=function(key){return options[key]},this.getOptions=function(){return options},this.addExtension=function(extension,name){_parseExtension(extension,name=name||null)},this.useExtension=function(extensionName){_parseExtension(extensionName)},this.setFlavor=function(name){if(!flavor.hasOwnProperty(name))throw Error(name+" flavor was not found");var preset=flavor[name];for(var option in setConvFlavor=name,preset)preset.hasOwnProperty(option)&&(options[option]=preset[option])},this.getFlavor=function(){return setConvFlavor},this.removeExtension=function(extension){showdown.helper.isArray(extension)||(extension=[extension]);for(var a=0;a<extension.length;++a){for(var ext=extension[a],i=0;i<langExtensions.length;++i)langExtensions[i]===ext&&langExtensions[i].splice(i,1);for(;0<outputModifiers.length;++i)outputModifiers[0]===ext&&outputModifiers[0].splice(i,1)}},this.getAllExtensions=function(){return{language:langExtensions,output:outputModifiers}},this.getMetadata=function(raw){return raw?metadata.raw:metadata.parsed},this.getMetadataFormat=function(){return metadata.format},this._setMetadataPair=function(key,value){metadata.parsed[key]=value},this._setMetadataFormat=function(format){metadata.format=format},this._setMetadataRaw=function(raw){metadata.raw=raw}};"function"==typeof define&&define.amd?define((function(){"use strict";return showdown})):"undefined"!=typeof module&&module.exports?module.exports=showdown:this.showdown=showdown}).call(window);