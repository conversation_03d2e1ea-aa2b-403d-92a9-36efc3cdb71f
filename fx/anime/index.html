<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="shortcut icon" href="../fx.png" />
    <link rel="apple-touch-icon" sizes="64x64" href="../fx.png" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="author" content="foxess.ru">
    <meta name="description" content="fx-anime" />
    <title>fx-anime</title>
</head>

<body>
    <fx-anime id="fx_anime" showAnime></fx-anime>
    <script type="module">
        import './anime.js';
        fx_anime.args = {
            animemax: 30,
            // animecolor: ['#b9dff5', '#7fc7ff', '#7fb1ff', '#7fc7ff', '#b9dff5', 'blue', 'white'],
            animeletter: '⭐', // '&#10052;',
            animemaxsize: 60,
            animeminsize: 30
        }
    </script>
</body>

</html>
